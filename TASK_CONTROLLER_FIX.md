# TaskController 请求体缺失问题修复

## 问题描述

在Spring Boot应用中，TaskController的几个方法出现了以下错误：

```
org.springframework.http.converter.HttpMessageNotReadableException: Required request body is missing
```

错误发生在以下方法：
- `getTasksByCourseId(@PathVariable Long courseId, @RequestBody TaskWorkQueryDTO queryDTO)`
- `getTasksByacademicYear(@PathVariable Long courseId, @RequestBody TaskWorkQueryDTO queryDTO)`
- `getCourseTaskWorkPage(@PathVariable Long courseId, @RequestBody TaskWorkQueryDTO queryDTO)`

## 问题原因

这些方法使用了`@RequestBody`注解来接收`TaskWorkQueryDTO`参数，但是：
1. 客户端发送的请求没有包含请求体（request body）
2. Spring Boot默认情况下`@RequestBody`是必需的，如果没有请求体会抛出异常

## 解决方案

### 1. 修改`@RequestBody`为可选

将所有相关方法的`@RequestBody`改为`@RequestBody(required = false)`：

```java
// 修改前
public R<List<TaskWorkDetailVO>> getTasksByCourseId(@PathVariable Long courseId, @RequestBody TaskWorkQueryDTO queryDTO)

// 修改后  
public R<List<TaskWorkDetailVO>> getTasksByCourseId(@PathVariable Long courseId, @RequestBody(required = false) TaskWorkQueryDTO queryDTO)
```

### 2. 添加参数验证

为每个方法添加适当的参数验证：

```java
// 参数验证
if (courseId == null || courseId <= 0) {
    return R.fail(400, "课程ID参数无效");
}

// 对于需要学年学期的方法
if (queryDTO == null) {
    return R.fail(400, "查询条件不能为空，请提供学年和学期信息");
}

if (queryDTO.getTaskYear() == null || queryDTO.getTaskTerm() == null) {
    return R.fail(400, "学年和学期参数不能为空");
}

// 对于分页查询方法，提供默认值
if (queryDTO == null) {
    queryDTO = new TaskWorkQueryDTO();
}
```

### 3. 添加异常处理

为所有方法添加try-catch块来处理可能的异常：

```java
try {
    List<TaskWorkDetailVO> taskDetails = taskWorkService.getCourseTaskWorkBySemester(courseId, queryDTO);
    return R.ok(taskDetails);
} catch (Exception e) {
    return R.fail(500, "获取教学任务数据失败: " + e.getMessage());
}
```

## 修改的方法

### 1. getTasksByCourseId
- 将`@RequestBody`改为`@RequestBody(required = false)`
- 添加courseId和queryDTO的null检查
- 验证taskYear和taskTerm不能为空
- 添加异常处理

### 2. getTasksByacademicYear  
- 将`@RequestBody`改为`@RequestBody(required = false)`
- 添加courseId和queryDTO的null检查
- 验证taskYear和taskTerm不能为空
- 添加异常处理

### 3. getCourseTaskWorkPage
- 将`@RequestBody`改为`@RequestBody(required = false)`
- 添加courseId的null检查
- 为null的queryDTO提供默认值（因为分页查询可以使用默认分页参数）
- 添加异常处理

## 测试

创建了`TaskControllerTest`类来测试修复后的功能：
- 测试没有请求体时的错误处理
- 测试空请求体的处理
- 测试有效请求体的处理
- 测试无效课程ID的处理

## API使用说明

### 对于需要学年学期的接口（getTasksByCourseId, getTasksByacademicYear）

**必须**提供包含taskYear和taskTerm的请求体：

```json
POST /teaching/task/course/{courseId}
Content-Type: application/json

{
    "taskYear": 2024,
    "taskTerm": 1
}
```

### 对于分页查询接口（getCourseTaskWorkPage）

可以不提供请求体（使用默认分页），也可以提供查询条件：

```json
POST /teaching/task/page/{courseId}
Content-Type: application/json

{
    "taskYear": 2024,
    "taskTerm": 1,
    "page": {
        "current": 1,
        "size": 10
    }
}
```

## 注意事项

1. 这个修复保持了向后兼容性，现有的客户端代码仍然可以正常工作
2. 新的错误消息更加明确，帮助客户端了解需要提供什么参数
3. 所有修改都遵循了Spring Boot的最佳实践
4. 添加了适当的日志记录和异常处理
