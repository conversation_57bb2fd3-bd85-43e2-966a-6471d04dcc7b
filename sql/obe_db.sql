/*
 Navicat Premium Dump SQL

 Source Server         : 本地MySQL
 Source Server Type    : MySQL
 Source Server Version : 80404 (8.4.4)
 Source Host           : localhost:3306
 Source Schema         : obe_db

 Target Server Type    : MySQL
 Target Server Version : 80404 (8.4.4)
 File Encoding         : 65001

 Date: 15/06/2025 11:42:44
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for assessment
-- ----------------------------
DROP TABLE IF EXISTS `assessment`;
CREATE TABLE `assessment`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '记录id',
  `task_id` bigint NOT NULL COMMENT '教学任务id，查task_worklist -> id',
  `course_id` bigint NOT NULL COMMENT '课程id，查tp_course -> id',
  `assessment_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '考核名称',
  `assessment_type` int NOT NULL COMMENT '考核类型（对应教学大纲中配置考核方式的序号:作业、测验、期末等）',
  `assessment_date` json NULL COMMENT '考核时间，json格式：{\"startTime\": \"2023-10-01 08:00:00\", \"endTime\": \"2023-10-01 10:00:00\"}',
  `assessment_weight` decimal(10, 0) NULL DEFAULT NULL COMMENT '考核权重',
  `assessment_detail` json NULL COMMENT '考核试卷内容详情，json格式：[{ qid:题库题目id, qscore:本次考核设置分值,...}]',
  `status` int NOT NULL DEFAULT 0 COMMENT '记录状态{0:正常状态；-1:删除状态；}',
  `creator` bigint NOT NULL DEFAULT 0 COMMENT '记录创建者',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录',
  `modifier` bigint NOT NULL DEFAULT 0 COMMENT '记录最后修改者',
  `modify_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录最后修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '教学任务考核表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of assessment
-- ----------------------------

-- ----------------------------
-- Table structure for assessment_score_detail
-- ----------------------------
DROP TABLE IF EXISTS `assessment_score_detail`;
CREATE TABLE `assessment_score_detail`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '记录id',
  `student_id` bigint NOT NULL COMMENT '学生id',
  `assessment_id` bigint NOT NULL COMMENT '考核id，查assessment -> id',
  `repository_answer_id` bigint NOT NULL COMMENT '题目答案id，查repository_answer -> id',
  `student_answer` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '学生答案',
  `score` decimal(10, 0) NULL DEFAULT NULL COMMENT '学生得分',
  `question_score` decimal(10, 0) NULL DEFAULT NULL COMMENT '题目总分（冗余）',
  `course_target_no` int NOT NULL COMMENT '对应课程目标几，查tp_course -> course_target',
  `grae_id` bigint NULL DEFAULT NULL COMMENT '对应该专业的毕业要求的id，查tp_po -> id，需要冗余，计算达成度需要',
  `task_id` bigint NOT NULL COMMENT '教学任务id，查task_worklist -> id,冗余数据',
  `course_id` bigint NULL DEFAULT NULL COMMENT '课程id，查tp_course -> id',
  `major_id` bigint NULL DEFAULT NULL COMMENT '专业id，查base_major -> id',
  `status` int NOT NULL DEFAULT 0 COMMENT '记录状态{0:正常状态；-1:删除状态；}',
  `creator` bigint NOT NULL DEFAULT 0 COMMENT '记录创建者',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录',
  `modifier` bigint NOT NULL DEFAULT 0 COMMENT '记录最后修改者',
  `modify_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录最后修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '教学任务考核结果详情数据表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of assessment_score_detail
-- ----------------------------

-- ----------------------------
-- Table structure for assessment_score_target
-- ----------------------------
DROP TABLE IF EXISTS `assessment_score_target`;
CREATE TABLE `assessment_score_target`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '记录id',
  `student_id` bigint NOT NULL COMMENT '学生id',
  `assessment_id` bigint NOT NULL COMMENT '考核id，查assessment -> id',
  `repository_answer_id` bigint NOT NULL COMMENT '题目答案id，查repository_answer -> id',
  `score` decimal(10, 0) NULL DEFAULT NULL COMMENT '学生得分',
  `course_target_no` int NOT NULL COMMENT '对应课程目标几，查tp_course -> course_target',
  `grae_id` bigint NULL DEFAULT NULL COMMENT '对应该专业的毕业要求的id，查tp_po -> id，需要冗余，计算达成度需要',
  `task_id` bigint NOT NULL COMMENT '教学任务id，查task_worklist -> id',
  `course_id` bigint NULL DEFAULT NULL COMMENT '课程id，查tp_course -> id',
  `major_id` bigint NULL DEFAULT NULL COMMENT '专业id，查base_major -> id',
  `status` int NOT NULL DEFAULT 0 COMMENT '记录状态{0:正常状态；-1:删除状态；}',
  `creator` bigint NOT NULL DEFAULT 0 COMMENT '记录创建者',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录',
  `modifier` bigint NOT NULL DEFAULT 0 COMMENT '记录最后修改者',
  `modify_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录最后修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '教学任务考核结果详情数据表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of assessment_score_target
-- ----------------------------

-- ----------------------------
-- Table structure for base_academy
-- ----------------------------
DROP TABLE IF EXISTS `base_academy`;
CREATE TABLE `base_academy`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '学院id',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '学院名称',
  `president_id` bigint NULL DEFAULT NULL COMMENT '院系负责人id:关联用户ID',
  `status` tinyint(1) NULL DEFAULT 0 COMMENT '记录状态{0:正常状态；-1:删除状态；}',
  `creator` bigint NULL DEFAULT NULL COMMENT '记录创建人',
  `create_time` datetime NULL DEFAULT NULL COMMENT '记录创建时间',
  `modifier` bigint NULL DEFAULT NULL COMMENT '记录最后修改人',
  `modify_time` datetime NULL DEFAULT NULL COMMENT '记录最后修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 22 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '院系表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of base_academy
-- ----------------------------
INSERT INTO `base_academy` VALUES (1, '计算机科学与技术学院', NULL, 0, 1, '2025-05-11 20:27:51', 96, '2025-06-13 00:49:31');
INSERT INTO `base_academy` VALUES (2, '新能源学院', NULL, -1, 1, '2025-05-22 13:52:22', 1, '2025-05-22 13:52:37');
INSERT INTO `base_academy` VALUES (3, '电子信息', NULL, 0, 1, '2025-05-22 13:52:24', 96, '2025-06-13 00:49:38');
INSERT INTO `base_academy` VALUES (4, '人工智能', 111, 0, 1, '2025-05-22 13:53:14', 96, '2025-06-13 00:49:04');
INSERT INTO `base_academy` VALUES (5, '计算机学院', NULL, 0, 1, '2025-05-22 13:53:59', 96, '2025-06-13 00:48:54');
INSERT INTO `base_academy` VALUES (15, '电子信息工程学院', 110, 0, 96, '2025-06-12 21:23:01', 96, '2025-06-13 00:49:17');
INSERT INTO `base_academy` VALUES (16, '软件学院', NULL, -1, 96, '2025-06-13 01:37:09', 96, '2025-06-13 01:38:05');
INSERT INTO `base_academy` VALUES (17, '哈哈学院', NULL, -1, 96, '2025-06-13 01:37:09', 96, '2025-06-13 01:38:10');
INSERT INTO `base_academy` VALUES (18, '机械工程学院', NULL, -1, 96, '2025-06-13 01:37:09', 96, '2025-06-13 01:38:14');
INSERT INTO `base_academy` VALUES (19, '软件学院', NULL, 0, 96, '2025-06-13 01:38:31', 96, '2025-06-13 01:38:31');
INSERT INTO `base_academy` VALUES (20, '哈哈学院', NULL, 0, 96, '2025-06-13 01:38:31', 96, '2025-06-13 01:38:31');
INSERT INTO `base_academy` VALUES (21, '机械工程学院', NULL, 0, 96, '2025-06-13 01:38:31', 96, '2025-06-13 01:38:31');

-- ----------------------------
-- Table structure for base_classes
-- ----------------------------
DROP TABLE IF EXISTS `base_classes`;
CREATE TABLE `base_classes`  (
  `class_id` bigint NOT NULL AUTO_INCREMENT COMMENT '班级ID',
  `major_id` bigint NULL DEFAULT NULL COMMENT '专业ID',
  `class_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '班级名称',
  `entrance_year` year NULL DEFAULT NULL COMMENT '入学年份',
  `headteacher_id` bigint NULL DEFAULT NULL COMMENT '班主任ID',
  `student_number` int NULL DEFAULT NULL COMMENT '学生人数',
  `class_status` tinyint(1) NULL DEFAULT 0 COMMENT '班级状态 -1:毕业 0:在读',
  `status` tinyint(1) NULL DEFAULT NULL COMMENT '记录状态{0:正常状态；-1:删除状态；}',
  `creator` bigint NULL DEFAULT NULL COMMENT '记录创建人',
  `create_time` datetime NULL DEFAULT NULL COMMENT '记录创建时间',
  `modifier` bigint NULL DEFAULT NULL COMMENT '记录最后修改人',
  `modify_time` datetime NULL DEFAULT NULL COMMENT '记录最后修改时间',
  PRIMARY KEY (`class_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '班级表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of base_classes
-- ----------------------------
INSERT INTO `base_classes` VALUES (1, 1, 'RB软工数231', 2023, 0, 73, 0, 0, 0, '2025-05-11 20:15:38', 0, '2025-05-11 20:15:42');
INSERT INTO `base_classes` VALUES (2, 1, 'RB软工数232', 2023, 0, 73, 0, 0, 0, '2025-05-11 20:15:38', 0, '2025-05-11 20:15:42');
INSERT INTO `base_classes` VALUES (3, 1, 'RB软工数233', 2023, 0, 73, 0, 0, 0, '2025-05-11 20:15:38', 0, '2025-05-11 20:15:42');
INSERT INTO `base_classes` VALUES (4, 1, 'RB软工数234', 2023, 0, 73, 0, 0, 0, '2025-05-11 20:15:38', 0, '2025-05-11 20:15:42');
INSERT INTO `base_classes` VALUES (5, 2, 'RB软工数235', 2023, 0, 73, 0, 0, 0, '2025-05-11 20:15:38', 0, '2025-05-11 20:15:42');
INSERT INTO `base_classes` VALUES (6, 2, 'RB软工数236', 2023, 0, 73, 0, 0, 0, '2025-05-11 20:15:38', 0, '2025-05-11 20:15:42');
INSERT INTO `base_classes` VALUES (7, 4, 'RB软工数237', 2023, 0, 73, 0, 0, 0, '2025-05-11 20:15:38', 0, '2025-05-11 20:15:42');

-- ----------------------------
-- Table structure for base_major
-- ----------------------------
DROP TABLE IF EXISTS `base_major`;
CREATE TABLE `base_major`  (
  `major_id` bigint NOT NULL AUTO_INCREMENT COMMENT '专业id',
  `major_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '专业名称',
  `professional_overview` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '专业概述',
  `major_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '专业代码',
  `academy_id` bigint NULL DEFAULT NULL COMMENT '学院id',
  `academy_leader_id` bigint NULL DEFAULT NULL COMMENT '专业负责人，用户ID',
  `status` tinyint UNSIGNED NULL DEFAULT 0 COMMENT '记录状态{0:正常状态；-1:删除状态；}',
  `creator` bigint NULL DEFAULT NULL COMMENT '记录创建人',
  `create_time` datetime NULL DEFAULT NULL COMMENT '记录创建时间',
  `modifier` bigint NULL DEFAULT NULL COMMENT '记录最后修改人',
  `modify_time` datetime NULL DEFAULT NULL COMMENT '记录最后修改时间',
  PRIMARY KEY (`major_id`) USING BTREE,
  UNIQUE INDEX `base_major_leader_unique`(`academy_leader_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 13 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '专业信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of base_major
-- ----------------------------
INSERT INTO `base_major` VALUES (1, '软件工程', '2005 年中原工学院获批河南第一个软件工程专业并招生，2014 年获批省 “专业综合改革试点 ”，2018 年通过省本科专业评估，2020 年入选国家级一流 本科专业建设点。软件工程专业依托于软件学院，2004 年获批省首批示范性软 件学院，后加入全国示范性软件学院联盟，2009 年通过省示范性软件学院评估， 2020 年获批省级特色化示范性软件学院，同年成立了河南省首批鲲鹏产业学院， 与软件学院合署办公。\n本专业面向国家战略与区域经济社会发展需求。旨在为国家战略性新兴产业 培养扎实的软件工程基础理论，系统掌握软件工程知识与方法，具有较强的工程 实践能力与创新精神的高素质软件工程人才。\n经过多年的建设，形成了独特的产学研协同办学和育人模式，具备鲜明的科 研和社会服务领域特色。全面贯彻基于产出（OBE）的工程教育理念，培养与国 际接轨的软件工程技术人才，形成了“程序设计能力→软件设计能力→软件工程 能力 ”的培养路线。创新实践教学体系，构建以能力为中心的研究性教学培养路 线，形成了课程设计与分阶段综合实践同步实施的“创新创业实践+课程设计+   分阶段综合实践+毕业设计 ”的实践教学体系。\n本专业已累计为国家和地方经济建设培养了6000 余名高素质人才，现有在 校生 2600  余人。', '080902', 1, 6192, 0, 1, '2025-05-11 19:40:11', 6192, '2025-05-30 21:39:19');
INSERT INTO `base_major` VALUES (2, '软工', '软件工程', '2025052301', 1, 3, 0, 1, '2025-05-23 11:05:00', 1, '2025-05-23 11:05:11');
INSERT INTO `base_major` VALUES (3, '计科', '计算机科学专业', '2025052301', 1, 2025052404, 0, 1, '2025-05-23 11:05:02', 6192, '2025-05-30 21:39:30');
INSERT INTO `base_major` VALUES (4, '软件工程1', '软件工程专业1', '2025052301', 2, 2025052405, 0, 1, '2025-05-23 13:00:31', 6192, '2025-05-30 21:39:35');
INSERT INTO `base_major` VALUES (10, '计算机科学与技术', '培养计算机科学与技术专业人才', 'CS001', NULL, NULL, 0, 96, '2025-06-15 09:38:36', 96, '2025-06-15 09:38:36');
INSERT INTO `base_major` VALUES (11, '软件工程', '培养软件工程专业人才', 'SE001', NULL, NULL, 0, 96, '2025-06-15 09:38:36', 96, '2025-06-15 09:38:36');
INSERT INTO `base_major` VALUES (12, '电子信息工程', '培养电子信息工程专业人才', 'EE001', NULL, NULL, 0, 96, '2025-06-15 09:38:36', 96, '2025-06-15 09:38:36');

-- ----------------------------
-- Table structure for base_standard
-- ----------------------------
DROP TABLE IF EXISTS `base_standard`;
CREATE TABLE `base_standard`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '工程教育认证标准库ID',
  `standard_version` bigint NULL DEFAULT NULL COMMENT '专业ID',
  `standard_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '班级名称',
  `standard_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '工程认证指标点描述',
  `discipline_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `release_date` date NULL DEFAULT NULL,
  `parent_id` bigint NULL DEFAULT NULL COMMENT '指标点或者工程认证版本，parent_id=0是工程认证版本，非0是对应的认证版本的指标',
  `status` tinyint(1) NULL DEFAULT NULL COMMENT '记录状态{0:正常状态；-1:删除状态；}',
  `creator` bigint NULL DEFAULT NULL COMMENT '记录创建人',
  `create_time` datetime NULL DEFAULT NULL COMMENT '记录创建时间',
  `modifier` bigint NULL DEFAULT NULL COMMENT '记录最后修改人',
  `modify_time` datetime NULL DEFAULT NULL COMMENT '记录最后修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 61 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '工程教育认证标准库表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of base_standard
-- ----------------------------
INSERT INTO `base_standard` VALUES (1, 2022, '《工程教育认证标准》12项一级指标点（PO1-PO12）', 'test2', '1', '2025-05-02', 0, 0, 0, '2025-05-11 20:51:45', 6192, '2025-06-07 13:47:16');
INSERT INTO `base_standard` VALUES (2, NULL, '工程知识', '能够将数学、 自然科学、工程基础和专业知识用于解决复杂工程问题。', NULL, NULL, 1, 0, 0, '2025-05-11 20:52:56', 6192, '2025-06-07 13:47:16');
INSERT INTO `base_standard` VALUES (26, 2022, '问题分析', '能够应用数学、自然科学和工程科学的基本原理，识别、表达并通过文献研究分析复杂工程问题，得出有效结论。', NULL, NULL, 1, 0, 6192, '2025-05-25 12:59:18', 6192, '2025-06-07 13:47:16');
INSERT INTO `base_standard` VALUES (27, 2022, '设计/开发解决方案', '能够设计针对复杂工程问题的解决方案，设计满足特定需求的系统、单元（部件）或工艺流程，并能够在设计环节中体现创新意识，考虑社会、健康、安全、法律、文化以及环境等因素。', NULL, NULL, 1, 0, 6192, '2025-05-25 12:59:18', 6192, '2025-06-07 13:47:16');
INSERT INTO `base_standard` VALUES (28, 2024, '《工程教育认证标准》12项一级指标点（PO1-PO12）', 'test', '1', '2024-05-02', 0, 0, 6192, '2025-05-26 21:29:59', 6192, '2025-05-26 21:29:59');
INSERT INTO `base_standard` VALUES (29, 2024, '1', '1', NULL, NULL, 28, 0, 6192, '2025-05-26 21:30:00', 6192, '2025-05-26 21:30:00');
INSERT INTO `base_standard` VALUES (30, 2024, '2', '2', NULL, NULL, 28, 0, 6192, '2025-05-26 21:30:00', 6192, '2025-05-26 21:30:00');
INSERT INTO `base_standard` VALUES (31, 2022, '研究', '能够基于科学原理并采用科学方法对复杂工程问题进行研究，包括设计实验、分析与解释数据、并通过信息综合得到合理有效的结论。', NULL, NULL, 1, -1, 6192, '2025-06-07 13:47:09', 6192, '2025-06-07 13:47:12');
INSERT INTO `base_standard` VALUES (32, 2022, '使用现代工具', '能够针对复杂工程问题，开发、选择与使用恰当的技术、资源、现代工程工具和信息技术工具，包括对复杂工程问题的预测与模拟，并能够理解其局限性。', NULL, NULL, 1, -1, 6192, '2025-06-07 13:47:09', 6192, '2025-06-07 13:47:12');
INSERT INTO `base_standard` VALUES (33, 2022, '工程与社会', '能够基于工程相关背景知识进行合理分析，评价专业工程实践和复杂工程问题解决方案对社会、健康、安全、法律以及文化的影响，并理解应承担的责任。', NULL, NULL, 1, -1, 6192, '2025-06-07 13:47:09', 6192, '2025-06-07 13:47:12');
INSERT INTO `base_standard` VALUES (34, 2022, '环境和可持续发展', '能够理解和评价针对复杂工程问题的工程实践对环境、社会可持续发展的影响', NULL, NULL, 1, -1, 6192, '2025-06-07 13:47:09', 6192, '2025-06-07 13:47:12');
INSERT INTO `base_standard` VALUES (35, 2022, '职业规范', '具有人文社会科学素养、社会责任感，能够在工程实践中理解并遵守工程职业道德和规范，履行责任。', NULL, NULL, 1, -1, 6192, '2025-06-07 13:47:09', 6192, '2025-06-07 13:47:12');
INSERT INTO `base_standard` VALUES (36, 2022, '个人和团队', '能够在多学科背景下的团队中承担个体、团队成员以及负责人的角色', NULL, NULL, 1, -1, 6192, '2025-06-07 13:47:09', 6192, '2025-06-07 13:47:12');
INSERT INTO `base_standard` VALUES (37, 2022, '沟通', '能够就复杂工程问题与业界同行及社会公众进行有效沟通和交流，包括撰写报告和设计文稿、陈述发言、清晰表达或回应指令，并具备一定的国际视野，能够在跨文化背景下进行沟通和交流。', NULL, NULL, 1, -1, 6192, '2025-06-07 13:47:09', 6192, '2025-06-07 13:47:12');
INSERT INTO `base_standard` VALUES (38, 2022, '项目管理', '理解并掌握工程管理原理与经济决策方法，并能在多学科环境中应用。', NULL, NULL, 1, -1, 6192, '2025-06-07 13:47:09', 6192, '2025-06-07 13:47:12');
INSERT INTO `base_standard` VALUES (39, 2022, '终身学习', '具有自主学习和终身学习的意识，有不断学习和适应发展的能力。', NULL, NULL, 1, -1, 6192, '2025-06-07 13:47:09', 6192, '2025-06-07 13:47:12');
INSERT INTO `base_standard` VALUES (40, 2022, '研究', '能够基于科学原理并采用科学方法对复杂工程问题进行研究，包括设计实验、分析与解释数据、并通过信息综合得到合理有效的结论。', NULL, NULL, 1, -1, 6192, '2025-06-07 13:47:12', 6192, '2025-06-07 13:47:16');
INSERT INTO `base_standard` VALUES (41, 2022, '使用现代工具', '能够针对复杂工程问题，开发、选择与使用恰当的技术、资源、现代工程工具和信息技术工具，包括对复杂工程问题的预测与模拟，并能够理解其局限性。', NULL, NULL, 1, -1, 6192, '2025-06-07 13:47:12', 6192, '2025-06-07 13:47:16');
INSERT INTO `base_standard` VALUES (42, 2022, '工程与社会', '能够基于工程相关背景知识进行合理分析，评价专业工程实践和复杂工程问题解决方案对社会、健康、安全、法律以及文化的影响，并理解应承担的责任。', NULL, NULL, 1, -1, 6192, '2025-06-07 13:47:12', 6192, '2025-06-07 13:47:16');
INSERT INTO `base_standard` VALUES (43, 2022, '环境和可持续发展', '能够理解和评价针对复杂工程问题的工程实践对环境、社会可持续发展的影响', NULL, NULL, 1, -1, 6192, '2025-06-07 13:47:12', 6192, '2025-06-07 13:47:16');
INSERT INTO `base_standard` VALUES (44, 2022, '职业规范', '具有人文社会科学素养、社会责任感，能够在工程实践中理解并遵守工程职业道德和规范，履行责任。', NULL, NULL, 1, -1, 6192, '2025-06-07 13:47:12', 6192, '2025-06-07 13:47:16');
INSERT INTO `base_standard` VALUES (45, 2022, '个人和团队', '能够在多学科背景下的团队中承担个体、团队成员以及负责人的角色', NULL, NULL, 1, -1, 6192, '2025-06-07 13:47:12', 6192, '2025-06-07 13:47:16');
INSERT INTO `base_standard` VALUES (46, 2022, '沟通', '能够就复杂工程问题与业界同行及社会公众进行有效沟通和交流，包括撰写报告和设计文稿、陈述发言、清晰表达或回应指令，并具备一定的国际视野，能够在跨文化背景下进行沟通和交流。', NULL, NULL, 1, -1, 6192, '2025-06-07 13:47:12', 6192, '2025-06-07 13:47:16');
INSERT INTO `base_standard` VALUES (47, 2022, '项目管理', '理解并掌握工程管理原理与经济决策方法，并能在多学科环境中应用。', NULL, NULL, 1, -1, 6192, '2025-06-07 13:47:12', 6192, '2025-06-07 13:47:16');
INSERT INTO `base_standard` VALUES (48, 2022, '终身学习', '具有自主学习和终身学习的意识，有不断学习和适应发展的能力。', NULL, NULL, 1, -1, 6192, '2025-06-07 13:47:12', 6192, '2025-06-07 13:47:16');
INSERT INTO `base_standard` VALUES (49, 2022, '研究', '能够基于科学原理并采用科学方法对复杂工程问题进行研究，包括设计实验、分析与解释数据、并通过信息综合得到合理有效的结论。', NULL, NULL, 1, 0, 6192, '2025-06-07 13:47:16', 6192, '2025-06-07 13:47:16');
INSERT INTO `base_standard` VALUES (50, 2022, '使用现代工具', '能够针对复杂工程问题，开发、选择与使用恰当的技术、资源、现代工程工具和信息技术工具，包括对复杂工程问题的预测与模拟，并能够理解其局限性。', NULL, NULL, 1, 0, 6192, '2025-06-07 13:47:16', 6192, '2025-06-07 13:47:16');
INSERT INTO `base_standard` VALUES (51, 2022, '工程与社会', '能够基于工程相关背景知识进行合理分析，评价专业工程实践和复杂工程问题解决方案对社会、健康、安全、法律以及文化的影响，并理解应承担的责任。', NULL, NULL, 1, 0, 6192, '2025-06-07 13:47:16', 6192, '2025-06-07 13:47:16');
INSERT INTO `base_standard` VALUES (52, 2022, '环境和可持续发展', '能够理解和评价针对复杂工程问题的工程实践对环境、社会可持续发展的影响', NULL, NULL, 1, 0, 6192, '2025-06-07 13:47:16', 6192, '2025-06-07 13:47:16');
INSERT INTO `base_standard` VALUES (53, 2022, '职业规范', '具有人文社会科学素养、社会责任感，能够在工程实践中理解并遵守工程职业道德和规范，履行责任。', NULL, NULL, 1, 0, 6192, '2025-06-07 13:47:16', 6192, '2025-06-07 13:47:16');
INSERT INTO `base_standard` VALUES (54, 2022, '个人和团队', '能够在多学科背景下的团队中承担个体、团队成员以及负责人的角色', NULL, NULL, 1, 0, 6192, '2025-06-07 13:47:16', 6192, '2025-06-07 13:47:16');
INSERT INTO `base_standard` VALUES (55, 2022, '沟通', '能够就复杂工程问题与业界同行及社会公众进行有效沟通和交流，包括撰写报告和设计文稿、陈述发言、清晰表达或回应指令，并具备一定的国际视野，能够在跨文化背景下进行沟通和交流。', NULL, NULL, 1, 0, 6192, '2025-06-07 13:47:16', 6192, '2025-06-07 13:47:16');
INSERT INTO `base_standard` VALUES (56, 2022, '项目管理', '理解并掌握工程管理原理与经济决策方法，并能在多学科环境中应用。', NULL, NULL, 1, 0, 6192, '2025-06-07 13:47:16', 6192, '2025-06-07 13:47:16');
INSERT INTO `base_standard` VALUES (57, 2022, '终身学习', '具有自主学习和终身学习的意识，有不断学习和适应发展的能力。', NULL, NULL, 1, 0, 6192, '2025-06-07 13:47:16', 6192, '2025-06-07 13:47:16');
INSERT INTO `base_standard` VALUES (58, 2029, 'test陈沛豪1', '这是一个测试样例', '1', '2025-06-08', 0, 0, 6192, '2025-06-09 14:47:28', 6192, '2025-06-09 14:47:28');
INSERT INTO `base_standard` VALUES (59, 2029, '1', '1', NULL, NULL, 58, 0, 6192, '2025-06-09 14:47:28', 6192, '2025-06-09 14:47:28');
INSERT INTO `base_standard` VALUES (60, 2029, '2', '2', NULL, NULL, 58, 0, 6192, '2025-06-09 14:47:28', 6192, '2025-06-09 14:47:28');

-- ----------------------------
-- Table structure for base_student
-- ----------------------------
DROP TABLE IF EXISTS `base_student`;
CREATE TABLE `base_student`  (
  `student_id` bigint NOT NULL AUTO_INCREMENT COMMENT '学生id',
  `base_user_id` bigint UNSIGNED NULL DEFAULT NULL COMMENT '用户基本信息ID',
  `number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '学生学号',
  `class_id` bigint NULL DEFAULT NULL COMMENT '班级id',
  `major_id` bigint NULL DEFAULT NULL COMMENT '专业id',
  `academy_id` bigint NULL DEFAULT NULL COMMENT '学院id',
  `entrance_year` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '入学年份',
  `student_status` tinyint(1) NULL DEFAULT NULL COMMENT '学籍状态，-1表示毕业，0表示在读',
  `creator` bigint NULL DEFAULT NULL COMMENT '记录创建者',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录',
  `modifier` bigint NULL DEFAULT NULL COMMENT '记录最后修改者',
  `modify_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '记录最后修改时间',
  `status` int NULL DEFAULT 0 COMMENT '记录状态{0:正常状态；-1:删除状态；}',
  PRIMARY KEY (`student_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 15 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '学生表\r\n本表只存储用户的学生的详细信息【基本信息表存在的数据在本表不再存储】' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of base_student
-- ----------------------------
INSERT INTO `base_student` VALUES (1, NULL, NULL, 1, 1, 3, '2024', 1, 1, '2025-05-24 20:34:05', NULL, NULL, 1);
INSERT INTO `base_student` VALUES (2, NULL, NULL, 1, 1, 1, '2024', 0, 1, '2025-05-24 20:34:08', 1, '2025-05-24 20:34:16', 0);
INSERT INTO `base_student` VALUES (3, NULL, NULL, 1, 1, 1, '2024', 0, 1, '2025-05-24 20:34:18', 1, '2025-05-24 20:34:19', 0);
INSERT INTO `base_student` VALUES (4, NULL, NULL, 1, 1, 1, '2025', 1, 1, '2025-05-24 20:34:20', 1, '2025-05-24 20:34:21', 0);
INSERT INTO `base_student` VALUES (5, NULL, NULL, 1, 1, 1, '2026', 2, 1, '2025-05-24 20:34:22', 1, '2025-05-24 20:34:24', 0);
INSERT INTO `base_student` VALUES (6, NULL, NULL, 1, 1, 1, '2027', 3, 1, '2025-05-24 20:34:26', 1, '2025-05-24 20:34:31', 0);
INSERT INTO `base_student` VALUES (8, NULL, NULL, 1, 1, 1, '2024', 0, NULL, NULL, 6192, '2025-06-10 17:04:39', -1);
INSERT INTO `base_student` VALUES (14, NULL, NULL, 1, 1, 1, '2024', 0, NULL, NULL, 6192, '2025-06-10 17:02:30', -1);

-- ----------------------------
-- Table structure for base_teacher
-- ----------------------------
DROP TABLE IF EXISTS `base_teacher`;
CREATE TABLE `base_teacher`  (
  `teacher_id` bigint NOT NULL AUTO_INCREMENT COMMENT '教师id',
  `base_user_id` bigint UNSIGNED NULL DEFAULT NULL COMMENT '关联用户基本信息ID',
  `number` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '工号',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '职称',
  `academy_id` bigint NULL DEFAULT NULL COMMENT '学院id',
  `status` int NULL DEFAULT 0 COMMENT '记录状态{0:正常状态；-1:删除状态；}',
  `creator` bigint NULL DEFAULT NULL COMMENT '记录创建者',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录',
  `modifier` bigint NULL DEFAULT NULL COMMENT '记录最后修改者',
  `modify_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '记录最后修改时间',
  PRIMARY KEY (`teacher_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 74 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '教师表\r\n本表存储教师的详细信息【基本信息表存在的数据在本表不再存储】' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of base_teacher
-- ----------------------------
INSERT INTO `base_teacher` VALUES (1, NULL, '6192', '教授', 1, -1, 1, '2025-05-11 20:33:51', 96, '2025-06-12 20:16:30');
INSERT INTO `base_teacher` VALUES (3, NULL, '2025052401', '教师', 4, -1, NULL, NULL, 96, '2025-06-12 20:16:30');
INSERT INTO `base_teacher` VALUES (4, NULL, '2025052402', '教师', 2, -1, NULL, NULL, 96, '2025-06-12 20:16:30');
INSERT INTO `base_teacher` VALUES (5, NULL, '2025052403', '教师', NULL, -1, NULL, NULL, 6192, '2025-06-10 15:23:53');
INSERT INTO `base_teacher` VALUES (6, NULL, '2025052404', '教师', NULL, -1, NULL, NULL, 6192, '2025-06-11 12:54:49');
INSERT INTO `base_teacher` VALUES (7, NULL, '2025052405', '教师', NULL, -1, NULL, NULL, 6192, '2025-06-11 12:54:49');
INSERT INTO `base_teacher` VALUES (8, NULL, '20250524', '教授', 2, -1, NULL, NULL, 6192, '2025-06-08 23:58:51');
INSERT INTO `base_teacher` VALUES (14, NULL, '12', '副教授', NULL, -1, 6192, '2025-06-08 23:45:21', 6192, '2025-06-11 12:54:49');
INSERT INTO `base_teacher` VALUES (26, NULL, '202505240102', '教授', 2, -1, NULL, NULL, 96, '2025-06-12 20:16:30');
INSERT INTO `base_teacher` VALUES (27, NULL, '2121', '副教授', 2, -1, 6192, '2025-06-10 11:06:33', 96, '2025-06-12 20:16:26');
INSERT INTO `base_teacher` VALUES (28, NULL, '1', '教师', NULL, -1, NULL, NULL, 6192, '2025-06-11 13:09:16');
INSERT INTO `base_teacher` VALUES (29, NULL, '2', '教师', NULL, -1, NULL, NULL, 6192, '2025-06-11 13:09:16');
INSERT INTO `base_teacher` VALUES (30, NULL, '3', '教师', NULL, -1, NULL, NULL, 96, '2025-06-12 20:16:30');
INSERT INTO `base_teacher` VALUES (31, NULL, '4', '教师', NULL, -1, NULL, NULL, 96, '2025-06-12 20:16:30');
INSERT INTO `base_teacher` VALUES (32, NULL, '5', '教师', NULL, -1, NULL, NULL, 96, '2025-06-12 20:16:30');
INSERT INTO `base_teacher` VALUES (38, NULL, '2025052412', '教师', NULL, -1, 6192, '2025-06-10 15:20:57', 96, '2025-06-12 20:16:26');
INSERT INTO `base_teacher` VALUES (39, NULL, '202505212', '教师', NULL, -1, 6192, '2025-06-10 15:20:57', 96, '2025-06-12 20:16:26');
INSERT INTO `base_teacher` VALUES (40, NULL, '20250524134', '教师', NULL, -1, 6192, '2025-06-10 15:20:57', 96, '2025-06-12 20:16:26');
INSERT INTO `base_teacher` VALUES (41, NULL, '2025052445', '教师', NULL, -1, 6192, '2025-06-10 15:20:57', 96, '2025-06-12 20:16:26');
INSERT INTO `base_teacher` VALUES (42, NULL, '2025052488', '教师', NULL, -1, 6192, '2025-06-10 15:20:57', 96, '2025-06-12 20:16:26');
INSERT INTO `base_teacher` VALUES (43, NULL, '42432', '教授/副教授/讲师/助教', 4, -1, 6192, '2025-06-11 12:36:10', 96, '2025-06-12 20:16:25');
INSERT INTO `base_teacher` VALUES (44, NULL, '2000', '教授/副教授/讲师/助教', NULL, -1, 6192, '2025-06-11 12:47:08', 96, '2025-06-12 20:16:25');
INSERT INTO `base_teacher` VALUES (46, NULL, '211', '教授', NULL, -1, 6192, '2025-06-11 12:48:54', 96, '2025-06-12 20:16:25');
INSERT INTO `base_teacher` VALUES (48, NULL, '2001', '教授', 5, -1, 6192, '2025-06-11 13:01:53', 96, '2025-06-12 20:15:56');
INSERT INTO `base_teacher` VALUES (49, NULL, '2002', '副教授', NULL, -1, 6192, '2025-06-11 13:01:53', 96, '2025-06-12 20:16:25');
INSERT INTO `base_teacher` VALUES (51, NULL, '6555', '教授', NULL, -1, 6192, '2025-06-11 13:17:05', 96, '2025-06-12 19:51:54');
INSERT INTO `base_teacher` VALUES (52, NULL, '555', '副教授', NULL, -1, 6192, '2025-06-11 13:17:05', 96, '2025-06-12 19:51:54');
INSERT INTO `base_teacher` VALUES (53, NULL, '2002553', '讲师', NULL, -1, 6192, '2025-06-11 13:17:05', 96, '2025-06-12 19:51:54');
INSERT INTO `base_teacher` VALUES (57, NULL, '7666', '讲师', NULL, -1, 6192, '2025-06-11 13:17:47', 96, '2025-06-12 19:51:54');
INSERT INTO `base_teacher` VALUES (59, NULL, '9999', '教授', NULL, -1, 6192, '2025-06-11 13:19:00', 96, '2025-06-12 19:51:54');
INSERT INTO `base_teacher` VALUES (60, NULL, '8888', '副教授', NULL, -1, 6192, '2025-06-11 13:19:00', 96, '2025-06-12 19:51:54');
INSERT INTO `base_teacher` VALUES (61, NULL, '77777', '讲师', NULL, -1, 6192, '2025-06-11 13:19:00', 96, '2025-06-12 19:51:54');
INSERT INTO `base_teacher` VALUES (65, NULL, '900', '教授', NULL, -1, 6192, '2025-06-11 13:22:37', 96, '2025-06-12 19:51:54');
INSERT INTO `base_teacher` VALUES (66, NULL, '901', '副教授', NULL, -1, 6192, '2025-06-11 13:22:37', 96, '2025-06-12 19:51:54');
INSERT INTO `base_teacher` VALUES (67, NULL, '902', '讲师', NULL, -1, 6192, '2025-06-11 13:22:37', 96, '2025-06-12 20:07:43');
INSERT INTO `base_teacher` VALUES (71, 109, '2', '副教授', 1, 0, 96, '2025-06-12 21:28:17', 96, '2025-06-12 21:53:19');
INSERT INTO `base_teacher` VALUES (72, 110, '2001', '副教授', 1, 0, 96, '2025-06-12 21:28:17', 96, '2025-06-12 21:28:17');
INSERT INTO `base_teacher` VALUES (73, 111, '2002', '讲师', 15, 0, 96, '2025-06-12 21:28:17', 96, '2025-06-12 21:28:17');

-- ----------------------------
-- Table structure for base_teacher_roles
-- ----------------------------
DROP TABLE IF EXISTS `base_teacher_roles`;
CREATE TABLE `base_teacher_roles`  (
  `teacher_number` bigint NOT NULL,
  `role_id` bigint NOT NULL,
  `creator` bigint NOT NULL,
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP,
  `modifier` bigint NOT NULL,
  `modify_time` datetime NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`teacher_number`, `role_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of base_teacher_roles
-- ----------------------------
INSERT INTO `base_teacher_roles` VALUES (6192, 0, 1, '2025-05-24 00:00:00', 0, '2025-06-04 20:42:14');
INSERT INTO `base_teacher_roles` VALUES (6192, 1, 1, '2025-05-24 00:00:00', 0, '2025-06-04 20:42:14');
INSERT INTO `base_teacher_roles` VALUES (6192, 2, 1, '2025-05-24 00:00:00', 0, '2025-06-04 20:42:14');
INSERT INTO `base_teacher_roles` VALUES (6192, 3, 1, '2025-05-24 00:00:00', 0, '2025-06-04 20:42:14');
INSERT INTO `base_teacher_roles` VALUES (6192, 6, 1, '2025-05-24 00:00:00', 0, '2025-06-04 20:42:14');

-- ----------------------------
-- Table structure for graph_links
-- ----------------------------
DROP TABLE IF EXISTS `graph_links`;
CREATE TABLE `graph_links`  (
  `link_id` int NOT NULL AUTO_INCREMENT,
  `link_source` int NULL DEFAULT NULL,
  `link_target` int NULL DEFAULT NULL,
  `link_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
  `status` int NOT NULL DEFAULT 0 COMMENT '记录状态{0:正常状态；-1:删除状态；}',
  `creator` int NOT NULL DEFAULT 0 COMMENT '记录创建者',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录',
  `modifier` int NOT NULL DEFAULT 0 COMMENT '记录最后修改者',
  `modify_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录最后修改时间',
  PRIMARY KEY (`link_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '知识图谱关系链接' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of graph_links
-- ----------------------------

-- ----------------------------
-- Table structure for graph_nodes
-- ----------------------------
DROP TABLE IF EXISTS `graph_nodes`;
CREATE TABLE `graph_nodes`  (
  `node_id` int(11) UNSIGNED ZEROFILL NOT NULL AUTO_INCREMENT,
  `node_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `node_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
  `node_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `parent_id` int NULL DEFAULT NULL,
  `status` int NOT NULL DEFAULT 0 COMMENT '记录状态{0:正常状态；-1:删除状态；}',
  `creator` int NOT NULL DEFAULT 0 COMMENT '记录创建者',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录',
  `modifier` int NOT NULL DEFAULT 0 COMMENT '记录最后修改者',
  `modify_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录最后修改时间',
  PRIMARY KEY (`node_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '知识图谱节点' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of graph_nodes
-- ----------------------------

-- ----------------------------
-- Table structure for repository_answer
-- ----------------------------
DROP TABLE IF EXISTS `repository_answer`;
CREATE TABLE `repository_answer`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '题目对应的答案id',
  `question_id` bigint NOT NULL COMMENT '题目id',
  `question_answer` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '选择题：A、B、C...,填空题：当前空的答案,简答题：第n个才采分点',
  `answer_no` int NOT NULL DEFAULT 0 COMMENT '同一个题目的答案序号，默认是0',
  `answer_score` decimal(10, 0) NOT NULL COMMENT '当前答案得分',
  `course_target_no` int NOT NULL COMMENT '对应课程目标几，查tp_course -> course_target',
  `status` int NOT NULL DEFAULT 0 COMMENT '记录状态{0:正常状态；-1:删除状态；}',
  `creator` bigint NOT NULL DEFAULT 0 COMMENT '记录创建者',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录',
  `modifier` bigint NOT NULL DEFAULT 0 COMMENT '记录最后修改者',
  `modify_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录最后修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '题目答案评分表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of repository_answer
-- ----------------------------

-- ----------------------------
-- Table structure for repository_path
-- ----------------------------
DROP TABLE IF EXISTS `repository_path`;
CREATE TABLE `repository_path`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '存储路径id',
  `path_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文件夹名称',
  `path` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '完整路径，每次update相关节点都需要同步修改该内容',
  `parent_id` int NULL DEFAULT 0 COMMENT '父文件夹id',
  `is_leaf` bit(1) NOT NULL DEFAULT b'1' COMMENT '是否叶子节点',
  `course_id` bigint NULL DEFAULT NULL COMMENT '所属课程id，查cs_course -> id',
  `status` int NOT NULL DEFAULT 0 COMMENT '记录状态{0:正常状态；-1:删除状态；}',
  `creator` bigint NOT NULL DEFAULT 0 COMMENT '记录创建者',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录',
  `modifier` bigint NOT NULL DEFAULT 0 COMMENT '记录最后修改者',
  `modify_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录最后修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '考核题目存储路径' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of repository_path
-- ----------------------------

-- ----------------------------
-- Table structure for repository_question
-- ----------------------------
DROP TABLE IF EXISTS `repository_question`;
CREATE TABLE `repository_question`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '题目id',
  `question_topic` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '题目题干',
  `question_detail` json NULL COMMENT '题目详情',
  `question_type` int NULL DEFAULT NULL COMMENT '题目类型（1、选择，2、填空，3、主观题[需要指明自定义题目类型名称]）',
  `question_score` decimal(10, 0) NULL DEFAULT NULL COMMENT '题目总分',
  `citation_count` int NULL DEFAULT NULL COMMENT '引用量',
  `scoring_rate` decimal(10, 0) NULL DEFAULT NULL COMMENT '得分率',
  `path_id` bigint NULL DEFAULT NULL COMMENT '文件路径id，查repository_struct -> id',
  `course_id` bigint NULL DEFAULT NULL COMMENT '所属课程id，查cs_course -> id',
  `status` int NOT NULL DEFAULT 0 COMMENT '记录状态{0:正常状态；-1:删除状态；}',
  `creator` bigint NOT NULL DEFAULT 0 COMMENT '记录创建者',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录',
  `modifier` bigint NOT NULL DEFAULT 0 COMMENT '记录最后修改者',
  `modify_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录最后修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '考核题目库' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of repository_question
-- ----------------------------

-- ----------------------------
-- Table structure for sys_base_user
-- ----------------------------
DROP TABLE IF EXISTS `sys_base_user`;
CREATE TABLE `sys_base_user`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户id',
  `username` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户名（学号）',
  `real_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '姓名',
  `gender` tinyint UNSIGNED NULL DEFAULT 0 COMMENT '性别:1为男，2为女，0为保密',
  `phone` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '手机号',
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '密码',
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '邮箱地址',
  `password_salt` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '密码盐值',
  `avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '头像路径',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `creator` bigint UNSIGNED NULL DEFAULT NULL COMMENT '创建人',
  `modify_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `modifier` bigint UNSIGNED NULL DEFAULT NULL COMMENT '修改人id',
  `last_login_time` datetime NULL DEFAULT NULL COMMENT '最近一次登录时间',
  `last_update_password_time` datetime NULL DEFAULT NULL COMMENT '最近一次更改密码的时间',
  `status` tinyint UNSIGNED NULL DEFAULT 0 COMMENT '用户状态：0表示正常，99表示拉黑',
  `is_deleted` tinyint UNSIGNED NULL DEFAULT 0 COMMENT '逻辑删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 112 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_base_user
-- ----------------------------
INSERT INTO `sys_base_user` VALUES (96, '测试1', '龙震南', 1, '13566668888', '$argon2id$v=19$m=15360,t=2,p=1$eTlzcldyQWtLQ2dKajNlZXhMUjRRZz09$5Z+xVEXoLSizpGmWXcgRcCv8ZFscM/VHEBwTmtvx5y0', '<EMAIL>', 'y9srWrAkKCgJj3eexLR4Qg==', NULL, '2025-06-08 09:58:59', NULL, '2025-06-11 02:28:28', 96, NULL, NULL, 0, 0);
INSERT INTO `sys_base_user` VALUES (97, NULL, '余晓明', 1, '13566668889', '$argon2id$v=19$m=15360,t=2,p=1$c1VvUXdBZWx2cElWZzRLT2dnMzl6QT09$azkf93ZvH8QElrcFBwYbCIy79wL2NGUHJBFaB0e2Rew', '<EMAIL>', 'sUoQwAelvpIVg4KOgg39zA==', NULL, '2025-06-08 09:58:59', NULL, '2025-06-08 09:58:59', NULL, NULL, NULL, 0, 0);
INSERT INTO `sys_base_user` VALUES (98, NULL, '汤震南', 1, '13566668890', '$argon2id$v=19$m=15360,t=2,p=1$eU5La2RmL3c5T2E4ZmpzQjhSRWdTZz09$G5rW3RZLamKIXpjvf3V5c+GY+1/PSnyYFDCaaAe4Nco', '<EMAIL>', 'yNKkdf/w9Oa8fjsB8REgSg==', NULL, '2025-06-08 09:58:59', NULL, '2025-06-08 09:58:59', NULL, NULL, NULL, 0, 0);
INSERT INTO `sys_base_user` VALUES (99, NULL, '汤子异', 1, '13566668891', '$argon2id$v=19$m=15360,t=2,p=1$V3g2NS9xSzM3clA0OWJIbjB4QWpodz09$HDzv+FjWAMci6Su8tTj21mr/xR0YgmC5vDMePXzXTCM', '<EMAIL>', 'Wx65/qK37rP49bHn0xAjhw==', NULL, '2025-06-08 09:58:59', NULL, '2025-06-08 09:58:59', NULL, NULL, NULL, 0, 0);
INSERT INTO `sys_base_user` VALUES (100, NULL, '薛安琪', 2, '13566668892', '$argon2id$v=19$m=15360,t=2,p=1$dW5welpuUnJRamZWMDh1RkVtRXhBdz09$UApwdxzTC5HKcxIqdqYHzYDp9Kq6xZzVsZcKR1fP3YQ', '<EMAIL>', 'unpzZnRrQjfV08uFEmExAw==', NULL, '2025-06-08 09:58:59', NULL, '2025-06-08 09:58:59', NULL, NULL, NULL, 0, 0);
INSERT INTO `sys_base_user` VALUES (101, NULL, '周詩涵', 2, '13566668893', '$argon2id$v=19$m=15360,t=2,p=1$c2hmVlA4ZHdLeDN1SnczUnRXeDB1QT09$rFrf3BXbzXs8/NdT3DKAF/ob+QksciILTSeRsBlchLE', '<EMAIL>', 'shfVP8dwKx3uJw3RtWx0uA==', NULL, '2025-06-08 09:58:59', NULL, '2025-06-08 09:58:59', NULL, NULL, NULL, 0, 0);
INSERT INTO `sys_base_user` VALUES (102, NULL, '薛詩涵', 2, '13566668894', '$argon2id$v=19$m=15360,t=2,p=1$TWNqUlErYjZxY2tmeHlSQ2pUeXpVdz09$xTZ0xxAPyDrT8rmhj187eJUA1BEb+V+wnCotERHxrdo', '<EMAIL>', 'McjRQ+b6qckfxyRCjTyzUw==', NULL, '2025-06-08 09:58:59', NULL, '2025-06-08 09:58:59', NULL, NULL, NULL, 0, 0);
INSERT INTO `sys_base_user` VALUES (103, NULL, '向岚', 2, '13566668895', '$argon2id$v=19$m=15360,t=2,p=1$QUNOcUNMQmlDcWhnVlZLczB1VDl1dz09$vjisX4iUJLPPzLwJPqCNp+LgNbSKI0jHB6DgZAzad9o', '<EMAIL>', 'ACNqCLBiCqhgVVKs0uT9uw==', NULL, '2025-06-08 09:58:59', NULL, '2025-06-08 09:58:59', NULL, NULL, NULL, 0, 0);
INSERT INTO `sys_base_user` VALUES (104, NULL, '邓秀英', 2, '13566668896', '$argon2id$v=19$m=15360,t=2,p=1$N1BhMEozaENwd1JBTVFTanlMNVhBdz09$W7NbcuHO+XZxb+0ZBukhldpLUW35grTDQKQv6LXi5Uk', '<EMAIL>', '7Pa0J3hCpwRAMQSjyL5XAw==', NULL, '2025-06-08 09:58:59', NULL, '2025-06-08 09:58:59', NULL, NULL, NULL, 0, 0);
INSERT INTO `sys_base_user` VALUES (105, NULL, '姚晓明', 1, '13566668897', '$argon2id$v=19$m=15360,t=2,p=1$NkZ5MXRwRHVHWUVNMzBhaUZBU2txZz09$rIWprXifXDWJZEIyKK1wwzripmtPVF+wsv1tt/YPGHY', '<EMAIL>', '6Fy1tpDuGYEM30aiFASkqg==', NULL, '2025-06-08 09:58:59', NULL, '2025-06-08 09:58:59', NULL, NULL, NULL, 0, 0);
INSERT INTO `sys_base_user` VALUES (109, '2000', '张三', 1, '13800000000', 'e10adc3949ba59abbe56e057f20f883e', '<EMAIL>', NULL, NULL, '2025-06-12 21:28:17', 96, '2025-06-12 22:08:08', 96, NULL, NULL, 0, 0);
INSERT INTO `sys_base_user` VALUES (110, '2001', '李四', 2, '13800000001', 'e10adc3949ba59abbe56e057f20f883e', '<EMAIL>', NULL, NULL, '2025-06-12 21:28:17', 96, '2025-06-12 21:28:17', 96, NULL, NULL, 0, 0);
INSERT INTO `sys_base_user` VALUES (111, '2002', '王五', 1, '13800000002', 'e10adc3949ba59abbe56e057f20f883e', '<EMAIL>', NULL, NULL, '2025-06-12 21:28:17', 96, '2025-06-12 21:28:17', 96, NULL, NULL, 0, 0);

-- ----------------------------
-- Table structure for sys_dict_data
-- ----------------------------
DROP TABLE IF EXISTS `sys_dict_data`;
CREATE TABLE `sys_dict_data`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '字典数据ID',
  `label` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '字典标签',
  `value` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '字典的键值',
  `type_id` bigint UNSIGNED NULL DEFAULT NULL COMMENT '字典类型的ID',
  `sort` int UNSIGNED NULL DEFAULT NULL COMMENT '字典的排序',
  `css_class` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '该字典数据的样式集',
  `list_class` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '该字典数据在表格回显的时候的样式集',
  `is_default` tinyint UNSIGNED NULL DEFAULT 1 COMMENT '是否为默认：0表示默认，1表示不默认',
  `status` tinyint UNSIGNED NULL DEFAULT 0 COMMENT '0表示正常，9表示停用',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `creator` bigint UNSIGNED NULL DEFAULT NULL COMMENT '创建人',
  `modifyTime` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `modifier` bigint UNSIGNED NULL DEFAULT NULL COMMENT '更新人',
  `is_deleted` tinyint UNSIGNED NULL DEFAULT 0 COMMENT '是否已删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '字典数据表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_dict_data
-- ----------------------------
INSERT INTO `sys_dict_data` VALUES (2, '讲师', '讲师', 2, 0, NULL, NULL, 1, 0, '', '2025-06-11 21:46:59', 96, '2025-06-11 21:48:25', 96, 0);
INSERT INTO `sys_dict_data` VALUES (3, '教授', '教授', 2, 1, NULL, NULL, 1, 0, '', '2025-06-11 21:47:28', 96, '2025-06-11 21:48:30', 96, 0);
INSERT INTO `sys_dict_data` VALUES (4, '副教授', '副教授', 2, 2, NULL, NULL, 1, 0, '', '2025-06-11 21:47:57', 96, '2025-06-11 21:48:36', 96, 0);

-- ----------------------------
-- Table structure for sys_dict_type
-- ----------------------------
DROP TABLE IF EXISTS `sys_dict_type`;
CREATE TABLE `sys_dict_type`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '字典类型id',
  `title` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '字典类型的名称',
  `status` tinyint UNSIGNED NULL DEFAULT 0 COMMENT '字典的状态：0表示正常，9表示停用',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '字典类型描述',
  `creator` bigint UNSIGNED NULL DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `modifier` bigint UNSIGNED NULL DEFAULT NULL COMMENT '更新人',
  `modify_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `is_deleted` tinyint UNSIGNED NULL DEFAULT 0 COMMENT '逻辑删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '字典类型表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_dict_type
-- ----------------------------
INSERT INTO `sys_dict_type` VALUES (1, '教师职称', 0, '', NULL, '2025-06-11 12:52:08', 96, '2025-06-11 12:59:00', 1);
INSERT INTO `sys_dict_type` VALUES (2, '教师职称', 0, '', 96, '2025-06-11 12:59:10', 96, '2025-06-11 13:08:31', 0);

-- ----------------------------
-- Table structure for sys_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_menu`;
CREATE TABLE `sys_menu`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '菜单ID',
  `parent_id` bigint NULL DEFAULT NULL COMMENT '父菜单ID',
  `path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '路由地址',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '组件名：唯一',
  `component` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '组件',
  `redirect` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '重定向到的子路由',
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '菜单名称',
  `icon` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '菜单图标',
  `expanded` tinyint(1) NULL DEFAULT 0 COMMENT '是否默认展开',
  `order_no` int NULL DEFAULT 0 COMMENT '菜单排序号',
  `is_hidden` tinyint(1) NULL DEFAULT 0 COMMENT '是否隐藏：默认不隐藏',
  `hidden_breadcrumb` tinyint(1) NULL DEFAULT 0 COMMENT '是否隐藏面包屑',
  `single` tinyint(1) NULL DEFAULT 0 COMMENT '是否只显示一级菜单',
  `frame_src` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'iframe地址',
  `frame_blank` tinyint(1) NULL DEFAULT 0 COMMENT 'iframe是否新窗口打开',
  `keep_alive` tinyint(1) NULL DEFAULT 0 COMMENT '是否开启keep-alive',
  `menu_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '权限码',
  `menu_type` tinyint NULL DEFAULT 0 COMMENT '0表示目录，1表示菜单，2表示按钮',
  `is_no_closable` tinyint(1) NULL DEFAULT 0 COMMENT '是否可关闭多标签页，默认false',
  `is_no_column` tinyint(1) NULL DEFAULT 1 COMMENT '是否显示列，默认true',
  `badge` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '徽标内容',
  `target` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '从字典表获取数据进行展示，对应字典表中value',
  `active_menu` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '激活的菜单',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `creator` bigint NULL DEFAULT NULL COMMENT '创建人',
  `modify_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `modifier` bigint NULL DEFAULT NULL COMMENT '更新人',
  `is_deleted` tinyint UNSIGNED NULL DEFAULT 0 COMMENT '逻辑删除',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_parent_id`(`parent_id` ASC) USING BTREE,
  INDEX `idx_menu_code`(`menu_code` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 186 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '菜单表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_menu
-- ----------------------------
INSERT INTO `sys_menu` VALUES (1, 0, '/system', 'System', 'Layout', '/system/home', '系统中心', 'setting-1-filled', 0, 1, 0, 0, 0, NULL, 0, 0, 'system', 0, 0, 1, '', '', '', '2025-06-09 17:33:44', 1, '2025-06-11 01:52:31', 96, 0);
INSERT INTO `sys_menu` VALUES (2, NULL, '/directers', 'Directers', 'Layout', '/directers/goal', '专业管理', 'chart-bubble', 0, 2, 0, 0, 0, NULL, 0, 0, 'directers', 0, 0, 1, NULL, NULL, NULL, '2025-06-09 17:33:45', 1, '2025-06-09 17:33:45', 1, 0);
INSERT INTO `sys_menu` VALUES (3, NULL, '/courses', 'Courses', 'Layout', '/courses/home', '课程管理', 'book', 0, 3, 0, 0, 0, NULL, 0, 0, 'courses', 0, 0, 1, NULL, NULL, NULL, '2025-06-09 17:33:46', 1, '2025-06-09 17:33:46', 1, 0);
INSERT INTO `sys_menu` VALUES (4, NULL, '/teachers', 'Teachers', 'TeacherHomeLayout', '/teachers/main', '教学管理', 'user', 0, 4, 0, 0, 0, NULL, 0, 0, 'teachers', 0, 0, 1, NULL, NULL, NULL, '2025-06-09 17:33:47', 1, '2025-06-09 17:33:47', 1, 0);
INSERT INTO `sys_menu` VALUES (5, NULL, '/students', 'Students', 'Layout', '/students/home', '学生学习', 'user-circle', 0, 5, 0, 0, 0, NULL, 0, 0, 'students', 0, 0, 1, NULL, NULL, NULL, '2025-06-09 17:33:48', 1, '2025-06-09 17:33:48', 1, 0);
INSERT INTO `sys_menu` VALUES (11, 1, '/system/home', 'SystemHome', 'system/home/<USER>', NULL, '系统首页', 'home', 0, 1, 0, 0, 0, NULL, 0, 0, 'system:home', 1, 0, 1, NULL, NULL, NULL, '2025-06-09 17:33:48', 1, '2025-06-09 17:33:48', 1, 0);
INSERT INTO `sys_menu` VALUES (12, 179, 'college', 'BaseCollege', '/system/college/index.vue', '', '学院管理', 'houses-2-filled', 0, 1, 0, 0, 0, NULL, 0, 0, 'base:college', 1, 0, 1, '', '', '', '2025-06-09 17:33:49', 1, '2025-06-11 00:54:45', 96, 0);
INSERT INTO `sys_menu` VALUES (13, 179, 'major', 'BaseMajor', '/system/major-all/index.vue', '', '专业管理', 'chart', 0, 3, 0, 0, 0, NULL, 0, 0, 'system:major', 1, 0, 1, '', '', '', '2025-06-09 17:33:50', 1, '2025-06-11 00:54:30', 96, 0);
INSERT INTO `sys_menu` VALUES (14, 179, 'teacher', 'BaseTeacher', '/system/teacher-tree/index.vue', '', '教师管理', 'assignment-user-filled', 0, 4, 0, 0, 0, NULL, 0, 0, 'system:teacher', 1, 0, 1, '', '', '', '2025-06-09 17:33:52', 1, '2025-06-11 00:55:54', 96, 0);
INSERT INTO `sys_menu` VALUES (15, 179, 'student', 'BaseStudent', '/system/student-tree/index.vue', '', '学生管理', 'assignment-user', 0, 5, 0, 0, 0, NULL, 0, 0, 'system:student', 1, 0, 1, '', '', '', '2025-06-09 17:33:54', 1, '2025-06-11 00:57:00', 96, 0);
INSERT INTO `sys_menu` VALUES (16, 1, '/system/document-review', 'SystemDocumentReview', 'system/document-final-review', NULL, '文件审核与发布', 'file', 0, 6, 0, 0, 0, NULL, 0, 0, 'system:document:review', 1, 0, 1, NULL, NULL, NULL, '2025-06-09 17:33:56', 1, '2025-06-09 17:33:56', 1, 0);
INSERT INTO `sys_menu` VALUES (17, 1, '/system/knowledge-graph', 'KnowledgeGraph', 'system/knowledge-graph/index', NULL, '知识图谱', 'chart-bubble', 0, 7, 0, 0, 0, NULL, 0, 0, 'system:knowledge', 0, 0, 1, NULL, NULL, NULL, '2025-06-09 17:33:56', 1, '2025-06-09 17:33:56', 1, 0);
INSERT INTO `sys_menu` VALUES (18, 1, '/system/knowledge-graph-management', 'KnowledgeGraphManagement', 'system/knowledge-graph/management/academicstyle', NULL, '知识图谱管理', 'chart-bubble', 0, 8, 0, 0, 0, NULL, 0, 0, 'system:knowledge:manage', 1, 0, 1, NULL, NULL, NULL, '2025-06-09 17:33:57', 1, '2025-06-09 17:33:57', 1, 0);
INSERT INTO `sys_menu` VALUES (19, 1, '/system/document', 'SystemDocument', 'system/document/index', NULL, '文件管理', 'folder-open', 0, 9, 0, 0, 0, NULL, 0, 0, 'system:document', 1, 0, 1, NULL, NULL, NULL, '2025-06-09 17:33:57', 1, '2025-06-09 17:33:57', 1, 0);
INSERT INTO `sys_menu` VALUES (20, 1, '/system/quota', 'SystemQuota', 'system/quota/index', NULL, '系统监控', 'chart', 0, 10, 0, 0, 0, NULL, 0, 0, 'system:quota', 1, 0, 1, NULL, NULL, NULL, '2025-06-09 17:33:58', 1, '2025-06-09 17:33:58', 1, 0);
INSERT INTO `sys_menu` VALUES (21, 1, '/system/backup', 'SystemBackup', 'system/backup/index', NULL, '数据备份', 'backup', 0, 11, 0, 0, 0, NULL, 0, 0, 'system:backup', 1, 0, 1, NULL, NULL, NULL, '2025-06-09 17:33:59', 1, '2025-06-09 17:33:59', 1, 0);
INSERT INTO `sys_menu` VALUES (22, 1, '/system/graduation-standard', 'SystemGraduationStandard', 'system/graduation-standard/index', NULL, '毕业要求标准管理', 'book-open', 0, 12, 0, 0, 0, NULL, 0, 0, 'system:graduation', 1, 0, 1, NULL, NULL, NULL, '2025-06-09 17:34:00', 1, '2025-06-09 17:34:00', 1, 0);
INSERT INTO `sys_menu` VALUES (23, 1, '/system/role', 'SystemRole', 'system/role/index', NULL, '角色管理', 'user-group', 0, 13, 0, 0, 0, NULL, 0, 0, 'system:role', 1, 0, 1, NULL, NULL, NULL, '2025-06-09 17:34:00', 1, '2025-06-09 17:34:00', 1, 0);
INSERT INTO `sys_menu` VALUES (24, 1, '/system/menu', 'SystemMenu', 'system/menu/index', NULL, '菜单管理', 'menu-unfold', 0, 14, 0, 0, 0, NULL, 0, 0, 'system:menu', 1, 0, 1, NULL, NULL, NULL, '2025-06-09 17:34:00', 1, '2025-06-09 17:34:00', 1, 0);
INSERT INTO `sys_menu` VALUES (25, 1, '/system/user', 'SystemUser', 'system/user/index', NULL, '用户管理', 'user-avatar', 0, 15, 0, 0, 0, NULL, 0, 0, 'system:user', 1, 0, 1, NULL, NULL, NULL, '2025-06-09 17:34:02', 1, '2025-06-09 17:34:02', 1, 0);
INSERT INTO `sys_menu` VALUES (31, 2, '/directers/goal', 'StudentsGoal', 'directers/goal/index', NULL, '培养目标管理', 'target', 0, 1, 0, 0, 0, NULL, 0, 0, 'directers:goal', 1, 0, 1, NULL, NULL, NULL, '2025-06-09 17:34:05', 1, '2025-06-09 17:34:05', 1, 0);
INSERT INTO `sys_menu` VALUES (32, 2, '/directers/require', 'StudentsRequire', 'directers/require/index', NULL, '毕业要求管理', 'book-open', 0, 2, 0, 0, 0, NULL, 0, 0, 'directers:require', 1, 0, 1, NULL, NULL, NULL, '2025-06-09 17:34:05', 1, '2025-06-09 17:34:05', 1, 0);
INSERT INTO `sys_menu` VALUES (33, 2, '/directers/curriculum', 'CurriculumSystem', 'directers/excel/index', NULL, '课程体系分配', 'table', 0, 3, 0, 0, 0, NULL, 0, 0, 'directers:curriculum', 1, 0, 1, NULL, NULL, NULL, '2025-06-09 17:34:05', 1, '2025-06-09 17:34:05', 1, 0);
INSERT INTO `sys_menu` VALUES (34, 2, '/directers/courses', 'CourseAllocation', 'directers/courses/index', NULL, '课程负责人分配', 'user-switch', 0, 4, 0, 0, 0, NULL, 0, 0, 'directers:courses', 1, 0, 1, NULL, NULL, NULL, '2025-06-09 17:34:05', 1, '2025-06-09 17:34:05', 1, 0);
INSERT INTO `sys_menu` VALUES (35, 2, '/directers/matrix', 'Matrix', 'directers/matrix/index', NULL, '目标支撑矩阵', 'table', 0, 5, 0, 0, 0, NULL, 0, 0, 'directers:matrix', 1, 0, 1, NULL, NULL, NULL, '2025-06-09 17:34:15', 1, '2025-06-09 17:34:15', 1, 0);
INSERT INTO `sys_menu` VALUES (36, 2, '/directers/questionnaire', 'Questionnaire', 'directers/questionnaire/index', NULL, '调查问卷管理', 'form', 0, 6, 0, 0, 0, NULL, 0, 0, 'directers:questionnaire', 1, 0, 1, NULL, NULL, NULL, '2025-06-09 17:34:16', 1, '2025-06-09 17:34:16', 1, 0);
INSERT INTO `sys_menu` VALUES (37, 2, '/directers/achievement', 'Achievement', 'directers/achievement/index', NULL, '总体达成度分析', 'chart-line', 0, 7, 0, 0, 0, NULL, 0, 0, 'directers:achievement', 1, 0, 1, NULL, NULL, NULL, '2025-06-09 17:34:18', 1, '2025-06-09 17:34:18', 1, 0);
INSERT INTO `sys_menu` VALUES (38, 2, '/directers/document-review', 'DirecterDocumentReview', 'directers/document-review', NULL, '文件审核', 'file-check', 0, 8, 0, 0, 0, NULL, 0, 0, 'directers:document', 1, 0, 1, NULL, NULL, NULL, '2025-06-09 17:34:18', 1, '2025-06-09 17:34:18', 1, 0);
INSERT INTO `sys_menu` VALUES (41, 3, '/courses/home', 'CourseHome', 'courses/home', NULL, '课程首页', 'home', 0, 1, 0, 0, 0, NULL, 0, 0, 'courses:home', 1, 0, 1, NULL, NULL, NULL, '2025-06-09 17:34:22', 1, '2025-06-09 17:34:22', 1, 0);
INSERT INTO `sys_menu` VALUES (42, 3, '/coursesList/list', 'CourseList', 'courses/list', NULL, '课程列表管理', 'list', 0, 2, 0, 0, 0, NULL, 0, 0, 'courses:list', 1, 0, 1, NULL, NULL, NULL, '2025-06-09 17:34:24', 1, '2025-06-09 17:34:24', 1, 0);
INSERT INTO `sys_menu` VALUES (43, 3, '/coursesQuestion/list', 'Question', 'courses/courseQuestion', NULL, '课程题库管理', 'database', 0, 3, 0, 0, 0, NULL, 0, 0, 'courses:question', 1, 0, 1, NULL, NULL, NULL, '2025-06-09 17:34:24', 1, '2025-06-09 17:34:24', 1, 0);
INSERT INTO `sys_menu` VALUES (44, 3, '/coursesList/courseConfig', 'CourseConfig', NULL, NULL, '课程配置管理', 'setting', 0, 4, 0, 0, 0, NULL, 0, 0, 'courses:config', 1, 0, 1, NULL, NULL, NULL, '2025-06-09 17:34:25', 1, '2025-06-09 17:34:25', 1, 0);
INSERT INTO `sys_menu` VALUES (45, 3, '/coursesList/courseObjective', 'CourseObjective', NULL, NULL, '课程目标管理', 'target', 0, 5, 0, 0, 0, NULL, 0, 0, 'courses:objective', 1, 0, 1, NULL, NULL, NULL, '2025-06-09 17:34:27', 1, '2025-06-09 17:34:27', 1, 0);
INSERT INTO `sys_menu` VALUES (51, 4, '/teachers/main', 'TeacherMain', 'teachers/main', NULL, '教师首页', 'home', 0, 1, 0, 0, 0, NULL, 0, 0, 'teachers:main', 1, 0, 1, NULL, NULL, NULL, '2025-06-09 17:34:28', 1, '2025-06-09 17:34:28', 1, 0);
INSERT INTO `sys_menu` VALUES (52, 4, '/teachers/course/homework', 'CourseHomework', 'teachers/task', NULL, '作业管理', 'file-text', 0, 2, 0, 0, 0, NULL, 0, 0, 'teachers:homework', 1, 0, 1, NULL, NULL, NULL, '2025-06-09 17:34:29', 1, '2025-06-09 17:34:29', 1, 0);
INSERT INTO `sys_menu` VALUES (53, 4, '/teachers/course/exam', 'CourseExam', 'teachers/paperTask', NULL, '试卷管理', 'file', 0, 3, 0, 0, 0, NULL, 0, 0, 'teachers:exam', 1, 0, 1, NULL, NULL, NULL, '2025-06-09 17:34:29', 1, '2025-06-09 17:34:29', 1, 0);
INSERT INTO `sys_menu` VALUES (54, 4, '/teachers/course/question', 'CourseQuestion', 'teachers/question-bank', NULL, '题库管理', 'database', 0, 4, 0, 0, 0, NULL, 0, 0, 'teachers:question', 1, 0, 1, NULL, NULL, NULL, '2025-06-09 17:34:30', 1, '2025-06-09 17:34:30', 1, 0);
INSERT INTO `sys_menu` VALUES (55, 4, '/teachers/course/score', 'CourseScore', 'teachers/score-manage', NULL, '成绩权重分配', 'percentage', 0, 5, 0, 0, 0, NULL, 0, 0, 'teachers:score', 1, 0, 1, NULL, NULL, NULL, '2025-06-09 17:34:31', 1, '2025-06-09 17:34:31', 1, 0);
INSERT INTO `sys_menu` VALUES (56, 4, '/teachers/document-submit', 'TeacherDocumentSubmit', 'teachers/document-submit', NULL, '文件提交与审核', 'file-check', 0, 6, 0, 0, 0, NULL, 0, 0, 'teachers:document', 1, 0, 1, NULL, NULL, NULL, '2025-06-09 17:34:32', 1, '2025-06-09 17:34:32', 1, 0);
INSERT INTO `sys_menu` VALUES (57, 4, '/teachers/grading', 'GradingManagement', NULL, NULL, '批阅管理', 'edit', 0, 7, 0, 0, 0, NULL, 0, 0, 'teachers:grading', 1, 0, 1, NULL, NULL, NULL, '2025-06-09 17:34:33', 1, '2025-06-09 17:34:33', 1, 0);
INSERT INTO `sys_menu` VALUES (61, 5, '/studentsHome/list', 'StudentHomeList', 'student/studentsHome/index', NULL, '学生首页', 'home', 0, 1, 0, 0, 0, NULL, 0, 0, 'students:home', 1, 0, 1, NULL, NULL, NULL, '2025-06-09 17:34:35', 1, '2025-06-09 17:34:35', 1, 0);
INSERT INTO `sys_menu` VALUES (62, 5, '/studentPaper/list', 'StudentPaperList', 'student/studentPaper/paperShow', NULL, '在线考试', 'file-text', 0, 2, 0, 0, 0, NULL, 0, 0, 'students:exam', 1, 0, 1, NULL, NULL, NULL, '2025-06-09 17:34:38', 1, '2025-06-09 17:34:38', 1, 0);
INSERT INTO `sys_menu` VALUES (63, 5, '/studentsQuestion/list', 'StudentsQuestionList', 'student/questionList', NULL, '问卷调查', 'form', 0, 3, 0, 0, 0, NULL, 0, 0, 'students:questionnaire', 1, 0, 1, NULL, NULL, NULL, '2025-06-09 17:34:41', 1, '2025-06-09 17:34:41', 1, 0);
INSERT INTO `sys_menu` VALUES (64, 5, '/studentsHome/studentGraduationCourse/list', 'StudentGraduationCourseList', 'student/studentGraduationCourse/index', NULL, '毕业目标对应课程', 'book', 0, 4, 0, 0, 0, NULL, 0, 0, 'students:graduation:course', 1, 0, 1, NULL, NULL, NULL, '2025-06-09 17:34:49', 1, '2025-06-09 17:34:49', 1, 0);
INSERT INTO `sys_menu` VALUES (65, 5, '/studentsHome/studentAttainment/list', 'StudentAttainmentList', 'student/studentAttainment/index', NULL, '课程达成度查看', 'chart-line', 0, 5, 0, 0, 0, NULL, 0, 0, 'students:attainment', 1, 0, 1, NULL, NULL, NULL, '2025-06-09 17:34:49', 1, '2025-06-09 17:34:49', 1, 0);
INSERT INTO `sys_menu` VALUES (171, 17, '/system/knowledge-graph/professional', 'ProfessionalKnowledgeGraph', 'system/knowledge-graph/index', NULL, '专业指标知识图谱', NULL, 0, 1, 0, 0, 0, NULL, 0, 0, 'system:knowledge:professional', 1, 0, 1, NULL, NULL, NULL, '2025-06-09 17:34:03', 1, '2025-06-09 17:34:03', 1, 0);
INSERT INTO `sys_menu` VALUES (172, 17, '/system/knowledge-graph/academicstyle', 'AcademicStyleKnowledgeGraph', 'system/knowledge-graph/indexpro', NULL, '学风知识图谱', NULL, 0, 2, 0, 0, 0, NULL, 0, 0, 'system:knowledge:academic', 1, 0, 1, NULL, NULL, NULL, '2025-06-09 17:34:04', 1, '2025-06-09 17:34:04', 1, 0);
INSERT INTO `sys_menu` VALUES (176, 11, '', '', '', '', '', '', 0, 0, 0, 0, 0, NULL, 0, 0, '', 2, 0, 1, NULL, NULL, NULL, '2025-06-10 01:24:54', 96, '2025-06-10 01:25:02', 96, 1);
INSERT INTO `sys_menu` VALUES (177, 24, '', '', '', '', '新增', '', 0, 0, 0, 0, 0, NULL, 0, 0, 'menu:add', 2, 0, 1, NULL, NULL, NULL, '2025-06-10 01:42:26', 96, '2025-06-11 00:37:03', 96, 1);
INSERT INTO `sys_menu` VALUES (178, 24, '', '', '', '', '删除', '', 0, 1, 0, 0, 0, NULL, 0, 0, 'menu:delete', 2, 0, 1, NULL, NULL, NULL, '2025-06-10 01:45:07', 96, '2025-06-11 00:37:03', 96, 1);
INSERT INTO `sys_menu` VALUES (179, 0, '/base', 'Base', 'Layout', '', '基础数据', 'data-base-filled', 0, 1, 0, 0, 0, NULL, 0, 0, 'base', 0, 0, 1, '', '', '', '2025-06-11 00:49:31', 96, '2025-06-11 01:57:54', 96, 0);
INSERT INTO `sys_menu` VALUES (180, 24, '', '', '', '', '新增', '', 0, 1, 0, 0, 0, NULL, 0, 0, 'menu:add', 2, 0, 1, '', '', '', '2025-06-11 01:20:56', 96, '2025-06-11 01:20:56', 96, 0);
INSERT INTO `sys_menu` VALUES (181, 24, '', '', '', '', '删除', 'menu-unfold', 0, 2, 0, 0, 0, NULL, 0, 0, 'menu:delete', 2, 0, 1, '', '', '', '2025-06-11 01:21:28', 96, '2025-06-11 01:24:43', 96, 1);
INSERT INTO `sys_menu` VALUES (182, 24, '', '', '', '', '删除', '', 0, 2, 0, 0, 0, NULL, 0, 0, 'menu:delete', 2, 0, 1, '', '', '', '2025-06-11 01:30:31', 96, '2025-06-11 01:32:04', 96, 1);
INSERT INTO `sys_menu` VALUES (183, 24, '', '', '', '', '删除', '', 0, 2, 0, 0, 0, NULL, 0, 0, 'menu:delete', 2, 0, 1, '', '', '', '2025-06-11 01:39:47', 96, '2025-06-11 01:39:56', 96, 0);
INSERT INTO `sys_menu` VALUES (184, 24, '', '', '', '', '测试', '', 0, 0, 0, 0, 0, NULL, 0, 0, '111', 2, 0, 1, '', '', '', '2025-06-11 01:41:49', 96, '2025-06-11 01:41:56', 96, 1);
INSERT INTO `sys_menu` VALUES (185, 1, 'dict', 'SystemDict', '/system/dict/index.vue', '', '字典管理', 'map-setting', 0, 0, 0, 0, 0, NULL, 0, 0, '', 1, 0, 1, '', '', '', '2025-06-11 12:45:22', 96, '2025-06-11 12:45:22', 96, 0);

-- ----------------------------
-- Table structure for sys_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_role`;
CREATE TABLE `sys_role`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '角色id',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '角色名',
  `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '角色码',
  `sort` tinyint UNSIGNED NULL DEFAULT NULL COMMENT '排序',
  `status` tinyint UNSIGNED NULL DEFAULT 0 COMMENT '角色状态：0表示启用，1表示停用',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `creator` bigint UNSIGNED NULL DEFAULT NULL COMMENT '创建人',
  `modify_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `modifier` bigint UNSIGNED NULL DEFAULT NULL COMMENT '更新人',
  `is_deleted` tinyint UNSIGNED NULL DEFAULT 0 COMMENT '是否删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 14 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '角色表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_role
-- ----------------------------
INSERT INTO `sys_role` VALUES (6, '超级管理员', 'admin', 0, 0, '', NULL, NULL, '2025-06-11 02:05:34', 96, 0);
INSERT INTO `sys_role` VALUES (7, '学校管理员', 'schooladmin', 1, 0, '', NULL, NULL, NULL, NULL, 0);
INSERT INTO `sys_role` VALUES (8, '学院院长', 'collageDean', 2, 0, '', NULL, NULL, NULL, NULL, 0);
INSERT INTO `sys_role` VALUES (9, '专业负责人', 'majorperson', 3, 0, '', NULL, NULL, NULL, NULL, 0);
INSERT INTO `sys_role` VALUES (10, '课程负责人', 'courseperson', 4, 0, '', NULL, NULL, NULL, NULL, 0);
INSERT INTO `sys_role` VALUES (11, '授课教师', 'teacher', 5, 0, '', NULL, NULL, NULL, NULL, 0);
INSERT INTO `sys_role` VALUES (12, '学生', 'student', 6, 0, '', NULL, NULL, '2025-06-10 09:26:04', 96, 1);
INSERT INTO `sys_role` VALUES (13, '学生', 'student', 8, 0, '', '2025-06-10 09:26:58', 96, '2025-06-10 09:26:58', 96, 0);

-- ----------------------------
-- Table structure for sys_role_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_menu`;
CREATE TABLE `sys_role_menu`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '角色菜单关联ID',
  `role_id` bigint UNSIGNED NULL DEFAULT NULL COMMENT '角色ID',
  `menu_id` bigint UNSIGNED NULL DEFAULT NULL COMMENT '菜单ID',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 328 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '角色菜单关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_role_menu
-- ----------------------------
INSERT INTO `sys_role_menu` VALUES (324, 6, 12);
INSERT INTO `sys_role_menu` VALUES (325, 6, 13);
INSERT INTO `sys_role_menu` VALUES (326, 6, 14);
INSERT INTO `sys_role_menu` VALUES (327, 6, 15);

-- ----------------------------
-- Table structure for sys_role_user
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_user`;
CREATE TABLE `sys_role_user`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '角色用户关联表ID',
  `role_id` bigint UNSIGNED NULL DEFAULT NULL COMMENT '角色ID',
  `user_id` bigint UNSIGNED NULL DEFAULT NULL COMMENT '用户ID',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 60 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '角色用户关联中间表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_role_user
-- ----------------------------
INSERT INTO `sys_role_user` VALUES (58, 6, 96);
INSERT INTO `sys_role_user` VALUES (59, 7, 96);

-- ----------------------------
-- Table structure for system_role
-- ----------------------------
DROP TABLE IF EXISTS `system_role`;
CREATE TABLE `system_role`  (
  `role_id` bigint NOT NULL,
  `role_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `role` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `create_time` datetime NULL DEFAULT NULL,
  `update_time` datetime NULL DEFAULT NULL,
  `status` tinyint NULL DEFAULT NULL,
  `creator` bigint NULL DEFAULT NULL,
  `modifier` bigint NULL DEFAULT NULL,
  PRIMARY KEY (`role_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of system_role
-- ----------------------------
INSERT INTO `system_role` VALUES (0, '超级管理员', 'admin', 'https://domain.com/images/teacher3.jpg', '2025-04-21 13:51:20', '2025-04-21 13:51:25', 0, 1, 1);
INSERT INTO `system_role` VALUES (1, '专业负责人', 'director', 'https://domain.com/images/teacher3.jpg', '2025-04-21 09:54:28', '2025-04-21 09:54:32', 0, 1, 1);
INSERT INTO `system_role` VALUES (2, '授课教师', 'teacher', 'https://domain.com/images/teacher3.jpg', '2025-04-21 09:55:31', '2025-04-21 09:55:35', 0, 1, 1);
INSERT INTO `system_role` VALUES (3, '课程负责人', 'subject', 'https://domain.com/images/teacher3.jpg', '2025-04-21 10:03:07', '2025-04-21 10:03:10', 0, 1, 1);
INSERT INTO `system_role` VALUES (4, '学生', 'student', 'https://domain.com/images/teacher3.jpg', '2025-04-21 10:03:31', '2025-04-21 10:03:33', 0, 1, 1);
INSERT INTO `system_role` VALUES (5, '学院院长', NULL, 'https://domain.com/images/teacher3.jpg', '2025-04-21 10:03:59', '2025-04-21 10:04:01', 0, 1, 1);
INSERT INTO `system_role` VALUES (6, '学校管理员', 'college_admin', 'https://domain.com/images/teacher3.jpg', '2025-04-21 10:05:17', '2025-04-21 10:05:20', 0, 1, 1);

-- ----------------------------
-- Table structure for task_score
-- ----------------------------
DROP TABLE IF EXISTS `task_score`;
CREATE TABLE `task_score`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '教学任务对应成绩id',
  `course_id` bigint NOT NULL COMMENT '课程id，查tp_course -> id',
  `task_number` int NOT NULL COMMENT '课程序号，同一课程的不同授课班级',
  `class_id` bigint NOT NULL COMMENT '教授班级id，对应小班，如RB软工数241',
  `teacher_id` bigint NOT NULL COMMENT '授课教师id',
  `task_year` int NOT NULL COMMENT '授课年份(2023)，冗余',
  `task_term` int NOT NULL COMMENT '授课学期(1~8)，班级可以查到入学年份，对应学期',
  `student_id` bigint NOT NULL COMMENT '成绩所属的学生id',
  `task_score` decimal(10, 0) NOT NULL COMMENT '综合成绩',
  `task_daily_score` decimal(10, 0) NOT NULL COMMENT '平时成绩',
  `task_exam_score` decimal(10, 0) NOT NULL COMMENT '期末成绩',
  `score_source` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '成绩来源，json格式，1：考核表同步（默认）2：自行导入无考核详情,json格式：\r\n{\r\n\"finalScore\":1/2,\r\n\"dailyScore\":1/2,\r\n\"examScore\":1/2\r\n}',
  `status` int NOT NULL DEFAULT 0 COMMENT '记录状态{0:正常状态；-1:删除状态；}',
  `creator` bigint NOT NULL DEFAULT 0 COMMENT '记录创建者',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录',
  `modifier` bigint NOT NULL DEFAULT 0 COMMENT '记录最后修改者',
  `modify_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录最后修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '教学任务对应学生成绩表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of task_score
-- ----------------------------

-- ----------------------------
-- Table structure for task_worklist
-- ----------------------------
DROP TABLE IF EXISTS `task_worklist`;
CREATE TABLE `task_worklist`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '教学任务id（taskId）',
  `course_id` bigint NOT NULL COMMENT '课程id，查tp_course -> id',
  `task_number` int NOT NULL COMMENT '课程序号，同一课程的不同授课班级',
  `class_id` bigint NOT NULL COMMENT '教授班级id，对应小班，如RB软工数241',
  `teacher_id` bigint NOT NULL COMMENT '授课教师id',
  `task_year` int NOT NULL COMMENT '授课年份(2023)，冗余',
  `task_term` int NOT NULL COMMENT '授课学期(1~8)，班级可以查到入学年份，对应学期',
  `student_count` int NOT NULL COMMENT '授课人数，冗余，base_class表中有',
  `teach_week` int NOT NULL COMMENT '授课周数',
  `week_hours` int NOT NULL COMMENT '周学时',
  `total_hours` int NOT NULL COMMENT '总学时',
  `course_leader_id` bigint NOT NULL COMMENT '课程负责人id（临时负责人）',
  `status` int NOT NULL DEFAULT 0 COMMENT '记录状态{0:正常状态；-1:删除状态；}',
  `creator` bigint NOT NULL DEFAULT 0 COMMENT '记录创建者',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录',
  `modifier` bigint NOT NULL DEFAULT 0 COMMENT '记录最后修改者',
  `modify_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录最后修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '教学任务信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of task_worklist
-- ----------------------------

-- ----------------------------
-- Table structure for tp_course
-- ----------------------------
DROP TABLE IF EXISTS `tp_course`;
CREATE TABLE `tp_course`  (
  `course_id` bigint NOT NULL AUTO_INCREMENT,
  `course_code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '课程编码，与版本号一起是唯一的，如2025_RB7001152',
  `course_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '课程名称',
  `course_leader` bigint NULL DEFAULT NULL,
  `course_credit` int NULL DEFAULT NULL COMMENT '课程学分',
  `course_core` bit(1) NULL DEFAULT NULL COMMENT '是否是核心课程',
  `course_exam` bit(1) NULL DEFAULT NULL COMMENT '是否是考试课',
  `course_hours_total` int NULL DEFAULT NULL COMMENT '总学学时',
  `course_hours_theory` int NULL DEFAULT NULL COMMENT '理教学时',
  `course_hours_experiment` int NULL DEFAULT NULL COMMENT '实验学时',
  `course_hours_other` int NULL DEFAULT NULL COMMENT '其他学时',
  `course_hours_extracurricular` int NULL DEFAULT NULL COMMENT '课外学时',
  `course_semester` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '上课学期（1-8）',
  `course_type1` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '本专业课程类型（专业基础课、个性化发展课等）',
  `course_type2` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '专业认证课程类型',
  `course_type3` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '国标课程类别',
  `course_nature` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '课程性质（必修、选修、限选等）',
  `course_target` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '课程目标，json{     targetCount:2；     targetList:[         {             targetTitle:\"掌握JAVA程序设计语法，能够按照要求编写程序，运行出正确的结果。\";             GraduateTargetId:\"4.1\"         };         {             targetTitle:\"理解面向对象程序设计思想，完成软件需求的抽象分析，设计合理的软件类图。\";             GraduateTargetId:\"5.2\"         };     ] }',
  `assessment_method` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '考核方式列表，json：[{typeId:1,typeName:\"作业\"}，{typeId:2,typeName:\"阶段性测验\"},{typeId:3,typeName:\"期末考试\"}]',
  `assessment_weight` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '分课程目标设置的不同考核方式对应的考核权重：json',
  `assessment_proportion` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '具体课程目标不同考核方式中考核占比要求：json',
  `course_version` int NULL DEFAULT NULL COMMENT '所属培养方案的版本',
  `plan_id` int NOT NULL COMMENT '课程所属培养方案id',
  `major_id` int NOT NULL COMMENT '专业编号-专业表',
  `status` int NOT NULL DEFAULT 0 COMMENT '记录状态{0:正常状态；-1:删除状态；}',
  `creator` bigint NOT NULL COMMENT '记录创建者',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `modifier` bigint NOT NULL COMMENT '记录最后修改者',
  `modify_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '记录最后修改时间',
  PRIMARY KEY (`course_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 29 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '专业培养方案中的课程体系，课程要求带有版本号' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of tp_course
-- ----------------------------
INSERT INTO `tp_course` VALUES (1, 'RB7001152', 'Java程序设计', NULL, 4, b'1', b'1', 60, 48, 12, NULL, NULL, '2', '1', '1', '1', '1', '{\n    targetCount:2；\n    targetList:[\n        {\n            targetTitle:\"掌握JAVA程序设计语法，能够按照要求编写程序，运行出正确的结果。\";\n            GraduateTargetId:\"4.1\"\n        };\n        {\n            targetTitle:\"理解面向对象程序设计思想，完成软件需求的抽象分析，设计合理的软件类图。\";\n            GraduateTargetId:\"5.2\"\n        };\n    ]\n}', '[{typeId:1,typeName:\"作业\"}，{typeId:2,typeName:\"阶段性测验\"},{typeId:3,typeName:\"期末考试\"}]', '{\n    examWeightList:[\n        {\n            typeId:1,\n            wight:0.3\n        },\n        {\n            typeId:2,\n            wight:0.2\n        },\n        {\n            typeId:3,\n            wight:0.5\n        }\n    ]\n,\n    targetWeightList:[\n        {\n            targetId:1,\n            weightList:[\n                {\n                    typeId:1,\n                    typeWeight:0.2\n                },\n                {\n                    typeId:2,\n                    typeWeight:0.4\n                },\n                {\n                    typeId:3,\n                    typeWeight:0.4\n                },\n            ]\n        },\n        {\n            targetId:2,\n            weightList:[\n                {\n                    typeId:1,\n                    typeWeight:0.3\n                },\n                {\n                    typeId:2,\n                    typeWeight:0.3\n                },\n                {\n                    typeId:3,\n                    typeWeight:0.4\n                },\n            ]\n        }\n    ]\n}', '{\n    assessmentCount:3,\n    proportionList:[\n        {\n            typeId:1,\n            proportionList:[\n                {\n                    targetId:1,\n                    proportion:0.4\n                },\n                {\n                    targetId:2,\n                    proportion:0.6\n                }\n            ]\n        },\n        {\n            typeId:2,\n            proportionList:[\n                {\n                    targetId:2,\n                    proportion:0.5\n                },\n                {\n                    targetId:3,\n                    typeWeight:0.5\n                },\n            ]\n        },\n        {\n            typeId:3,\n            proportionList:[\n                {\n                    targetId:1,\n                    proportion:0.3\n                },\n                {\n                    targetId:2,\n                    proportion:0.5\n                },\n                {\n                    targetId:3,\n                    typeWeight:0.2\n                },\n            ]\n        }\n    ]\n}', 2025, 0, 0, 0, 0, '2025-05-11 18:47:24', 0, '2025-05-11 18:47:15');
INSERT INTO `tp_course` VALUES (2, 'A0012', 'A0012', NULL, 1, b'1', b'1', 3, 1, 1, 0, 1, '1', '1', '1', '1', '1', '', '', '', '', 2025, 7, 1, -1, 6192, '2025-05-27 22:07:34', 6192, '2025-05-27 22:26:52');
INSERT INTO `tp_course` VALUES (3, 'A111', 'A111', NULL, 2, b'1', b'1', 2, 1, 1, 0, 0, '2', '7', '1', '1', '3', '', '', '', '', 2025, 7, 3, 0, 6192, '2025-05-27 22:30:44', 6192, '2025-05-27 22:48:15');
INSERT INTO `tp_course` VALUES (4, '1', '1', NULL, 0, b'0', b'0', 0, 0, 0, 0, 0, '1', '1', NULL, NULL, '1', NULL, NULL, NULL, NULL, 2025, 1, 1, 0, 6192, '2025-05-28 20:26:55', 6192, NULL);
INSERT INTO `tp_course` VALUES (5, '2', '2', NULL, 0, b'0', b'0', 0, 0, 0, 0, 0, '1', '1', NULL, NULL, '2', NULL, NULL, NULL, NULL, 2025, 1, 1, 0, 6192, '2025-05-28 20:27:03', 6192, NULL);
INSERT INTO `tp_course` VALUES (6, '1', '1', NULL, 0, b'0', b'0', 0, 0, 0, 0, 0, '1', '2', NULL, NULL, '1', NULL, NULL, NULL, NULL, 2025, 1, 1, 0, 6192, '2025-05-28 20:27:40', 6192, NULL);
INSERT INTO `tp_course` VALUES (7, '1', '1', NULL, 0, b'0', b'0', 0, 0, 0, 0, 0, '1', '2', NULL, NULL, '2', NULL, NULL, NULL, NULL, 2025, 1, 1, 0, 6192, '2025-05-28 20:27:48', 6192, NULL);
INSERT INTO `tp_course` VALUES (8, '1', '1', NULL, 0, b'0', b'0', 0, 0, 0, 0, 0, '3', '2', NULL, NULL, '1', NULL, NULL, NULL, NULL, 2025, 1, 1, 0, 6192, '2025-05-28 20:27:55', 6192, NULL);
INSERT INTO `tp_course` VALUES (9, '1', '1', NULL, 0, b'0', b'0', 0, 0, 0, 0, 0, '1', '2', NULL, NULL, '2', NULL, NULL, NULL, NULL, 2025, 1, 1, 0, 6192, '2025-05-28 20:28:02', 6192, NULL);
INSERT INTO `tp_course` VALUES (10, '1', '1', NULL, 0, b'0', b'0', 0, 0, 0, 0, 0, '2', '2', NULL, NULL, '2', NULL, NULL, NULL, NULL, 2026, 1, 1, 0, 6192, '2025-05-28 20:28:12', 6192, NULL);
INSERT INTO `tp_course` VALUES (11, '1', '1', NULL, 0, b'0', b'0', 0, 0, 0, 0, 0, '2', '5', NULL, NULL, '3', NULL, NULL, NULL, NULL, 2025, 1, 1, 0, 6192, '2025-05-28 20:28:18', 6192, NULL);
INSERT INTO `tp_course` VALUES (12, '1', '1', NULL, 0, b'0', b'0', 0, 0, 0, 0, 0, '3', '2', NULL, NULL, '2', NULL, NULL, NULL, NULL, 2025, 1, 1, 0, 6192, '2025-05-28 20:28:25', 6192, NULL);
INSERT INTO `tp_course` VALUES (13, '1', '1', 6192, 0, b'0', b'0', 0, 0, 0, 0, 0, '1', '2', NULL, NULL, '2', NULL, NULL, NULL, NULL, 2025, 1, 1, 0, 6192, '2025-05-28 20:28:33', 6192, '2025-05-31 15:01:49');
INSERT INTO `tp_course` VALUES (14, '1', '1', 6192, 0, b'0', b'0', 0, 0, 0, 0, 0, '2', '2', NULL, NULL, '2', NULL, NULL, NULL, NULL, 2025, 1, 1, 0, 6192, '2025-05-28 20:28:39', 6192, '2025-05-30 20:26:22');
INSERT INTO `tp_course` VALUES (15, '1', '1', 6192, 0, b'0', b'0', 0, 0, 0, 0, 0, '1', '2', NULL, NULL, '2', NULL, NULL, NULL, NULL, 2025, 1, 1, 0, 6192, '2025-05-28 20:28:49', 6192, '2025-05-31 15:00:33');
INSERT INTO `tp_course` VALUES (16, '1', '11112222222222', 6192, 110, b'0', b'0', 110, 10, 0, 0, 0, '1', '8', NULL, NULL, '3', NULL, NULL, NULL, NULL, 2025, 1, 1, -1, 6192, '2025-06-04 15:24:58', 6192, '2025-06-04 15:25:11');
INSERT INTO `tp_course` VALUES (17, '2', '2', 6192, 1, b'0', b'0', 0, 0, 0, 0, 0, '1', '6', NULL, NULL, '2', NULL, NULL, NULL, NULL, 2025, 1, 1, 0, 6192, '2025-06-04 20:32:50', 6192, '2025-06-04 20:32:50');
INSERT INTO `tp_course` VALUES (18, '3', '3', 6192, 0, b'0', b'0', 0, 0, 0, 0, 0, '1', '2', NULL, NULL, '2', NULL, NULL, NULL, NULL, 2025, 1, 1, 0, 6192, '2025-06-04 20:38:02', 6192, '2025-06-04 20:38:02');
INSERT INTO `tp_course` VALUES (19, '4', '4', 6192, 1, b'0', b'0', 0, 0, 0, 0, 0, '1', '6', NULL, NULL, '2', NULL, NULL, NULL, NULL, 2025, 1, 1, 0, 6192, '2025-06-04 20:44:50', 6192, '2025-06-04 20:44:50');
INSERT INTO `tp_course` VALUES (20, 'JB015E', '数据结构', 6192, 3, b'1', b'1', 56, 48, 8, 0, 0, '3', '3', '0', NULL, '1', NULL, NULL, NULL, NULL, 2021, 9, 1, 0, 6192, '2025-06-07 15:05:48', 6192, NULL);
INSERT INTO `tp_course` VALUES (21, 'KB504A', '离散数学', 6192, 3, b'1', b'1', 56, 56, 0, 0, 0, '3', '2', NULL, NULL, '1', NULL, NULL, NULL, NULL, 2021, 9, 1, 0, 6192, '2025-06-07 15:07:18', 6192, '2025-06-07 15:07:27');
INSERT INTO `tp_course` VALUES (22, 'JS015B', '程序设计综合实践', 6192, 2, b'1', b'1', 24, 0, 24, 0, 0, '3', '6', NULL, NULL, '1', NULL, NULL, NULL, NULL, 2021, 9, 1, 0, 6192, '2025-06-07 16:52:55', 6192, NULL);
INSERT INTO `tp_course` VALUES (23, 'JS163A', '数据库开发实践', 6192, 2, b'1', b'1', 24, 0, 24, 0, 0, '5', '6', NULL, NULL, '1', NULL, NULL, NULL, NULL, 2021, 9, 1, 0, 6192, '2025-06-07 16:53:46', 6192, NULL);
INSERT INTO `tp_course` VALUES (24, 'JS261D', '毕业论文（设计）', 6192, 14, b'1', b'0', 0, 0, 0, 0, 0, '8', '7', NULL, NULL, '1', NULL, NULL, NULL, NULL, 2021, 9, 1, 0, 6192, '2025-06-07 16:56:47', 6192, NULL);
INSERT INTO `tp_course` VALUES (25, 'JB114B', '算法分析与设计', 6192, 3, b'1', b'1', 48, 32, 16, 0, 0, '5', '4', NULL, NULL, '1', NULL, NULL, NULL, NULL, 2021, 9, 1, 0, 6192, '2025-06-07 17:03:45', 6192, NULL);
INSERT INTO `tp_course` VALUES (26, 'JB111B', '数据库原理', 6192, 3, b'1', b'1', 56, 40, 16, 0, 0, '4', '4', NULL, NULL, '1', NULL, NULL, NULL, NULL, 2021, 9, 1, 0, 6192, '2025-06-07 17:05:29', 6192, NULL);
INSERT INTO `tp_course` VALUES (27, 'JB018D', '计算机组成原理', 6192, 4, b'1', b'1', 64, 48, 16, 0, 0, '5', '3', NULL, NULL, '1', NULL, NULL, NULL, NULL, 2021, 9, 1, 0, 6192, '2025-06-07 17:10:09', 6192, NULL);
INSERT INTO `tp_course` VALUES (28, 'MB005C', '电工电子技术基础I', 6192, 4, b'0', b'1', 64, 56, 8, 0, 0, '2', '2', NULL, NULL, '1', NULL, NULL, NULL, NULL, 2021, 9, 1, 0, 6192, '2025-06-07 17:11:23', 6192, NULL);

-- ----------------------------
-- Table structure for tp_course_route
-- ----------------------------
DROP TABLE IF EXISTS `tp_course_route`;
CREATE TABLE `tp_course_route`  (
  `category_id` bigint NOT NULL AUTO_INCREMENT,
  `course_id` bigint NULL DEFAULT NULL COMMENT '课程ID',
  `predecessor_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '前驱课程ids，直接前驱获取第一个',
  `successor_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '后继课程ids，直接后继获取第一个',
  `category` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '课程门类（分类代码如MATH/CS/PHYS）',
  `term` tinyint(1) NULL DEFAULT NULL COMMENT '开设学期,1~8(冗余，tp_course表中有)',
  `is_core` tinyint(1) NULL DEFAULT NULL COMMENT '是否核心课（0: 否，1: 是）(冗余，tp_course表中有)',
  `status` int NOT NULL DEFAULT 0 COMMENT '记录状态{0:正常状态；-1:删除状态；}',
  `creator` bigint NOT NULL DEFAULT 0 COMMENT '记录创建者',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录',
  `modifier` bigint NOT NULL DEFAULT 0 COMMENT '记录最后修改者',
  `modify_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录最后修改时间',
  PRIMARY KEY (`category_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '专业培养路线表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of tp_course_route
-- ----------------------------

-- ----------------------------
-- Table structure for tp_eo
-- ----------------------------
DROP TABLE IF EXISTS `tp_eo`;
CREATE TABLE `tp_eo`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '培养目标ID',
  `eo_title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '培养目标标题',
  `eo_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '培养目标详情',
  `plan_id` bigint NULL DEFAULT NULL COMMENT '所属专业培养计划id',
  `major_id` bigint NULL DEFAULT NULL COMMENT '所属专业id（冗余）',
  `academy_id` bigint NULL DEFAULT NULL COMMENT '所属学院id（冗余）',
  `status` int NOT NULL DEFAULT 0 COMMENT '记录状态{0:正常状态；-1:删除状态；}',
  `creator` bigint NOT NULL DEFAULT 0 COMMENT '记录创建者',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录',
  `modifier` bigint NOT NULL DEFAULT 0 COMMENT '记录最后修改者',
  `modify_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录最后修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 22 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '培养目标表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of tp_eo
-- ----------------------------
INSERT INTO `tp_eo` VALUES (1, '培养目标11', '具有扎实的数学与自然科学基础知识、软件工程基础理论、良好的 科学素养。1', 1, 1, 1, 0, 0, '2025-05-11 21:07:32', 0, '2025-05-11 21:07:32');
INSERT INTO `tp_eo` VALUES (2, '培养目标2', '更新后的培养目标详情（仅验证此字段）', 1, 1, 1, 0, 0, '2025-05-11 21:07:32', 0, '2025-06-04 15:35:44');
INSERT INTO `tp_eo` VALUES (3, '培养目标3', '具有高度社会责任感，具备服务社会能力，理解并遵守法律与职业 道德规范，能够在专业领域综合考虑社会安全、环境与可持续性发展等因素影响。', 1, 1, 1, 0, 0, '2025-05-11 21:07:32', 0, '2025-05-11 21:07:32');
INSERT INTO `tp_eo` VALUES (4, '培养目标4', '具备健康的身心和良好的人文与职业素养，具备较强的协调、管理、 沟通、竞争与合作能力，能够胜任项目负责人、团队负责人、项目经理等工作。', 1, 1, 1, 0, 0, '2025-05-11 21:07:32', 0, '2025-05-11 21:07:32');
INSERT INTO `tp_eo` VALUES (5, '培养目标5', '具有国际视野与终身学习能力，能够主动了解国内外形势，自觉跟 踪软件行业及相关领域的前沿技术，能自主学习以适应专业领域不断发展的需  要。', 1, 1, 1, -1, 0, '2025-05-11 21:07:32', 0, '2025-05-28 21:57:26');
INSERT INTO `tp_eo` VALUES (6, NULL, '1', NULL, NULL, NULL, 0, 0, '2025-05-26 22:21:07', 0, '2025-05-26 22:21:07');
INSERT INTO `tp_eo` VALUES (7, '111', '111', NULL, NULL, NULL, 0, 0, '2025-05-26 22:26:16', 0, '2025-05-26 22:26:16');
INSERT INTO `tp_eo` VALUES (8, '指标点1', '描述1', 6, NULL, NULL, 0, 0, '2025-05-26 22:29:07', 0, '2025-05-26 22:29:07');
INSERT INTO `tp_eo` VALUES (9, '222', '222', 6, NULL, NULL, 0, 0, '2025-05-26 22:29:13', 0, '2025-05-26 22:29:13');
INSERT INTO `tp_eo` VALUES (10, '111', '3', NULL, NULL, NULL, 0, 0, '2025-05-26 22:32:45', 0, '2025-05-26 22:29:07');
INSERT INTO `tp_eo` VALUES (11, '111', '333', NULL, NULL, NULL, 0, 0, '2025-05-26 22:33:00', 0, '2025-05-26 22:29:07');
INSERT INTO `tp_eo` VALUES (12, '111', '22', NULL, NULL, NULL, 0, 0, '2025-05-26 22:35:10', 0, '2025-05-26 22:29:07');
INSERT INTO `tp_eo` VALUES (13, '111', '3', NULL, NULL, NULL, 0, 0, '2025-05-26 22:35:26', 0, '2025-05-26 22:29:07');
INSERT INTO `tp_eo` VALUES (14, '111', '2', NULL, NULL, NULL, 0, 0, '2025-05-26 22:37:41', 0, '2025-05-26 22:29:07');
INSERT INTO `tp_eo` VALUES (15, '33', '33', 6, NULL, NULL, 0, 0, '2025-05-26 22:46:36', 0, '2025-05-26 22:46:36');
INSERT INTO `tp_eo` VALUES (16, '44', '44', 6, NULL, NULL, 0, 0, '2025-05-26 22:46:40', 0, '2025-05-26 22:46:40');
INSERT INTO `tp_eo` VALUES (17, '55', '555555555555', 1, NULL, NULL, 0, 0, '2025-05-28 22:02:53', 0, '2025-05-28 22:02:53');
INSERT INTO `tp_eo` VALUES (18, '66666666', '666666666666', 1, NULL, NULL, -1, 0, '2025-05-28 22:03:04', 0, '2025-05-28 22:07:49');
INSERT INTO `tp_eo` VALUES (19, '11', '111111111111', 1, NULL, NULL, -1, 0, NULL, 0, '2025-06-04 15:23:37');
INSERT INTO `tp_eo` VALUES (20, '44', '444444455555555556', 1, NULL, NULL, 0, 0, NULL, 6192, '2025-06-07 16:28:16');
INSERT INTO `tp_eo` VALUES (21, '666', '6666666666', 1, NULL, NULL, -1, 6192, '2025-06-07 16:31:32', 6192, '2025-06-07 16:31:34');

-- ----------------------------
-- Table structure for tp_plan
-- ----------------------------
DROP TABLE IF EXISTS `tp_plan`;
CREATE TABLE `tp_plan`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '专业培养计划ID',
  `plan_version` bigint NULL DEFAULT NULL COMMENT '专业培养计划版本，按照年份输入',
  `plan_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '专业培养计划标题',
  `major_id` bigint NULL DEFAULT NULL COMMENT '所属专业id',
  `academy_id` bigint NULL DEFAULT NULL COMMENT '所属学院id',
  `standard_id` bigint NOT NULL COMMENT '标准id',
  `status` int NOT NULL DEFAULT 0 COMMENT '记录状态{0:正常状态；-1:删除状态；}',
  `creator` bigint NOT NULL COMMENT '记录创建者',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录',
  `modifier` bigint NOT NULL COMMENT '记录最后修改者',
  `modify_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '记录最后修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `tp_plan_version_major_academy_unique`(`plan_version` ASC, `major_id` ASC, `academy_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 15 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '专业培养计划表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of tp_plan
-- ----------------------------
INSERT INTO `tp_plan` VALUES (1, 2025, '软件工程专业培养方案2025版', 1, 1, 28, 0, 1, '2025-05-11 21:01:04', 1, '2025-05-27 21:31:19');
INSERT INTO `tp_plan` VALUES (5, 2024, '软件工程专业培养方案2024版', 1, 1, 28, 0, 6192, '2025-05-25 16:41:24', 6192, '2025-05-27 21:31:19');
INSERT INTO `tp_plan` VALUES (6, 2023, '软工2023', 1, 1, 1, 0, 6192, '2025-05-26 21:41:33', 6192, NULL);
INSERT INTO `tp_plan` VALUES (7, 2026, '软工202611', 1, 1, 28, 0, 6192, '2025-05-27 21:33:34', 6192, '2025-06-08 21:41:54');
INSERT INTO `tp_plan` VALUES (8, 2021, '计算机科学与技术', 2, 1, 1, 0, 6192, '2025-06-07 13:15:23', 6192, NULL);
INSERT INTO `tp_plan` VALUES (9, 2021, '计算机科学与技术专业培养方案2021版', 1, 1, 1, 0, 6192, NULL, 6192, NULL);
INSERT INTO `tp_plan` VALUES (11, 2028, 'test陈沛豪', 1, 1, 28, 0, 6192, NULL, 6192, NULL);
INSERT INTO `tp_plan` VALUES (13, 2027, 'test2cph', 1, 1, 28, 0, 6192, NULL, 6192, NULL);
INSERT INTO `tp_plan` VALUES (14, 2029, 'qq', 1, 1, 58, 0, 6192, '2025-06-09 14:50:48', 6192, '2025-06-09 14:50:48');

-- ----------------------------
-- Table structure for tp_po
-- ----------------------------
DROP TABLE IF EXISTS `tp_po`;
CREATE TABLE `tp_po`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '专业毕业要求二级指标ID',
  `po_title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '毕业要求标题',
  `po_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '毕业要求详情',
  `is_requirement` bit(1) NULL DEFAULT b'0' COMMENT '该条记录是毕业要求（true），还是拆分指标（fasle）',
  `standard_id` bigint NULL DEFAULT NULL COMMENT '所属工程教育认证指标id',
  `requirement_id` bigint NULL DEFAULT NULL,
  `plan_id` bigint NULL DEFAULT NULL COMMENT '所属专业培养计划id',
  `major_id` bigint NULL DEFAULT NULL COMMENT '所属专业id（冗余）',
  `academy_id` bigint NULL DEFAULT NULL COMMENT '所属学院id（冗余）',
  `status` int NOT NULL DEFAULT 0 COMMENT '记录状态{0:正常状态；-1:删除状态；}',
  `creator` bigint NOT NULL DEFAULT 0 COMMENT '记录创建者',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录',
  `modifier` bigint NOT NULL DEFAULT 0 COMMENT '记录最后修改者',
  `modify_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录最后修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 77 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '毕业要求表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of tp_po
-- ----------------------------
INSERT INTO `tp_po` VALUES (1, '2.1', '掌握数学和自然科学知识，奠定本专业相关的数理和自然科学基础', b'0', 2, 29, 1, 1, 1, 0, 0, '2025-05-11 21:17:06', 6192, '2025-06-07 16:21:15');
INSERT INTO `tp_po` VALUES (2, '2.2', '掌握软件工程核心理论和知识，能够结合数学和自然科学知识对软件\n\n工程问题进行表述、推演和建模。', b'0', 2, 30, 1, 1, 1, 0, 0, '2025-05-11 21:17:06', 0, '2025-05-29 20:29:16');
INSERT INTO `tp_po` VALUES (3, '2.3', '能够将软件工程知识和数学模型方法用于复杂工程问题解决方案的比 较和综合。', b'0', 2, 30, 1, 1, 1, 0, 0, '2025-05-11 21:17:06', 0, '2025-05-29 20:29:16');
INSERT INTO `tp_po` VALUES (8, '22', '22', b'0', NULL, 2, 6, NULL, NULL, 0, 0, '2025-05-28 21:10:59', 0, '2025-05-28 21:10:59');
INSERT INTO `tp_po` VALUES (9, '3', '3', b'0', NULL, 2, 6, NULL, NULL, 0, 0, '2025-05-28 21:11:29', 0, '2025-05-28 21:11:29');
INSERT INTO `tp_po` VALUES (10, '毕业要求1', '毕业要求1', b'0', NULL, 29, 1, NULL, NULL, -1, 0, '2025-05-29 20:12:11', 6192, '2025-06-07 16:23:35');
INSERT INTO `tp_po` VALUES (11, '毕业要求33', '毕业要求2333', b'0', NULL, 30, 1, NULL, NULL, -1, 0, '2025-05-29 20:12:17', 6192, '2025-06-07 16:49:32');
INSERT INTO `tp_po` VALUES (12, '1.1', '能够将数学、自然科学、计算机工程基础与专业知识用于表述计算机复杂工程问题', b'0', NULL, 2, 9, NULL, NULL, 0, 0, NULL, 0, '2025-06-07 13:54:18');
INSERT INTO `tp_po` VALUES (13, '1.2', '掌握数学、自然科学相关知识，对计算机复杂工程领域的具体对象建立数学模型并求解', b'0', NULL, 2, 9, NULL, NULL, 0, 0, NULL, 0, '2025-06-07 13:54:52');
INSERT INTO `tp_po` VALUES (14, '1.3', '能够将计算机专业知识和数学模型用于推理、分析、验证复杂工程问题', b'0', NULL, 2, 9, NULL, NULL, 0, 0, NULL, 0, '2025-06-07 13:55:19');
INSERT INTO `tp_po` VALUES (15, '1.4', '综合运用相关知识，进行计算机复杂工程问题解决方案的对比和整合。', b'0', NULL, 2, 9, NULL, NULL, 0, 0, NULL, 6192, '2025-06-08 19:51:02');
INSERT INTO `tp_po` VALUES (16, '2.1', '通过相关科学原理、数学模型方法和计算机工程技术，识别和判断计算机复杂工程问题的关键环节和核心因素，并能对其正确表达', b'0', NULL, 26, 9, NULL, NULL, 0, 0, NULL, 0, '2025-06-07 14:27:15');
INSERT INTO `tp_po` VALUES (17, '2.2', '能认识到工程问题存在多种潜在解决方案，并通过文献研究寻求替代解决方案', b'0', NULL, 26, 9, NULL, NULL, 0, 0, NULL, 0, '2025-06-07 14:27:36');
INSERT INTO `tp_po` VALUES (18, '2.3', '根据对复杂工程问题的抽象结果，结合文献研究，确定复杂工程问题的可计算部分，划分功能边界，获得有效结论', b'0', NULL, 26, 9, NULL, NULL, 0, 0, NULL, 6192, '2025-06-08 19:51:23');
INSERT INTO `tp_po` VALUES (19, '3.1', '了解影响设计目标和技术方案的各种因素，掌握工程设计和产品开发全周期、全流程的基本研发方法和技术', b'0', NULL, 27, 9, NULL, NULL, 0, 0, NULL, 0, '2025-06-07 14:28:27');
INSERT INTO `tp_po` VALUES (20, '3.1', '了解影响设计目标和技术方案的各种因素，掌握工程设计和产品开发全周期、全流程的基本研发方法和技术', b'0', NULL, 27, 9, NULL, NULL, -1, 0, NULL, 0, '2025-06-07 14:28:30');
INSERT INTO `tp_po` VALUES (21, '3.2', '确定设计目标，并根据其构建解决方案，设计、开发满足特定需求的模块和组件', b'0', NULL, 27, 9, NULL, NULL, 0, 0, NULL, 0, '2025-06-07 14:28:51');
INSERT INTO `tp_po` VALUES (22, '3.3', '综合考虑社会、健康、安全、法律、文化及环境因素，进行系统或流程设计并体现创新意识', b'0', NULL, 27, 9, NULL, NULL, 0, 0, NULL, 0, '2025-06-07 14:29:11');
INSERT INTO `tp_po` VALUES (23, '4.1', '能够基于计算机学科相关原理和方法，对复杂工程问题方案进行调研、分析', b'0', NULL, 49, 9, NULL, NULL, 0, 0, NULL, 0, '2025-06-07 14:29:33');
INSERT INTO `tp_po` VALUES (24, '4.2', '根据对计算机相关复杂工程问题的分析结果，辨别研究路线，设计实验方案', b'0', NULL, 49, 9, NULL, NULL, 0, 0, NULL, 0, '2025-06-07 14:32:47');
INSERT INTO `tp_po` VALUES (25, '4.3', '通过实验、仿真等手段安全有序开展研究，获取数据与信息，并对结果进行分析和解释，形成合理有效的结论', b'0', NULL, 49, 9, NULL, NULL, 0, 0, NULL, 0, '2025-06-07 14:33:11');
INSERT INTO `tp_po` VALUES (26, '5.1', '掌握专业常用的信息技术工具、工程工具和模拟软件的使用原理和方法，并理解其局限性', b'0', NULL, 50, 9, NULL, NULL, 0, 0, NULL, 0, '2025-06-07 14:33:40');
INSERT INTO `tp_po` VALUES (27, '5.2', '能够针对复杂工程问题的解决方案，选择、开发与使用相关技术、工具和资源，以实施对该问题的模拟、仿真和预测', b'0', NULL, 50, 9, NULL, NULL, 0, 0, NULL, 0, '2025-06-07 14:34:09');
INSERT INTO `tp_po` VALUES (28, '5.3', '能够分析和比较所用技术、工具、平台、资源的优劣，理解其对实现结果的影响和局限性', b'0', NULL, 50, 9, NULL, NULL, 0, 0, NULL, 0, '2025-06-07 14:34:28');
INSERT INTO `tp_po` VALUES (29, '6.1', '了解计算机相关领域的技术标准体系、知识产权、产业政策和法律法规，并理解应承担的责任', b'0', NULL, 51, 9, NULL, NULL, 0, 0, NULL, 0, '2025-06-07 14:34:54');
INSERT INTO `tp_po` VALUES (30, '6.2', '能分析和评价计算机复杂工程问题解决方案及其实践与社会、健康、安全、法律、文化的相互影响，并理解应承担的责任', b'0', NULL, 51, 9, NULL, NULL, 0, 0, NULL, 0, '2025-06-07 14:35:13');
INSERT INTO `tp_po` VALUES (31, '7.1', '理解环境保护和社会可持续发展的内涵和意义，能够运用其指导、约束、规范具体的计算机工\n程实践行为', b'0', NULL, 52, 9, NULL, NULL, -1, 0, NULL, 0, '2025-06-07 14:35:56');
INSERT INTO `tp_po` VALUES (32, '7.1', '理解环境保护和社会可持续发展的内涵和意义，能够运用其指导、约束、规范具体的计算机工程实践行为', b'0', NULL, 52, 9, NULL, NULL, 0, 0, NULL, 0, '2025-06-07 14:36:13');
INSERT INTO `tp_po` VALUES (33, '7.2', '能够正确认识和评价计算机复杂工程问题的工程实践对环境、社会可持续发展的影响', b'0', NULL, 52, 9, NULL, NULL, 0, 0, NULL, 0, '2025-06-07 14:36:39');
INSERT INTO `tp_po` VALUES (34, '8.1', '具备符合时代要求的人文和科学素养，爱国、敬业、诚信、友善，能够正确处理人与人、人与社会、人与自然的关系，了解国情，具有推动民族复兴、社会进步的责任感', b'0', NULL, 53, 9, NULL, NULL, 0, 0, NULL, 0, '2025-06-07 14:37:02');
INSERT INTO `tp_po` VALUES (35, '8.2', '熟悉计算机软硬件工程师的职业性质和责任，理解工程实践中应该遵守的职业道德、伦理和规范，并能自觉自愿履行责任', b'0', NULL, 53, 9, NULL, NULL, 0, 0, NULL, 0, '2025-06-07 14:37:27');
INSERT INTO `tp_po` VALUES (36, '9.1', '理解多学科协同融合以解决计算机复杂工程问题的作用和意义，并能够在团队中发挥自身特长和优势，承担个体和团队成员的职责，独立或合作开展工作，完成相关任务。', b'0', NULL, 54, 9, NULL, NULL, 0, 0, NULL, 0, '2025-06-07 14:37:54');
INSERT INTO `tp_po` VALUES (37, '9.2', '具有团队协同工作能力，能够组织、协调和指挥团队开展工作', b'0', NULL, 54, 9, NULL, NULL, 0, 0, NULL, 0, '2025-06-07 14:38:17');
INSERT INTO `tp_po` VALUES (38, '10.1', '能够针对计算机领域复杂工程问题，分别面向业界同行及普通社会公众，通过撰写报告文档或者口述发言等形式清晰的阐明自身观点、想法，回应对方关心的问题和要求，形成有效的沟通和交流。', b'0', NULL, 55, 9, NULL, NULL, 0, 0, NULL, 0, '2025-06-07 14:40:43');
INSERT INTO `tp_po` VALUES (39, '10.2', '至少掌握一门外语，能够查阅专业外文资料，认识到国际上计算机工程领域的技术研发现状，并能够在跨文化背景下进行沟通与交流。', b'0', NULL, 55, 9, NULL, NULL, 0, 0, NULL, 0, '2025-06-07 14:41:05');
INSERT INTO `tp_po` VALUES (40, '11.1', '理解计算机工程项目中资源分配和经济决策知识和方法，并能够进行经济可行性分析。', b'0', NULL, 56, 9, NULL, NULL, 0, 0, NULL, 0, '2025-06-07 14:41:28');
INSERT INTO `tp_po` VALUES (41, '11.2', '掌握计算机工程项目管理的原理和知识，能在多\n学科环境中进行工程管理实践。', b'0', NULL, 56, 9, NULL, NULL, 0, 0, NULL, 0, '2025-06-07 14:41:51');
INSERT INTO `tp_po` VALUES (42, '12.1', '能够正确认识个人发展与社会发展的关系，理解终身学习的必要性，具有自主学习和终身学习的意识。', b'0', NULL, 57, 9, NULL, NULL, 0, 0, NULL, 0, '2025-06-07 14:42:11');
INSERT INTO `tp_po` VALUES (43, '12.2', '通过不断探索和学习，持续提升个人知识能力和素养，从而满足社会及个体的发展需求。', b'0', NULL, 57, 9, NULL, NULL, 0, 0, NULL, 0, '2025-06-07 14:42:32');
INSERT INTO `tp_po` VALUES (44, '', '', b'0', NULL, NULL, 9, NULL, NULL, 0, 6192, '2025-06-07 15:05:53', 6192, '2025-06-07 15:05:53');
INSERT INTO `tp_po` VALUES (45, '', '', b'0', NULL, NULL, 9, NULL, NULL, 0, 6192, '2025-06-07 15:07:10', 6192, '2025-06-07 15:07:10');
INSERT INTO `tp_po` VALUES (46, '', '', b'0', NULL, NULL, 9, NULL, NULL, 0, 6192, '2025-06-07 15:26:35', 6192, '2025-06-07 15:26:35');
INSERT INTO `tp_po` VALUES (47, '', '', b'0', NULL, NULL, 9, NULL, NULL, 0, 6192, '2025-06-07 15:28:05', 6192, '2025-06-07 15:28:05');
INSERT INTO `tp_po` VALUES (48, '', '', b'0', NULL, NULL, 9, NULL, NULL, 0, 6192, '2025-06-07 16:17:55', 6192, '2025-06-07 16:17:55');
INSERT INTO `tp_po` VALUES (49, '毕业要求3', '毕业要求3', b'0', NULL, 29, 1, NULL, NULL, -1, 6192, '2025-06-07 16:22:12', 6192, '2025-06-07 16:23:34');
INSERT INTO `tp_po` VALUES (50, '毕业要求4', '毕业要求45', b'0', NULL, 29, 1, NULL, NULL, -1, 6192, '2025-06-07 16:23:27', 6192, '2025-06-07 16:23:33');
INSERT INTO `tp_po` VALUES (51, '22', '222', b'0', NULL, 29, 1, NULL, NULL, 0, 6192, '2025-06-07 16:24:37', 6192, '2025-06-07 16:24:37');
INSERT INTO `tp_po` VALUES (52, '6.3', 'test6.3', b'0', NULL, 51, 9, NULL, NULL, -1, 0, NULL, 0, '2025-06-07 17:45:22');
INSERT INTO `tp_po` VALUES (53, '1', '工程知识应用:能够将数学、自然科学、计算机工程基础和专业知识用于解决计算机复杂工程问题。', b'1', NULL, 2, 9, NULL, NULL, 0, 6192, NULL, 6192, '2025-06-08 21:56:16');
INSERT INTO `tp_po` VALUES (54, '', '', b'0', NULL, NULL, 9, NULL, NULL, 0, 6192, '2025-06-07 20:57:18', 6192, '2025-06-07 20:57:18');
INSERT INTO `tp_po` VALUES (55, '', '', b'0', NULL, NULL, 1, NULL, NULL, 0, 6192, '2025-06-08 13:12:12', 6192, '2025-06-08 13:12:12');
INSERT INTO `tp_po` VALUES (56, '', '', b'0', NULL, NULL, 9, NULL, NULL, 0, 6192, '2025-06-08 16:50:48', 6192, '2025-06-08 16:50:48');
INSERT INTO `tp_po` VALUES (57, '', '', b'0', NULL, NULL, 9, NULL, NULL, 0, 6192, '2025-06-08 16:51:18', 6192, '2025-06-08 16:51:18');
INSERT INTO `tp_po` VALUES (58, '', '', b'0', NULL, NULL, 9, NULL, NULL, 0, 6192, '2025-06-08 16:51:20', 6192, '2025-06-08 16:51:20');
INSERT INTO `tp_po` VALUES (59, '', '', b'0', NULL, NULL, 9, NULL, NULL, 0, 6192, '2025-06-08 17:02:21', 6192, '2025-06-08 17:02:21');
INSERT INTO `tp_po` VALUES (66, '1', '问题分析:能够应用数学、自然科学和计算机工程科学的基本原理,识别、表达、并通过文献研究分析计算机复杂工程问题,以获得有效结论。', b'1', NULL, 26, 9, NULL, NULL, 0, 6192, '2025-06-08 19:07:57', 6192, '2025-06-08 21:56:34');
INSERT INTO `tp_po` VALUES (67, '2', '设计/开发解决方案：能够设计针对计算机复杂工程问题的解决方案，设计满足特定需求的系统、单元(部件)或工艺流程，并能够在设计环节中体现创新意识,考虑社会、健康、安全、法律、文化以及环境等因素。', b'1', NULL, 27, 9, NULL, NULL, 0, 6192, '2025-06-08 21:54:16', 6192, '2025-06-08 21:59:17');
INSERT INTO `tp_po` VALUES (68, '3', '研究：能够基于科学原理并采用科学方法对计算机复杂工程问题进行研究，包括设计实验、分析与解释数据、并通过信息综合得到合理有效的结论。', b'1', NULL, 49, 9, NULL, NULL, 0, 6192, '2025-06-08 21:57:27', 6192, '2025-06-08 21:57:35');
INSERT INTO `tp_po` VALUES (69, '4', '使用现代工具：能够针对计算机复杂工程问题，开发、选择与使用恰当的技术、资源、现代工程工具和信息技术工具，包括对复杂工程问题的预测与模拟，并能够理解其局限性。', b'1', NULL, 50, 9, NULL, NULL, 0, 6192, '2025-06-08 21:59:45', 6192, '2025-06-08 21:59:52');
INSERT INTO `tp_po` VALUES (70, '5', '工程与社会：能够基于工程相关背景知识，进行合理分析，评价专业工程实践和复杂工程问题解决方案对社会、健康、安全、法律以及文化的影响，并理解应承担的责任。', b'1', NULL, 51, 9, NULL, NULL, 0, 6192, '2025-06-08 22:03:21', 6192, '2025-06-08 22:03:21');
INSERT INTO `tp_po` VALUES (71, '6', '环境和可持续发展：能够理解和评价针对计算机复杂工程问题的工程实践对环境、社会可持续发展的影响。', b'1', NULL, 52, 9, NULL, NULL, 0, 6192, '2025-06-08 22:04:18', 6192, '2025-06-08 22:11:45');
INSERT INTO `tp_po` VALUES (72, '7', '职业规范：具有人文社会科学素养、社会责任感，能够在工程实践中理解并遵守工程职业道德和规范，履行责任。', b'1', NULL, 53, 9, NULL, NULL, 0, 6192, '2025-06-08 22:12:12', 6192, '2025-06-08 22:20:53');
INSERT INTO `tp_po` VALUES (73, '8', '个人和团队：能够在多学科背景下的团队中承担个体、团队成员以及负责人的角色。', b'1', NULL, 54, 9, NULL, NULL, 0, 6192, '2025-06-08 22:21:06', 6192, '2025-06-08 22:21:14');
INSERT INTO `tp_po` VALUES (74, '9', '沟通：能够就计算机领域的复杂工程问题与业界同行及社会公众进行有效沟通和交流，包括撰写报告和设计文稿、陈述发言、清晰表达或回应指令，并具备一定的国际视野，能够在跨文化背景下进行沟通和交流。', b'1', NULL, 55, 9, NULL, NULL, 0, 6192, '2025-06-08 22:21:24', 6192, '2025-06-08 22:21:36');
INSERT INTO `tp_po` VALUES (75, '10', '项目管理：理解并掌握工程管理原理与经济决策方法，能在多学科环境中应用。', b'1', NULL, 56, 9, NULL, NULL, 0, 6192, '2025-06-08 22:22:00', 6192, '2025-06-08 22:22:10');
INSERT INTO `tp_po` VALUES (76, '11', '终身学习：有自主学习和终身学习的意识，特别是行业有不断学习和适应发展的能力。', b'1', NULL, 57, 9, NULL, NULL, 0, 6192, '2025-06-08 22:25:35', 6192, '2025-06-08 22:28:41');

-- ----------------------------
-- Table structure for tp_po_matrix
-- ----------------------------
DROP TABLE IF EXISTS `tp_po_matrix`;
CREATE TABLE `tp_po_matrix`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '支撑点ID',
  `po_id` bigint NULL DEFAULT NULL COMMENT '毕业要求id',
  `course_id` bigint NULL DEFAULT NULL COMMENT '课程id',
  `weight` decimal(5, 2) NULL DEFAULT NULL COMMENT '支撑权重',
  `standard_id` tinyint(1) NULL DEFAULT NULL COMMENT '所属工程教育认证指标id',
  `plan_id` bigint NULL DEFAULT NULL COMMENT '所属专业培养计划id',
  `major_id` bigint NULL DEFAULT NULL COMMENT '所属专业id（冗余）',
  `academy_id` bigint NULL DEFAULT NULL COMMENT '所属学院id（冗余）',
  `status` int NOT NULL DEFAULT 0 COMMENT '记录状态{0:正常状态；-1:删除状态；}',
  `creator` bigint NOT NULL DEFAULT 0 COMMENT '记录创建者',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录',
  `modifier` bigint NOT NULL DEFAULT 0 COMMENT '记录最后修改者',
  `modify_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录最后修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `tp_po_matrix_po_id_course_id_unique`(`po_id` ASC, `course_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 79 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '课程体系支撑毕业要求矩阵' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of tp_po_matrix
-- ----------------------------
INSERT INTO `tp_po_matrix` VALUES (1, 1, 1, 0.50, 1, 1, 1, 1, 0, 0, '2025-05-11 21:22:41', 0, '2025-05-11 21:22:41');
INSERT INTO `tp_po_matrix` VALUES (2, 2, 1, 0.50, 1, 1, 1, 1, 0, 0, '2025-05-11 21:22:41', 0, '2025-05-11 21:22:41');
INSERT INTO `tp_po_matrix` VALUES (10, 1, 15, 0.20, 29, 1, NULL, NULL, 0, 6192, '2025-05-29 21:38:53', 6192, '2025-05-31 11:07:07');
INSERT INTO `tp_po_matrix` VALUES (11, 1, 14, 0.30, 29, 1, NULL, NULL, 0, 6192, '2025-05-29 21:38:55', 6192, '2025-05-29 21:38:55');
INSERT INTO `tp_po_matrix` VALUES (12, 1, 13, 0.20, 29, 1, NULL, NULL, 0, 6192, '2025-05-31 11:06:25', 6192, '2025-05-31 11:06:25');
INSERT INTO `tp_po_matrix` VALUES (13, 10, 15, 0.50, 29, 1, NULL, NULL, 0, 6192, '2025-05-31 14:59:37', 6192, '2025-05-31 14:59:37');
INSERT INTO `tp_po_matrix` VALUES (14, 10, 12, 0.20, 29, 1, NULL, NULL, 0, 6192, '2025-05-31 14:59:41', 6192, '2025-05-31 14:59:41');
INSERT INTO `tp_po_matrix` VALUES (15, 10, 6, 0.30, 29, 1, NULL, NULL, 0, 6192, '2025-05-31 14:59:46', 6192, '2025-05-31 14:59:46');
INSERT INTO `tp_po_matrix` VALUES (16, 2, 10, 0.00, 30, 1, NULL, NULL, 0, 6192, '2025-05-31 15:00:11', 6192, '2025-05-31 15:00:11');
INSERT INTO `tp_po_matrix` VALUES (17, 1, 19, 1.00, 29, 1, NULL, NULL, 0, 6192, '2025-06-07 16:52:58', 6192, '2025-06-08 21:30:56');
INSERT INTO `tp_po_matrix` VALUES (18, 1, 18, 0.00, 29, 1, NULL, NULL, 0, 6192, '2025-06-07 16:52:59', 6192, '2025-06-09 16:06:33');
INSERT INTO `tp_po_matrix` VALUES (19, 1, 17, 0.00, 29, 1, NULL, NULL, 0, 6192, '2025-06-07 16:53:07', 6192, '2025-06-08 19:51:55');
INSERT INTO `tp_po_matrix` VALUES (20, 3, 19, 0.00, 30, 1, NULL, NULL, 0, 6192, '2025-06-07 16:53:40', 6192, '2025-06-07 16:53:40');
INSERT INTO `tp_po_matrix` VALUES (21, 2, 19, 0.00, 30, 1, NULL, NULL, 0, 6192, '2025-06-07 16:53:40', 6192, '2025-06-07 16:53:40');
INSERT INTO `tp_po_matrix` VALUES (22, 51, 19, 0.00, 29, 1, NULL, NULL, 0, 6192, '2025-06-07 16:53:42', 6192, '2025-06-07 16:53:42');
INSERT INTO `tp_po_matrix` VALUES (23, 51, 18, 0.00, 29, 1, NULL, NULL, 0, 6192, '2025-06-07 16:53:46', 6192, '2025-06-09 16:06:33');
INSERT INTO `tp_po_matrix` VALUES (24, 12, 28, 1.00, 2, 9, NULL, NULL, 0, 6192, '2025-06-07 17:11:41', 6192, '2025-06-07 17:30:48');
INSERT INTO `tp_po_matrix` VALUES (25, 13, 28, 0.20, 2, 9, NULL, NULL, 0, 6192, '2025-06-07 17:11:43', 6192, '2025-06-07 17:21:41');
INSERT INTO `tp_po_matrix` VALUES (26, 14, 28, 0.00, 2, 9, NULL, NULL, 0, 6192, '2025-06-07 17:13:24', 6192, '2025-06-07 17:21:47');
INSERT INTO `tp_po_matrix` VALUES (27, 15, 28, 0.00, 2, 9, NULL, NULL, 0, 6192, '2025-06-07 17:13:43', 6192, '2025-06-07 17:13:43');
INSERT INTO `tp_po_matrix` VALUES (28, 23, 28, 0.00, 49, 9, NULL, NULL, 0, 6192, '2025-06-07 17:13:48', 6192, '2025-06-07 17:22:04');
INSERT INTO `tp_po_matrix` VALUES (29, 24, 28, 0.00, 49, 9, NULL, NULL, 0, 6192, '2025-06-07 17:13:53', 6192, '2025-06-07 17:22:02');
INSERT INTO `tp_po_matrix` VALUES (30, 25, 28, 0.00, 49, 9, NULL, NULL, 0, 6192, '2025-06-07 17:13:58', 6192, '2025-06-07 17:22:00');
INSERT INTO `tp_po_matrix` VALUES (31, 26, 28, 0.00, 50, 9, NULL, NULL, 0, 6192, '2025-06-07 17:13:58', 6192, '2025-06-07 17:21:56');
INSERT INTO `tp_po_matrix` VALUES (32, 12, 27, 0.00, 2, 9, NULL, NULL, 0, 6192, '2025-06-07 17:14:40', 6192, '2025-06-07 17:22:30');
INSERT INTO `tp_po_matrix` VALUES (33, 26, 26, 0.00, 50, 9, NULL, NULL, 0, 6192, '2025-06-07 17:21:54', 6192, '2025-06-07 17:21:54');
INSERT INTO `tp_po_matrix` VALUES (34, 26, 27, 0.00, 50, 9, NULL, NULL, 0, 6192, '2025-06-07 17:22:01', 6192, '2025-06-07 17:22:01');
INSERT INTO `tp_po_matrix` VALUES (35, 16, 26, 0.00, 26, 9, NULL, NULL, 0, 6192, '2025-06-07 17:22:31', 6192, '2025-06-07 17:22:31');
INSERT INTO `tp_po_matrix` VALUES (36, 23, 27, 1.00, 49, 9, NULL, NULL, 0, 6192, '2025-06-07 17:22:49', 6192, '2025-06-07 17:30:07');
INSERT INTO `tp_po_matrix` VALUES (37, 24, 27, 1.00, 49, 9, NULL, NULL, 0, 6192, '2025-06-07 17:22:52', 6192, '2025-06-07 17:30:08');
INSERT INTO `tp_po_matrix` VALUES (38, 14, 26, 0.30, 2, 9, NULL, NULL, 0, 6192, '2025-06-07 17:23:29', 6192, '2025-06-07 17:30:53');
INSERT INTO `tp_po_matrix` VALUES (39, 28, 26, 1.00, 50, 9, NULL, NULL, 0, 6192, '2025-06-07 17:23:44', 6192, '2025-06-07 17:31:07');
INSERT INTO `tp_po_matrix` VALUES (40, 29, 25, 0.00, 51, 9, NULL, NULL, 0, 6192, '2025-06-07 17:23:44', 6192, '2025-06-07 17:23:44');
INSERT INTO `tp_po_matrix` VALUES (41, 14, 25, 0.30, 2, 9, NULL, NULL, 0, 6192, '2025-06-07 17:24:16', 6192, '2025-06-07 17:24:16');
INSERT INTO `tp_po_matrix` VALUES (42, 18, 25, 0.30, 26, 9, NULL, NULL, 0, 6192, '2025-06-07 17:24:30', 6192, '2025-06-07 17:24:30');
INSERT INTO `tp_po_matrix` VALUES (43, 25, 25, 0.30, 49, 9, NULL, NULL, 0, 6192, '2025-06-07 17:24:34', 6192, '2025-06-07 17:24:48');
INSERT INTO `tp_po_matrix` VALUES (44, 26, 24, 0.00, 50, 9, NULL, NULL, 0, 6192, '2025-06-07 17:24:35', 6192, '2025-06-07 17:24:35');
INSERT INTO `tp_po_matrix` VALUES (45, 17, 24, 1.00, 26, 9, NULL, NULL, 0, 6192, '2025-06-07 17:25:19', 6192, '2025-06-07 17:30:54');
INSERT INTO `tp_po_matrix` VALUES (46, 18, 24, 0.00, 26, 9, NULL, NULL, 0, 6192, '2025-06-07 17:25:39', 6192, '2025-06-07 17:25:39');
INSERT INTO `tp_po_matrix` VALUES (47, 22, 24, 1.00, 27, 9, NULL, NULL, 0, 6192, '2025-06-07 17:25:49', 6192, '2025-06-07 17:30:13');
INSERT INTO `tp_po_matrix` VALUES (48, 23, 24, 0.00, 49, 9, NULL, NULL, 0, 6192, '2025-06-07 17:25:59', 6192, '2025-06-07 17:25:59');
INSERT INTO `tp_po_matrix` VALUES (49, 27, 24, 0.40, 50, 9, NULL, NULL, 0, 6192, '2025-06-07 17:26:13', 6192, '2025-06-07 17:26:20');
INSERT INTO `tp_po_matrix` VALUES (50, 28, 24, 0.00, 50, 9, NULL, NULL, 0, 6192, '2025-06-07 17:26:13', 6192, '2025-06-07 17:26:13');
INSERT INTO `tp_po_matrix` VALUES (51, 38, 24, 1.00, 55, 9, NULL, NULL, 0, 6192, '2025-06-07 17:26:42', 6192, '2025-06-07 17:30:36');
INSERT INTO `tp_po_matrix` VALUES (52, 40, 24, 1.00, 56, 9, NULL, NULL, 0, 6192, '2025-06-07 17:26:55', 6192, '2025-06-07 17:30:36');
INSERT INTO `tp_po_matrix` VALUES (53, 41, 24, 1.00, 56, 9, NULL, NULL, 0, 6192, '2025-06-07 17:26:58', 6192, '2025-06-07 17:30:35');
INSERT INTO `tp_po_matrix` VALUES (54, 19, 23, 1.00, 27, 9, NULL, NULL, 0, 6192, '2025-06-07 17:27:24', 6192, '2025-06-07 17:30:19');
INSERT INTO `tp_po_matrix` VALUES (55, 21, 23, 1.00, 27, 9, NULL, NULL, 0, 6192, '2025-06-07 17:27:30', 6192, '2025-06-07 17:30:17');
INSERT INTO `tp_po_matrix` VALUES (56, 27, 23, 0.60, 50, 9, NULL, NULL, 0, 6192, '2025-06-07 17:27:42', 6192, '2025-06-07 17:27:55');
INSERT INTO `tp_po_matrix` VALUES (57, 27, 22, 0.00, 50, 9, NULL, NULL, 0, 6192, '2025-06-07 17:27:43', 6192, '2025-06-07 17:27:43');
INSERT INTO `tp_po_matrix` VALUES (58, 18, 22, 0.40, 26, 9, NULL, NULL, 0, 6192, '2025-06-07 17:28:21', 6192, '2025-06-07 17:29:36');
INSERT INTO `tp_po_matrix` VALUES (59, 18, 23, 0.00, 26, 9, NULL, NULL, 0, 6192, '2025-06-07 17:28:28', 6192, '2025-06-07 17:28:28');
INSERT INTO `tp_po_matrix` VALUES (60, 43, 22, 1.00, 57, 9, NULL, NULL, 0, 6192, '2025-06-07 17:28:33', 6192, '2025-06-07 17:28:33');
INSERT INTO `tp_po_matrix` VALUES (61, 43, 21, 0.00, 57, 9, NULL, NULL, 0, 6192, '2025-06-07 17:28:39', 6192, '2025-06-07 17:28:39');
INSERT INTO `tp_po_matrix` VALUES (62, 13, 21, 0.80, 2, 9, NULL, NULL, 0, 6192, '2025-06-07 17:28:58', 6192, '2025-06-07 17:28:58');
INSERT INTO `tp_po_matrix` VALUES (63, 16, 21, 1.00, 26, 9, NULL, NULL, 0, 6192, '2025-06-07 17:29:04', 6192, '2025-06-07 17:29:05');
INSERT INTO `tp_po_matrix` VALUES (64, 16, 20, 0.00, 26, 9, NULL, NULL, 0, 6192, '2025-06-07 17:29:04', 6192, '2025-06-07 17:29:04');
INSERT INTO `tp_po_matrix` VALUES (65, 13, 20, 0.00, 2, 9, NULL, NULL, 0, 6192, '2025-06-07 17:29:13', 6192, '2025-06-07 17:29:13');
INSERT INTO `tp_po_matrix` VALUES (66, 14, 20, 0.40, 2, 9, NULL, NULL, 0, 6192, '2025-06-07 17:29:30', 6192, '2025-06-07 17:29:30');
INSERT INTO `tp_po_matrix` VALUES (67, 18, 20, 0.30, 26, 9, NULL, NULL, 0, 6192, '2025-06-07 17:29:34', 6192, '2025-06-07 17:29:34');
INSERT INTO `tp_po_matrix` VALUES (68, 25, 20, 0.70, 49, 9, NULL, NULL, 0, 6192, '2025-06-07 17:29:59', 6192, '2025-06-07 17:29:59');
INSERT INTO `tp_po_matrix` VALUES (69, 32, 26, 0.00, 52, 9, NULL, NULL, 0, 6192, '2025-06-07 17:31:16', 6192, '2025-06-07 17:31:16');
INSERT INTO `tp_po_matrix` VALUES (70, 1, 12, 1.00, 29, 1, NULL, NULL, 0, 6192, '2025-06-07 21:38:10', 6192, '2025-06-07 21:38:10');
INSERT INTO `tp_po_matrix` VALUES (71, 1, 11, 0.00, 29, 1, NULL, NULL, 0, 6192, '2025-06-07 21:38:13', 6192, '2025-06-07 21:43:28');
INSERT INTO `tp_po_matrix` VALUES (72, 51, 17, 0.00, 29, 1, NULL, NULL, 0, 6192, '2025-06-07 21:41:07', 6192, '2025-06-09 16:06:33');
INSERT INTO `tp_po_matrix` VALUES (73, 1, 10, 0.00, 29, 1, NULL, NULL, 0, 6192, '2025-06-07 21:41:59', 6192, '2025-06-07 21:43:28');
INSERT INTO `tp_po_matrix` VALUES (74, 1, 9, 0.00, 29, 1, NULL, NULL, 0, 6192, '2025-06-07 21:42:00', 6192, '2025-06-07 21:43:26');
INSERT INTO `tp_po_matrix` VALUES (75, 16, 24, 0.00, 26, 9, NULL, NULL, 0, 6192, '2025-06-08 22:29:36', 6192, '2025-06-08 22:29:36');
INSERT INTO `tp_po_matrix` VALUES (76, 51, 14, 0.00, 29, 1, NULL, NULL, 0, 6192, '2025-06-09 16:06:34', 6192, '2025-06-09 16:06:34');
INSERT INTO `tp_po_matrix` VALUES (77, 3, 13, 0.00, 30, 1, NULL, NULL, 0, 6192, '2025-06-09 16:06:35', 6192, '2025-06-09 16:06:35');
INSERT INTO `tp_po_matrix` VALUES (78, 2, 17, 0.00, 30, 1, NULL, NULL, 0, 6192, '2025-06-09 16:06:36', 6192, '2025-06-09 16:06:36');

SET FOREIGN_KEY_CHECKS = 1;
