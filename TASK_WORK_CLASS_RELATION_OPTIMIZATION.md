# TaskWork班级关联关系优化实现文档

## 问题描述

在TaskWorkServiceImpl的createTaskWork方法中存在业务逻辑问题：当前代码在创建教学任务后直接保存班级关联关系，但没有处理可能存在的重复关联问题。这可能导致同一个课程与班级之间存在多个关联记录，影响数据一致性。

## 优化方案

### 1. 核心优化逻辑

#### 1.1 重复关联检查与清理
在调用`taskWorklistClassesService.saveTaskClassRelations()`之前，先清理可能存在的重复关联关系：

```java
// 2. 清理重复的班级关联关系并保存新的关联关系
if (!CollectionUtils.isEmpty(dto.getClassIds())) {
    // 2.1 清理该课程与这些班级的现有关联关系，避免重复
    int cleanedCount = taskWorklistClassesService.cleanupDuplicateClassRelations(dto.getCourseId(), dto.getClassIds());
    if (cleanedCount > 0) {
        log.info("创建教学任务时清理了 {} 条重复的班级关联关系", cleanedCount);
    }
    
    // 2.2 保存新的班级关联关系
    taskWorklistClassesService.saveTaskClassRelations(taskWork.getId(), dto.getClassIds(), taskWork.getCreator());
}
```

#### 1.2 清理逻辑实现
新增`cleanupDuplicateClassRelations`方法，实现智能清理：

```java
@Override
@Transactional(rollbackFor = Exception.class)
public int cleanupDuplicateClassRelations(Long courseId, List<Long> classIds) {
    // 1. 查询该课程下所有的教学任务ID
    List<TaskWork> taskWorks = taskWorkMapper.selectList(
        Wrappers.lambdaQuery(TaskWork.class)
            .eq(TaskWork::getCourseId, courseId)
            .eq(TaskWork::getStatus, 0)
    );
    
    // 2. 查询这些教学任务与指定班级的现有关联关系
    List<TaskWorklistClasses> existingRelations = this.list(
        Wrappers.lambdaQuery(TaskWorklistClasses.class)
            .in(TaskWorklistClasses::getTaskId, taskIds)
            .in(TaskWorklistClasses::getClassId, classIds)
    );
    
    // 3. 批量删除现有关联关系
    return this.removeByIds(relationIds) ? relationIds.size() : 0;
}
```

### 2. 实现特性

#### 2.1 批量查询优化
- 使用`IN`查询批量获取相关的教学任务
- 使用`IN`查询批量获取需要清理的关联关系
- 避免逐条查询导致的性能问题

#### 2.2 事务安全性
- 所有操作都在`@Transactional`注解保护下执行
- 确保查询、删除、新增操作的原子性
- 任何步骤失败都会触发整个事务回滚

#### 2.3 物理删除策略
- 使用物理删除清理重复关联关系
- 确保数据的彻底清理，避免垃圾数据
- 减少数据库存储空间占用

### 3. 性能优化

#### 3.1 批量操作
```java
// 批量查询教学任务
List<TaskWork> taskWorks = taskWorkMapper.selectList(taskWrapper);

// 批量查询关联关系
List<TaskWorklistClasses> existingRelations = this.list(relationWrapper);

// 批量删除关联关系
boolean removed = this.removeByIds(relationIds);
```

#### 3.2 条件优化
- 只查询有效状态的教学任务（`status=0`）
- 使用精确的查询条件减少数据扫描
- 提前验证参数有效性，避免无效查询

### 4. 日志记录

#### 4.1 操作日志
```java
log.info("开始清理课程 {} 与班级 {} 的重复关联关系", courseId, classIds);
log.info("成功清理课程 {} 与班级 {} 的 {} 条重复关联关系", courseId, classIds, deletedCount);
```

#### 4.2 异常处理
- 参数验证和空值检查
- 详细的警告和信息日志
- 清理结果的统计和报告

## 业务场景

### 1. 重复关联产生的场景

#### 1.1 同一课程多个教学任务
- 同一门课程可能有多个教学任务（不同班级、不同学期）
- 新建教学任务时可能与现有任务的班级有重叠
- 需要清理旧的关联关系，建立新的关联关系

#### 1.2 教学任务调整
- 教学任务的班级分配可能需要调整
- 班级可能从一个教学任务转移到另一个教学任务
- 需要确保关联关系的唯一性

### 2. 清理策略

#### 2.1 全量清理
当创建新的教学任务时，清理该课程与所有指定班级的现有关联关系：
- 查询条件：`courseId = dto.getCourseId() AND classId IN (dto.getClassIds())`
- 删除操作：物理删除所有匹配的关联记录

#### 2.2 增量清理
支持部分班级的关联关系清理：
- 只清理指定班级的关联关系
- 保留其他班级的关联关系不变
- 提供灵活的清理粒度

## 数据一致性保证

### 1. 唯一性约束
- 确保每个班级与课程的关联关系唯一
- 避免重复数据导致的业务逻辑错误
- 保证数据查询结果的准确性

### 2. 引用完整性
- 清理操作只影响关联关系表
- 不影响教学任务主表和班级主表
- 保持数据模型的完整性

### 3. 业务规则一致性
- 新的教学任务优先级高于旧的教学任务
- 最新的关联关系覆盖历史关联关系
- 符合教学管理的业务逻辑

## 测试验证

### 1. 功能测试
- 测试重复关联关系的检查和清理
- 测试部分重复关联关系的处理
- 测试空参数和边界条件的处理

### 2. 性能测试
- 验证批量操作的性能优势
- 测试大量数据情况下的处理效率
- 确保清理操作不影响系统性能

### 3. 事务测试
- 验证事务的原子性
- 测试异常情况下的回滚机制
- 确保数据一致性

## 使用示例

### 1. 创建教学任务
```java
TaskWorkDTO dto = new TaskWorkDTO();
dto.setCourseId(1L);
dto.setClassIds(Arrays.asList(101L, 102L, 103L));
// ... 其他字段设置

// 创建教学任务，自动清理重复关联关系
boolean result = taskWorkService.createTaskWork(dto);
```

### 2. 手动清理重复关联
```java
// 清理指定课程与班级的重复关联关系
int cleanedCount = taskWorklistClassesService.cleanupDuplicateClassRelations(
    courseId, Arrays.asList(101L, 102L, 103L)
);
```

## 注意事项

### 1. 数据备份
在生产环境中执行清理操作前，建议备份相关数据表。

### 2. 业务影响
清理操作可能会影响正在进行的教学任务查询，建议在业务低峰期执行。

### 3. 权限控制
确保只有授权用户才能执行清理操作，避免误删除重要数据。

### 4. 监控告警
建议对清理操作进行监控，当清理的记录数量异常时及时告警。

这个优化确保了教学任务与班级关联关系的唯一性和一致性，提高了系统的数据质量和业务逻辑的正确性。
