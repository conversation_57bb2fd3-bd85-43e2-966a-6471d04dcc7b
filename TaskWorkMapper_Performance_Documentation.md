# TaskWorkMapper 高性能SQL查询实现文档

## 概述

本文档描述了在 `TaskWorkMapper.java` 中实现的高性能SQL查询，用于检索教师的所有教学任务信息。该实现使用MyBatis注解方式，通过子查询优化性能，最小化数据库调用次数。

## 实现特性

### 1. 使用MyBatis注解而非XML配置
- 所有SQL查询都使用 `@Select` 注解定义
- 结果映射使用 `@Results` 和 `@Result` 注解
- 支持复杂的一对多关系映射

### 2. 高性能查询优化
- **子查询聚合**: 使用子查询计算统计数据，避免多次数据库调用
- **单次JOIN**: 主查询只进行必要的表连接
- **索引友好**: 查询条件基于主键和外键，充分利用数据库索引

### 3. 数据完整性
- 返回完整的课程信息（courseId, courseName, courseCode, planId）
- 包含任务信息（taskId, taskTerm, taskYear, isCurrentSemester）
- 提供聚合统计（classCount, totalStudents, teacherCount）
- 支持班级详情列表（classes字段）

## 方法说明

### 1. selectCoursesByTeacherId (基础版本)

```java
List<CourseStaticsByTaskVO> selectCoursesByTeacherId(@Param("teacherId") Long teacherId);
```

**特点:**
- 使用延迟加载获取班级详情
- 适用于不需要立即访问班级信息的场景
- 内存占用较小

**SQL优化点:**
- 使用子查询计算班级数量、学生总数、教师数量
- 避免了复杂的GROUP BY操作
- 减少了数据传输量

### 2. getTeachingTaskStatisticsOptimized (推荐版本)

```java
List<CourseStaticsByTaskVO> getTeachingTaskStatisticsOptimized(@Param("teacherId") Long teacherId);
```

**特点:**
- 使用即时加载获取班级详情
- 适用于需要完整数据的场景
- 性能更佳，减少N+1查询问题

### 3. getClassesByTaskId (辅助方法)

```java
List<ClassesVO> getClassesByTaskId(@Param("taskId") Long taskId);
```

**特点:**
- 支持延迟加载的班级详情查询
- 返回完整的班级信息
- 按班级名称排序

## 性能优化策略

### 1. 子查询优化
```sql
-- 班级数量统计
(
    SELECT COUNT(DISTINCT twc.class_id)
    FROM task_worklist_classes twc
    WHERE twc.task_id = tw.id
) as class_count
```

### 2. 条件过滤
- 只查询状态正常的记录 (`status = 0`)
- 基于教师ID进行精确过滤
- 排除已删除的数据

### 3. 排序优化
```sql
ORDER BY tw.task_year DESC, tw.task_term DESC, c.course_name
```
- 按年份和学期降序排列
- 最新的教学任务排在前面

## 数据库表关系

```
task_worklist (主表)
├── tp_course (课程信息)
├── task_worklist_teachers (教师关联)
├── task_worklist_classes (班级关联)
└── base_classes (班级详情)
```

## 使用示例

### 基本用法
```java
@Autowired
private TaskWorkMapper taskWorkMapper;

public List<CourseStaticsByTaskVO> getTeacherTasks(Long teacherId) {
    return taskWorkMapper.getTeachingTaskStatisticsOptimized(teacherId);
}
```

### 处理返回数据
```java
List<CourseStaticsByTaskVO> results = taskWorkMapper.getTeachingTaskStatisticsOptimized(teacherId);

for (CourseStaticsByTaskVO courseStats : results) {
    System.out.println("课程: " + courseStats.getCourseName());
    System.out.println("班级数: " + courseStats.getClassCount());
    System.out.println("学生总数: " + courseStats.getTotalStudents());
    
    // 访问班级详情
    if (courseStats.getClasses() != null) {
        courseStats.getClasses().forEach(clazz -> {
            System.out.println("班级: " + clazz.getClassName());
        });
    }
}
```

## 性能对比

| 方法 | 数据库调用次数 | 内存使用 | 适用场景 |
|------|---------------|----------|----------|
| 传统多次查询 | N+1 | 低 | 小数据量 |
| selectCoursesByTeacherId | 1+N (延迟加载) | 中 | 不需要班级详情 |
| getTeachingTaskStatisticsOptimized | 1+N (即时加载) | 高 | 需要完整数据 |

## 注意事项

1. **索引要求**: 确保相关表的外键字段有适当的索引
2. **数据一致性**: 查询会过滤掉状态为删除的记录
3. **内存管理**: 对于大量数据，考虑使用分页查询
4. **缓存策略**: 可以考虑对结果进行缓存以进一步提升性能

## 扩展建议

1. **分页支持**: 可以添加分页参数支持大数据量查询
2. **条件过滤**: 可以添加年份、学期等过滤条件
3. **缓存集成**: 集成Redis等缓存系统
4. **监控指标**: 添加查询性能监控
