# 工程教育专业认证智能管理信息系统 (OBE)

## 项目简介
工程教育专业认证智能管理信息系统（OBE - Outcome-Based Education）是一个专门为高校工程教育专业认证设计的智能管理平台。该系统旨在帮助高校更好地管理和评估工程教育专业的认证过程，提高认证效率，确保教育质量。

主要功能：
- 专业认证全流程管理
- 教学质量监控与评估
- 学生成果追踪与分析
- 认证材料智能管理
- 数据可视化展示

## 技术栈
- **后端框架**：Spring Boot 3.4.5
- **数据库**：MySQL
- **缓存**：Redis
- **ORM框架**：MyBatis-Plus 3.5.12
- **权限认证**：Sa-Token 1.42.0
- **工具库**：
  - Hutool 5.8.37
  - Fastjson2 2.0.57
  - EasyExcel 4.0.3
  - MapStruct 1.5.5.Final
- **其他**：
  - Lombok
  - Druid 连接池
  - 阿里云短信服务

## 开发环境要求
- JDK 21
- Maven 3.8+
- MySQL 8.0+
- Redis 6.0+

## 项目结构
```
src/main/java/com/hnumi/obe/
├── common/           # 公共模块
│   ├── config/      # 配置类
│   ├── constant/    # 常量定义
│   ├── entity/      # 实体类
│   ├── exception/   # 异常处理
│   ├── log/         # 日志处理
│   └── util/        # 工具类
├── controller/      # 控制器层
├── mapper/         # MyBatis映射层
├── service/        # 服务层
│   └── impl/       # 服务实现
└── AppRun.java     # 应用程序入口
```

## 代码规范

### 1. 命名规范
- **类名**：使用大驼峰命名法（UpperCamelCase）
- **方法名**：使用小驼峰命名法（lowerCamelCase）
- **变量名**：使用小驼峰命名法
- **常量名**：全大写，单词间用下划线分隔
- **包名**：全小写，使用点号分隔

### 2. 注释规范
- 类注释：必须包含类描述、作者、创建时间
- 方法注释：必须包含方法描述、参数说明、返回值说明
- 关键代码注释：对复杂逻辑必须添加注释说明

### 3. 代码格式
- 使用4个空格进行缩进
- 每行代码不超过120个字符
- 方法之间空一行
- 相关的代码块之间空一行

#### 3.1 缩进和空格
- 使用4个空格进行缩进，禁止使用Tab
- 运算符前后必须加空格
- 方法参数列表中的逗号后必须加空格
- if/for/while等关键字与括号之间必须加空格
- 括号内不要加空格
- 大括号的开始必须位于行尾，结束必须独占一行
- 空行中不要包含空格或制表符

#### 3.2 行宽和换行
- 每行代码不超过120个字符
- 超长行需要换行时，遵循以下原则：
  - 在逗号后换行
  - 在运算符前换行
  - 换行后缩进8个空格
  - 方法参数换行时，与第一个参数对齐

#### 3.3 命名格式
- 类名：使用大驼峰命名法，如`UserService`
- 方法名：使用小驼峰命名法，如`getUserById`
- 变量名：使用小驼峰命名法，如`userName`
- 常量名：全大写，单词间用下划线分隔，如`MAX_COUNT`
- 包名：全小写，使用点号分隔，如`com.hnumi.obe`
- 接口名：使用大驼峰命名法，必须加`I`前缀，如`IUserService`
- 抽象类：使用`Abstract`或`Base`前缀，如`AbstractService`
- 异常类：使用`Exception`结尾，如`APIException`
- 测试类：使用`Test`结尾，如`UserServiceTest`

#### 3.4 注释格式
- 类注释：使用Javadoc格式，必须包含类描述、作者、创建时间
```java
/**
 * 用户服务类
 * 提供用户相关的业务操作
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
```

- 方法注释：使用Javadoc格式，必须包含方法描述、参数说明、返回值说明
```java
/**
 * 根据ID查询用户信息
 *
 * @param id 用户ID
 * @return 用户信息
 * @throws BusinessException 当用户不存在时抛出
 */
```

- 变量注释：使用行注释，与变量保持同一缩进级别
```java
// 用户最大登录失败次数
private static final int MAX_LOGIN_FAIL_COUNT = 5;
```

- 代码块注释：使用行注释，说明代码块的功能
```java
// 校验用户权限
if (!hasPermission) {
    throw new BusinessException("无权限访问");
}
```

#### 3.5 代码组织
- 类成员顺序：
  1. 静态变量
  2. 实例变量
  3. 构造方法
  4. 公共方法
  5. 私有方法
  6. 内部类

- 方法组织：
  1. 公共方法在前
  2. 私有方法在后
  3. 相关的方法放在一起
  4. 重载方法放在一起

#### 3.6 代码示例
```java
/**
 * 用户服务实现类
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Service
@Slf4j
public class UserServiceImpl implements UserService {

    // 静态常量
    private static final String DEFAULT_PASSWORD = "123456";
    
    // 实例变量
    @Autowired
    private UserMapper userMapper;
    
    // 构造方法
    public UserServiceImpl() {
        // 初始化代码
    }
    
    // 公共方法
    @Override
    public UserVO getUserById(Long id) {
        // 参数校验
        if (id == null) {
            throw new BusinessException("用户ID不能为空");
        }
        
        // 查询用户
        User user = userMapper.selectById(id);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        
        // 转换并返回
        return UserConverter.INSTANCE.toVO(user);
    }
    
    // 私有方法
    private void validateUser(User user) {
        // 验证逻辑
    }
}
```

#### 3.7 最佳实践
- 保持方法简短，一个方法只做一件事
- 避免过深的嵌套，最多不超过3层
- 使用有意义的变量名，避免使用单字母
- 及时删除注释掉的代码
- 使用常量替代魔法数字
- 优先使用Java 8+的新特性
- 使用Stream API处理集合
- 使用Optional处理空值
- 使用Lambda表达式简化代码

### 4. 异常处理
- 统一使用全局异常处理
- 自定义业务异常必须继承 `BaseException`
- 异常信息必须明确具体

### 5. 日志规范
- 使用 SLF4J 进行日志记录
- 日志级别使用规范：
  - ERROR：系统错误
  - WARN：警告信息
  - INFO：重要业务信息
  - DEBUG：调试信息

### 6. 接口规范
- RESTful API 设计规范
- 统一响应格式
- 接口文档使用 Swagger 注解

## 项目配置
1. 数据库配置：`application.yml`
2. Redis配置：`application.yml`
3. 阿里云短信配置：`application.yml`

## 构建和运行
```bash
# 编译项目
mvn clean install

# 运行项目
mvn spring-boot:run
```

## 代码质量检查
项目集成了以下代码质量检查工具：
- Checkstyle：代码风格检查
- PMD：代码质量检查

## 开发规范
1. 遵循阿里巴巴Java开发手册
2. 使用统一的代码格式化工具
3. 提交前进行代码质量检查
4. 保持代码简洁，避免重复代码

## 版本控制
- 使用 Git 进行版本控制
- 遵循 Git Flow 工作流
- 提交信息必须清晰明确

## 贡献指南
1. Fork 本仓库
2. 创建特性分支
3. 提交代码
4. 创建 Pull Request

## 许可证
本项目采用 MIT 许可证，详情请参见 [LICENSE](LICENSE) 文件。

## 联系方式
- 作者：hnumi
- 邮箱：[您的邮箱地址]
- 项目地址：[项目仓库地址]

## 请求规范

### REST API 设计原则

1. 请求路径规范
   - 分隔符：`/` 仅作为分隔符，不应出现在URL末尾
   - 连字符：使用 `-` 代替下划线，提高可读性
   - 小写字母：URL 对大小写敏感，统一使用小写
   - 禁止使用动词：URL 中不应出现动词
   - 资源命名：使用名词复数形式，如`/users`而不是`/user`
   - 查询参数：使用`?`分隔，多个参数用`&`连接

2. 请求方式规范

| 请求方式 | 路径 | 描述 | 说明 |
|---------|------|------|------|
| GET | `/user` | 查询单个用户信息 | 查询当前用户 |
| GET | `/user/{id}` | 查询单个用户信息 | 查询指定用户 |
| POST | `/user` | 添加用户信息 | 新增用户 |
| PUT | `/user` | 修改用户信息 | 更新用户 |
| DELETE | `/user/{id}` | 删除用户信息 | 删除指定用户 |
| DELETE | `/user` | 批量删除用户信息 | 批量删除 |
| POST | `/user/list` | 分页查询用户列表 | 分页查询 |

3. 请求方式说明
   - GET：幂等操作，不改变数据状态
   - POST：非幂等操作，用于创建资源
   - PUT：幂等操作，用于更新资源
   - DELETE：幂等操作，用于删除资源

4. 请求参数规范
   - `@RequestBody`：接收 JSON 数据
   - `@RequestParam`：接收 URL 参数或表单数据
   - `@PathVariable`：接收路径参数
   - 参数校验：必须使用注解进行校验

### VO和DTO使用规范

1. VO（View Object）
   - 用于展示层，把某个指定页面（或组件）的所有数据封装起来
   - 命名规范：以`VO`结尾，如`UserVO`
   - 只包含展示所需的字段
   - 可以包含展示层特有的字段，如格式化后的日期

2. DTO（Data Transfer Object）
   - 用于服务层之间的数据传输
   - 命名规范：以`DTO`结尾，如`UserDTO`
   - 包含业务所需的字段
   - 可以包含多个实体的字段

## 响应规范

### 统一响应格式

```json
{
    "code": "200",      // 响应码
    "msg": "ok",      // 响应消息
    "data": {}        // 响应数据
}
```

### 响应码规范

1. HTTP 标准响应码
   - 2xx：成功
     - 200：请求成功
     - 201：创建成功
     - 204：删除成功
   - 4xx：客户端错误
     - 400：请求错误
     - 401：未认证
     - 403：禁止访问
     - 404：资源不存在
     - 422：参数验证错误
   - 5xx：服务器错误
     - 500：服务器内部错误
     - 503：服务不可用

2. 业务响应码
   - 格式：5位字符
   - 规则：首字母为业务代号，后4位为具体错误码
   - 示例：
     - A0001：用户名或密码错误
     - A0002：用户不存在
     - A0003：用户已存在
     - B0001：参数验证失败
     - C0001：系统内部错误

## Git使用规范

### 提交信息规范

1. 提交信息格式
   - 类型：feat、fix、docs、style、refactor、test、chore
   - 作用域：影响范围，如模块名
   - 描述：简短描述，不超过50个字符
   - 示例：`feat(user): add user login functionality`

2. 提交类型说明
   - feat：新功能
   - fix：修复bug
   - docs：文档更新
   - style：代码风格调整
   - refactor：代码重构
   - test：测试相关
   - chore：构建过程或辅助工具的变动

### 分支管理策略

1. 分支类型
   - master：主分支，用于生产环境
   - develop：开发分支，用于集成功能
   - feature：功能分支，用于开发新功能
   - hotfix：修复分支，用于修复生产环境bug

2. 分支命名规范
   - 功能分支：`feature/功能名称`
   - 修复分支：`hotfix/问题描述`

## 开发工具

### IDEA 插件
- `.ignore`：Git忽略文件配置
- `MyBatisX`：MyBatis增强插件
- `Lombok`：简化代码插件
- `MapStruct Support`：对象映射插件
- `Trae`：树形结构展示

### 常用注解
| 注解 | 描述 | 说明 |
|------|------|------|
| `@Log` | 自动回写日志 | 待优化，暂不使用 |
| `@Pattern` | 正则校验 | 如手机号校验 |
| `@Email` | 邮箱校验 | 邮箱格式校验 |
| `@Validated` | 参数校验 | 用于Controller或参数 |

## 学习资源
1. [MapStruct使用指南](https://www.yuque.com/zhangyanfeng/lkyhw1/gf8cuh21z780yvwz)
2. [Lombok注解详解](https://juejin.cn/post/6844904062962368525)
3. [参数校验最佳实践](https://mp.weixin.qq.com/s/XBdI_HNxgc4cIKDlQ2eZHQ)
4. [MyBatis-Plus教程](https://blog.csdn.net/zdsg45/article/details/105138493)
5. [钉钉开发文档](https://developers.dingtalk.com/document/app)

## 快速开始

### 1. 克隆项目
```bash
git clone https://github.com/your-username/obe-server.git
cd obe-server
git config --global user.name "张炎峰"  
git config --global user.email "<EMAIL>"
# 配置 Git 别名
git config alias.hooks-setup '!git config core.hooksPath .githooks && chmod +x .githooks/pre-commit'

# 立即执行配置
git hooks-setup

#检查代码的合规性
.\.mvn\mvnw.cmd checkstyle:check
```

### 2. 配置数据库
1. 创建数据库
```sql
CREATE DATABASE obe DEFAULT CHARACTER SET utf8mb4;
```

2. 修改配置文件
编辑 `src/main/resources/application.yml`，配置数据库连接信息：
```yaml
spring:
  datasource:
    url: **********************************************************************************************************
    username: your_username
    password: your_password
```

### 3. 配置Redis
编辑 `src/main/resources/application.yml`，配置Redis连接信息：
```yaml
spring:
  redis:
    host: localhost
    port: 6379
    password: your_password
```

### 4. 运行项目
```bash
mvn spring-boot:run
```

## 部署说明

### 开发环境部署
1. 确保已安装所需环境（JDK 21、Maven 3.8+、MySQL 8.0+、Redis 6.0+）
2. 克隆项目并进入项目目录
3. 执行 `mvn clean install` 安装依赖
4. 配置数据库和Redis连接信息
5. 运行 `mvn spring-boot:run` 启动项目

### 生产环境部署
1. 打包项目
```bash
mvn clean package -Dmaven.test.skip=true
```

2. 运行jar包
```bash
java -jar target/obe-server.jar --spring.profiles.active=prod
```

### 注意事项
- 生产环境部署前请确保修改默认密码
- 建议使用nginx进行反向代理
- 建议配置SSL证书确保安全性
- 定期备份数据库
