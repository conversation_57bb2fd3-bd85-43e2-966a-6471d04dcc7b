# TeacherService 批量查询方法实现文档

## 概述

在 TeacherService 中实现了一个高性能的批量查询教师信息的方法 `getTeacherMapByIds`，用于替换原有的基于 BaseUser 的查询方式，提供更完整的教师信息和更好的性能。

## 实现内容

### 1. TeacherMapper 接口扩展

#### 新增方法
```java
/**
 * 批量查询教师详细信息
 * 使用 @MapKey 注解直接返回 Map 结构，以教师ID为key，TeacherVO为value
 * 
 * @param teacherIds 教师ID列表
 * @return 教师ID到教师详细信息的映射
 */
@MapKey("id")
Map<Long, TeacherVO> selectTeacherMapByIds(@Param("teacherIds") List<Long> teacherIds);
```

#### 关键特性
- **@MapKey("id")**: 使用教师ID作为Map的key，自动构建Map结构
- **批量查询**: 使用IN查询避免N+1问题
- **完整信息**: 包含用户信息、学院信息等完整的教师数据

### 2. TeacherMapper.xml 实现

```xml
<!-- 批量查询教师详细信息 -->
<select id="selectTeacherMapByIds" resultMap="TeacherVOResultMap">
    SELECT 
        <include refid="BaseSelectColumns"/>,
        a.academy_name,
        creator_user.real_name as creator_name,
        modifier_user.real_name as modifier_name
    FROM base_teacher t
    LEFT JOIN sys_base_user u ON t.base_user_id = u.id
    LEFT JOIN base_academy a ON t.academy_id = a.id
    LEFT JOIN sys_base_user creator_user ON t.creator = creator_user.id
    LEFT JOIN sys_base_user modifier_user ON t.modifier = modifier_user.id
    WHERE t.status = 0
    <if test="teacherIds != null and teacherIds.size() > 0">
        AND t.teacher_id IN
        <foreach collection="teacherIds" item="teacherId" open="(" separator="," close=")">
            #{teacherId}
        </foreach>
    </if>
    ORDER BY u.real_name
</select>
```

#### 查询特点
- **关联查询**: 一次性获取用户信息、学院信息
- **条件过滤**: 只查询状态正常的教师记录
- **动态SQL**: 支持空列表的情况
- **排序优化**: 按教师姓名排序

### 3. TeacherService 接口扩展

```java
/**
 * 批量查询教师详细信息（优化版本）
 * 使用单次数据库查询获取所有教师的完整信息，包括用户信息和学院信息
 * 
 * @param teacherIds 教师ID列表
 * @return 教师ID到教师详细信息的映射，key为教师ID，value为TeacherVO
 */
Map<Long, TeacherVO> getTeacherMapByIds(List<Long> teacherIds);
```

### 4. TeacherServiceImpl 实现

```java
@Override
public Map<Long, TeacherVO> getTeacherMapByIds(List<Long> teacherIds) {
    log.debug("开始批量查询教师详细信息，教师ID列表: {}", teacherIds);
    
    // 参数验证
    if (teacherIds == null || teacherIds.isEmpty()) {
        log.debug("教师ID列表为空，返回空Map");
        return new HashMap<>();
    }

    // 去重处理
    List<Long> distinctIds = teacherIds.stream()
            .filter(Objects::nonNull)
            .distinct()
            .collect(Collectors.toList());

    if (distinctIds.isEmpty()) {
        log.debug("去重后教师ID列表为空，返回空Map");
        return new HashMap<>();
    }

    log.debug("去重后的教师ID列表: {}，数量: {}", distinctIds, distinctIds.size());

    try {
        // 使用 Mapper 的批量查询方法，直接返回 Map 结构
        Map<Long, TeacherVO> teacherMap = teacherMapper.selectTeacherMapByIds(distinctIds);
        
        log.info("成功批量查询教师信息，请求数量: {}，返回数量: {}", distinctIds.size(), teacherMap.size());
        
        // 记录未找到的教师ID（用于调试）
        if (teacherMap.size() < distinctIds.size()) {
            List<Long> notFoundIds = distinctIds.stream()
                    .filter(id -> !teacherMap.containsKey(id))
                    .collect(Collectors.toList());
            log.warn("以下教师ID未找到对应记录: {}", notFoundIds);
        }
        
        return teacherMap;
        
    } catch (Exception e) {
        log.error("批量查询教师信息失败，教师ID列表: {}", distinctIds, e);
        throw new RuntimeException("批量查询教师信息失败", e);
    }
}
```

#### 实现特点
- **参数验证**: 处理null和空列表情况
- **去重优化**: 避免重复查询相同的教师ID
- **异常处理**: 完善的错误处理和日志记录
- **性能监控**: 记录查询结果统计信息

## 使用示例

### 基本用法
```java
// 准备教师ID列表
List<Long> teacherIds = Arrays.asList(1L, 2L, 3L, 4L, 5L);

// 批量查询教师信息
Map<Long, TeacherVO> teacherMap = teacherService.getTeacherMapByIds(teacherIds);

// 使用查询结果
for (Map.Entry<Long, TeacherVO> entry : teacherMap.entrySet()) {
    Long teacherId = entry.getKey();
    TeacherVO teacher = entry.getValue();
    
    System.out.println("教师ID: " + teacherId);
    System.out.println("教师姓名: " + teacher.getName());
    System.out.println("学院: " + teacher.getAcademyName());
    System.out.println("职称: " + teacher.getTitle());
}
```

### 在 TaskWorkService 中的应用
```java
// 原代码（使用BaseUser）
Set<Long> allTeacherIds = allTaskTeacherMap.values().stream()
    .flatMap(List::stream)
    .collect(Collectors.toSet());
Map<Long, BaseUser> teacherMap = CollectionUtils.isEmpty(allTeacherIds) ?
    Map.of() : baseUserService.getMap(allTeacherIds);

// 优化后代码（使用TeacherVO）
Set<Long> allTeacherIds = allTaskTeacherMap.values().stream()
    .flatMap(List::stream)
    .collect(Collectors.toSet());
Map<Long, TeacherVO> teacherMap = CollectionUtils.isEmpty(allTeacherIds) ?
    Map.of() : teacherService.getTeacherMapByIds(new ArrayList<>(allTeacherIds));
```

## 性能优势

### 1. 数据库查询优化
- **单次查询**: 使用IN查询一次性获取所有教师信息
- **避免N+1**: 不会为每个教师ID单独查询
- **关联优化**: 一次性获取用户信息和学院信息

### 2. 内存使用优化
- **直接映射**: 使用@MapKey直接构建Map结构
- **去重处理**: 避免重复查询相同的教师ID
- **按需查询**: 只查询需要的教师信息

### 3. 类型安全
- **强类型**: 返回TeacherVO而不是BaseUser
- **完整信息**: 包含教师的所有相关信息
- **避免转换**: 减少类型转换的开销

## 对比分析

| 特性 | 原方法 (BaseUser) | 新方法 (TeacherVO) |
|------|------------------|-------------------|
| 返回类型 | Map<Long, BaseUser> | Map<Long, TeacherVO> |
| 信息完整性 | 只有基础用户信息 | 包含教师、用户、学院信息 |
| 查询次数 | 1次 | 1次 |
| 类型安全 | 需要类型转换 | 强类型，无需转换 |
| 学院信息 | 无 | 包含完整学院信息 |
| 教师职称 | 无 | 包含职称信息 |
| 工号信息 | 无 | 包含教师工号 |

## 注意事项

1. **参数验证**: 方法会自动处理null和空列表的情况
2. **去重处理**: 自动去除重复的教师ID
3. **异常处理**: 查询失败时会抛出RuntimeException
4. **日志记录**: 提供详细的调试和错误日志
5. **性能监控**: 记录查询统计信息便于性能分析

## 后续优化建议

1. **缓存集成**: 可以考虑添加Redis缓存提升性能
2. **分页支持**: 对于大量教师ID的查询可以考虑分页处理
3. **异步处理**: 对于非关键路径可以考虑异步查询
4. **监控指标**: 添加查询性能监控指标
