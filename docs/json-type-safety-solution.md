# JSON 类型安全解决方案

## 问题根源分析

### 🔍 **为什么返回 LinkedHashMap 而不是 CourseObjectiveVO**

```java
// 问题代码
public List<T> parseJson(String json) {
    return objectMapper.readValue(json, new TypeReference<List<T>>() {});  // ❌ 泛型擦除
}
```

**根本原因**：
1. **Java 泛型擦除**：运行时 `T` 的类型信息丢失，变成 `Object`
2. **TypeReference 失效**：`new TypeReference<List<T>>()` 中的 `T` 在运行时无法确定具体类型
3. **Jackson 默认行为**：无法确定目标类型时，JSON 对象默认反序列化为 `LinkedHashMap`

## 解决方案

### 🛠 **方案1：传递 Class 参数（推荐）**

#### 修改 JSONUtil 类

```java
/**
 * 解析JSON数据为指定类型的列表（类型安全版本）
 */
public <U> List<U> parseJson(String json, Class<U> clazz) {
    try {
        if (json == null || json.trim().isEmpty()) {
            return new ArrayList<>();
        }
        
        JavaType listType = objectMapper.getTypeFactory().constructCollectionType(List.class, clazz);
        return objectMapper.readValue(json, listType);
    } catch (Exception e) {
        log.error("解析JSON失败，json: {}, targetClass: {}, error: {}", json, clazz.getSimpleName(), e.getMessage());
        return new ArrayList<>();
    }
}
```

#### 修改调用方式

```java
// 修改前（有问题）
List<CourseObjectiveVO> objectives = objectiveJSONUtil.parseJson(json);

// 修改后（类型安全）
List<CourseObjectiveVO> objectives = objectiveJSONUtil.parseJson(json, CourseObjectiveVO.class);
```

### 🛠 **方案2：专门的工具类**

创建 `CourseObjectiveJsonUtil` 类：

```java
@Component
public class CourseObjectiveJsonUtil {
    private final ObjectMapper objectMapper;

    public List<CourseObjectiveVO> parseJson(String json) {
        try {
            // 使用明确的类型引用
            TypeReference<List<CourseObjectiveVO>> typeReference = new TypeReference<List<CourseObjectiveVO>>() {};
            return objectMapper.readValue(json, typeReference);
        } catch (Exception e) {
            log.error("解析课程目标JSON失败", e);
            return new ArrayList<>();
        }
    }

    public List<CourseObjectiveVO> parseJsonSafe(String json) {
        try {
            // 使用 JavaType 构造器，更安全
            JavaType listType = objectMapper.getTypeFactory()
                .constructCollectionType(List.class, CourseObjectiveVO.class);
            return objectMapper.readValue(json, listType);
        } catch (Exception e) {
            log.error("安全解析课程目标JSON失败", e);
            return new ArrayList<>();
        }
    }
}
```

## 技术原理

### 📚 **泛型擦除机制**

```java
// 编译时
List<CourseObjectiveVO> list = new ArrayList<CourseObjectiveVO>();

// 运行时（泛型擦除后）
List list = new ArrayList();
```

### 📚 **TypeReference 工作原理**

```java
// ✅ 正确用法 - 具体类型
TypeReference<List<CourseObjectiveVO>> typeRef = new TypeReference<List<CourseObjectiveVO>>() {};

// ❌ 错误用法 - 泛型参数
TypeReference<List<T>> typeRef = new TypeReference<List<T>>() {};  // T 会被擦除
```

### 📚 **JavaType 构造器**

```java
// 使用 JavaType 明确指定类型
JavaType listType = objectMapper.getTypeFactory().constructCollectionType(List.class, CourseObjectiveVO.class);
List<CourseObjectiveVO> result = objectMapper.readValue(json, listType);
```

## 最佳实践

### 1. **类型安全的 JSON 处理**

```java
// ✅ 推荐：明确指定类型
public <T> List<T> parseJsonSafe(String json, Class<T> clazz) {
    JavaType listType = objectMapper.getTypeFactory().constructCollectionType(List.class, clazz);
    return objectMapper.readValue(json, listType);
}

// ❌ 避免：依赖泛型擦除
public List<T> parseJson(String json) {
    return objectMapper.readValue(json, new TypeReference<List<T>>() {});
}
```

### 2. **异常处理和日志**

```java
public List<CourseObjectiveVO> parseJson(String json) {
    try {
        if (json == null || json.trim().isEmpty()) {
            return new ArrayList<>();
        }
        
        TypeReference<List<CourseObjectiveVO>> typeRef = new TypeReference<List<CourseObjectiveVO>>() {};
        List<CourseObjectiveVO> result = objectMapper.readValue(json, typeRef);
        
        // 验证结果类型
        for (Object item : result) {
            if (!(item instanceof CourseObjectiveVO)) {
                log.warn("解析结果包含非期望类型: {}", item.getClass().getSimpleName());
            }
        }
        
        return result;
    } catch (Exception e) {
        log.error("解析JSON失败，json: {}", json, e);
        return new ArrayList<>();
    }
}
```

### 3. **配置 ObjectMapper**

```java
@Bean
public ObjectMapper objectMapper() {
    ObjectMapper mapper = new ObjectMapper();
    // 忽略未知属性
    mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    // 处理空值
    mapper.configure(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT, true);
    return mapper;
}
```

## 单元测试

```java
@Test
public void testJsonParsing() {
    String json = "[{\"number\":1,\"gindicatorId\":100,\"objectiveName\":\"目标1\"}]";
    
    // 测试类型安全解析
    List<CourseObjectiveVO> result = jsonUtil.parseJson(json, CourseObjectiveVO.class);
    
    assertNotNull(result);
    assertEquals(1, result.size());
    assertTrue(result.get(0) instanceof CourseObjectiveVO);
    assertEquals(Integer.valueOf(1), result.get(0).getNumber());
    assertEquals(Long.valueOf(100), result.get(0).getGindicatorId());
}

@Test
public void testInvalidJson() {
    String invalidJson = "invalid json";
    
    List<CourseObjectiveVO> result = jsonUtil.parseJson(invalidJson, CourseObjectiveVO.class);
    
    assertNotNull(result);
    assertTrue(result.isEmpty());
}
```

## 性能对比

| 方法 | 类型安全 | 性能 | 内存使用 | 推荐度 |
|------|----------|------|----------|--------|
| 泛型擦除 | ❌ | 高 | 低 | ❌ |
| Class 参数 | ✅ | 高 | 低 | ⭐⭐⭐ |
| TypeReference | ✅ | 中 | 中 | ⭐⭐ |
| JavaType | ✅ | 高 | 低 | ⭐⭐⭐ |

## 总结

通过以下方式可以彻底解决 JSON 反序列化的类型安全问题：

1. **✅ 使用 Class 参数**：`parseJson(json, CourseObjectiveVO.class)`
2. **✅ 使用具体的 TypeReference**：`new TypeReference<List<CourseObjectiveVO>>() {}`
3. **✅ 使用 JavaType 构造器**：`constructCollectionType(List.class, CourseObjectiveVO.class)`
4. **✅ 添加类型验证**：检查解析结果的实际类型
5. **✅ 完善异常处理**：提供详细的错误信息和日志

这样可以确保：
- 🎯 **类型安全**：返回正确的 `CourseObjectiveVO` 对象
- 🛡️ **健壮性**：完善的异常处理机制
- 📊 **可维护性**：清晰的日志和错误信息
- ⚡ **高性能**：避免运行时类型转换开销
