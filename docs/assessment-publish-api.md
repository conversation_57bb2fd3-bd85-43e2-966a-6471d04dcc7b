# 考核发布功能 API 文档

## 概述

考核发布功能允许将考核发布到匹配的教学任务中，支持全部发布和部分发布两种模式。发布时会自动将考核状态设置为"进行中"。

## API 接口

### 1. 查询可发布的教学任务列表

**接口地址：** `POST /teaching/assessment/publish/tasks`

**请求参数：**
```json
{
  "assessmentId": 1
}
```

**响应示例：**
```json
{
  "code": "200",
  "msg": "ok",
  "data": [
    {
      "taskId": 1,
      "taskName": "数据库原理(计2301,02班)",
      "courseId": 1,
      "taskNumber": 1,
      "taskYear": 2024,
      "taskTerm": 1,
      "teachWeek": 16,
      "weekHours": 4,
      "totalHours": 64,
      "classes": [
        {
          "classId": 1,
          "className": "RB软工数231",
          "studentCount": 73
        }
      ],
      "teachers": [
        {
          "teacherId": 1,
          "teacherName": "张三",
          "role": 1,
          "roleName": "主讲教师"
        }
      ],
      "isPublished": false
    }
  ]
}
```

### 2. 发布考核到教学任务

**接口地址：** `POST /teaching/assessment/publish`

**全部发布请求示例：**
```json
{
  "assessmentId": 1,
  "publishToAll": true,
  "publishNote": "期末考核发布给全部班级"
}
```

**部分发布请求示例：**
```json
{
  "assessmentId": 1,
  "publishToAll": false,
  "taskIds": [1, 2, 3],
  "publishNote": "发布给指定的教学任务"
}
```

**响应示例：**
```json
{
  "code": "200",
  "msg": "ok",
  "data": {
    "assessmentId": 1,
    "assessmentName": "数据库原理期末考试",
    "publishToAll": true,
    "publishedTaskCount": 5,
    "publishNote": "期末考核发布给全部班级",
    "publishStatus": 0,
    "publishMessage": "考核已成功发布给全部匹配的教学任务",
    "publishTime": "2024-01-15T10:30:00"
  }
}
```

### 3. 查询考核发布状态

**接口地址：** `GET /teaching/assessment/publish/status/{assessmentId}`

**响应示例：**
```json
{
  "code": "200",
  "msg": "ok",
  "data": {
    "assessmentId": 1,
    "assessmentName": "数据库原理期末考试",
    "publishToAll": true,
    "publishedTaskCount": 5,
    "publishStatus": 0,
    "publishMessage": "已发布给全部匹配的教学任务"
  }
}
```

### 4. 撤销考核发布

**接口地址：** `DELETE /teaching/assessment/publish/{assessmentId}`

**撤销全部发布：**
```
DELETE /teaching/assessment/publish/1
```

**撤销指定教学任务的发布：**
```
DELETE /teaching/assessment/publish/1?taskIds=1,2,3
```

**响应示例：**
```json
{
  "code": "200",
  "msg": "ok",
  "data": true
}
```

## 业务逻辑说明

### 发布模式

1. **全部发布模式 (publishToAll=true)**
   - 将考核发布给所有匹配条件的教学任务
   - 通过设置 `assessment.task_id = -1` 实现
   - 不在 `assessment_task` 表中创建记录
   - 设置考核状态为进行中 (`assessment_status = 2`)

2. **部分发布模式 (publishToAll=false)**
   - 将考核发布给指定的教学任务
   - 在 `assessment_task` 表中为每个教学任务创建关联记录
   - 设置 `assessment.task_id` 为第一个教学任务的ID
   - 设置考核状态为进行中 (`assessment_status = 2`)

### 考核状态变化

- **发布前：** 考核状态为配置中(0)或编辑中(1)
- **发布后：** 考核状态自动设置为进行中(2)
- **撤销发布后：** 考核状态重置为编辑中(1)

### 匹配条件

教学任务匹配条件：
- 课程ID相同 (`course_id`)
- 学年相同 (`task_year = assessment_year`)
- 学期相同 (`task_term = assessment_term`)
- 教学任务状态正常 (`status = 0`)

### 发布状态

- `0`: 发布成功
- `1`: 部分成功
- `2`: 发布失败

### 考核状态说明

- `0`: 配置中
- `1`: 编辑中
- `2`: 进行中
- `3`: 已结束

## 数据库表结构

### assessment_task 表

```sql
CREATE TABLE `assessment_task` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `assessment_id` bigint NOT NULL COMMENT '考核ID',
  `task_id` bigint NOT NULL COMMENT '教学任务ID',
  `publish_note` varchar(500) DEFAULT NULL COMMENT '发布说明',
  `publish_status` int NOT NULL DEFAULT 0 COMMENT '发布状态',
  `status` int NOT NULL DEFAULT 0 COMMENT '记录状态',
  `creator` bigint NOT NULL DEFAULT 0 COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `modifier` bigint NOT NULL DEFAULT 0 COMMENT '修改者',
  `modify_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_assessment_task` (`assessment_id`,`task_id`)
);
```

## 错误码说明

- `400`: 请求参数错误
- `404`: 考核不存在
- `500`: 服务器内部错误

## 使用示例

### 前端调用示例

```javascript
// 查询可发布的教学任务
const getPublishableTasks = async (assessmentId) => {
  const response = await fetch('/teaching/assessment/publish/tasks', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ assessmentId })
  });
  return response.json();
};

// 发布考核
const publishAssessment = async (publishData) => {
  const response = await fetch('/teaching/assessment/publish', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(publishData)
  });
  return response.json();
};

// 撤销发布
const unpublishAssessment = async (assessmentId, taskIds = null) => {
  let url = `/teaching/assessment/publish/${assessmentId}`;
  if (taskIds && taskIds.length > 0) {
    url += `?taskIds=${taskIds.join(',')}`;
  }
  
  const response = await fetch(url, {
    method: 'DELETE'
  });
  return response.json();
};
```

## 注意事项

1. **考核状态管理：** 发布考核时会自动将考核状态设置为"进行中"，撤销发布时会重置为"编辑中"
2. **权限控制：** 只有具有相应权限的用户才能执行发布和撤销操作
3. **数据一致性：** 发布和撤销操作都在事务中执行，确保数据一致性
4. **重复发布：** 系统会检查是否已发布，避免重复创建关联记录
