# ClassCastException 问题分析与解决方案

## 问题描述

在调用批量保存学生课程目标成绩接口时，出现以下错误：

```
java.lang.ClassCastException: class java.util.LinkedHashMap cannot be cast to class com.hnumi.obe.tp.vo.CourseObjectiveVO
```

## 问题分析

### 🔍 **根本原因**

1. **类型不匹配**：`courseService.getCourseObjectives(courseId)` 方法声明返回 `List<CourseObjectiveVO>`，但实际运行时返回的是 `List<LinkedHashMap>`

2. **JSON序列化问题**：课程目标数据在数据库中以JSON格式存储，在反序列化时被转换为 `LinkedHashMap` 而不是具体的 `CourseObjectiveVO` 对象

3. **泛型擦除**：在运行时，由于Java泛型擦除机制，类型信息丢失，导致类型转换失败

### 📊 **问题定位**

错误发生在 `AssessmentScoreServiceImpl.getCourseTargetPoMapping()` 方法的第1048行：

```java
CourseObjectiveVO objective = courseObjectives.get(i);  // 这里发生 ClassCastException
```

实际上 `courseObjectives.get(i)` 返回的是 `LinkedHashMap` 对象，而不是 `CourseObjectiveVO` 对象。

## 解决方案

### 🛠 **方案1：类型安全转换（已实现）**

修改 `getCourseTargetPoMapping` 方法，添加类型检查和安全转换：

```java
private Map<Integer, Long> getCourseTargetPoMapping(Long courseId) {
    try {
        List<CourseObjectiveVO> courseObjectives = courseService.getCourseObjectives(courseId);
        if (CollectionUtils.isEmpty(courseObjectives)) {
            return new HashMap<>();
        }

        Map<Integer, Long> mapping = new HashMap<>();
        
        for (Object objectiveObj : courseObjectives) {
            try {
                // 处理可能的类型转换问题
                if (objectiveObj instanceof LinkedHashMap) {
                    LinkedHashMap<String, Object> map = (LinkedHashMap<String, Object>) objectiveObj;
                    Integer number = extractInteger(map.get("number"));
                    Long gindicatorId = extractLong(map.get("gindicatorId"));
                    
                    if (number != null && gindicatorId != null) {
                        mapping.put(number, gindicatorId);
                    }
                } else if (objectiveObj instanceof CourseObjectiveVO) {
                    // 正常的 CourseObjectiveVO 对象
                    CourseObjectiveVO courseObj = (CourseObjectiveVO) objectiveObj;
                    if (courseObj.getNumber() != null && courseObj.getGindicatorId() != null) {
                        mapping.put(courseObj.getNumber(), courseObj.getGindicatorId());
                    }
                }
            } catch (Exception e) {
                log.warn("处理课程目标对象失败，跳过该目标", e);
            }
        }

        return mapping;
    } catch (Exception e) {
        log.error("获取课程目标和毕业要求映射关系失败", e);
        return new HashMap<>();
    }
}
```

### 🔧 **辅助方法**

添加安全的类型转换方法：

```java
/**
 * 安全地从 Object 中提取 Integer 值
 */
private Integer extractInteger(Object value) {
    if (value == null) return null;
    if (value instanceof Integer) return (Integer) value;
    if (value instanceof Number) return ((Number) value).intValue();
    if (value instanceof String) {
        try {
            return Integer.parseInt((String) value);
        } catch (NumberFormatException e) {
            return null;
        }
    }
    return null;
}

/**
 * 安全地从 Object 中提取 Long 值
 */
private Long extractLong(Object value) {
    if (value == null) return null;
    if (value instanceof Long) return (Long) value;
    if (value instanceof Number) return ((Number) value).longValue();
    if (value instanceof String) {
        try {
            return Long.parseLong((String) value);
        } catch (NumberFormatException e) {
            return null;
        }
    }
    return null;
}
```

## 其他可能的解决方案

### 🔄 **方案2：修复JSON反序列化**

在 `CourseServiceImpl.getCourseObjectives()` 方法中，确保正确的JSON反序列化：

```java
// 在 objectiveJSONUtil.parseJson() 方法中
public List<CourseObjectiveVO> parseJson(String json) {
    try {
        ObjectMapper mapper = new ObjectMapper();
        return mapper.readValue(json, new TypeReference<List<CourseObjectiveVO>>() {});
    } catch (Exception e) {
        log.error("JSON解析失败", e);
        return new ArrayList<>();
    }
}
```

### 🏗 **方案3：使用MapStruct转换**

创建专门的转换器：

```java
@Mapper
public interface CourseObjectiveMapper {
    CourseObjectiveVO mapFromLinkedHashMap(LinkedHashMap<String, Object> map);
    
    default Integer mapInteger(Object value) {
        // 实现安全的Integer转换
    }
    
    default Long mapLong(Object value) {
        // 实现安全的Long转换
    }
}
```

## 预防措施

### 1. **类型安全检查**
在处理可能的类型转换时，始终进行 `instanceof` 检查

### 2. **异常处理**
对每个可能失败的转换操作添加try-catch块

### 3. **日志记录**
添加详细的日志记录，便于问题定位

### 4. **单元测试**
为类型转换方法编写单元测试：

```java
@Test
public void testExtractInteger() {
    // 测试各种输入类型
    assertEquals(Integer.valueOf(123), extractInteger(123));
    assertEquals(Integer.valueOf(456), extractInteger("456"));
    assertEquals(Integer.valueOf(789), extractInteger(789L));
    assertNull(extractInteger(null));
    assertNull(extractInteger("invalid"));
}
```

## 最佳实践

### 1. **避免直接类型转换**
```java
// ❌ 危险的做法
CourseObjectiveVO obj = (CourseObjectiveVO) list.get(i);

// ✅ 安全的做法
Object objRaw = list.get(i);
if (objRaw instanceof CourseObjectiveVO) {
    CourseObjectiveVO obj = (CourseObjectiveVO) objRaw;
    // 处理逻辑
}
```

### 2. **使用泛型通配符**
```java
// 当不确定具体类型时
List<?> unknownList = someMethod();
for (Object item : unknownList) {
    if (item instanceof ExpectedType) {
        ExpectedType typedItem = (ExpectedType) item;
        // 处理逻辑
    }
}
```

### 3. **JSON处理最佳实践**
```java
// 使用明确的类型引用
TypeReference<List<CourseObjectiveVO>> typeRef = new TypeReference<List<CourseObjectiveVO>>() {};
List<CourseObjectiveVO> result = objectMapper.readValue(json, typeRef);
```

## 总结

这个 `ClassCastException` 问题是由于JSON反序列化过程中类型信息丢失导致的。通过添加类型安全检查和转换逻辑，我们可以有效地解决这个问题，同时保持代码的健壮性和可维护性。

**关键要点**：
- ✅ 始终进行类型检查
- ✅ 提供安全的类型转换方法
- ✅ 添加完善的异常处理
- ✅ 记录详细的日志信息
- ✅ 编写相应的单元测试

这种解决方案不仅修复了当前问题，还为将来可能遇到的类似问题提供了防护机制。
