# TooManyResultsException 问题分析与解决方案

## 问题描述

在查询学生成绩详情时出现以下错误：

```
org.apache.ibatis.exceptions.TooManyResultsException: Expected one result (or null) to be returned by selectOne(), but found: 3
```

## 问题分析

### 🔍 **错误根源**

从日志分析可以看出：

1. **查询返回了3条记录**：
   ```sql
   SELECT * FROM assessment_score_target 
   WHERE assessment_id = 11 AND task_id = 1 AND student_id = 15 AND status = 0
   ```
   返回结果：
   ```
   Row: 1, 15, 11, 0, 33, 1, 212, 1, 32, 1, 0, 96, ...  // 课程目标1
   Row: 2, 15, 11, 0, 33, 2, 213, 1, 32, 1, 0, 96, ...  // 课程目标2  
   Row: 3, 15, 11, 0, 33, 3, 208, 1, 32, 1, 0, 96, ...  // 课程目标3
   ```

2. **`getMainScore()` 方法使用了 `selectOne()`**：期望返回单条记录，但实际返回了多条

3. **数据结构理解错误**：`assessment_score_target` 表存储的是按课程目标分解的成绩，一个学生在一个考核中可以有多条记录

### 📊 **数据结构分析**

#### `assessment_score_target` 表的设计目的：
- **存储按课程目标分解的成绩**：每个学生每个课程目标一条记录
- **支持课程目标达成度计算**：需要知道每个课程目标的具体得分
- **不是存储总成绩的表**：没有"主成绩"的概念

#### 数据示例：
| student_id | assessment_id | course_target_no | score | po_id |
|------------|---------------|------------------|-------|-------|
| 15         | 11            | 1                | 33    | 212   |
| 15         | 11            | 2                | 33    | 213   |
| 15         | 11            | 3                | 33    | 208   |

这表示学生15在考核11中：
- 课程目标1得了33分
- 课程目标2得了33分  
- 课程目标3得了33分
- **总分 = 33 + 33 + 33 = 99分**

## 解决方案

### 🛠 **方案1：修复业务逻辑（已实现）**

#### 1. 移除错误的 `getMainScore()` 调用

```java
// ❌ 错误的做法
AssessmentScoreTarget mainScore = getMainScore(assessmentId, taskId, studentId);

// ✅ 正确的做法
// 直接使用课程目标成绩，通过聚合计算总分
```

#### 2. 修改总分计算逻辑

```java
// 5. 计算总成绩和状态（基于课程目标成绩）
if (!CollectionUtils.isEmpty(targetScores)) {
    // 计算总分（所有课程目标成绩之和）
    BigDecimal totalScore = targetScores.stream()
            .map(AssessmentScoreTarget::getScore)
            .filter(Objects::nonNull)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
    
    StudentScoreDetailConvert.INSTANCE.updateTotalScoreInfo(
            totalScore, BigDecimal.valueOf(0), studentVO);
    
    // 使用第一个成绩记录的时间和录入者信息
    AssessmentScoreTarget firstScore = targetScores.get(0);
    studentVO.setEntryTime(firstScore.getCreateTime());
    studentVO.setLastModifyTime(firstScore.getModifyTime());
    studentVO.setEntryUserId(firstScore.getCreator());
    studentVO.setEntryUserName(getUserName(firstScore.getCreator()));
}
```

#### 3. 废弃 `getMainScore()` 方法

```java
@Deprecated
private AssessmentScoreTarget getMainScore(Long assessmentId, Long taskId, Long studentId) {
    log.warn("getMainScore方法已废弃，assessment_score_target表存储的是分解成绩，不是主成绩");
    return null;
}
```

### 🛠 **方案2：如果确实需要主成绩表**

如果业务确实需要存储学生的总成绩，可以考虑：

#### 创建专门的总成绩表

```sql
CREATE TABLE assessment_score_summary (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    student_id BIGINT NOT NULL,
    assessment_id BIGINT NOT NULL,
    task_id BIGINT NOT NULL,
    total_score DECIMAL(10,2),
    max_score DECIMAL(10,2),
    score_status INT DEFAULT 0,
    entry_time DATETIME,
    entry_user_id BIGINT,
    -- 其他字段...
    UNIQUE KEY uk_student_assessment_task (student_id, assessment_id, task_id)
);
```

#### 在保存课程目标成绩时同步更新总成绩

```java
@Transactional
public void saveTargetScores(List<AssessmentScoreTarget> targetScores) {
    // 1. 保存课程目标成绩
    targetScores.forEach(this::save);
    
    // 2. 计算并更新总成绩
    updateScoreSummary(targetScores.get(0).getStudentId(), 
                      targetScores.get(0).getAssessmentId(), 
                      targetScores.get(0).getTaskId());
}
```

## 最佳实践

### 1. **数据表设计原则**

```java
// ✅ 明确表的职责
assessment_score_target    // 存储按课程目标分解的成绩
assessment_score_summary   // 存储学生总成绩（如果需要）
assessment_score_detail    // 存储按题目分解的成绩
```

### 2. **查询方法设计**

```java
// ✅ 根据数据特点选择合适的查询方法
public List<AssessmentScoreTarget> getTargetScores(Long assessmentId, Long taskId, Long studentId) {
    // 一个学生可能有多个课程目标成绩，使用 selectList
    return assessmentScoreTargetMapper.selectByAssessmentAndStudent(assessmentId, taskId, studentId);
}

public AssessmentScoreSummary getScoreSummary(Long assessmentId, Long taskId, Long studentId) {
    // 一个学生只有一个总成绩，使用 selectOne
    return assessmentScoreSummaryMapper.selectOne(wrapper);
}
```

### 3. **异常处理**

```java
public AssessmentScoreTarget getSingleScore(Long assessmentId, Long taskId, Long studentId) {
    try {
        return this.getOne(wrapper);
    } catch (TooManyResultsException e) {
        log.error("查询返回多条记录，请检查数据和查询条件: assessmentId={}, taskId={}, studentId={}", 
                 assessmentId, taskId, studentId, e);
        // 根据业务需求决定返回第一条记录还是抛出异常
        List<AssessmentScoreTarget> list = this.list(wrapper);
        return list.isEmpty() ? null : list.get(0);
    }
}
```

## 验证方案

### 1. **数据验证**

```sql
-- 验证学生成绩数据结构
SELECT 
    student_id,
    assessment_id,
    task_id,
    course_target_no,
    score,
    COUNT(*) as record_count
FROM assessment_score_target 
WHERE assessment_id = 11 AND task_id = 1 AND student_id = 15 AND status = 0
GROUP BY student_id, assessment_id, task_id, course_target_no;

-- 验证总分计算
SELECT 
    student_id,
    assessment_id,
    task_id,
    SUM(score) as total_score,
    COUNT(*) as target_count
FROM assessment_score_target 
WHERE assessment_id = 11 AND task_id = 1 AND student_id = 15 AND status = 0
GROUP BY student_id, assessment_id, task_id;
```

### 2. **单元测试**

```java
@Test
public void testGetStudentScoreDetails() {
    Long assessmentId = 11L;
    Long taskId = 1L;
    Long studentId = 15L;
    
    // 测试不会抛出 TooManyResultsException
    StudentScoreDetailVO result = assessmentScoreService.getStudentScoreDetail(
        assessmentId, taskId, studentId);
    
    assertNotNull(result);
    assertEquals(studentId, result.getStudentId());
    
    // 验证总分计算正确
    BigDecimal expectedTotal = new BigDecimal("99"); // 33 + 33 + 33
    assertEquals(expectedTotal, result.getTotalScore());
    
    // 验证课程目标成绩
    assertEquals(3, result.getCourseTargetScores().size());
}
```

## 总结

这个 `TooManyResultsException` 问题的根本原因是：

1. **数据结构理解错误**：误认为 `assessment_score_target` 表存储主成绩
2. **查询方法选择错误**：对多条记录使用了 `selectOne()`
3. **业务逻辑设计问题**：没有正确处理按课程目标分解的成绩结构

**解决方案**：
- ✅ 移除错误的 `getMainScore()` 调用
- ✅ 通过聚合课程目标成绩计算总分
- ✅ 修正业务逻辑以适应实际的数据结构
- ✅ 添加适当的日志和异常处理

现在系统能够正确处理按课程目标分解的成绩数据，不再出现 `TooManyResultsException` 错误。
