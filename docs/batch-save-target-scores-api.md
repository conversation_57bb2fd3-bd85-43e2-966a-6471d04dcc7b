# 批量保存学生课程目标成绩接口文档

## 接口概述

**接口名称**：批量保存学生课程目标成绩  
**接口路径**：`POST /api/assessment-score/batch-save-target-scores`  
**功能描述**：批量保存一组学生在特定考核中按课程目标分解的详细成绩数据到 `assessment_score_target` 表

## 请求参数

### 请求头
```
Content-Type: application/json
Authorization: Bearer {token}  // 根据实际认证方式
```

### 请求体
```json
{
    "assessmentId": 1001,
    "taskId": 2001,
    "studentScores": [
        {
            "studentId": 3001,
            "courseTargetNo": 1,
            "repositoryAnswerId": 4001,
            "score": 85.5,
            "remark": "表现良好"
        },
        {
            "studentId": 3001,
            "courseTargetNo": 2,
            "repositoryAnswerId": 4002,
            "score": 90.0
        },
        {
            "studentId": 3002,
            "courseTargetNo": 1,
            "repositoryAnswerId": 4001,
            "score": 78.0
        }
    ]
}
```

### 参数说明

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| assessmentId | Long | 是 | 考核ID |
| taskId | Long | 是 | 教学任务ID |
| studentScores | Array | 是 | 学生成绩列表 |

#### studentScores 数组元素说明

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| studentId | Long | 是 | 学生ID |
| courseTargetNo | Integer | 是 | 课程目标编号（1,2,3...） |
| repositoryAnswerId | Long | 是 | 题目答案ID |
| score | BigDecimal | 是 | 学生得分 |
| remark | String | 否 | 备注信息 |

## 响应结果

### 成功响应
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "success": true,
        "totalCount": 3,
        "successCount": 3,
        "insertCount": 2,
        "updateCount": 1,
        "failureCount": 0,
        "failureDetails": [],
        "message": "批量保存成功"
    }
}
```

### 部分成功响应
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "success": true,
        "totalCount": 3,
        "successCount": 2,
        "insertCount": 1,
        "updateCount": 1,
        "failureCount": 1,
        "failureDetails": [
            {
                "studentId": 3003,
                "courseTargetNo": 1,
                "repositoryAnswerId": 4001,
                "reason": "课程目标编号不存在或未关联毕业要求指标",
                "errorDetail": null
            }
        ],
        "message": "批量保存完成，成功2条，失败1条"
    }
}
```

### 失败响应
```json
{
    "code": 400,
    "message": "无权限操作该教学任务",
    "data": {
        "success": false,
        "totalCount": 0,
        "successCount": 0,
        "insertCount": 0,
        "updateCount": 0,
        "failureCount": 0,
        "failureDetails": [],
        "message": "无权限操作该教学任务"
    }
}
```

### 响应字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| success | Boolean | 操作是否成功 |
| totalCount | Integer | 总记录数 |
| successCount | Integer | 成功保存的记录数 |
| insertCount | Integer | 新增的记录数 |
| updateCount | Integer | 更新的记录数 |
| failureCount | Integer | 失败的记录数 |
| failureDetails | Array | 失败记录详情 |
| message | String | 操作消息 |

## 业务逻辑

### 1. 权限验证
- 验证当前教师是否有权限操作指定的教学任务
- 验证考核是否已发布给该教学任务

### 2. 数据补全
系统会自动补全以下字段：
- `courseId`：从 `task_worklist` 表根据 `taskId` 获取
- `majorId`：从 `task_worklist` 表根据 `taskId` 获取
- `poId`：从课程目标配置中获取对应的毕业要求ID

### 3. 重复处理
- 如果存在相同的 `(student_id, assessment_id, repository_answer_id)` 记录，则更新成绩
- 否则新增记录

### 4. 事务处理
- 整个批量操作在一个事务中执行
- 如果部分记录失败，成功的记录仍会保存，失败的记录会在响应中详细说明

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 400 | 参数错误或业务规则验证失败 |
| 401 | 未授权访问 |
| 403 | 权限不足 |
| 500 | 服务器内部错误 |

## 常见错误情况

### 1. 参数验证错误
```json
{
    "code": 400,
    "message": "考核ID不能为空"
}
```

### 2. 权限错误
```json
{
    "code": 400,
    "message": "无权限操作该教学任务"
}
```

### 3. 业务规则错误
```json
{
    "code": 400,
    "message": "考核未发布给该教学任务"
}
```

## 使用示例

### JavaScript/Ajax 调用示例
```javascript
$.ajax({
    url: '/api/assessment-score/batch-save-target-scores',
    type: 'POST',
    contentType: 'application/json',
    data: JSON.stringify({
        assessmentId: 1001,
        taskId: 2001,
        studentScores: [
            {
                studentId: 3001,
                courseTargetNo: 1,
                repositoryAnswerId: 4001,
                score: 85.5
            }
        ]
    }),
    success: function(response) {
        if (response.code === 200 && response.data.success) {
            console.log('保存成功:', response.data);
        } else {
            console.error('保存失败:', response.message);
        }
    },
    error: function(xhr, status, error) {
        console.error('请求失败:', error);
    }
});
```

### Java 客户端调用示例
```java
// 构建请求数据
BatchSaveTargetScoresDTO request = new BatchSaveTargetScoresDTO();
request.setAssessmentId(1001L);
request.setTaskId(2001L);

List<BatchSaveTargetScoresDTO.StudentTargetScoreDTO> studentScores = new ArrayList<>();
BatchSaveTargetScoresDTO.StudentTargetScoreDTO score = new BatchSaveTargetScoresDTO.StudentTargetScoreDTO();
score.setStudentId(3001L);
score.setCourseTargetNo(1);
score.setRepositoryAnswerId(4001L);
score.setScore(new BigDecimal("85.5"));
studentScores.add(score);

request.setStudentScores(studentScores);

// 发送请求
R<BatchSaveTargetScoresResultVO> response = assessmentScoreController.batchSaveTargetScores(request);
```

## 注意事项

1. **数据量限制**：建议单次批量操作不超过1000条记录，避免事务过长
2. **并发控制**：同一学生的同一题目答案成绩可能存在并发更新，系统会以最后更新为准
3. **数据一致性**：确保 `repositoryAnswerId` 对应的题目属于指定的考核
4. **课程目标验证**：`courseTargetNo` 必须是该课程已配置的课程目标编号
5. **成绩范围**：建议在前端验证成绩范围的合理性（如0-100分）
