# AssessmentScoreService 优化后使用示例

## 基本使用方法

优化后的 `getAssessmentTaskDetails` 方法使用方式保持不变，但性能得到了显著提升。

### 1. 控制器层调用示例

```java
@RestController
@RequestMapping("/api/assessment")
public class AssessmentController {

    @Autowired
    private IAssessmentScoreService assessmentScoreService;

    /**
     * 获取考核关联的教学任务详情
     */
    @GetMapping("/{assessmentId}/task-details")
    public Result<List<AssessmentTaskDetailVO>> getAssessmentTaskDetails(
            @PathVariable Long assessmentId) {
        
        List<AssessmentTaskDetailVO> taskDetails = 
            assessmentScoreService.getAssessmentTaskDetails(assessmentId);
        
        return Result.success(taskDetails);
    }
}
```

### 2. 服务层调用示例

```java
@Service
public class AssessmentReportService {

    @Autowired
    private IAssessmentScoreService assessmentScoreService;

    /**
     * 生成考核报告
     */
    public AssessmentReportVO generateAssessmentReport(Long assessmentId) {
        // 获取教学任务详情（优化后的方法）
        List<AssessmentTaskDetailVO> taskDetails = 
            assessmentScoreService.getAssessmentTaskDetails(assessmentId);
        
        AssessmentReportVO report = new AssessmentReportVO();
        report.setAssessmentId(assessmentId);
        report.setTaskDetails(taskDetails);
        
        // 计算汇总统计
        int totalStudents = taskDetails.stream()
            .mapToInt(task -> task.getScoreStatistics().getTotalStudents())
            .sum();
        
        int totalScoredStudents = taskDetails.stream()
            .mapToInt(task -> task.getScoreStatistics().getTotalScoredStudents())
            .sum();
        
        report.setTotalStudents(totalStudents);
        report.setTotalScoredStudents(totalScoredStudents);
        
        return report;
    }
}
```

## 返回数据结构说明

### AssessmentTaskDetailVO 结构

```java
{
    "taskId": 1001,
    "taskName": "数据库原理(计2301,02班)",
    "taskNumber": 1,
    "courseId": 2001,
    "courseName": "数据库原理",
    "taskYear": "2024",
    "taskTerm": 1,
    "classes": [
        {
            "classId": 3001,
            "className": "计2301班",
            "studentCount": 30,
            "scoredCount": 25,
            "unscoredCount": 5,
            "scoreProgress": 83.33
        },
        {
            "classId": 3002,
            "className": "计2302班",
            "studentCount": 28,
            "scoredCount": 28,
            "unscoredCount": 0,
            "scoreProgress": 100.00
        }
    ],
    "scoreStatistics": {
        "totalStudents": 58,
        "totalScoredStudents": 53,
        "totalUnscoredStudents": 5,
        "averageScore": 85.6,
        "maxScore": 98.0,
        "minScore": 72.0,
        "passRate": 94.34,
        "scoreProgress": 91.38
    },
    "submissionStatus": "IN_PROGRESS",
    "publishTime": "2024-03-15T10:30:00"
}
```

## 性能监控示例

### 1. 添加性能监控

```java
@Component
@Slf4j
public class PerformanceMonitor {

    @EventListener
    @Async
    public void handleMethodExecution(MethodExecutionEvent event) {
        if ("getAssessmentTaskDetails".equals(event.getMethodName())) {
            long executionTime = event.getExecutionTime();
            
            if (executionTime > 500) {
                log.warn("getAssessmentTaskDetails 执行时间过长: {}ms, 参数: {}", 
                    executionTime, event.getParameters());
            } else {
                log.info("getAssessmentTaskDetails 执行完成: {}ms", executionTime);
            }
        }
    }
}
```

### 2. 使用 AOP 进行性能监控

```java
@Aspect
@Component
@Slf4j
public class PerformanceAspect {

    @Around("execution(* com.hnumi.obe.assessment.service.impl.AssessmentScoreServiceImpl.getAssessmentTaskDetails(..))")
    public Object monitorPerformance(ProceedingJoinPoint joinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();
        
        try {
            Object result = joinPoint.proceed();
            long executionTime = System.currentTimeMillis() - startTime;
            
            log.info("getAssessmentTaskDetails 执行时间: {}ms", executionTime);
            
            return result;
        } catch (Exception e) {
            long executionTime = System.currentTimeMillis() - startTime;
            log.error("getAssessmentTaskDetails 执行失败, 耗时: {}ms", executionTime, e);
            throw e;
        }
    }
}
```

## 错误处理示例

### 1. 业务异常处理

```java
@Service
public class AssessmentTaskService {

    @Autowired
    private IAssessmentScoreService assessmentScoreService;

    public List<AssessmentTaskDetailVO> getTaskDetailsWithErrorHandling(Long assessmentId) {
        try {
            if (assessmentId == null) {
                throw new IllegalArgumentException("考核ID不能为空");
            }
            
            List<AssessmentTaskDetailVO> result = 
                assessmentScoreService.getAssessmentTaskDetails(assessmentId);
            
            if (CollectionUtils.isEmpty(result)) {
                log.warn("考核ID {} 没有关联的教学任务", assessmentId);
                return new ArrayList<>();
            }
            
            return result;
            
        } catch (IllegalArgumentException e) {
            log.error("参数错误: {}", e.getMessage());
            throw new BusinessException("参数错误", e);
        } catch (Exception e) {
            log.error("查询教学任务详情失败, assessmentId: {}", assessmentId, e);
            throw new BusinessException("查询失败，请稍后重试", e);
        }
    }
}
```

## 缓存集成示例

### 1. 使用 Spring Cache

```java
@Service
public class CachedAssessmentScoreService {

    @Autowired
    private IAssessmentScoreService assessmentScoreService;

    @Cacheable(value = "assessmentTaskDetails", key = "#assessmentId")
    public List<AssessmentTaskDetailVO> getCachedAssessmentTaskDetails(Long assessmentId) {
        return assessmentScoreService.getAssessmentTaskDetails(assessmentId);
    }

    @CacheEvict(value = "assessmentTaskDetails", key = "#assessmentId")
    public void evictCache(Long assessmentId) {
        // 当成绩数据更新时，清除缓存
    }
}
```

### 2. 使用 Redis 缓存

```java
@Service
public class RedisAssessmentScoreService {

    @Autowired
    private IAssessmentScoreService assessmentScoreService;
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    public List<AssessmentTaskDetailVO> getAssessmentTaskDetailsWithRedis(Long assessmentId) {
        String cacheKey = "assessment:task:details:" + assessmentId;
        
        // 尝试从缓存获取
        List<AssessmentTaskDetailVO> cached = 
            (List<AssessmentTaskDetailVO>) redisTemplate.opsForValue().get(cacheKey);
        
        if (cached != null) {
            return cached;
        }
        
        // 从数据库查询
        List<AssessmentTaskDetailVO> result = 
            assessmentScoreService.getAssessmentTaskDetails(assessmentId);
        
        // 缓存结果（5分钟过期）
        redisTemplate.opsForValue().set(cacheKey, result, 5, TimeUnit.MINUTES);
        
        return result;
    }
}
```

## 最佳实践建议

### 1. 合理使用缓存
- 对于频繁查询且数据变化不频繁的场景，建议使用缓存
- 缓存过期时间建议设置为 5-10 分钟
- 在成绩数据更新时及时清除相关缓存

### 2. 监控和告警
- 监控方法执行时间，设置合理的告警阈值
- 监控数据库连接池使用情况
- 关注内存使用情况，特别是在大数据量场景下

### 3. 错误处理
- 对输入参数进行严格校验
- 提供友好的错误信息
- 记录详细的错误日志便于问题排查

### 4. 性能测试
- 在不同数据量下进行性能测试
- 进行并发测试确保系统稳定性
- 定期进行性能回归测试
