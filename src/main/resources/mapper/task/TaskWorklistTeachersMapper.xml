<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hnumi.obe.task.mapper.TaskWorklistTeachersMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hnumi.obe.task.entity.TaskWorklistTeachers">
        <id column="id" property="id" />
        <result column="task_id" property="taskId" />
        <result column="teacher_id" property="teacherId" />
        <result column="role" property="role" />
        <result column="creator" property="creator" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, task_id, teacher_id, role, creator, create_time
    </sql>

</mapper>
