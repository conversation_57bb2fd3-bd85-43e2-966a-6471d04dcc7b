<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hnumi.obe.task.mapper.TaskWorklistClassesMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hnumi.obe.task.entity.TaskWorklistClasses">
        <id column="id" property="id" />
        <result column="task_id" property="taskId" />
        <result column="class_id" property="classId" />
        <result column="creator" property="creator" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, task_id, class_id, creator, create_time
    </sql>

</mapper>
