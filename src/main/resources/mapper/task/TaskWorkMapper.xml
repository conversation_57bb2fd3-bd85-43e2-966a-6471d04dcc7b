<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hnumi.obe.task.mapper.TaskWorkMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hnumi.obe.task.entity.TaskWork">
        <id column="id" property="id" />
        <result column="course_id" property="courseId" />
        <result column="task_name" property="taskName" />
        <result column="task_number" property="taskNumber" />
        <result column="task_year" property="taskYear" />
        <result column="task_term" property="taskTerm" />
        <result column="teach_week" property="teachWeek" />
        <result column="week_hours" property="weekHours" />
        <result column="total_hours" property="totalHours" />
        <result column="course_leader_id" property="courseLeaderId" />
        <result column="status" property="status" />
        <result column="creator" property="creator" />
        <result column="create_time" property="createTime" />
        <result column="modifier" property="modifier" />
        <result column="modify_time" property="modifyTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, course_id, task_name, task_number, task_year, task_term,
        teach_week, week_hours, total_hours, course_leader_id, major_id, plan_id,
        status, creator, create_time, modifier, modify_time
    </sql>

</mapper>
