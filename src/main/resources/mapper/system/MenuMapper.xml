<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hnumi.obe.system.mapper.MenuMapper">
    <!-- 基础列 -->
    <sql id="Base_Column_List">
        id, parent_id, path, name, component, redirect, title, icon, expanded, order_no,
        is_hidden, hidden_breadcrumb, single, frame_src, frame_blank, keep_alive, menu_code,
        menu_type, is_no_closable, is_no_column, badge, target, active_menu, create_time,
        creator, modify_time, modifier
    </sql>

    <!-- 菜单树形选项 -->
    <resultMap id="MenuVOMap" type="com.hnumi.obe.system.vo.MenuVO">
        <id column="id" property="id"/>
        <result column="title" property="title"/>
        <result column="parent_id" property="pid"/>
        <result column="menu_type" property="type"/>
        <result column="menu_code" property="code"/>
        <collection property="children" select="getChildById" column="id"/>
    </resultMap>

    <!--<select id="listTreeOption" resultMap="MenuVOMap">
        select 
            <include refid="Base_Column_List"/>
        from sys_menu 
        where parent_id is null or parent_id = 0 and is_deleted = 0
        order by order_no
    </select>

    <select id="getChildById" resultMap="MenuVOMap">
        select 
            <include refid="Base_Column_List"/>
        from sys_menu 
        where parent_id = #{id} and is_deleted = 0
        order by order_no
    </select>-->

    <!-- 菜单树形结构 -->
    <resultMap id="MenuTreeVOMap" type="com.hnumi.obe.system.vo.MenuTreeVO">
        <id column="id" property="id"/>
        <result property="pid" column="parent_id" jdbcType="BIGINT"/>
        <result property="path" column="path"/>
        <result property="name" column="name"/>
        <result property="component" column="component"/>
        <result property="redirect" column="redirect"/>
        <result property="type" column="menu_type"/>
        <association property="meta" javaType="com.hnumi.obe.system.vo.MenuTreeMetaVO">
            <result property="title" column="title"/>
            <result property="icon" column="icon"/>
            <result property="expanded" column="expanded"/>
            <result property="orderNo" column="order_no"/>
            <result property="hidden" column="is_hidden"/>
            <result property="hiddenBreadcrumb" column="hidden_breadcrumb"/>
            <result property="single" column="single"/>
            <result property="frameSrc" column="frame_src"/>
            <result property="frameBlank" column="frame_blank"/>
            <result property="keepAlive" column="keep_alive"/>
            <result property="noClosable" column="is_no_closable"/>
            <result property="noColumn" column="is_no_column"/>
            <result property="badge" column="badge"/>
            <result property="target" column="target"/>
            <result property="activeMenu" column="active_menu"/>
        </association>
    </resultMap>

    <!-- 查询菜单列表 -->
    <select id="listAll" resultMap="MenuTreeVOMap">
        select 
            <include refid="Base_Column_List"/>
        from sys_menu
        where is_deleted = 0
        <if test="title != null and title != ''">
            and title like concat('%', #{title}, '%')
        </if>
        <if test="code != null and code != ''">
            and menu_code like concat('%', #{code}, '%')
        </if>
        <if test="type != null">
            and menu_type = #{type}
        </if>
        <if test="hidden != null">
            and is_hidden = #{hidden}
        </if>
        order by order_no
    </select>

    <!-- 查询路由菜单 -->
    <select id="listRoute" resultMap="MenuTreeVOMap">
        select 
            <include refid="Base_Column_List"/>
        from sys_menu
        where menu_type != 2 and is_deleted = 0
        order by order_no
    </select>

    <!-- 查询用户权限码 -->
    <select id="listPermission" resultType="string">
        select distinct m.menu_code 
        from sys_menu m
        inner join sys_role_menu rm on m.id = rm.menu_id
        inner join sys_role_user ru on rm.role_id = ru.role_id
        where ru.user_id = #{uid}
    </select>

    <!-- 查询菜单选项 -->
    <select id="listTreeOption" resultType="com.hnumi.obe.system.vo.MenuOptionVO">
        select 
            id,
            parent_id as pid,
            title as name,
            icon,
            menu_type as type,
            order_no as sort
        from sys_menu
        order by order_no
    </select>
</mapper>
