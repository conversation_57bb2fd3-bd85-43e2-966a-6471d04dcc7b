<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hnumi.obe.tp.mapper.CourseMapper">
    
    <!-- 当前学期课程统计查询结果映射 -->
    <resultMap id="CourseCurrentSemesterResultMap" type="com.hnumi.obe.tp.vo.CourseCurrentSemesterVO">
        <id property="courseId" column="course_id"/>
        <result property="courseName" column="course_name"/>
        <result property="courseCode" column="course_code"/>
        <result property="planId" column="plan_id"/>
        <result property="taskId" column="task_id"/>
        <result property="classCount" column="class_count"/>
        <result property="totalStudents" column="total_students"/>
        <result property="teacherCount" column="teacher_count"/>
        <result property="taskTerm" column="task_term"/>
        <result property="taskYear" column="task_year"/>
        <collection property="classes" ofType="com.hnumi.obe.base.vo.ClassesVO">
            <id property="classId" column="class_id"/>
            <result property="className" column="class_name"/>
            <result property="studentNumber" column="student_number"/>
        </collection>
    </resultMap>

    <!-- 教师学期课程统计查询 -->
    <select id="getTeacherSemesterCourses" resultMap="CourseCurrentSemesterResultMap">
        WITH course_stats AS (
            SELECT 
                tw.course_id,
                COUNT(DISTINCT twc.class_id) AS class_count,
                SUM(bc.student_number) AS total_students,
                COUNT(DISTINCT twt.teacher_id) AS teacher_count
            FROM task_worklist tw
            INNER JOIN task_worklist_classes twc ON tw.id = twc.task_id
            INNER JOIN base_classes bc ON twc.class_id = bc.class_id
            INNER JOIN task_worklist_teachers twt ON tw.id = twt.task_id
            WHERE tw.task_year = #{year}
                AND tw.status = 0
                AND bc.status = 0
                AND twt.teacher_id = #{teacherId}
                <if test="termType != null">
                    <choose>
                        <when test="termType == 0">
                            AND tw.task_term % 2 = 0
                        </when>
                        <when test="termType == 1">
                            AND tw.task_term % 2 = 1
                        </when>
                    </choose>
                </if>
            GROUP BY tw.course_id
        )
        SELECT 
            tc.course_id,
            tc.course_name,
            tc.course_code,
            tc.plan_id,
            twc.task_id,
            cs.class_count,
            cs.total_students,
            cs.teacher_count,
            bc.class_id,
            bc.class_name,
            bc.student_number,
            tw.task_term,
            tw.task_year
        FROM task_worklist tw
        INNER JOIN tp_course tc ON tw.course_id = tc.course_id
        INNER JOIN task_worklist_classes twc ON tw.id = twc.task_id
        INNER JOIN base_classes bc ON twc.class_id = bc.class_id
        INNER JOIN course_stats cs ON tc.course_id = cs.course_id
        WHERE tw.task_year = #{year}
            AND tw.status = 0
            AND tc.status = 0
            AND bc.status = 0
            AND EXISTS (
                SELECT 1
                FROM task_worklist_teachers twt
                WHERE twt.task_id = tw.id
                    AND twt.teacher_id = #{teacherId}
            )
            <if test="termType != null">
                <choose>
                    <when test="termType == 0">
                        AND tw.task_term % 2 = 0
                    </when>
                    <when test="termType == 1">
                        AND tw.task_term % 2 = 1
                    </when>
                </choose>
            </if>
        ORDER BY tc.course_name, bc.class_name
    </select>
    <!-- 课程授课历史统计查询 -->
    <select id="getTeachingHistoryCourses" resultType="com.hnumi.obe.tp.vo.CourseTeachingHistoryVO">
        WITH course_stats AS (
            SELECT 
                tw.course_id,
                COUNT(DISTINCT twc.class_id) AS class_count,
                SUM(COALESCE(bc.student_number, 0)) AS total_students,
                COUNT(DISTINCT CONCAT(tw.task_year, '-', tw.task_term)) AS semester_count
            FROM task_worklist tw
            INNER JOIN task_worklist_teachers twt ON tw.id = twt.task_id
            INNER JOIN task_worklist_classes twc ON tw.id = twc.task_id
            INNER JOIN base_classes bc ON twc.class_id = bc.class_id
            WHERE twt.teacher_id = #{teacherId}
                AND tw.status = 0 
                AND bc.status = 0
            GROUP BY tw.course_id
        )
        SELECT 
            tc.course_id,
            tc.course_name,
            tc.course_code,
            tc.course_credit,
            cs.class_count,
            cs.total_students,
            cs.semester_count
        FROM task_worklist tw
        INNER JOIN tp_course tc ON tw.course_id = tc.course_id
        INNER JOIN task_worklist_teachers twt ON tw.id = twt.task_id
        INNER JOIN task_worklist_classes twc ON tw.id = twc.task_id
        INNER JOIN base_classes bc ON twc.class_id = bc.class_id
        INNER JOIN course_stats cs ON tc.course_id = cs.course_id
        WHERE twt.teacher_id = #{teacherId}
            AND tw.status = 0 
            AND tc.status = 0 
            AND bc.status = 0
        GROUP BY tc.course_id, tc.course_name, tc.course_code, tc.course_credit, cs.class_count, cs.total_students, cs.semester_count
        ORDER BY tc.course_name
    </select>

    <!-- 课程任务基本信息查询 -->
    <select id="getCourseTaskInfo" resultType="com.hnumi.obe.tp.vo.CourseTaskInfoVO">
        SELECT 
            tc.course_id,
            tc.course_name,
            tc.course_code,
            tc.course_credit,
            tc.course_nature,
            tw.task_year,
            tw.task_term
        FROM tp_course tc
        INNER JOIN task_worklist tw ON tc.course_id = tw.course_id
        WHERE tc.course_id = #{courseId}
            AND tw.id = #{taskId}
            AND tc.status = 0
            AND tw.status = 0
    </select>

    <!-- 课程任务统计信息查询 -->
    <select id="getCourseTaskStatistics" resultType="com.hnumi.obe.tp.vo.CourseTaskStatisticsVO">
        SELECT 
            0 AS class_count,
            0 AS student_count,
            COUNT(DISTINCT a.id) AS assessment_count,
            NULL AS average_score
        FROM assessment a
        WHERE a.course_id = #{courseId}
            AND a.task_id = #{taskId}
            AND a.creator = #{teacherId}
            AND a.status = 0
    </select>

    <!-- 课程任务班级信息查询 -->
    <select id="getCourseTaskClasses" resultType="com.hnumi.obe.tp.vo.CourseTaskClassesVO">
        SELECT 
            bc.class_id,
            bc.class_name,
            bc.student_number AS student_count,
            bc.entrance_year,
            bt.teacher_id,
            bt.teacher_name AS teacher_name,
            bt.teacher_title AS teacher_title,
            ba.academy_name AS teacher_academy_name,
            twt.role AS teacher_role,
            tw.teach_week,
            tw.week_hours,
            tw.total_hours
        FROM task_worklist tw
        INNER JOIN task_worklist_classes twc ON tw.id = twc.task_id
        INNER JOIN base_classes bc ON twc.class_id = bc.class_id
        INNER JOIN task_worklist_teachers twt ON tw.id = twt.task_id
        INNER JOIN base_teacher bt ON twt.teacher_id = bt.teacher_id
        INNER JOIN base_academy ba ON bt.academy_id = ba.id
        WHERE tw.course_id = #{courseId}
            AND tw.id = #{taskId}
            AND tw.status = 0
            AND bc.status = 0
            AND twt.teacher_id = #{teacherId}
        ORDER BY bc.class_name
    </select>

    <!-- 课程任务教师团队信息查询 -->
    <select id="getCourseTaskTeachers" resultType="com.hnumi.obe.tp.vo.CourseTaskTeacherVO">
        SELECT 
            bt.teacher_id,
            bt.teacher_name AS teacher_name,
            bt.teacher_title AS teacher_title,
            ba.academy_name AS academy_name,
            twt.role AS role_id,
            bu.email,
            COUNT(DISTINCT twc.class_id) AS class_count,
            SUM(COALESCE(bc.student_number, 0)) AS student_count,
            CASE WHEN tw.course_leader_id = bt.teacher_id THEN 1 ELSE 0 END AS is_course_leader
        FROM task_worklist tw
        INNER JOIN task_worklist_teachers twt ON tw.id = twt.task_id
        INNER JOIN base_teacher bt ON twt.teacher_id = bt.teacher_id
        INNER JOIN base_academy ba ON bt.academy_id = ba.id
        INNER JOIN sys_base_user bu ON bt.base_user_id = bu.id
        LEFT JOIN task_worklist_classes twc ON tw.id = twc.task_id
        LEFT JOIN base_classes bc ON twc.class_id = bc.class_id
        WHERE tw.course_id = #{courseId}
            AND tw.id = #{taskId}
            AND tw.status = 0
            AND bt.status = 0
        GROUP BY bt.teacher_id, bt.teacher_name, bt.teacher_title, ba.academy_name, twt.role, bu.email, tw.course_leader_id
        ORDER BY twt.role, bt.teacher_name
    </select>
    
    <!-- 课程基础信息结果映射 -->
    <resultMap id="CourseBaseInfoResultMap" type="com.hnumi.obe.tp.entity.Course">
        <id property="courseId" column="course_id"/>
        <result property="courseCode" column="course_code"/>
        <result property="courseName" column="course_name"/>
        <result property="courseLeader" column="course_leader"/>
        <result property="courseCredit" column="course_credit"/>
        <result property="courseCore" column="course_core"/>
        <result property="courseExam" column="course_exam"/>
        <result property="courseTarget" column="course_target"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <!-- 根据ID查询课程基础信息 -->
    <select id="selectCourseBaseInfoById" parameterType="java.lang.Long" resultMap="CourseBaseInfoResultMap">
        SELECT
            course_id,
            course_code,
            course_name,
            course_leader,
            course_credit,
            course_core,
            course_exam,
            course_target,

            status,
            create_time
        FROM tp_course
        WHERE course_id = #{courseId}
        AND status = 0
    </select>

    <!-- 根据条件查询课程基础信息列表 -->
    <select id="selectCourseBaseInfoList" resultMap="CourseBaseInfoResultMap">
        SELECT
            course_id,
            course_code,
            course_name,
            course_leader,
            course_credit,
            course_core,
            course_exam,
            course_target,
            status,
            create_time
        FROM tp_course
        WHERE 1=1
        <if test="courseId != null">
            AND course_id = #{courseId}
        </if>
        <if test="courseCode != null and courseCode != ''">
            AND course_code = #{courseCode}
        </if>
        <if test="courseName != null and courseName != ''">
            AND course_name LIKE CONCAT('%', #{courseName}, '%')
        </if>
        <if test="courseLeader != null">
            AND course_leader = #{courseLeader}
        </if>
        <if test="status != null">
            AND status = #{status}
        </if>
        <if test="status == null">
            AND status = 0
        </if>
        ORDER BY create_time DESC
    </select>
</mapper>
