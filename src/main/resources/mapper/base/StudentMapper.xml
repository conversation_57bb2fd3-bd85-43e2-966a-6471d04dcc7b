<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hnumi.obe.base.mapper.StudentMapper">

    <!-- 结果映射：学生详细信息 -->
    <resultMap id="StudentVOResultMap" type="com.hnumi.obe.base.vo.StudentVO">
        <id property="id" column="student_id"/>
        <result property="studentNumber" column="student_number"/>
        <result property="entranceYear" column="entrance_year"/>
        <result property="studentStatus" column="student_status"/>
        <result property="status" column="status"/>
        <result property="creator" column="creator"/>
        <result property="createTime" column="create_time"/>
        <result property="modifier" column="modifier"/>
        <result property="modifyTime" column="modify_time"/>
        
        <!-- 用户基本信息 -->
        <association property="user" javaType="com.hnumi.obe.system.vo.UserVO">
            <id property="id" column="user_id"/>
            <result property="name" column="student_name"/>
            <result property="gender" column="gender"/>
            <result property="phone" column="phone"/>
            <result property="email" column="email"/>
            <result property="username" column="username"/>
            <result property="avatar" column="avatar"/>
        </association>
        
        <!-- 学院信息 -->
        <association property="academy" javaType="com.hnumi.obe.base.vo.StudentVO$AcademyVO">
            <id property="academyId" column="academy_id"/>
            <result property="academyName" column="academy_name"/>
        </association>
        
        <!-- 专业信息 -->
        <association property="major" javaType="com.hnumi.obe.base.vo.StudentVO$MajorVO">
            <id property="majorId" column="major_id"/>
            <result property="majorName" column="major_name"/>
            <result property="academyId" column="academy_id"/>
        </association>
        
        <!-- 班级信息 -->
        <association property="classInfo" javaType="com.hnumi.obe.base.vo.StudentVO$ClassVO">
            <id property="classId" column="class_id"/>
            <result property="className" column="class_name"/>
        </association>
    </resultMap>

    <!-- 基础查询字段 -->
    <sql id="BaseSelectColumns">
        s.student_id,
        s.student_number,
        s.class_id,
        s.major_id,
        s.academy_id,
        s.entrance_year,
        s.student_status,
        s.status,
        s.creator,
        s.create_time,
        s.modifier,
        s.modify_time,
        u.id as user_id,
        u.real_name as student_name,
        u.gender,
        u.phone,
        u.email,
        u.username,
        u.avatar,
        a.academy_name,
        m.major_name,
        c.class_name
    </sql>

    <!-- 基础表关联 -->
    <sql id="BaseJoins">
        FROM base_student s
        LEFT JOIN sys_base_user u ON s.base_user_id = u.id
        LEFT JOIN base_academy a ON s.academy_id = a.id
        LEFT JOIN base_major m ON s.major_id = m.major_id
        LEFT JOIN base_classes c ON s.class_id = c.class_id
    </sql>

    <!-- 查询条件 -->
    <sql id="QueryConditions">
        <where>
            <if test="academyId != null">
                AND s.academy_id = #{academyId}
            </if>
            <if test="majorId != null">
                AND s.major_id = #{majorId}
            </if>
            <if test="classId != null">
                AND s.class_id = #{classId}
            </if>
            <if test="studentStatus != null">
                AND s.student_status = #{studentStatus}
            </if>
            <if test="gender != null">
                AND u.gender = #{gender}
            </if>
            <if test="entranceYear != null and entranceYear != ''">
                AND s.entrance_year = #{entranceYear}
            </if>
            <if test="status != null">
                AND s.status = #{status}
            </if>
            <if test="studentName != null and studentName != ''">
                AND u.real_name LIKE CONCAT('%', #{studentName}, '%')
            </if>
            <if test="number != null and number != ''">
                AND s.student_number LIKE CONCAT('%', #{number}, '%')
            </if>
            <if test="phone != null and phone != ''">
                AND u.phone LIKE CONCAT('%', #{phone}, '%')
            </if>
            <if test="email != null and email != ''">
                AND u.email LIKE CONCAT('%', #{email}, '%')
            </if>
        </where>
    </sql>

    <!-- 分页查询学生详细信息 -->
    <select id="selectStudentPageWithDetails" resultMap="StudentVOResultMap">
        SELECT 
        <include refid="BaseSelectColumns"/>
        <include refid="BaseJoins"/>
        <include refid="QueryConditions"/>
        ORDER BY s.student_number
    </select>

    <!-- 根据ID查询学生详细信息 -->
    <select id="selectStudentDetailById" resultMap="StudentVOResultMap">
        SELECT 
        <include refid="BaseSelectColumns"/>,
        creator_user.real_name as creator_name,
        modifier_user.real_name as modifier_name
        <include refid="BaseJoins"/>
        LEFT JOIN sys_base_user creator_user ON s.creator = creator_user.id
        LEFT JOIN sys_base_user modifier_user ON s.modifier = modifier_user.id
        WHERE s.student_id = #{studentId}
    </select>

    <!-- 查询学生列表用于导出 -->
    <select id="selectStudentListForExport" resultMap="StudentVOResultMap">
        SELECT 
        <include refid="BaseSelectColumns"/>
        <include refid="BaseJoins"/>
        <where>
            <if test="academyId != null">
                AND s.academy_id = #{academyId}
            </if>
            <if test="majorId != null">
                AND s.major_id = #{majorId}
            </if>
            <if test="classId != null">
                AND s.class_id = #{classId}
            </if>
            <if test="studentStatus != null">
                AND s.student_status = #{studentStatus}
            </if>
            <if test="gender != null">
                AND u.gender = #{gender}
            </if>
            <if test="entranceYear != null and entranceYear != ''">
                AND s.entrance_year = #{entranceYear}
            </if>
            <if test="status != null">
                AND s.status = #{status}
            </if>
            <if test="status == null">
                AND (s.status = 0 OR s.status = 1)
            </if>
        </where>
        ORDER BY s.create_time DESC
    </select>

    <!-- 根据班级ID列表批量统计学生数量 -->
    <select id="countStudentsByClassIds" resultType="java.util.Map">
        SELECT 
            class_id as classId,
            COUNT(*) as studentCount
        FROM base_student
        WHERE class_id IN
        <foreach collection="classIds" item="classId" open="(" separator="," close=")">
            #{classId}
        </foreach>
        AND status = 0
        GROUP BY class_id
    </select>

    <!-- 查询课程下的所有学生 -->
    <select id="getStudentsByCourseId" resultType="com.hnumi.obe.base.vo.StudentVO">
        SELECT
            s.student_id,
            s.student_name,
            s.class_id,
            c.class_name
        FROM
            base_student s
        JOIN
            base_classes c ON s.class_id = c.class_id
        WHERE
            c.course_id = #{courseId}
    </select>

</mapper>
