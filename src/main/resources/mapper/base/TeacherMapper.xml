<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hnumi.obe.base.mapper.TeacherMapper">

    <!-- 结果映射：教师详细信息 -->
    <resultMap id="TeacherVOResultMap" type="com.hnumi.obe.base.vo.TeacherVO">
        <id property="id" column="teacher_id"/>
        <result property="number" column="teacher_number"/>
        <result property="title" column="teacher_title"/>
        <result property="status" column="status"/>
        <result property="creator" column="creator"/>
        <result property="creatorName" column="creator_name"/>
        <result property="createTime" column="create_time"/>
        <result property="modifier" column="modifier"/>
        <result property="modifierName" column="modifier_name"/>
        <result property="modifyTime" column="modify_time"/>
        
        <!-- 用户基本信息 -->
        <association property="user" javaType="com.hnumi.obe.system.vo.UserVO">
            <id property="id" column="user_id"/>
            <result property="name" column="teacher_name"/>
            <result property="gender" column="gender"/>
            <result property="phone" column="phone"/>
            <result property="email" column="email"/>
            <result property="username" column="username"/>
            <result property="avatar" column="avatar"/>
        </association>
        
        <!-- 学院信息 -->
        <association property="academy" javaType="com.hnumi.obe.base.vo.TeacherVO$AcademyVO">
            <id property="academyId" column="academy_id"/>
            <result property="academyName" column="academy_name"/>
        </association>
    </resultMap>

    <!-- 基础查询字段 -->
    <sql id="BaseSelectColumns">
        t.teacher_id,
        t.teacher_number,
        t.teacher_title,
        t.academy_id,
        t.status,
        t.creator,
        t.create_time,
        t.modifier,
        t.modify_time,
        u.id as user_id,
        u.real_name as teacher_name,
        u.gender,
        u.phone,
        u.email,
        u.username,
        u.avatar,
        a.academy_name,
        creator_user.real_name as creator_name,
        modifier_user.real_name as modifier_name
    </sql>

    <!-- 基础表关联 -->
    <sql id="BaseJoins">
        FROM base_teacher t
        LEFT JOIN sys_base_user u ON t.base_user_id = u.id
        LEFT JOIN base_academy a ON t.academy_id = a.id
        LEFT JOIN sys_base_user creator_user ON t.creator = creator_user.id
        LEFT JOIN sys_base_user modifier_user ON t.modifier = modifier_user.id
    </sql>

    <!-- 查询条件 -->
    <sql id="QueryConditions">
        <where>
            <!-- 默认过滤掉已删除的数据，只查询正常(0)和停用(1)状态的教师 -->
            AND t.status != -1
            <if test="academyId != null">
                AND t.academy_id = #{academyId}
            </if>
            <if test="teacherName != null and teacherName != ''">
                AND u.real_name LIKE CONCAT('%', #{teacherName}, '%')
            </if>
            <if test="number != null">
                AND t.teacher_number = #{number}
            </if>
            <if test="title != null and title != ''">
                AND t.teacher_title = #{title}
            </if>
            <if test="gender != null">
                AND u.gender = #{gender}
            </if>
            <if test="status != null">
                AND t.status = #{status}
            </if>
        </where>
    </sql>

    <!-- 分页查询教师详细信息 -->
    <select id="selectTeacherPageWithDetails" resultMap="TeacherVOResultMap">
        SELECT 
        <include refid="BaseSelectColumns"/>
        <include refid="BaseJoins"/>
        <include refid="QueryConditions"/>
        ORDER BY t.create_time DESC
    </select>

    <!-- 根据ID查询教师详细信息 -->
    <select id="selectTeacherDetailById" resultMap="TeacherVOResultMap">
        SELECT 
        <include refid="BaseSelectColumns"/>
        <include refid="BaseJoins"/>
        WHERE t.teacher_id = #{teacherId} AND t.status != -1
    </select>

    <!-- 查询教师列表用于导出 -->
    <select id="selectTeacherListForExport" resultMap="TeacherVOResultMap">
        SELECT 
        <include refid="BaseSelectColumns"/>
        <include refid="BaseJoins"/>
        <where>
            <if test="academyId != null">
                AND t.academy_id = #{academyId}
            </if>
            <if test="teacherName != null and teacherName != ''">
                AND u.real_name LIKE CONCAT('%', #{teacherName}, '%')
            </if>
            <if test="number != null">
                AND t.teacher_number = #{number}
            </if>
            <if test="title != null and title != ''">
                AND t.teacher_title = #{title}
            </if>
            <if test="gender != null">
                AND u.gender = #{gender}
            </if>
            <if test="status != null">
                AND t.status = #{status}
            </if>
            <if test="status == null">
                AND (t.status = 0 OR t.status = 1)
            </if>
        </where>
        ORDER BY t.create_time DESC
    </select>

    <!-- 获取院长选项列表 -->
    <select id="getDeanOptions" resultType="com.hnumi.obe.base.vo.DeanOptionsVO">
        SELECT 
            u.id as value,
            u.real_name as label,
            current_academy.id as currentAcademyId,
            current_academy.academy_name as currentAcademyName
        FROM base_teacher t
        LEFT JOIN sys_base_user u ON t.base_user_id = u.id
        LEFT JOIN base_academy current_academy ON current_academy.academy_president_id = u.id AND current_academy.status != -1
        WHERE t.status = 0
        ORDER BY 
            CASE WHEN current_academy.id IS NULL THEN 0 ELSE 1 END,
            u.real_name
    </select>

    <!-- 获取学院负责人选项列表:后期会禁用，因为该方法存在问题：出现同名的情况是存在的，因此不能仅仅获取姓名，还有工号等信息也要获取 -->
    <select id="getAcademyLeaderOptions" resultType="com.hnumi.obe.base.vo.AcademyLeaderOptionsVO">
        SELECT 
            u.id as value,
            u.real_name as label
        FROM base_teacher t
        LEFT JOIN sys_base_user u ON t.base_user_id = u.id
        WHERE t.status = 0
        ORDER BY u.real_name
    </select>
    <!-- 获取教师选项列表：主要用于需要用到下拉教师选项的地方，注意这里不包含教师的院系信息 -->
    <select id="listTeacherOptions" resultMap="TeacherVOResultMap">
        SELECT
            t.teacher_id as teacher_id,
            u.real_name as teacher_name,
            u.id as user_id,
            u.gender as gender,
            t.teacher_number as teacher_number
        FROM base_teacher t
        LEFT JOIN sys_base_user u ON t.base_user_id = u.id
        WHERE t.status = 0
        ORDER BY u.real_name
    </select>

    <!-- 批量查询教师详细信息 -->
    <select id="selectTeacherMapByIds" resultMap="TeacherVOResultMap">
        SELECT
            <include refid="BaseSelectColumns"/>,
            a.academy_name,
            creator_user.real_name as creator_name,
            modifier_user.real_name as modifier_name
        FROM base_teacher t
        LEFT JOIN sys_base_user u ON t.base_user_id = u.id
        LEFT JOIN base_academy a ON t.academy_id = a.id
        LEFT JOIN sys_base_user creator_user ON t.creator = creator_user.id
        LEFT JOIN sys_base_user modifier_user ON t.modifier = modifier_user.id
        WHERE t.status = 0
        <if test="teacherIds != null and teacherIds.size() > 0">
            AND t.teacher_id IN
            <foreach collection="teacherIds" item="teacherId" open="(" separator="," close=")">
                #{teacherId}
            </foreach>
        </if>
        ORDER BY u.real_name
    </select>

</mapper>
