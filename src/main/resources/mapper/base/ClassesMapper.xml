<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hnumi.obe.base.mapper.ClassesMapper">

    <!-- 根据条件统计各班级的实际学生人数 -->
    <select id="getStudentCountByConditions" resultType="Classes">
        SELECT 
            s.class_id as classId,
            COUNT(s.student_id) as studentNumber
        FROM base_student s
        INNER JOIN base_classes c ON s.class_id = c.class_id
        INNER JOIN base_major m ON c.major_id = m.major_id
        WHERE s.status = 0
        <if test="academyId != null">
            AND m.academy_id = #{academyId}
        </if>
        <if test="majorId != null">
            AND c.major_id = #{majorId}
        </if>
        <if test="classId != null">
            AND s.class_id = #{classId}
        </if>
        GROUP BY s.class_id
    </select>

    <!-- 批量更新班级的学生人数 -->
    <update id="updateStudentNumber">
        UPDATE base_classes 
        SET student_number = #{studentNumber},
            modify_time = NOW()
        WHERE class_id = #{classId}
    </update>

    <!-- 预览同步班级人数 -->
    <select id="previewSyncStudentCount" resultType="java.util.Map">
        SELECT 
            c.class_id as classId,
            c.class_name as className,
            c.student_number as oldStudentNumber,
            (
                SELECT COUNT(s.student_id)
                FROM base_student s
                WHERE s.class_id = c.class_id AND s.status = 0
            ) as actualStudentNumber
        FROM base_classes c
        <where>
            <if test="academyId != null">
                AND c.major_id IN (SELECT major_id FROM base_major WHERE academy_id = #{academyId})
            </if>
            <if test="majorId != null">
                AND c.major_id = #{majorId}
            </if>
            <if test="classId != null">
                AND c.class_id = #{classId}
            </if>
            AND c.status = 0
        </where>
        ORDER BY c.class_id
    </select>

</mapper>
