<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hnumi.obe.base.mapper.MajorMapper">

    <!-- 结果映射：专业详细信息 -->
    <resultMap id="MajorVOResultMap" type="com.hnumi.obe.base.vo.MajorVO">
        <id property="id" column="major_id"/>
        <result property="name" column="major_name"/>
        <result property="code" column="major_code"/>
        <result property="discipline" column="discipline"/>
        <result property="professionalOverview" column="professional_overview"/>
        <result property="status" column="status"/>
        <result property="creator" column="creator_name"/>
        <result property="createTime" column="create_time"/>
        <result property="updater" column="updater_name"/>
        <result property="updateTime" column="modify_time"/>
        <result property="courses" column="course_count"/>
        <result property="classes" column="class_count"/>
        <result property="students" column="student_count"/>
        <result property="director" column="director_name"/>
        <result property="collegeName" column="academy_name"/>
        
        <!-- 学院信息 -->
        <association property="college" javaType="com.hnumi.obe.base.vo.MajorVO$CollegeVO">
            <id property="collegeId" column="academy_id"/>
            <result property="collegeName" column="academy_name"/>
        </association>
        
        <!-- 专业负责人信息 -->
        <association property="directorInfo" javaType="com.hnumi.obe.base.vo.MajorVO$DirectorVO">
            <id property="directorId" column="academy_leader_id"/>
            <result property="directorName" column="director_name"/>
            <result property="directorNumber" column="director_number"/>
            <result property="directorTitle" column="director_title"/>
        </association>
    </resultMap>

    <!-- 基础查询字段 -->
    <sql id="BaseSelectColumns">
        m.major_id,
        m.major_name,
        m.major_code,
        m.professional_overview,
        m.academy_id,
        m.academy_leader_id,
        m.status,
        m.creator,
        m.create_time,
        m.modifier,
        m.modify_time,
        m.discipline,
        a.academy_name as academy_name,
        director_user.real_name as director_name,
        director_teacher.teacher_number as director_number,
        director_teacher.teacher_title as director_title,
        creator_user.real_name as creator_name,
        modifier_user.real_name as updater_name,
        COALESCE(course_stats.course_count, 0) as course_count,
        COALESCE(class_stats.class_count, 0) as class_count,
        COALESCE(student_stats.student_count, 0) as student_count
    </sql>

    <!-- 基础表关联 -->
    <sql id="BaseJoins">
        FROM base_major m
        LEFT JOIN base_academy a ON m.academy_id = a.id
        LEFT JOIN base_teacher director_teacher ON m.academy_leader_id = director_teacher.base_user_id
        LEFT JOIN sys_base_user director_user ON m.academy_leader_id = director_user.id
        LEFT JOIN sys_base_user creator_user ON m.creator = creator_user.id
        LEFT JOIN sys_base_user modifier_user ON m.modifier = modifier_user.id
        LEFT JOIN (
            SELECT major_id, COUNT(*) as course_count 
            FROM tp_course 
            WHERE status != -1 
            GROUP BY major_id
        ) course_stats ON m.major_id = course_stats.major_id
        LEFT JOIN (
            SELECT major_id, COUNT(*) as class_count 
            FROM base_classes 
            WHERE status != -1 
            GROUP BY major_id
        ) class_stats ON m.major_id = class_stats.major_id
        LEFT JOIN (
            SELECT major_id, COUNT(*) as student_count 
            FROM base_student 
            WHERE status != -1 
            GROUP BY major_id
        ) student_stats ON m.major_id = student_stats.major_id
    </sql>

    <!-- 查询条件 -->
    <sql id="QueryConditions">
        <where>
            <!-- 默认过滤掉已删除的数据，只查询正常(0)和停用(1)状态的专业 -->
            AND m.status != -1
            <if test="majorName != null and majorName != ''">
                AND m.major_name LIKE CONCAT('%', #{majorName}, '%')
            </if>
            <if test="majorCode != null and majorCode != ''">
                AND m.major_code LIKE CONCAT('%', #{majorCode}, '%')
            </if>
            <if test="academyId != null">
                AND m.academy_id = #{academyId}
            </if>
            <if test="academyLeaderId != null">
                AND m.academy_leader_id = #{academyLeaderId}
            </if>
            <if test="status != null">
                AND m.status = #{status}
            </if>
            <if test="createTimeStart != null">
                AND m.create_time >= #{createTimeStart}
            </if>
            <if test="createTimeEnd != null">
                AND m.create_time &lt;= #{createTimeEnd}
            </if>
        </where>
    </sql>

    <!-- 分页查询专业详细信息 -->
    <select id="pageListWithDetails" resultMap="MajorVOResultMap">
        SELECT 
        <include refid="BaseSelectColumns"/>
        <include refid="BaseJoins"/>
        <include refid="QueryConditions"/>
        ORDER BY m.create_time DESC
    </select>

    <!-- 根据ID查询专业详细信息 -->
    <select id="selectMajorDetailById" resultMap="MajorVOResultMap">
        SELECT 
        <include refid="BaseSelectColumns"/>
        <include refid="BaseJoins"/>
        WHERE m.major_id = #{majorId} AND m.status != -1
    </select>

    <!-- 查询专业列表用于导出 -->
    <select id="selectMajorListForExport" resultMap="MajorVOResultMap">
        SELECT 
        <include refid="BaseSelectColumns"/>
        <include refid="BaseJoins"/>
        <include refid="QueryConditions"/>
        ORDER BY m.create_time DESC
    </select>

    <!-- 现有的简单查询方法保持不变 -->
    <select id="getMajorCountByAcademyId" resultType="java.lang.Integer">
        select count(1) from base_major where academy_id = #{academyId}
    </select>

    <select id="getCourseCountByMajorId" resultType="java.lang.Integer">
        select count(1) from tp_course where major_id = #{majorId}
    </select>

    <select id="getMajorNameById" resultType="java.lang.String">
        select major_name as name from base_major where major_id = #{id}
    </select>
    <!-- 根据专业ID查询班级和课程中是否有引用该ID的情况 -->
    <select id="countByClassOrCourse" resultType="java.lang.Long">
        SELECT 
            CASE 
                WHEN EXISTS(SELECT 1 FROM base_classes WHERE major_id = #{majorId} AND status != -1) 
                     OR EXISTS(SELECT 1 FROM tp_course WHERE major_id = #{majorId} AND status != -1) 
                THEN 1 ELSE 0 
            END
    </select>
    <select id="listMajorSelectorByTeacherId" resultType="com.hnumi.obe.base.vo.MajorSelectorVO">
        WITH teacher_courses AS (
            SELECT DISTINCT
                m.major_id,
                m.major_name,
                m.major_code,
                m.professional_overview,
                m.discipline,
                a.id as academy_id,
                a.academy_name,
                tc.course_id,
                twc.class_id,
                CONCAT(tw.task_year, '-', tw.task_term) as semester
            FROM base_teacher u
                INNER JOIN task_worklist_teachers twt ON u.teacher_id = twt.teacher_id
                INNER JOIN task_worklist tw ON twt.task_id = tw.id
                INNER JOIN tp_course tc ON tw.course_id = tc.course_id
                INNER JOIN base_major m ON tc.major_id = m.major_id
                INNER JOIN base_academy a ON m.academy_id = a.id
                LEFT JOIN task_worklist_classes twc ON tw.id = twc.task_id
            WHERE u.teacher_id = #{teacherId}
              AND u.status != -1
              AND tw.status != -1
              AND tc.status != -1
              AND m.status != -1
              AND a.status != -1
        )
        SELECT
            major_id,
            major_name,
            major_code,
            professional_overview,
            discipline,
            academy_id,
            academy_name,
            COUNT(DISTINCT course_id) as course_count,
            COUNT(DISTINCT class_id) as class_count,
            COUNT(DISTINCT semester) as semester_count
        FROM teacher_courses
        GROUP BY
            major_id, major_name, major_code, professional_overview, discipline, academy_id, academy_name
        ORDER BY major_id;
    </select>
    <select id="listMajorSelectorByCourseLeader" resultType="com.hnumi.obe.base.vo.MajorSelectorVO">
        WITH leader_courses AS (
            SELECT DISTINCT
                m.major_id,
                m.major_name,
                m.major_code,
                m.professional_overview,
                m.discipline,
                a.id as academy_id,
                a.academy_name,
                tc.course_id,
                twc.class_id,
                CONCAT(tw.task_year, '-', tw.task_term) as semester
            FROM task_worklist tw
                INNER JOIN task_worklist_teachers twt ON tw.id = twt.task_id
                INNER JOIN tp_course tc ON tw.course_id = tc.course_id
                INNER JOIN base_major m ON tc.major_id = m.major_id
                INNER JOIN base_academy a ON m.academy_id = a.id
                LEFT JOIN task_worklist_classes twc ON tw.id = twc.task_id
            WHERE tw.course_leader_id = #{teacherId}
              AND tw.status != -1
              AND tc.status != -1
              AND m.status != -1
              AND a.status != -1
        )
        SELECT
            major_id,
            major_name,
            major_code,
            professional_overview,
            discipline,
            academy_id,
            academy_name,
            COUNT(DISTINCT course_id) as course_count,
            COUNT(DISTINCT class_id) as class_count,
            COUNT(DISTINCT semester) as semester_count
        FROM leader_courses
        GROUP BY
            major_id, major_name, major_code, professional_overview, discipline, academy_id, academy_name
        ORDER BY major_id;
    </select>

</mapper>
