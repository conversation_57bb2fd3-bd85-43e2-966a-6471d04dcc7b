<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hnumi.obe.base.mapper.AcademyMapper">

    <!-- 结果映射：学院详细信息 -->
    <resultMap id="AcademyVOResultMap" type="com.hnumi.obe.base.vo.AcademyVO">
        <id property="id" column="id"/>
        <result property="name" column="academy_name"/>
        <result property="dean" column="dean_name"/>
        <result property="academyPresidentId" column="academy_president_id"/>
        <result property="status" column="status"/>
        <result property="creator" column="creator_name"/>
        <result property="createTime" column="create_time"/>
        <result property="updater" column="updater_name"/>
        <result property="updateTime" column="modify_time"/>
        <result property="majors" column="major_count"/>
        <result property="classes" column="class_count"/>
    </resultMap>

    <!-- 基础查询字段 -->
    <sql id="BaseSelectColumns">
        a.id,
        a.academy_name,
        a.academy_president_id,
        a.status,
        a.creator,
        a.create_time,
        a.modifier,
        a.modify_time,
        president_user.real_name as dean_name,
        creator_user.real_name as creator_name,
        modifier_user.real_name as updater_name,
        COALESCE(major_stats.major_count, 0) as major_count,
        COALESCE(class_stats.class_count, 0) as class_count
    </sql>

    <!-- 基础表关联 -->
    <sql id="BaseJoins">
        FROM base_academy a
        LEFT JOIN sys_base_user president_user ON a.academy_president_id = president_user.id
        LEFT JOIN sys_base_user creator_user ON a.creator = creator_user.id
        LEFT JOIN sys_base_user modifier_user ON a.modifier = modifier_user.id
        LEFT JOIN (
            SELECT academy_id, COUNT(*) as major_count 
            FROM base_major 
            WHERE status != -1 
            GROUP BY academy_id
        ) major_stats ON a.id = major_stats.academy_id
        LEFT JOIN (
            SELECT m.academy_id, COUNT(*) as class_count 
            FROM base_classes c
            INNER JOIN base_major m ON c.major_id = m.major_id
            WHERE c.status != -1 
            GROUP BY m.academy_id
        ) class_stats ON a.id = class_stats.academy_id
    </sql>

    <!-- 查询条件 -->
    <sql id="QueryConditions">
        <where>
            <!-- 默认过滤掉已删除的数据，只查询正常(0)和停用(1)状态的学院 -->
            AND a.status != -1
            <if test="academyName != null and academyName != ''">
                AND a.academy_name LIKE CONCAT('%', #{academyName}, '%')
            </if>
            <if test="academyPresidentId != null">
                AND a.academy_president_id = #{academyPresidentId}
            </if>
            <if test="status != null">
                AND a.status = #{status}
            </if>
        </where>
    </sql>

    <!-- 分页查询学院详细信息 -->
    <select id="pageListWithDetails" resultMap="AcademyVOResultMap">
        SELECT 
        <include refid="BaseSelectColumns"/>
        <include refid="BaseJoins"/>
        <include refid="QueryConditions"/>
        ORDER BY a.create_time DESC
    </select>

</mapper>
