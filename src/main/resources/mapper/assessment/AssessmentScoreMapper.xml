<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hnumi.obe.assessment.mapper.AssessmentScoreMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hnumi.obe.assessment.entity.AssessmentScore">
        <id column="id" property="id" />
        <result column="student_id" property="studentId" />
        <result column="assessment_id" property="assessmentId" />
        <result column="task_id" property="taskId" />
        <result column="score" property="score" />
        <result column="total_score" property="totalScore" />
        <result column="score_status" property="scoreStatus" />
        <result column="remark" property="remark" />
        <result column="status" property="status" />
        <result column="creator" property="creator" />
        <result column="create_time" property="createTime" />
        <result column="modifier" property="modifier" />
        <result column="modify_time" property="modifyTime" />
    </resultMap>

    <!-- 学生成绩VO映射结果 -->
    <resultMap id="StudentScoreVOMap" type="com.hnumi.obe.assessment.vo.StudentScoreTargetVO">
        <id column="score_id" property="scoreId" />
        <result column="student_id" property="studentId" />
        <result column="student_number" property="number" />
        <result column="student_name" property="studentName" />
        <result column="class_id" property="classId" />
        <result column="class_name" property="className" />
        <result column="assessment_id" property="assessmentId" />
        <result column="assessment_name" property="assessmentName" />
        <result column="task_id" property="taskId" />
        <result column="score" property="score" />
        <result column="total_score" property="totalScore" />
        <result column="score_rate" property="scoreRate" />
        <result column="score_grade" property="scoreGrade" />
        <result column="score_status" property="scoreStatus" />
        <result column="score_status_name" property="scoreStatusName" />
        <result column="remark" property="remark" />
        <result column="create_time" property="createTime" />
        <result column="modify_time" property="modifyTime" />
    </resultMap>

    <!-- 成绩统计VO映射结果 -->
    <resultMap id="ScoreStatisticsVOMap" type="com.hnumi.obe.assessment.vo.ScoreStatisticsVO">
        <result column="assessment_id" property="assessmentId" />
        <result column="assessment_name" property="assessmentName" />
        <result column="task_id" property="taskId" />
        <result column="total_student_count" property="totalStudentCount" />
        <result column="scored_student_count" property="scoredStudentCount" />
        <result column="unscored_student_count" property="unscoredStudentCount" />
        <result column="average_score" property="averageScore" />
        <result column="max_score" property="maxScore" />
        <result column="min_score" property="minScore" />
        <result column="median_score" property="medianScore" />
        <result column="standard_deviation" property="standardDeviation" />
        <result column="pass_rate" property="passRate" />
        <result column="excellent_rate" property="excellentRate" />
        <result column="good_rate" property="goodRate" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, student_id, assessment_id, task_id, score, total_score, score_status, remark, status, creator, create_time, modifier, modify_time
    </sql>

    <!-- 查询学生成绩列表（包含学生和班级信息） -->
    <select id="selectStudentScores" resultMap="StudentScoreVOMap">
        SELECT 
            s.id as score_id,
            s.student_id,
            st.student_number,
            u.real_name as student_name,
            st.class_id,
            c.class_name,
            s.assessment_id,
            a.assessment_name,
            s.task_id,
            s.score,
            s.total_score,
            CASE
                WHEN s.total_score &gt; 0 THEN ROUND((s.score / s.total_score) * 100, 2)
                ELSE 0
            END as score_rate,
            CASE
                WHEN s.score &gt;= s.total_score * 0.9 THEN '优秀'
                WHEN s.score &gt;= s.total_score * 0.8 THEN '良好'
                WHEN s.score &gt;= s.total_score * 0.7 THEN '中等'
                WHEN s.score &gt;= s.total_score * 0.6 THEN '及格'
                ELSE '不及格'
            END as score_grade,
            s.score_status,
            CASE s.score_status
                WHEN 0 THEN '未提交'
                WHEN 1 THEN '已提交'
                WHEN 2 THEN '已审核'
                ELSE '未知'
            END as score_status_name,
            s.remark,
            s.create_time,
            s.modify_time
        FROM assessment_score s
        LEFT JOIN base_student st ON s.student_id = st.student_id
        LEFT JOIN base_user u ON st.user_id = u.user_id
        LEFT JOIN base_classes c ON st.class_id = c.class_id
        LEFT JOIN assessment a ON s.assessment_id = a.id
        WHERE s.status = 0
        <if test="assessmentId != null">
            AND s.assessment_id = #{assessmentId}
        </if>
        <if test="taskId != null">
            AND s.task_id = #{taskId}
        </if>
        <if test="classId != null">
            AND st.class_id = #{classId}
        </if>
        <if test="studentName != null and studentName != ''">
            AND u.real_name LIKE CONCAT('%', #{studentName}, '%')
        </if>
        <if test="studentNumber != null and studentNumber != ''">
            AND st.student_number LIKE CONCAT('%', #{studentNumber}, '%')
        </if>
        <if test="scoreStatus != null">
            AND s.score_status = #{scoreStatus}
        </if>
        ORDER BY st.student_number
    </select>

    <!-- 查询考核成绩统计信息 -->
    <select id="selectScoreStatistics" resultMap="ScoreStatisticsVOMap">
        SELECT 
            #{assessmentId} as assessment_id,
            a.assessment_name,
            #{taskId} as task_id,
            (SELECT COUNT(DISTINCT st.student_id) 
             FROM task_worklist_classes twc 
             JOIN base_student st ON twc.class_id = st.class_id 
             WHERE twc.task_id = #{taskId} AND st.status = 0) as total_student_count,
            COUNT(s.id) as scored_student_count,
            (SELECT COUNT(DISTINCT st.student_id) 
             FROM task_worklist_classes twc 
             JOIN base_student st ON twc.class_id = st.class_id 
             WHERE twc.task_id = #{taskId} AND st.status = 0) - COUNT(s.id) as unscored_student_count,
            ROUND(AVG(s.score), 2) as average_score,
            MAX(s.score) as max_score,
            MIN(s.score) as min_score,
            ROUND(AVG(s.score), 2) as median_score,
            ROUND(STDDEV(s.score), 2) as standard_deviation,
            ROUND((SUM(CASE WHEN s.score &gt;= s.total_score * 0.6 THEN 1 ELSE 0 END) / COUNT(s.id)) * 100, 2) as pass_rate,
            ROUND((SUM(CASE WHEN s.score &gt;= s.total_score * 0.9 THEN 1 ELSE 0 END) / COUNT(s.id)) * 100, 2) as excellent_rate,
            ROUND((SUM(CASE WHEN s.score &gt;= s.total_score * 0.8 AND s.score &lt; s.total_score * 0.9 THEN 1 ELSE 0 END) / COUNT(s.id)) * 100, 2) as good_rate
        FROM assessment_score s
        LEFT JOIN assessment a ON s.assessment_id = a.id
        WHERE s.assessment_id = #{assessmentId} 
        AND s.task_id = #{taskId} 
        AND s.status = 0
        GROUP BY s.assessment_id, s.task_id
    </select>

    <!-- 查询班级成绩统计列表 -->
    <select id="selectClassScoreStatistics" resultType="com.hnumi.obe.assessment.vo.ScoreStatisticsVO$ClassScoreStatistics">
        SELECT 
            c.class_id as classId,
            c.class_name as className,
            COUNT(DISTINCT st.student_id) as totalCount,
            COUNT(s.id) as scoredCount,
            ROUND(AVG(s.score), 2) as averageScore,
            MAX(s.score) as maxScore,
            MIN(s.score) as minScore,
            ROUND((SUM(CASE WHEN s.score &gt;= s.total_score * 0.6 THEN 1 ELSE 0 END) / COUNT(s.id)) * 100, 2) as passRate
        FROM task_worklist_classes twc
        JOIN base_classes c ON twc.class_id = c.class_id
        JOIN base_student st ON c.class_id = st.class_id
        LEFT JOIN assessment_score s ON st.student_id = s.student_id 
            AND s.assessment_id = #{assessmentId} 
            AND s.task_id = #{taskId} 
            AND s.status = 0
        WHERE twc.task_id = #{taskId} 
        AND st.status = 0
        GROUP BY c.class_id, c.class_name
        ORDER BY c.class_name
    </select>

    <!-- 查询分数段分布 -->
    <select id="selectScoreDistribution" resultType="com.hnumi.obe.assessment.vo.ScoreStatisticsVO$ScoreRangeDistribution">
        SELECT 
            score_range as scoreRange,
            student_count as studentCount,
            ROUND((student_count / total_count) * 100, 2) as percentage
        FROM (
            SELECT 
                CASE
                    WHEN score &gt;= total_score * 0.9 THEN '90-100'
                    WHEN score &gt;= total_score * 0.8 THEN '80-89'
                    WHEN score &gt;= total_score * 0.7 THEN '70-79'
                    WHEN score &gt;= total_score * 0.6 THEN '60-69'
                    ELSE '0-59'
                END as score_range,
                COUNT(*) as student_count,
                (SELECT COUNT(*) FROM assessment_score WHERE assessment_id = #{assessmentId} AND task_id = #{taskId} AND status = 0) as total_count
            FROM assessment_score 
            WHERE assessment_id = #{assessmentId} 
            AND task_id = #{taskId} 
            AND status = 0
            GROUP BY
                CASE
                    WHEN score &gt;= total_score * 0.9 THEN '90-100'
                    WHEN score &gt;= total_score * 0.8 THEN '80-89'
                    WHEN score &gt;= total_score * 0.7 THEN '70-79'
                    WHEN score &gt;= total_score * 0.6 THEN '60-69'
                    ELSE '0-59'
                END
        ) t
        ORDER BY 
            CASE score_range
                WHEN '90-100' THEN 1
                WHEN '80-89' THEN 2
                WHEN '70-79' THEN 3
                WHEN '60-69' THEN 4
                WHEN '0-59' THEN 5
            END
    </select>

    <!-- 查询学生课程目标达成度 -->
    <select id="getStudentCourseTargetScores" resultType="com.hnumi.obe.assessment.vo.StudentCourseTargetScoreVO">
        SELECT
            s.student_id AS studentId,
            s.student_name AS studentName,
            t.target_id AS targetId,
            t.target_name AS targetName,
            SUM(a.score * a.weight) / SUM(a.total_score * a.weight) AS weightedAchievement,
            SUM(a.score) / SUM(a.total_score) AS unweightedAchievement
        FROM
            assessment_score a
        JOIN
            student s ON a.student_id = s.student_id
        JOIN
            assessment_target t ON a.target_id = t.target_id
        WHERE
            a.course_id = #{courseId}
        GROUP BY
            s.student_id, t.target_id
    </select>

</mapper>
