<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hnumi.obe.assessment.mapper.AssessmentTaskMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hnumi.obe.assessment.entity.AssessmentTask">
        <id column="id" property="id" />
        <result column="assessment_id" property="assessmentId" />
        <result column="task_id" property="taskId" />
        <result column="publish_note" property="publishNote" />
        <result column="publish_status" property="publishStatus" />
        <result column="status" property="status" />
        <result column="creator" property="creator" />
        <result column="create_time" property="createTime" />
        <result column="modifier" property="modifier" />
        <result column="modify_time" property="modifyTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, assessment_id, task_id, publish_note, publish_status, status, creator, create_time, modifier, modify_time
    </sql>

</mapper>
