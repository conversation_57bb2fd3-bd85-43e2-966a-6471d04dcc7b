<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hnumi.obe.assessment.mapper.AssessmentScoreTargetMapper">

    <!-- 批量查询多个学生的课程目标成绩 -->
    <select id="selectByAssessmentAndStudents" resultType="com.hnumi.obe.assessment.entity.AssessmentScoreTarget">
        SELECT * FROM assessment_score_target 
        WHERE assessment_id = #{assessmentId} 
        AND task_id = #{taskId} 
        <if test="studentIds != null and studentIds.size() > 0">
            AND student_id IN 
            <foreach collection="studentIds" item="studentId" open="(" separator="," close=")">
                #{studentId}
            </foreach>
        </if>
        AND status = 0 
        ORDER BY student_id, course_target_no
    </select>

</mapper>
