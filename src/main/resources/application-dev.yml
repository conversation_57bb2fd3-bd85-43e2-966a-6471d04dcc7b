spring:
  datasource:
    #数据库配置
    username: root
#    password: 123456
    password: 121384guoli
    url: *******************************************************************************************************************************************
#    url: ***************************************************************************************************************************************
    hikari:
      #设置事务自动提交
      auto-commit: true
      #设置最大连接数
      maximum-pool-size: 10
      minimum-idle: 1
      #获取连接是，检测语句
      connection-test-query: select 1
      #连接超时设置
      connection-timeout: 20000
      max-lifetime: 1800000
      idle-timeout: 600000
      keepalive-time: 30000
  data:
    # redis配置
    redis:
      # Redis数据库索引（默认为0）
      database: 0
      # Redis服务器地址
      host: *************
      # Redis服务器连接端口
      port: 51668
      # Redis服务器连接密码（默认为空）
      password: 121384guoli
      # 连接超时时间
      timeout: 30s
      lettuce:
        pool:
          # 连接池最大连接数
          max-active: 10
          # 连接池最大阻塞等待时间（使用负值表示没有限制）
          max-wait: -1ms
          # 连接池中的最大空闲连接
          max-idle: 10
          # 连接池中的最小空闲连接
          min-idle: 1

logging:
  level:
    com.hnumi.obe.mapper: debug