package ${package.Entity?replace("entity", "vo")};

import lombok.Data;
import java.io.Serializable;
<#list table.importPackages as pkg>
import ${pkg};
</#list>

/**
 * ${table.comment!} 视图对象（VO）
 * 
 * VO（View Object）用于展示层，把某个指定页面（或组件）的所有数据封装起来
 * 主要用于：
 * 1. 展示层数据封装
 * 2. 数据格式转换（如日期格式化）
 * 3. 数据脱敏处理
 * 4. 前端展示优化
 */
@Data
public class ${table.entityName}VO implements Serializable {

    private static final long serialVersionUID = 1L;

<#-- 循环属性 -->
<#list table.fields as field>
    <#if field.comment!?length gt 0>
    /**
     * ${field.comment}
     * 字段类型：${field.propertyType}
     * 字段名称：${field.propertyName}
     * 数据库字段：${field.columnName}
     * 展示说明：用于前端展示的${field.comment}
     */
    </#if>
    private ${field.propertyType} ${field.propertyName};
</#list>
} 