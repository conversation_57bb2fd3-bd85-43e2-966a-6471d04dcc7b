package ${package.Controller};

import ${package.Entity}.${entity};
import ${package.Service}.${table.serviceName};
import com.hnumi.obe.common.valid.ValidGroup;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import ${package.Entity?replace("entity", "mapstruct")}.${entity}Convert;
import ${package.Entity?replace("entity", "dto")}.${entity}DTO;
import ${package.Entity?replace("entity", "dto")}.${entity}QueryDTO;
import ${package.Entity?replace("entity", "vo")}.${entity}VO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import java.util.List;
import java.util.stream.Collectors;
<#if restControllerStyle>
import org.springframework.web.bind.annotation.RestController;
<#else>
import org.springframework.stereotype.Controller;
</#if>
<#if superControllerClassPackage??>
import ${superControllerClassPackage};
</#if>

/**
* ${table.comment!}
*
*/
<#if restControllerStyle>
@RestController
<#else>
@Controller
</#if>
@RequestMapping("<#if package.ModuleName?? && package.ModuleName != "">/${package.ModuleName}</#if>/<#if controllerMappingHyphenStyle>${controllerMappingHyphen}<#else>${table.entityPath}</#if>")
<#if kotlin>
class ${table.controllerName}<#if superControllerClass??> : ${superControllerClass}()</#if>
<#else>
<#if superControllerClass??>
public class ${table.controllerName} extends ${superControllerClass} {
<#else>
public class ${table.controllerName} {
</#if>
    @Autowired
    ${table.serviceName} ${table.entityPath}Service;

    @PostMapping
    public Object add${entity}(@Validated(ValidGroup.Add.class) @RequestBody ${entity}DTO dto) {
        return ${table.entityPath}Service.save(${entity}Convert.INSTANCE.toEntity(dto));
    }

    @PutMapping
    public Object update${entity}(@Validated(ValidGroup.Add.class) @RequestBody ${entity}DTO dto) {
        return ${table.entityPath}Service.updateById(${entity}Convert.INSTANCE.toEntity(dto));
    }

    @DeleteMapping("/{id}")
    public Object delete${entity}ById(@PathVariable Long id) {
        return ${table.entityPath}Service.removeById(id);
    }

    @GetMapping("/{id}")
    public Object get${entity}ById(@PathVariable Long id) {
        return ${entity}Convert.INSTANCE.toVO(${table.entityPath}Service.getById(id));
    }

    /**
     * 分页查询${table.comment!}列表
     *
     * @param query 查询条件
     * @return 分页结果
     */
    @PostMapping("/list")
    public Page<${entity}VO> page${entity}(@RequestBody ${entity}QueryDTO query) {
        // 构建查询条件
        LambdaQueryWrapper<${entity}> wrapper = Wrappers.lambdaQuery(${entity}.class);
        //请在下方配置分页查询所需的参数，如果不需要则删除即可
        /* 示例
        wrapper.like(StringUtil.isNotBlank(query.getPhone()), BaseUser::getPhone, query.getPhone());
        三个参数：第一个为判断是否为空或者是否符合要求，第二个为要给哪个字段设置值，第三个为值
        */
        // 设置分页参数
        Page<${entity}> page = query.getPage();
        // 执行分页查询
        List<${entity}> data = ${table.entityPath}Service.list(page, wrapper);
        page.setRecords(data);
        return ${entity}Convert.INSTANCE.toPageVO(page);
    }
}
</#if>