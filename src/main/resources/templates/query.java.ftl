package ${package.Entity?replace("entity", "dto")};

import com.hnumi.obe.common.entity.BasePage;
import ${package.Entity}.${table.entityName};
import lombok.Data;
import java.io.Serializable;

/**
 * ${table.comment!} 数据查询参数对象（DTO）
 * 
 * QueryDTO（Data Transfer Object）用于前端向后端查询数据封装
 * 主要用于：
 * 1. 封装请求参数
 * 2. 在不同服务层之间传递数据
 * 3. 隐藏实体类的敏感字段
 * 4. 优化数据传输结构
 */
@Data
public class ${table.entityName}QueryDTO extends BasePage<${table.entityName}> implements Serializable {

    private static final long serialVersionUID = 1L;

<#-- 循环属性 -->
<#list table.fields as field>
    <#if field.comment!?length gt 0>
    /**
     * ${field.comment}
     * 字段类型：${field.propertyType}
     * 字段名称：${field.propertyName}
     * 数据库字段：${field.columnName}
     */
    </#if>
    private ${field.propertyType} ${field.propertyName};
</#list>
} 