package ${package.Entity?replace("entity", "mapstruct")};

import com.hnumi.obe.common.mapstruct.BaseConvert;
import ${package.Entity}.${table.entityName};
import ${package.Entity?replace("entity", "vo")}.${table.entityName}VO;
import ${package.Entity?replace("entity", "dto")}.${table.entityName}DTO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

/**
 * ${table.comment!} 对象转换器
 * 
 * 使用 MapStruct 实现对象之间的转换
 * 主要功能：
 * 1. 实体类与DTO之间的转换
 * 2. 实体类与VO之间的转换
 * 3. 集合对象的批量转换
 * 4. 自定义字段映射规则
 * 
 * 使用说明：
 * 1. 通过 INSTANCE 获取转换器实例
 * 2. 调用相应的转换方法进行对象转换
 * 3. 支持自定义字段映射规则
 * 4. 支持集合对象的批量转换
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ${table.entityName}Convert extends BaseConvert<${table.entityName}VO, ${table.entityName}> {
    /**
     * 转换器实例
     * 使用方式：${table.entityName}Convert.INSTANCE.toVO(entity)
     */
    ${table.entityName}Convert INSTANCE = Mappers.getMapper(${table.entityName}Convert.class);

    ${table.entityName} toEntity(${table.entityName}DTO dto);
} 