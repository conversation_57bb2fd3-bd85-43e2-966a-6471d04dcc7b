package ${package.Entity?replace("entity", "dto")};

import lombok.Data;
import java.io.Serializable;

/**
 * ${table.comment!} 数据传输对象（DTO）
 * 
 * DTO（Data Transfer Object）用于服务层之间的数据传输
 * 主要用于：
 * 1. 封装请求参数
 * 2. 在不同服务层之间传递数据
 * 3. 隐藏实体类的敏感字段
 * 4. 优化数据传输结构
 */
@Data
public class ${table.entityName}DTO implements Serializable {

    private static final long serialVersionUID = 1L;

<#-- 循环属性 -->
<#list table.fields as field>
    <#if field.comment!?length gt 0>
    /**
     * ${field.comment}
     * 字段类型：${field.propertyType}
     * 字段名称：${field.propertyName}
     * 数据库字段：${field.columnName}
     */
    </#if>
    private ${field.propertyType} ${field.propertyName};
</#list>
} 