obe:
  redis-key-prefix-phone-code: obe:phone:code
  redis-key-prefix-tenant: obe:tenant
  sms:
    sign-name: 攸米科技 #短信签名
    region-id: cn-zhengzhou #区域位置ID
    access-key-id: LTAI5t6LsUA54FjYjBMUDxxp # accessKeyId
    access-secret: ****************************** # accessSecret
    register-template-code: SMS_187590843 #短信模板代码
    login-template-code: SMS_187560870 #短信模板代码
mybatis-plus:
  mapper-locations: classpath:/mapper/*/*Mapper.xml
  type-aliases-package: com.hnumi.obe.*.entity
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
server:
  port: 8080
spring:
  datasource:
    #数据库配置
    username: root
    #    password: 123456
    password: 121384guoli
    url: jdbc:mysql://*************:3306/obe_db?autoReconnect=true&useUnicode=true&characterEncoding=utf-8&useSSL=false&serverTimezone=Asia/Shanghai
    #    url: ***************************************************************************************************************************************
    hikari:
      #设置事务自动提交
      auto-commit: true
      #设置最大连接数
      maximum-pool-size: 10
      minimum-idle: 1
      #获取连接是，检测语句
      connection-test-query: select 1
      #连接超时设置
      connection-timeout: 20000
      max-lifetime: 1800000
      idle-timeout: 600000
      keepalive-time: 30000
  data:
    # redis配置
    redis:
      # Redis数据库索引（默认为0）
      database: 0
      # Redis服务器地址
      host: *************
      # Redis服务器连接端口
      port: 51668
      # Redis服务器连接密码（默认为空）
      password: 121384guoli
      # 连接超时时间
      timeout: 30s
      lettuce:
        pool:
          # 连接池最大连接数
          max-active: 10
          # 连接池最大阻塞等待时间（使用负值表示没有限制）
          max-wait: -1ms
          # 连接池中的最大空闲连接
          max-idle: 10
          # 连接池中的最小空闲连接
          min-idle: 1
  jackson:
    default-property-inclusion: non-null
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  devtools:
    restart:
      enabled: true
sa-token:
  # token 名称（同时也是 cookie 名称）
  token-name: Authorization
  # token 有效期（单位：秒） 默认30天，-1 代表永久有效
  timeout: 2592000
  # token 最低活跃频率（单位：秒），如果 token 超过此时间没有访问系统就会被冻结，默认-1 代表不限制，永不冻结
  active-timeout: -1
  # 是否允许同一账号多地同时登录 （为 true 时允许一起登录, 为 false 时新登录挤掉旧登录）
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个 token （为 true 时所有登录共用一个 token, 为 false 时每次登录新建一个 token）
  is-share: true
  # token 风格（默认可取值：uuid、simple-uuid、random-32、random-64、random-128、tik）
  token-style: simple-uuid
  # 是否输出操作日志
  is-log: true

password4j:
  argon2: { memory: 15360, iterations: 2, length: 32, parallelism: 1, type: id, version: 19, salt: 16, password: "123456" }