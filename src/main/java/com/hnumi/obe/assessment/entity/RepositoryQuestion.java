package com.hnumi.obe.assessment.entity;

import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 考核题目库实体类，对应表 obe_db.repository_question
 */
@Data
public class RepositoryQuestion {
    private Long id; // 题目id
    private String questionTopic; // 题目题干
    private String questionDetail; // 题目详情
    private Integer questionType; // 题目类型（1选择，2填空，3主观题）
    private BigDecimal questionScore; // 题目总分
    private Integer citationCount; // 引用量
    private BigDecimal scoringRate; // 得分率
    private Long pathId; // 文件路径id
    private Long courseId; // 所属课程id
    private Integer status; // 记录状态{0:正常；-1:删除}
    private Long creator; // 记录创建者
    private LocalDateTime createTime; // 创建时间
    private Long modifier; // 最后修改者
    private LocalDateTime modifyTime; // 最后修改时间
}

