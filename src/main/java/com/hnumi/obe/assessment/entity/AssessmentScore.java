package com.hnumi.obe.assessment.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hnumi.obe.base.entity.BaseEntity;
import com.hnumi.obe.common.annotation.ExcelColumn;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 考核成绩表
 * 
 * 用于存储学生在具体考核中的总成绩
 * 这是一个新增的实体类，用于简化成绩管理
 */
@Deprecated
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("assessment_score")
public class AssessmentScore extends BaseEntity {

    /**
     * 记录ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 学生ID
     */
    @TableField("student_id")
    @ExcelColumn("学生ID")
    private Long studentId;

    /**
     * 考核ID
     */
    @TableField("assessment_id")
    private Long assessmentId;

    /**
     * 教学任务ID
     */
    @TableField("task_id")
    private Long taskId;

    /**
     * 学生成绩
     */
    @TableField("score")
    @ExcelColumn("成绩")
    private BigDecimal score;

    /**
     * 考核总分
     */
    @TableField("total_score")
    private BigDecimal totalScore;

    /**
     * 成绩状态：0-未提交，1-已提交，2-已审核
     */
    @TableField("score_status")
    private Integer scoreStatus;

    /**
     * 备注
     */
    @TableField("remark")
    @ExcelColumn("备注")
    private String remark;

    /**
     * 记录状态{0:正常状态；-1:删除状态；}
     */
    @TableField("status")
    private Integer status;
}
