package com.hnumi.obe.assessment.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hnumi.obe.base.entity.BaseEntity;
import com.hnumi.obe.common.annotation.ExcelColumn;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 考核成绩详情表
 * 
 * 用于存储按考核详情导入的成绩数据
 * 基于考核内容表（assessment_content）中的试卷题目进行详细成绩录入
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("assessment_score_detail")
public class AssessmentScoreDetail extends BaseEntity {

    /**
     * 记录ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 学生ID
     */
    @TableField("student_id")
    @ExcelColumn("学生ID")
    private Long studentId;

    /**
     * 考核ID
     */
    @TableField("assessment_id")
    private Long assessmentId;

    /**
     * 题目答案ID（关联repository_answer表）
     */
    @TableField("repository_answer_id")
    private Long repositoryAnswerId;

    /**
     * 学生答案
     */
    @TableField("student_answer")
    @ExcelColumn("学生答案")
    private String studentAnswer;

    /**
     * 学生得分
     */
    @TableField("score")
    @ExcelColumn("得分")
    private BigDecimal score;

    /**
     * 题目总分（冗余字段）
     */
    @TableField("question_score")
    private BigDecimal questionScore;

    /**
     * 对应课程目标编号
     */
    @TableField("course_target_no")
    @ExcelColumn("课程目标编号")
    private Integer courseTargetNo;

    @TableField("objective_id")
    private String objectiveId;

    /**
     * 对应该专业的毕业要求的ID（冗余字段，用于计算达成度）
     */
    @TableField("po_id")
    private Long poId;

    /**
     * 教学任务ID（冗余字段）
     */
    @TableField("task_id")
    private Long taskId;

    /**
     * 课程ID（冗余字段）
     */
    @TableField("course_id")
    private Long courseId;

    /**
     * 专业ID（冗余字段）
     */
    @TableField("major_id")
    private Long majorId;

    /**
     * 记录状态{0:正常状态；-1:删除状态；}
     */
    @TableField("status")
    private Integer status;
}
