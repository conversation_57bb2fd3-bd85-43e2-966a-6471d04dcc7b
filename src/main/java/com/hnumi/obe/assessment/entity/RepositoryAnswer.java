package com.hnumi.obe.assessment.entity;

import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 题目答案评分表实体类，对应表 obe_db.repository_answer
 */
@Data
public class RepositoryAnswer {
    /** 题目对应的答案id */
    private Long id;
    /** 题目id */
    private Long questionId;
    /** 答案内容：选择题A/B/C，填空题答案，主观题采分点 */
    private String questionAnswer;
    /** 同一个题目的答案序号，默认0 */
    private Integer answerNo;
    /** 当前答案得分 */
    private BigDecimal answerScore;
    /** 对应课程目标id（查tp_course -> course_target） */
    private String courseObjectiveId;
    /** 所属课程id */
    private Long courseId;
    /** 记录状态{0:正常；-1:删除} */
    private Integer status;
    /** 记录创建者 */
    private Long creator;
    /** 创建时间 */
    private LocalDateTime createTime;
    /** 记录最后修改者 */
    private Long modifier;
    /** 记录最后修改时间 */
    private LocalDateTime modifyTime;
}

