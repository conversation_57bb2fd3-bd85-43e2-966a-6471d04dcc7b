package com.hnumi.obe.assessment.entity;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * assessment与question的关联表实体类，对应表 obe_db.assessment_content
 * 一张卷子的详细信息，关联考核与题目
 */
@Data
public class AssessmentContent {
    /** 主键 */
    private Integer id;
    /** 考核id */
    private Long assessmentId;
    /** 题目id */
    private Long questionId;
    /** 题目序号 */
    private Integer questionNo;
    /** 记录创建者 */
    private Long creator;
    /** 记录创建时间 */
    private LocalDateTime createTime;
    /** 修改者 */
    private Long modifier;
    /** 修改时间 */
    private LocalDateTime modifyTime;
}

