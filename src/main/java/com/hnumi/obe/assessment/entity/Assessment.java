package com.hnumi.obe.assessment.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hnumi.obe.base.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 教学任务考核表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("assessment")
public class Assessment extends BaseEntity {

    /**
     * 记录id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 教学任务id，查task_worklist -> id，如果是-1，代表面向全体期末考核
     */
    @TableField("task_id")
    private Long taskId;

    /**
     * 课程id，查tp_course -> id
     */
    @TableField("course_id")
    private Long courseId;

    /**
     * 考核名称
     */
    @TableField("assessment_name")
    private String assessmentName;

    /**
     * 考核描述
     */
    @TableField("description")
    private String description;

    /**
     * 考核类型（对应教学大纲中配置考核方式的序号:作业、测验、期末等）
     */
    @TableField("assessment_method")
    private Integer assessmentMethod;

    /**
     * 考核时间，json格式：{"startTime": "2023-10-01 08:00:00", "endTime": "2023-10-01 10:00:00"}
     */
    @TableField("assessment_date")
    private String assessmentDate;

    /**
     * 考核权重
     */
    @TableField("assessment_weight")
    private BigDecimal assessmentWeight;

    /**
     * 考核试卷内容详情，json格式：[{ qid:题库题目id, qscore:本次考核设置分值,...}]
     */
    @TableField("assessment_detail")
    private String assessmentDetail;

    /**
     * 考核年份
     */
    @TableField("assessment_year")
    private Integer assessmentYear;

    /**
     * 考核学期
     */
    @TableField("assessment_term")
    private Integer assessmentTerm;

    /**
     * 成绩录入方式（0:直接录入方式；1:详细录入方式）
     */
    @TableField("score_type")
    private Integer scoreType;

    @TableField("assessment_status")
    private Integer assessmentStatus; // 考核状态：0配置中，1编辑中，2进行中，3已结束

    /**
     * 记录状态{0:正常状态；-1:删除状态；}
     */
    @TableField("status")
    private Integer status; // 记录状态{0:正常状态；-1:删除状态；}

    /**
     * 是否参与达成度计算
     */
    @TableField("achievement")
    private Boolean achievement = false;

    /**
     * 试卷总分
     */
    @TableField("total_score")
    private BigDecimal totalScore;


}
