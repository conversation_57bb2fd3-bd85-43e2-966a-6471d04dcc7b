package com.hnumi.obe.assessment.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hnumi.obe.base.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 考核与教学任务关联表
 * 
 * 用于管理考核发布到具体教学任务的关联关系
 * 当考核发布给部分教学任务时，会在此表中创建对应的关联记录
 * 当考核发布给全部教学任务时，通过assessment表的task_id=-1来标识，不在此表创建记录
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("assessment_task")
public class AssessmentTask extends BaseEntity {

    /**
     * 记录ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 考核ID，关联assessment表
     */
    @TableField("assessment_id")
    private Long assessmentId;

    /**
     * 教学任务ID，关联task_worklist表
     */
    @TableField("task_id")
    private Long taskId;

    /**
     * 发布说明
     */
    @TableField("publish_note")
    private String publishNote;

    /**
     * 发布状态：0-正常发布，1-暂停，2-撤销
     */
    @TableField("publish_status")
    private Integer publishStatus;

    /**
     * 记录状态{0:正常状态；-1:删除状态；}
     */
    @TableField("status")
    private Integer status;

    @TableField("score_weight")
    private Double scoreWeight;
}
