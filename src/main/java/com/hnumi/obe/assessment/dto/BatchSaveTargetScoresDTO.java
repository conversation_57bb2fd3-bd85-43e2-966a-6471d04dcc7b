package com.hnumi.obe.assessment.dto;

import com.hnumi.obe.common.valid.ValidGroup;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;


import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 批量保存学生课程目标成绩请求DTO
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
public class BatchSaveTargetScoresDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;

    /**
     * 考核ID
     */
    @NotNull(message = "考核ID不能为空", groups = {ValidGroup.Add.class})
    private Long assessmentId;

    /**
     * 教学任务ID
     */
    @NotNull(message = "教学任务ID不能为空", groups = {ValidGroup.Add.class})
    private Long taskId;

    /**
     * 学生成绩列表
     */
    @NotEmpty(message = "学生成绩列表不能为空", groups = {ValidGroup.Add.class})
    @Valid
    private List<StudentTargetScoreDTO> studentScores;

    /**
     * 学生课程目标成绩DTO
     */
    @Data
    public static class StudentTargetScoreDTO implements Serializable {
        
        private static final long serialVersionUID = 1L;

        /**
         * 学生ID
         */
        @NotNull(message = "学生ID不能为空", groups = {ValidGroup.Add.class})
        private Long studentId;

        /**
         * 课程目标编号
         */
        @NotNull(message = "课程目标编号不能为空", groups = {ValidGroup.Add.class})
        private Integer courseTargetNo;
        /**
         * 课程目标编号
         */
        private String objectiveId;

        /**
         * 题目答案ID
         */
        private Long repositoryAnswerId;

        /**
         * 学生得分
         */
        @NotNull(message = "学生得分不能为空", groups = {ValidGroup.Add.class})
        private BigDecimal score;

        /**
         * 备注（可选）
         */
        private String remark;
    }
}
