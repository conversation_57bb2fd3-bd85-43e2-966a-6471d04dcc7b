package com.hnumi.obe.assessment.dto;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

/**
 * 教师教学任务查询 数据传输对象（DTO）
 * 
 * 用于查询当前登录教师负责的教学任务
 */
@Data
public class TeacherTaskQueryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 教师ID（从当前登录用户获取）
     */
    @NotNull(message = "教师ID不能为空")
    private Long teacherId;

    /**
     * 课程ID（可选，用于筛选特定课程的教学任务）
     */
    private Long courseId;

    /**
     * 教学任务状态（可选）
     * 0-进行中，1-已结束，不传则查询全部
     */
    private Integer taskStatus;

    /**
     * 授课年份（可选）
     */
    private Integer taskYear;

    /**
     * 授课学期（可选）
     */
    private Integer taskTerm;
}
