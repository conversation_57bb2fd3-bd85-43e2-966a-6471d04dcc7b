package com.hnumi.obe.assessment.dto;

import com.hnumi.obe.common.valid.ValidGroup;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 按考核详情导入成绩 数据传输对象（DTO）
 * 
 * 用于按考核详情导入学生考核成绩
 * 基于考核内容表（assessment_content）中的试卷题目进行详细成绩录入
 */
@Data
public class ScoreDetailImportDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 考核ID
     */
    @NotNull(message = "考核ID不能为空", groups = {ValidGroup.Add.class})
    private Long assessmentId;

    /**
     * 教学任务ID
     */
    @NotNull(message = "教学任务ID不能为空", groups = {ValidGroup.Add.class})
    private Long taskId;

    /**
     * 学生详细成绩列表
     */
    @NotEmpty(message = "学生成绩列表不能为空", groups = {ValidGroup.Add.class})
    @Valid
    private List<StudentDetailScoreDTO> studentScores;

    /**
     * 学生详细成绩DTO
     */
    @Data
    public static class StudentDetailScoreDTO implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 学生ID
         */
        @NotNull(message = "学生ID不能为空", groups = {ValidGroup.Add.class})
        private Long studentId;

        /**
         * 题目答案成绩列表
         */
        @NotEmpty(message = "题目答案成绩列表不能为空", groups = {ValidGroup.Add.class})
        @Valid
        private List<QuestionAnswerScoreDTO> answerScores;
    }

    /**
     * 题目答案成绩DTO
     */
    @Data
    public static class QuestionAnswerScoreDTO implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 题目答案ID
         */
        @NotNull(message = "题目答案ID不能为空", groups = {ValidGroup.Add.class})
        private Long repositoryAnswerId;

        /**
         * 学生答案
         */
        private String studentAnswer;

        /**
         * 学生得分
         */
        @NotNull(message = "学生得分不能为空", groups = {ValidGroup.Add.class})
        private BigDecimal score;

        /**
         * 题目总分（可选，用于验证）
         */
        private BigDecimal questionScore;
    }
}
