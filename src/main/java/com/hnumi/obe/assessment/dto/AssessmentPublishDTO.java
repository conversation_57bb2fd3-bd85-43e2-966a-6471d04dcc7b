package com.hnumi.obe.assessment.dto;

import com.hnumi.obe.common.valid.ValidGroup;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 考核发布 数据传输对象（DTO）
 * 
 * DTO（Data Transfer Object）用于服务层之间的数据传输
 * 主要用于：
 * 1. 封装考核发布请求参数
 * 2. 支持全部发布和部分发布两种模式
 * 3. 验证发布参数的有效性
 */
@Data
public class AssessmentPublishDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 考核ID
     */
    @NotNull(message = "考核ID不能为空", groups = {ValidGroup.Add.class})
    private Long assessmentId;

    /**
     * 发布类型：true-发布给全部教学任务，false-发布给指定教学任务
     */
    @NotNull(message = "发布类型不能为空", groups = {ValidGroup.Add.class})
    private Boolean publishToAll;

    /**
     * 指定的教学任务ID列表（当publishToAll为false时必填）
     */
    private List<Long> taskIds;

    /**
     * 发布说明
     */
    private String publishNote;
}
