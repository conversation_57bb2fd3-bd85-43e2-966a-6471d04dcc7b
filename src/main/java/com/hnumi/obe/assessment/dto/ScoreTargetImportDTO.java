package com.hnumi.obe.assessment.dto;

import com.hnumi.obe.common.valid.ValidGroup;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 按课程目标导入成绩 数据传输对象（DTO）
 * 
 * 用于按课程目标导入学生考核成绩
 * 每次考核针对不同的课程目标分别录入得分
 */
@Data
public class ScoreTargetImportDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 考核ID
     */
    @NotNull(message = "考核ID不能为空", groups = {ValidGroup.Add.class})
    private Long assessmentId;

    /**
     * 教学任务ID
     */
    @NotNull(message = "教学任务ID不能为空", groups = {ValidGroup.Add.class})
    private Long taskId;

    /**
     * 学生课程目标成绩列表
     */
    @NotEmpty(message = "学生成绩列表不能为空", groups = {ValidGroup.Add.class})
    @Valid
    private List<StudentTargetScoreDTO> studentScores;

    /**
     * 学生课程目标成绩DTO
     */
    @Data
    public static class StudentTargetScoreDTO implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 学生ID
         */
        @NotNull(message = "学生ID不能为空", groups = {ValidGroup.Add.class})
        private Long studentId;

        /**
         * 课程目标成绩列表
         */
        @NotEmpty(message = "课程目标成绩列表不能为空", groups = {ValidGroup.Add.class})
        @Valid
        private List<CourseTargetScoreDTO> targetScores;
    }

    /**
     * 课程目标成绩DTO
     */
    @Data
    public static class CourseTargetScoreDTO implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 课程目标编号
         */
        @NotNull(message = "课程目标编号不能为空", groups = {ValidGroup.Add.class})
        private Integer courseTargetNo;

        /**
         * 学生得分
         */
        @NotNull(message = "学生得分不能为空", groups = {ValidGroup.Add.class})
        private BigDecimal score;

        /**
         * 题目答案ID（可选，用于关联具体的答案）
         */
        private Long repositoryAnswerId;
    }
}
