package com.hnumi.obe.assessment.dto;

import lombok.Data;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;

/**
 * 教学任务考核查询DTO
 */
@Data
public class AssessmentQueryDTO {

    /**
     * 教学任务id
     */
    private Long taskId;

    /**
     * 课程id
     */
    private Long courseId;

    /**
     * 考核名称（模糊查询）
     */
    private String assessmentName;

    /**
     * 考核描述（模糊查询）
     */
    private String description;

    /**
     * 考核类型
     */
    private Integer assessmentType;

    /**
     * 考核开始时间（用于时间范围查询）
     */
    private String startTime;

    /**
     * 考核结束时间（用于时间范围查询）
     */
    private String endTime;

    /**
     * 考核年份
     */
    private Integer assessmentYear;

    /**
     * 考核学期
     */
    private Integer assessmentTerm;

    /**
     * 成绩录入方式（0:直接录入方式；1:详细录入方式）
     */
    private Integer scoreType;

    /**
     * 页码
     */
    @Min(value = 1, message = "页码必须大于0")
    private Integer pageNum = 1;

    /**
     * 每页大小
     */
    @Min(value = 1, message = "每页大小必须大于0")
    @Max(value = 100, message = "每页大小不能超过100")
    private Integer pageSize = 10;

    /**
     * 排序字段
     */
    private String orderBy;

    /**
     * 排序方式：asc/desc
     */
    private String orderType = "desc";
}
