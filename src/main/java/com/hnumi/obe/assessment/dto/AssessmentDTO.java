package com.hnumi.obe.assessment.dto;

import com.hnumi.obe.assessment.vo.DirectEntryConfig;
import com.hnumi.obe.common.valid.ValidGroup;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import jakarta.validation.constraints.DecimalMin;
import java.math.BigDecimal;
import java.util.List;

/**
 * 教学任务考核DTO
 */
@Data
public class AssessmentDTO {

    /**
     * 记录id
     */
    @NotNull(groups = ValidGroup.Update.class, message = "考核ID不能为空")
    private Long id;

    /**
     * 教学任务id，查task_worklist -> id，如果是-1，代表面向全体期末考核
     */
    @NotNull(groups = {ValidGroup.Add.class, ValidGroup.Update.class}, message = "教学任务ID不能为空")
    private Long taskId;

    /**
     * 课程id，查tp_course -> id
     */
    @NotNull(groups = {ValidGroup.Add.class, ValidGroup.Update.class}, message = "课程ID不能为空")
    private Long courseId;

    /**
     * 考核名称
     */
    @NotBlank(groups = {ValidGroup.Add.class, ValidGroup.Update.class}, message = "考核名称不能为空")
    private String assessmentName;

    /**
     * 考核描述
     */
    private String description;

    /**
     * 考核类型（对应教学大纲中配置考核方式的序号:作业、测验、期末等）
     */
    @NotNull(groups = {ValidGroup.Add.class, ValidGroup.Update.class}, message = "考核类型不能为空")
    @Positive(message = "考核类型必须为正数")
    private Integer assessmentMethod;

    /**
     * 考核时间，json格式：{"startTime": "2023-10-01 08:00:00", "endTime": "2023-10-01 10:00:00"}
     */
    private String assessmentDate;

    /**
     * 是否参与达成度计算
     */
    private Boolean achievement = false;

    /**
     * 考核权重
     */
    @DecimalMin(value = "0.0", inclusive = false, message = "考核权重必须大于0")
    private BigDecimal assessmentWeight;

    /**
     * 考核试卷内容详情，json格式：存储的是直接录入方式的不同课程目标的分数totalScore
     */
    private List<DirectEntryConfig> assessmentDetailList;

    /**
     * 考核年份
     */
    @NotNull(groups = {ValidGroup.Add.class, ValidGroup.Update.class}, message = "考核年份不能为空")
    @Positive(message = "考核年份必须为正数")
    private Integer assessmentYear;

    /**
     * 考核学期
     */
    @NotNull(groups = {ValidGroup.Add.class, ValidGroup.Update.class}, message = "考核学期不能为空")
    @Positive(message = "考核学期必须为正数")
    private Integer assessmentTerm;

    /**
     * 成绩录入方式（0:直接录入方式；1:详细录入方式）
     */
    private Integer scoreType;

    private Integer assessmentStatus; // 考核状态：0配置中，1编辑中，2进行中，3已结束
}
