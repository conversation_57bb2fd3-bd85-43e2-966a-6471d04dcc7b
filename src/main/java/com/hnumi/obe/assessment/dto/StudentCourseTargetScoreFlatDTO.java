package com.hnumi.obe.assessment.dto;

import lombok.Data;
import java.math.BigDecimal;

/**
 * 扁平化的学生课程目标成绩分组统计DTO
 */
@Data
public class StudentCourseTargetScoreFlatDTO {
    private Long studentId;
    private String studentName;
    private String studentNumber;
    private Integer courseTargetNo;
    private String poId;
    private String objectiveId;
    private String courseTargetName;
    private Integer assessmentMethod;
    private BigDecimal totalScore;
}

