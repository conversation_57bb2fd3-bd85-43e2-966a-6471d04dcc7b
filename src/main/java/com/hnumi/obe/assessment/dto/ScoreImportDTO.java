package com.hnumi.obe.assessment.dto;

import com.hnumi.obe.common.annotation.ExcelColumn;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 成绩导入 数据传输对象（DTO）
 * 
 * 用于Excel导入学生考核成绩
 */
@Data
public class ScoreImportDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 学号
     */
    @ExcelColumn("学号")
    private String studentNumber;

    /**
     * 学生姓名
     */
    @ExcelColumn("姓名")
    private String studentName;

    /**
     * 班级名称
     */
    @ExcelColumn("班级")
    private String className;

    /**
     * 成绩
     */
    @ExcelColumn("成绩")
    private BigDecimal score;

    /**
     * 备注
     */
    @ExcelColumn("备注")
    private String remark;
}
