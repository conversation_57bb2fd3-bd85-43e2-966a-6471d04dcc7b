package com.hnumi.obe.assessment.dto;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

/**
 * 查询可发布教学任务 数据传输对象（DTO）
 * 
 * DTO（Data Transfer Object）用于查询可发布的教学任务
 * 主要用于：
 * 1. 封装查询条件
 * 2. 根据考核信息查找匹配的教学任务
 */
@Data
public class AssessmentPublishQueryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 考核ID
     */
    @NotNull(message = "考核ID不能为空")
    private Long assessmentId;

    /**
     * 课程ID（从考核信息中获取）
     */
    private Long courseId;

    /**
     * 考核年份（从考核信息中获取）
     */
    private Integer assessmentYear;

    /**
     * 考核学期（从考核信息中获取）
     */
    private Integer assessmentTerm;
}
