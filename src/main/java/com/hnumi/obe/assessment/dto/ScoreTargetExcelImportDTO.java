package com.hnumi.obe.assessment.dto;

import com.hnumi.obe.common.annotation.ExcelColumn;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 课程目标成绩Excel导入 数据传输对象（DTO）
 * 
 * 用于Excel导入课程目标成绩数据
 */
@Data
public class ScoreTargetExcelImportDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 学号
     */
    @ExcelColumn("学号")
    private String studentNumber;

    /**
     * 学生姓名
     */
    @ExcelColumn("姓名")
    private String studentName;

    /**
     * 班级名称
     */
    @ExcelColumn("班级")
    private String className;

    /**
     * 课程目标编号
     */
    @ExcelColumn("课程目标编号")
    private Integer courseTargetNo;

    /**
     * 学生得分
     */
    @ExcelColumn("得分")
    private BigDecimal score;

    /**
     * 题目答案ID（可选）
     */
    @ExcelColumn("题目答案ID")
    private Long repositoryAnswerId;

    /**
     * 备注
     */
    @ExcelColumn("备注")
    private String remark;
}
