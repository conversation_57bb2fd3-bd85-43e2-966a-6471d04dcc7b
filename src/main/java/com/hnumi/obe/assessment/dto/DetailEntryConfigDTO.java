package com.hnumi.obe.assessment.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.hnumi.obe.assessment.vo.AssessmentContentDetailVO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 详细录入配置DTO
 * <AUTHOR>
 * @create 2025-07-13 23:52
 */
@Data
public class DetailEntryConfigDTO {
//    private Long assessmentId;
//    private List<AssessmentContentDetailVO.QuestionDetailVO> questions;
@JsonSerialize(using = ToStringSerializer.class)
    private Long courseId;
    @JsonSerialize(using = ToStringSerializer.class)
    private Long assessmentId;
    private String assessmentName;
    private BigDecimal totalScore;
    private List<QuestionDetailDTO> questions;

    @Data
    public static class QuestionDetailDTO {
        @JsonSerialize(using = ToStringSerializer.class)
        private Long questionId;
        private Integer questionNo;
        private String questionTopic;
        private String questionDetail;
        private Integer questionType;
        private BigDecimal questionScore;
        private List<AnswerDetailDTO> answers;
    }

    @Data
    public static class AnswerDetailDTO {
        @JsonSerialize(using = ToStringSerializer.class)
        private Long answerId;
        @JsonSerialize(using = ToStringSerializer.class)
        private Long questionId;
        private String questionAnswer;
        private Integer answerNo;
        private BigDecimal answerScore;
        private String courseObjectiveId;
    }
}
