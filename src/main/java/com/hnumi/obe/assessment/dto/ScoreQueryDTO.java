package com.hnumi.obe.assessment.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 成绩查询 数据传输对象（DTO）
 * 
 * 用于查询学生考核成绩
 */
@Data
public class ScoreQueryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 考核ID
     */
    private Long assessmentId;

    /**
     * 教学任务ID
     */
    private Long taskId;

    /**
     * 学生ID（可选，用于查询特定学生成绩）
     */
    private Long studentId;

    /**
     * 班级ID（可选，用于按班级查询）
     */
    private Long classId;

    /**
     * 学生姓名（可选，模糊查询）
     */
    private String studentName;

    /**
     * 学号（可选，模糊查询）
     */
    private String studentNumber;

    /**
     * 成绩状态（可选）
     * 0-未提交，1-已提交，2-已审核
     */
    private Integer scoreStatus;
}
