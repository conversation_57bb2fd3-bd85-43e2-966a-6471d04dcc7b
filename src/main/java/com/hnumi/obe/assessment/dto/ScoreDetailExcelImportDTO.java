package com.hnumi.obe.assessment.dto;

import com.hnumi.obe.common.annotation.ExcelColumn;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 考核详情成绩Excel导入 数据传输对象（DTO）
 * 
 * 用于Excel导入考核详情成绩数据
 */
@Data
public class ScoreDetailExcelImportDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 学号
     */
    @ExcelColumn("学号")
    private String studentNumber;

    /**
     * 学生姓名
     */
    @ExcelColumn("姓名")
    private String studentName;

    /**
     * 班级名称
     */
    @ExcelColumn("班级")
    private String className;

    /**
     * 题目答案ID
     */
    @ExcelColumn("题目答案ID")
    private Long repositoryAnswerId;

    /**
     * 题目编号（可选，用于显示）
     */
    @ExcelColumn("题目编号")
    private String questionNumber;

    /**
     * 学生答案
     */
    @ExcelColumn("学生答案")
    private String studentAnswer;

    /**
     * 学生得分
     */
    @ExcelColumn("得分")
    private BigDecimal score;

    /**
     * 题目总分
     */
    @ExcelColumn("题目总分")
    private BigDecimal questionScore;

    /**
     * 课程目标编号
     */
    @ExcelColumn("课程目标编号")
    private Integer courseTargetNo;

    /**
     * 备注
     */
    @ExcelColumn("备注")
    private String remark;
}
