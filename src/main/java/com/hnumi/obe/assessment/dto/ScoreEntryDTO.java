package com.hnumi.obe.assessment.dto;

import com.hnumi.obe.common.valid.ValidGroup;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 成绩录入 数据传输对象（DTO）
 * 
 * 用于批量录入学生考核成绩,单值综合成绩
 */
@Deprecated
@Data
public class ScoreEntryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 考核ID
     */
    @NotNull(message = "考核ID不能为空", groups = {ValidGroup.Add.class})
    private Long assessmentId;

    /**
     * 教学任务ID
     */
    @NotNull(message = "教学任务ID不能为空", groups = {ValidGroup.Add.class})
    private Long taskId;

    /**
     * 学生成绩列表
     */
    @NotEmpty(message = "学生成绩列表不能为空", groups = {ValidGroup.Add.class})
    @Valid
    private List<StudentScoreDTO> studentScores;

    /**
     * 学生成绩DTO
     */
    @Data
    public static class StudentScoreDTO implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 学生ID
         */
        @NotNull(message = "学生ID不能为空", groups = {ValidGroup.Add.class})
        private Long studentId;

        /**
         * 学生成绩
         */
        @NotNull(message = "学生成绩不能为空", groups = {ValidGroup.Add.class})
        private BigDecimal score;

        /**
         * 备注
         */
        private String remark;
    }
}
