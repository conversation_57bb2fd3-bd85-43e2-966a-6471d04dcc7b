package com.hnumi.obe.assessment.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 学生成绩详情VO
 * 
 * 用于展示学生在特定考核和教学任务下的详细成绩信息
 */
@Data
public class StudentScoreDetailVO {

    /**
     * 学生ID
     */
    private Long studentId;

    /**
     * 学号
     */
    private String studentNumber;

    /**
     * 学生姓名
     */
    private String studentName;

    /**
     * 班级ID
     */
    private Long classId;

    /**
     * 班级名称
     */
    private String className;

    /**
     * 总成绩
     */
    private BigDecimal totalScore;

    /**
     * 总分
     */
    private BigDecimal fullScore;

    /**
     * 得分率
     */
    private BigDecimal scoreRate;

    /**
     * 成绩等级
     */
    private String scoreGrade;

    /**
     * 按课程目标分组的成绩数据
     */
    private List<CourseTargetScore> courseTargetScores;

    /**
     * 考核详情成绩（题目级别）
     */
    private List<AssessmentDetailScore> detailScores;

    /**
     * 成绩录入状态
     */
    private ScoreEntryStatus entryStatus;

    /**
     * 成绩录入时间
     */
    private LocalDateTime entryTime;

    /**
     * 最后修改时间
     */
    private LocalDateTime lastModifyTime;

    /**
     * 录入者ID
     */
    private Long entryUserId;

    /**
     * 录入者姓名
     */
    private String entryUserName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 课程目标成绩
     */
    @Data
    public static class CourseTargetScore {
        /**
         * 课程目标Id
         */
        private String objectiveId;
        /**
         * 课程目标编号
         */
        private Integer courseTargetNo;

        /**
         * 课程目标名称
         */
        private String courseTargetName;

        /**
         * 课程目标得分
         */
        private BigDecimal score;

        /**
         * 课程目标满分
         */
        private BigDecimal fullScore;

        /**
         * 课程目标得分率
         */
        private BigDecimal scoreRate;

        /**
         * 对应的毕业要求指标ID
         */
        private Long poId;

        /**
         * 毕业要求指标名称
         */
        private String poName;

        /**
         * 权重
         */
        private BigDecimal weight;
    }

    /**
     * 考核详情成绩（题目级别）
     */
    @Data
    public static class AssessmentDetailScore {
        /**
         * 题目答案ID
         */
        private Long repositoryAnswerId;

        /**
         * 题目编号
         */
        private String questionNumber;

        /**
         * 题目内容（简要）
         */
        private String questionContent;

        /**
         * 学生答案
         */
        private String studentAnswer;

        /**
         * 学生得分
         */
        private BigDecimal score;

        /**
         * 题目满分
         */
        private BigDecimal questionScore;

        /**
         * 得分率
         */
        private BigDecimal scoreRate;

        /**
         * 对应课程目标编号
         */
        private Integer courseTargetNo;

        /**
         * 题目类型
         */
        private String questionType;

        /**
         * 难度等级
         */
        private String difficultyLevel;
    }

    /**
     * 成绩录入状态枚举
     */
    public enum ScoreEntryStatus {
        NOT_ENTERED("未录入", "成绩尚未录入"),
        PARTIALLY_ENTERED("部分录入", "部分成绩已录入"),
        FULLY_ENTERED("已录入", "成绩已完全录入"),
        SUBMITTED("已提交", "成绩已提交确认");

        private final String name;
        private final String description;

        ScoreEntryStatus(String name, String description) {
            this.name = name;
            this.description = description;
        }

        public String getName() {
            return name;
        }

        public String getDescription() {
            return description;
        }
    }
}
