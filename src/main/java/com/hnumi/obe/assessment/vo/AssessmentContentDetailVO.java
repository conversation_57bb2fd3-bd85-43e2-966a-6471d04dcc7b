package com.hnumi.obe.assessment.vo;

import lombok.Data;
import java.util.List;
import java.math.BigDecimal;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

/**
 * 一次考核的详细内容VO，聚合题目和答案
 */
@Data
public class AssessmentContentDetailVO {
    private Long assessmentId;
    private List<QuestionDetailVO> questions;

    @Data
    public static class QuestionDetailVO {
        @JsonSerialize(using = ToStringSerializer.class)
        private Long questionId;
        private String questionTopic;
        private String questionDetail;
        private Integer questionType;
        private BigDecimal questionScore;
        private Integer citationCount;
        private BigDecimal scoringRate;
        private Long pathId;
        private Long courseId;
        private List<AnswerDetailVO> answers;
    }

    @Data
    public static class AnswerDetailVO {
        /** 答案ID */
        @JsonSerialize(using = ToStringSerializer.class)
        private Long answerId;
        /** 题目ID */
        @JsonSerialize(using = ToStringSerializer.class)
        private Long questionId;
        private String questionAnswer;
        private Integer answerNo;
        private BigDecimal answerScore;
        private String courseObjectiveId;
        private Long courseId;
    }
}
