package com.hnumi.obe.assessment.vo;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 考核发布 视图对象（VO）
 * 
 * VO（View Object）用于展示层，把考核发布操作的结果数据封装起来
 * 主要用于：
 * 1. 展示发布操作的结果
 * 2. 返回发布的详细信息
 * 3. 前端展示优化
 */
@Data
public class AssessmentPublishVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 考核ID
     */
    private Long assessmentId;

    /**
     * 考核名称
     */
    private String assessmentName;

    /**
     * 发布类型：true-发布给全部教学任务，false-发布给指定教学任务
     */
    private Boolean publishToAll;

    /**
     * 发布的教学任务数量
     */
    private Integer publishedTaskCount;

    /**
     * 发布的教学任务ID列表
     */
    private List<Long> publishedTaskIds;

    /**
     * 发布说明
     */
    private String publishNote;

    /**
     * 发布状态：0-发布成功，1-部分成功，2-发布失败
     */
    private Integer publishStatus;

    /**
     * 发布结果消息
     */
    private String publishMessage;

    /**
     * 发布时间
     */
    private LocalDateTime publishTime;

    /**
     * 发布者ID
     */
    private Long publisherId;
}
