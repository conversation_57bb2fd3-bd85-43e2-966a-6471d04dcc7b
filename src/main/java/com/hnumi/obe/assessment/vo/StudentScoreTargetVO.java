package com.hnumi.obe.assessment.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 学生成绩 视图对象（VO）
 * 
 * 用于展示学生的考核成绩信息
 */
@Data
public class StudentScoreTargetVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 学生ID
     */
    private Long studentId;

    /**
     * 学号
     */
    private String studentNumber;

    /**
     * 学生姓名
     */
    private String studentName;

    /**
     * 班级ID
     */
    private Long classId;

    /**
     * 班级名称
     */
    private String className;

    /**
     * 总成绩
     */
    private BigDecimal totalScore;

    /**
     * 总分
     */
    private BigDecimal fullScore;

    /**
     * 得分率
     */
    private BigDecimal scoreRate;

    /**
     * 成绩等级
     */
    private String scoreGrade;

    /**
     * 按课程目标分组的成绩数据
     */
    private List<CourseTargetScore> courseTargetScores;


    /**
     * 成绩录入时间
     */
    private LocalDateTime entryTime;

    /**
     * 最后修改时间
     */
    private LocalDateTime lastModifyTime;

    /**
     * 录入者ID
     */
    private Long entryUserId;

    /**
     * 录入者姓名
     */
    private String entryUserName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 课程目标成绩
     */
    @Data
    public static class CourseTargetScore {
        /**
         * 课程目标Id
         */
        private String objectiveId;
        /**
         * 课程目标编号
         */
        private Integer courseTargetNo;

        /**
         * 课程目标名称
         */
        private String courseTargetName;

        /**
         * 课程目标得分
         */
        private BigDecimal score;

        /**
         * 课程目标满分
         */
        private BigDecimal fullScore;

        /**
         * 课程目标得分率
         */
        private BigDecimal scoreRate;

        /**
         * 对应的毕业要求指标ID
         */
        private Long poId;

        /**
         * 毕业要求指标名称
         */
        private String poName;

        /**
         * 权重
         */
        private BigDecimal weight;
    }
}
