package com.hnumi.obe.assessment.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 成绩导入结果 视图对象（VO）
 * 
 * 用于展示成绩导入操作的结果
 */
@Data
public class ScoreImportResultVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 导入是否成功
     */
    private Boolean success;

    /**
     * 总记录数
     */
    private Integer totalCount;

    /**
     * 成功导入数
     */
    private Integer successCount;

    /**
     * 失败数
     */
    private Integer failCount;

    /**
     * 跳过数（重复记录等）
     */
    private Integer skipCount;

    /**
     * 导入结果消息
     */
    private String message;

    /**
     * 错误信息列表
     */
    private List<String> errorMessages;

    /**
     * 成功导入的学生信息
     */
    private List<ImportedStudentInfo> successList;

    /**
     * 失败的记录信息
     */
    private List<FailedRecordInfo> failList;

    /**
     * 创建成功结果
     */
    public static ScoreImportResultVO success(Integer totalCount, Integer successCount, Integer failCount, Integer skipCount) {
        ScoreImportResultVO result = new ScoreImportResultVO();
        result.setSuccess(true);
        result.setTotalCount(totalCount);
        result.setSuccessCount(successCount);
        result.setFailCount(failCount);
        result.setSkipCount(skipCount);
        result.setMessage(String.format("导入完成：总计%d条，成功%d条，失败%d条，跳过%d条", 
                totalCount, successCount, failCount, skipCount));
        return result;
    }

    /**
     * 创建失败结果
     */
    public static ScoreImportResultVO failure(Integer totalCount, List<String> errorMessages) {
        ScoreImportResultVO result = new ScoreImportResultVO();
        result.setSuccess(false);
        result.setTotalCount(totalCount);
        result.setSuccessCount(0);
        result.setFailCount(totalCount);
        result.setSkipCount(0);
        result.setErrorMessages(errorMessages);
        result.setMessage("导入失败");
        return result;
    }

    /**
     * 导入成功的学生信息
     */
    @Data
    public static class ImportedStudentInfo implements Serializable {
        private static final long serialVersionUID = 1L;
        
        private String studentNumber;
        private String studentName;
        private String className;
        private String score;
    }

    /**
     * 导入失败的记录信息
     */
    @Data
    public static class FailedRecordInfo implements Serializable {
        private static final long serialVersionUID = 1L;
        
        private Integer rowNumber;
        private String studentNumber;
        private String studentName;
        private String errorMessage;
    }
}
