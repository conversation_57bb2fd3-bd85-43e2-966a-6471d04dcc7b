package com.hnumi.obe.assessment.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 教师教学任务 视图对象（VO）
 * 
 * 用于展示教师负责的教学任务信息
 */
@Data
public class TeacherTaskVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 教学任务ID
     */
    private Long taskId;

    /**
     * 教学任务名称
     */
    private String taskName;

    /**
     * 课程ID
     */
    private Long courseId;

    /**
     * 课程名称
     */
    private String courseName;

    /**
     * 课程代码
     */
    private String courseCode;

    /**
     * 课程序号
     */
    private Integer taskNumber;

    /**
     * 授课年份
     */
    private Integer taskYear;

    /**
     * 授课学期
     */
    private Integer taskTerm;

    /**
     * 授课周数
     */
    private Integer teachWeek;

    /**
     * 周学时
     */
    private Integer weekHours;

    /**
     * 总学时
     */
    private Integer totalHours;

    /**
     * 教学任务状态
     * 0-进行中，1-已结束
     */
    private Integer taskStatus;

    /**
     * 教学任务状态名称
     */
    private String taskStatusName;

    /**
     * 授课班级列表
     */
    private List<TaskClassInfo> classes;

    /**
     * 已发布的考核数量
     */
    private Integer publishedAssessmentCount;

    /**
     * 班级信息
     */
    @Data
    public static class TaskClassInfo implements Serializable {
        private static final long serialVersionUID = 1L;
        
        /**
         * 班级ID
         */
        private Long classId;
        
        /**
         * 班级名称
         */
        private String className;
        
        /**
         * 学生人数
         */
        private Integer studentCount;
    }
}
