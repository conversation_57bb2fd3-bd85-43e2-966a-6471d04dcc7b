package com.hnumi.obe.assessment.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 教学任务考核VO
 */
@Data
public class AssessmentVO {

    /**
     * 记录id
     */
    private Long id;

    /**
     * 教学任务id，查task_worklist -> id，如果是-1，代表面向全体期末考核
     */
    private Long taskId;

    /**
     * 课程id，查tp_course -> id
     */
    private Long courseId;

    /**
     * 考核名称
     */
    private String assessmentName;

    /**
     * 考核描述
     */
    private String description;

    /**
     * 考核类型（对应教学大纲中配置考核方式的序号:作业、测验、期末等）
     */
    private Integer assessmentMethod;

    /**
     * 考核时间，json格式：{"startTime": "2023-10-01 08:00:00", "endTime": "2023-10-01 10:00:00"}
     */
    private String assessmentDate;

    /**
     * 考核权重
     */
    private BigDecimal assessmentWeight;

    /**
     * 考核试卷内容详情，json格式：[{ qid:题库题目id, qscore:本次考核设置分值,...}]
     */
    private List<DirectEntryConfig> assessmentDetailList;

    /**
     * 考核年份
     */
    private Integer assessmentYear;

    /**
     * 考核学期
     */
    private Integer assessmentTerm;

    /**
     * 成绩录入方式（0:直接录入方式；1:详细录入方式）
     */
    private Integer scoreType;


    private Integer assessmentStatus; // 考核状态：0配置中，1编辑中，2进行中，3已结束

    private boolean achievement; // 是否参与达成度计算

    /**
     * 记录状态{0:正常状态；-1:删除状态；}
     */
    private Integer status;

    /**
     * 记录创建者
     */
    private Long creator;

    /**
     * 记录创建时间
     */
    private LocalDateTime createTime;

    /**
     * 记录最后修改者
     */
    private Long modifier;

    /**
     * 记录最后修改时间
     */
    private LocalDateTime modifyTime;
}
