package com.hnumi.obe.assessment.vo;

import lombok.Data;
import java.math.BigDecimal;

/**
 * 考核目标成绩数据 视图对象
 */
@Data
public class AssessmentTargetScoreVO {

    /**
     * 考核ID
     */
    private Long assessmentId;

    /**
     * 课程ID
     */
    private Long courseId;

    /**
     * 考核名称
     */
    private String assessmentName;

    /**
     * 考核方式
     */
    private Integer assessmentMethod;

    /**
     * 考核权重
     */
    private BigDecimal assessmentWeight;

    /**
     * 考核学期
     */
    private Integer assessmentTerm;

    /**
     * 考核年份
     */
    private Integer assessmentYear;

    /**
     * 课程目标编号
     */
    private Integer courseTargetNo;

    /**
     * 平均分
     */
    private Double averageScore;

    /**
     * 学生数量
     */
    private Integer studentCount;
}
