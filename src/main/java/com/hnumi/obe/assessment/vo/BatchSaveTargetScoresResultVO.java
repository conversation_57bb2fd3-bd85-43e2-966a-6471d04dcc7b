package com.hnumi.obe.assessment.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 批量保存学生课程目标成绩结果VO
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
public class BatchSaveTargetScoresResultVO implements Serializable {
    
    private static final long serialVersionUID = 1L;

    /**
     * 操作是否成功
     */
    private Boolean success;

    /**
     * 总记录数
     */
    private Integer totalCount;

    /**
     * 成功保存的记录数
     */
    private Integer successCount;

    /**
     * 新增的记录数
     */
    private Integer insertCount;

    /**
     * 更新的记录数
     */
    private Integer updateCount;

    /**
     * 失败的记录数
     */
    private Integer failureCount;

    /**
     * 失败记录详情
     */
    private List<FailureDetail> failureDetails = new ArrayList<>();

    /**
     * 操作消息
     */
    private String message;

    /**
     * 失败详情
     */
    @Data
    public static class FailureDetail implements Serializable {
        
        private static final long serialVersionUID = 1L;

        /**
         * 学生ID
         */
        private Long studentId;

        /**
         * 课程目标编号
         */
        private Integer courseTargetNo;

        /**
         * 题目答案ID
         */
        private Long repositoryAnswerId;

        /**
         * 失败原因
         */
        private String reason;

        /**
         * 错误详情
         */
        private String errorDetail;
    }

    /**
     * 创建成功结果
     */
    public static BatchSaveTargetScoresResultVO success(Integer totalCount, Integer insertCount, Integer updateCount) {
        BatchSaveTargetScoresResultVO result = new BatchSaveTargetScoresResultVO();
        result.setSuccess(true);
        result.setTotalCount(totalCount);
        result.setSuccessCount(insertCount + updateCount);
        result.setInsertCount(insertCount);
        result.setUpdateCount(updateCount);
        result.setFailureCount(0);
        result.setMessage("批量保存成功");
        return result;
    }

    /**
     * 创建部分成功结果
     */
    public static BatchSaveTargetScoresResultVO partialSuccess(Integer totalCount, Integer insertCount, 
                                                              Integer updateCount, List<FailureDetail> failures) {
        BatchSaveTargetScoresResultVO result = new BatchSaveTargetScoresResultVO();
        result.setSuccess(true);
        result.setTotalCount(totalCount);
        result.setSuccessCount(insertCount + updateCount);
        result.setInsertCount(insertCount);
        result.setUpdateCount(updateCount);
        result.setFailureCount(failures.size());
        result.setFailureDetails(failures);
        result.setMessage(String.format("批量保存完成，成功%d条，失败%d条", insertCount + updateCount, failures.size()));
        return result;
    }

    /**
     * 创建失败结果
     */
    public static BatchSaveTargetScoresResultVO failure(String message) {
        BatchSaveTargetScoresResultVO result = new BatchSaveTargetScoresResultVO();
        result.setSuccess(false);
        result.setTotalCount(0);
        result.setSuccessCount(0);
        result.setInsertCount(0);
        result.setUpdateCount(0);
        result.setFailureCount(0);
        result.setMessage(message);
        return result;
    }
}
