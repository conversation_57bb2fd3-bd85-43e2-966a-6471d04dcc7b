package com.hnumi.obe.assessment.vo;

import lombok.Data;
import java.math.BigDecimal;
import java.util.List;

/**
 * VO for representing a student's course target scores.
 */
@Data
public class StudentCourseTargetScoreVO {
    private String studentId;
    private String studentName;
    private String studentNumber;
    private Long CourseId;
    private List<TargetScoreVO> targetScores;

    @Data
    public static class TargetScoreVO {
        private String courseTargetNo; // 课程目标编号
        private String objectiveId;// 课程目标ID
        private String poId; // 对应专业的毕业要求ID
        private String courseTargetName; // 课程目标名称
        private BigDecimal totalScore; // 该目标总分
        private List<MethodScoreVO> methodScores; // 按考核方式细分得分
    }

    @Data
    public static class MethodScoreVO {
        private Integer assessmentMethod; // 考核方式
        private BigDecimal score; // 该方式下得分
    }
}