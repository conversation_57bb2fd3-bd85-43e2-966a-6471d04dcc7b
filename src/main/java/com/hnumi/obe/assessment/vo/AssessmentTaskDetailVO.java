package com.hnumi.obe.assessment.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 考核关联教学任务详情VO
 * 
 * 用于展示考核已发布的教学任务详情，包括班级信息和成绩录入统计
 */
@Data
public class AssessmentTaskDetailVO {

    /**
     * 教学任务ID
     */
    private Long taskId;

    /**
     * 教学任务名称
     */
    private String taskName;

    /**
     * 教学任务编号
     */
    private Integer taskNumber;

    /**
     * 教学任务状态
     */
    private Integer taskStatus;

    /**
     * 教学任务状态名称
     */
    private String taskStatusName;

    /**
     * 课程ID
     */
    private Long courseId;

    /**
     * 课程名称
     */
    private String courseName;

    /**
     * 课程代码
     */
    private String courseCode;

    /**
     * 学年
     */
    private String taskYear;

    /**
     * 学期
     */
    private Integer taskTerm;

    /**
     * 学期名称
     */
    private String taskTermName;

    /**
     * 关联班级信息列表
     */
    private List<TaskClassInfo> classes;

    /**
     * 成绩录入统计信息
     */
    private ScoreStatistics scoreStatistics;

    /**
     * 成绩提交状态
     */
    private ScoreSubmissionStatus submissionStatus;

    /**
     * 发布时间
     */
    private LocalDateTime publishTime;

    /**
     * 班级信息
     */
    @Data
    public static class TaskClassInfo {
        /**
         * 班级ID
         */
        private Long classId;

        /**
         * 班级名称
         */
        private String className;

        /**
         * 班级人数
         */
        private Integer studentCount;

        /**
         * 班级已录入成绩人数
         */
        private Integer scoredCount;

        /**
         * 班级未录入成绩人数
         */
        private Integer unscoredCount;

        /**
         * 班级成绩录入进度
         */
        private BigDecimal scoreProgress;
    }

    /**
     * 成绩录入统计信息
     */
    @Data
    public static class ScoreStatistics {
        /**
         * 总学生人数
         */
        private Integer totalStudentCount;

        /**
         * 已录入成绩人数
         */
        private Integer scoredStudentCount;

        /**
         * 未录入成绩人数
         */
        private Integer unscoredStudentCount;

        /**
         * 录入进度百分比
         */
        private BigDecimal scoreProgress;

        /**
         * 平均分
         */
        private BigDecimal averageScore;

        /**
         * 最高分
         */
        private BigDecimal maxScore;

        /**
         * 最低分
         */
        private BigDecimal minScore;

        /**
         * 及格率
         */
        private BigDecimal passRate;


    }

    /**
     * 成绩提交状态枚举
     */
    public enum ScoreSubmissionStatus {
        NOT_STARTED("未开始", "还未开始录入成绩"),
        IN_PROGRESS("录入中", "正在录入成绩"),
        COMPLETED("已完成", "成绩录入已完成");

        private final String name;
        private final String description;

        ScoreSubmissionStatus(String name, String description) {
            this.name = name;
            this.description = description;
        }

        public String getName() {
            return name;
        }

        public String getDescription() {
            return description;
        }
    }
}
