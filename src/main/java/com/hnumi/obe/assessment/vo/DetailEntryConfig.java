package com.hnumi.obe.assessment.vo;

import lombok.Data;
import java.util.List;
import java.math.BigDecimal;

/**
 * 详细录入配置
 * 存储一次考核的详细题目和答案数据，以及当前考核的详细数据
 */
@Data
public class DetailEntryConfig {
    /** 当前考核ID */
    private Long assessmentId;
    /** 当前考核名称 */
    private String assessmentName;
    /** 当前考核总分 */
    private BigDecimal totalScore;
    /** 题目列表 */
    private List<QuestionDetail> questions;

    @Data
    public static class QuestionDetail {
        /** 题目ID */
        private Long questionId;
        /** 题目序号 */
        private Integer questionNo;
        /** 题目题干 */
        private String questionTopic;
        /** 题目详情（JSON字符串） */
        private String questionDetail;
        /** 题目类型 */
        private Integer questionType;
        /** 题目分数 */
        private BigDecimal questionScore;
        /** 答案列表 */
        private List<AnswerDetail> answers;
    }

    @Data
    public static class AnswerDetail {
        /** 答案ID */
        private Long answerId;
        /** 答案内容 */
        private String questionAnswer;
        /** 答案序号 */
        private Integer answerNo;
        /** 当前答案得分 */
        private BigDecimal answerScore;
        /** 课程目标ID */
        private String courseObjectiveId;
    }
}
