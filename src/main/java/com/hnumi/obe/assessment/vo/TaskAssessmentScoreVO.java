package com.hnumi.obe.assessment.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 教学任务考核 视图对象（VO）
 * 
 * 用于展示教学任务下的考核信息
 */
@Data
public class TaskAssessmentScoreVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 考核ID
     */
    private Long assessmentId;

    /**
     * 考核名称
     */
    private String assessmentName;

    /**
     * 考核描述
     */
    private String description;

    /**
     * 考核类型
     */
    private Integer assessmentMethod;

    /**
     * 考核类型名称
     */
    private String assessmentMethodName;

    /**
     * 考核权重
     */
    private BigDecimal assessmentWeight;

    /**
     * 考核时间（JSON格式）
     */
    private String assessmentDate;

    /**
     * 考核状态
     * 0-配置中，1-编辑中，2-进行中，3-已结束
     */
    private Integer assessmentStatus;

    /**
     * 考核状态名称
     */
    private String assessmentStatusName;

    /**
     * 成绩录入方式
     * 0-直接录入方式，1-详细录入方式
     */
    private Integer scoreType;

    /**
     * 成绩录入方式名称
     */
    private String scoreTypeName;

    /**
     * 教学任务ID,可选参数，如果是某个老师的教学任务下的考核，则返回教学任务ID，否则返回null
     */
    private Long taskId;

    /**
     * 课程ID
     */
    private Long courseId;

    /**
     * 已录入成绩的学生数量
     */
    private Integer scoredStudentCount;

    /**
     * 总学生数量
     */
    private Integer totalStudentCount;

    /**
     * 成绩录入进度（百分比）
     */
    private BigDecimal scoreProgress;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime modifyTime;
}
