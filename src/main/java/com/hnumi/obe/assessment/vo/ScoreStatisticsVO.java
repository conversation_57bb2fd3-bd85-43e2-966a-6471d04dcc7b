package com.hnumi.obe.assessment.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 成绩统计 视图对象（VO）
 * 
 * 用于展示考核成绩的统计分析信息
 */
@Data
public class ScoreStatisticsVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 考核ID
     */
    private Long assessmentId;

    /**
     * 考核名称
     */
    private String assessmentName;

    /**
     * 教学任务ID
     */
    private Long taskId;

    /**
     * 总学生数
     */
    private Integer totalStudentCount;

    /**
     * 已录入成绩学生数
     */
    private Integer scoredStudentCount;

    /**
     * 未录入成绩学生数
     */
    private Integer unscoredStudentCount;

    /**
     * 平均分
     */
    private BigDecimal averageScore;

    /**
     * 最高分
     */
    private BigDecimal maxScore;

    /**
     * 最低分
     */
    private BigDecimal minScore;

    /**
     * 中位数
     */
    private BigDecimal medianScore;

    /**
     * 标准差
     */
    private BigDecimal standardDeviation;

    /**
     * 及格率（百分比）
     */
    private BigDecimal passRate;

    /**
     * 优秀率（百分比）
     */
    private BigDecimal excellentRate;

    /**
     * 良好率（百分比）
     */
    private BigDecimal goodRate;

    /**
     * 分数段分布
     */
    private List<ScoreRangeDistribution> scoreDistribution;

    /**
     * 班级成绩统计
     */
    private List<ClassScoreStatistics> classStatistics;

    /**
     * 分数段分布
     */
    @Data
    public static class ScoreRangeDistribution implements Serializable {
        private static final long serialVersionUID = 1L;
        
        /**
         * 分数段名称（如：90-100、80-89等）
         */
        private String scoreRange;
        
        /**
         * 该分数段学生数量
         */
        private Integer studentCount;
        
        /**
         * 该分数段占比（百分比）
         */
        private BigDecimal percentage;
    }

    /**
     * 班级成绩统计
     */
    @Data
    public static class ClassScoreStatistics implements Serializable {
        private static final long serialVersionUID = 1L;
        
        /**
         * 班级ID
         */
        private Long classId;
        
        /**
         * 班级名称
         */
        private String className;
        
        /**
         * 班级学生总数
         */
        private Integer totalCount;
        
        /**
         * 已录入成绩数
         */
        private Integer scoredCount;
        
        /**
         * 班级平均分
         */
        private BigDecimal averageScore;
        
        /**
         * 班级最高分
         */
        private BigDecimal maxScore;
        
        /**
         * 班级最低分
         */
        private BigDecimal minScore;
        
        /**
         * 班级及格率
         */
        private BigDecimal passRate;
    }
}
