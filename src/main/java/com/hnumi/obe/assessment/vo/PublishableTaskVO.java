package com.hnumi.obe.assessment.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 可发布教学任务 视图对象（VO）
 * 
 * VO（View Object）用于展示层，把可发布的教学任务信息封装起来
 * 主要用于：
 * 1. 展示可发布的教学任务列表
 * 2. 包含教学任务的基本信息
 * 3. 包含关联的班级和教师信息
 * 4. 前端选择发布目标时使用
 */
@Data
public class PublishableTaskVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 教学任务ID
     */
    private Long taskId;

    /**
     * 教学任务名称
     */
    private String taskName;

    /**
     * 课程ID
     */
    private Long courseId;

    /**
     * 课程名称
     */
    private String courseName;

    /**
     * 课程序号
     */
    private Integer taskNumber;

    /**
     * 授课年份
     */
    private Integer taskYear;

    /**
     * 授课学期
     */
    private Integer taskTerm;

    /**
     * 授课周数
     */
    private Integer teachWeek;

    /**
     * 周学时
     */
    private Integer weekHours;

    /**
     * 总学时
     */
    private Integer totalHours;

    /**
     * 授课班级信息列表
     */
    private List<TaskClassInfo> classes;

    /**
     * 授课教师信息列表
     */
    private List<TaskTeacherInfo> teachers;

    /**
     * 是否已发布（该考核是否已发布给此教学任务）
     */
    private Boolean isPublished;

    /**
     * 教学任务班级信息
     */
    @Data
    public static class TaskClassInfo implements Serializable {
        private static final long serialVersionUID = 1L;
        
        /**
         * 班级ID
         */
        private Long classId;
        
        /**
         * 班级名称
         */
        private String className;
        
        /**
         * 学生人数
         */
        private Integer studentCount;
    }

    /**
     * 教学任务教师信息
     */
    @Data
    public static class TaskTeacherInfo implements Serializable {
        private static final long serialVersionUID = 1L;
        
        /**
         * 教师ID
         */
        private Long teacherId;
        
        /**
         * 教师姓名
         */
        private String teacherName;
        
        /**
         * 教师角色
         */
        private Integer role;
        
        /**
         * 教师角色名称
         */
        private String roleName;
    }
}
