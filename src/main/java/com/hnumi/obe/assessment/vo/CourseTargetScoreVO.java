package com.hnumi.obe.assessment.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 课程目标达成度数据展示VO
 */
@Data
public class CourseTargetScoreVO implements Serializable {
    /**
     * 课程ID
     */
    private Long courseId;

    /**
     * 课程代码
     */
    private String courseCode;

    /**
     * 课程名称
     */
    private String courseName;

    /**
     * 雷达图数据
     */
    private RadarChartData radarChartData;

    /**
     * 柱状图数据
     */
    private BarChartData barChartData;

    /**
     * 趋势图数据
     */
    private TrendChartData trendChartData;

    /**
     * 考核年份
     */
    private Integer assessmentYear;

    /**
     * 考核学期
     */
    private Integer assessmentTerm;

    /**
     * 课程目标编号
     */
    private Integer courseTargetNo;

    /**
     * 课程目标平均分
     */
    private BigDecimal averageScore;

    /**
     * 累计达成度得分 (用于趋势分析)
     */
    private BigDecimal cumulativeScore;

    /**
     * 课程配置信息
     */
    private CourseConfig courseConfig;

    /**
     * 雷达图数据结构
     */
    @Data
    public static class RadarChartData {
        /**
         * 课程目标达成度雷达图数据
         * key: 课程目标ID
         * value: 达成度值
         */
        private Map<String, Double> courseTargetAchievement;

        /**
         * 毕业要求达成度雷达图数据
         * key: 毕业要求ID
         * value: 达成度值
         */
        private Map<String, Double> graduationRequirementAchievement;
    }

    /**
     * 柱状图数据结构
     */
    @Data
    public static class BarChartData {
        /**
         * 课程目标达成度柱状图数据
         * key: 课程目标ID
         * value: 达成度值
         */
        private Map<String, Double> courseTargetAchievement;

        /**
         * 毕业要求达成度柱状图数据
         * key: 毕业要求ID
         * value: 达成度值
         */
        private Map<String, Double> graduationRequirementAchievement;
    }

    /**
     * 趋势图数据结构
     */
    @Data
    public static class TrendChartData {
        /**
         * 按年份统计的课程目标达成度趋势
         * 外层Map的key是课程目标ID
         * 内层Map的key是年份，value是该年份的达成度值
         */
        private Map<String, Map<Integer, Double>> courseTargetTrends;

        /**
         * 统计的年份列表
         */
        private List<Integer> years;

        /**
         * 课程目标名称映射
         * key: 课程目标ID
         * value: 课程目标名称
         */
        private Map<String, String> courseTargetNames;
    }

    /**
     * 课程配置信息
     */
    @Data
    public static class CourseConfig implements Serializable {
        /**
         * 课程ID
         */
        private Long courseId;

        private String courseCode;

        /**
         * 考核权重配置(JSON字符串)
         * 格式: {"1": 0.3, "2": 0.3, "3": 0.4}
         * key为考核方式，value为权重
         */
        private String assessmentWeight;

        /**
         * 课程目标配置(JSON字符串)
         * 格式参考tp_course表的course_target字段
         */
        private String courseTarget;

        /**
         * 课程目标达成度权重配置
         * 格式: {"1": 0.3, "2": 0.3, "3": 0.4}
         * key为课程目标编号，value为权重
         */
        private String targetWeight;


    }
}
