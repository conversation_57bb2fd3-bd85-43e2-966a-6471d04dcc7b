package com.hnumi.obe.assessment.controller;

import com.hnumi.obe.assessment.service.IAchievementAnalysisService;
import com.hnumi.obe.assessment.vo.CourseTargetScoreVO;
import com.hnumi.obe.assessment.vo.StudentCourseTargetScoreVO;
import com.hnumi.obe.common.entity.R;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 课程目标达成度分析 控制器
 */
@Slf4j
@RestController
@RequestMapping("/teaching/achievement")
public class AchievementAnalysisController {

    @Autowired
    private IAchievementAnalysisService achievementAnalysisService;

    /**
     * 获取课程达成度分析数据
     */
    @GetMapping("/analysis/{courseCode}")
    public R<CourseTargetScoreVO> getTargetAchievementAnalysis(@PathVariable String courseCode) {
        try {
            CourseTargetScoreVO result = achievementAnalysisService.getTargetAchievementAnalysis(courseCode);
            return R.ok(result);
        } catch (Exception e) {
            log.error("获取课程达成度分析数据失败", e);
            return R.fail(500, "获取失败：" + e.getMessage());
        }
    }

    /**
     * 获取课程达成度趋势数据
     */
    @GetMapping("/trend/{courseCode}")
    public R<List<CourseTargetScoreVO>> getTargetAchievementTrend(@PathVariable String courseCode) {
        try {
            List<CourseTargetScoreVO> result = achievementAnalysisService.getTargetAchievementTrend(courseCode);
            return R.ok(result);
        } catch (Exception e) {
            log.error("获取课程达成度趋势数据失败", e);
            return R.fail(500, "获取失败：" + e.getMessage());
        }
    }

    /**
     * 计算单个课程的达成度结果
     */
    @GetMapping("/calculate/{courseId}")
    public R<CourseTargetScoreVO> calculateCourseAchievement(
            @PathVariable Long courseId,
            @RequestParam(defaultValue = "true") boolean useWeights) {
        try {
            CourseTargetScoreVO result = achievementAnalysisService.calculateCourseAchievement(courseId, useWeights);
            return R.ok(result);
        } catch (Exception e) {
            log.error("计算课程达成度结果失败", e);
            return R.fail(500, "计算失败：" + e.getMessage());
        }
    }

    /**
     * /target-scores/course/${courseId}`
     * 根据课程ID查询学生课程目标成绩
     */
//    @GetMapping("/target-scores/course/{courseId}")
//    public R<List<StudentCourseTargetScoreVO>> getStudentCourseTargetScores(@PathVariable Long courseId) {
//        try {
//            List<StudentCourseTargetScoreVO> result = assessmentScoreService.getStudentCourseTargetScores(courseId);
//            return R.ok(result);
//        } catch (Exception e) {
//            log.error("查询学生课程目标达成度失败", e);
//            return R.fail(500, "查询失败：" + e.getMessage());
//        }
//    }
}
