package com.hnumi.obe.assessment.controller;

import com.alibaba.excel.EasyExcel;
import com.hnumi.obe.assessment.dto.*;
import com.hnumi.obe.assessment.service.IAssessmentScoreService;
import com.hnumi.obe.assessment.vo.*;
import com.hnumi.obe.common.entity.R;
//import com.hnumi.obe.common.validation.ValidGroup;
import com.hnumi.obe.common.util.RequestUtil;
import com.hnumi.obe.common.valid.ValidGroup;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.List;

/**
 * 考核成绩管理 控制器
 *
 * 提供考核成绩管理的完整功能：
 * 1. 教师教学任务查询
 * 2. 教学任务考核列表查询
 * 3. 学生成绩录入（批量和单个）
 * 4. 学生成绩查询
 * 5. 成绩统计分析
 * 6. 成绩导入导出
 */
@Slf4j
@RestController
@RequestMapping("/teaching/assessment/score")
public class AssessmentScoreController {

    @Autowired
    private IAssessmentScoreService assessmentScoreService;

    /**
     * 查询教师负责的教学任务列表
     */
    @PostMapping("/teacher/tasks")
    public R<List<TeacherTaskVO>> getTeacherTasks(@Validated @RequestBody TeacherTaskQueryDTO queryDTO) {
        List<TeacherTaskVO> taskList = assessmentScoreService.getTeacherTasks(queryDTO);
        return R.ok(taskList);
    }

    /**
     * 查询某个教师的某个教学任务下的考核列表
     */
    @GetMapping("/task/{taskId}/assessments")
    public R<List<TaskAssessmentScoreVO>> getTaskAssessments(@PathVariable Long taskId,
                                                             @RequestParam Long teacherId) {
        List<TaskAssessmentScoreVO> assessmentList = assessmentScoreService.getTaskAssessments(taskId, teacherId);
        return R.ok(assessmentList);
    }



    //================== 新增的考核任务查询与成绩录入等核心业务功能接口 ==================
    /**
     * 根据考核id（assessmentId）查询已发布的所有教学任务的详情，供成绩管理调用
     * 教学任务名称、教学任务对应的班级信息，包括班级名称、班级人数，成绩提交状态、已录入人数、未录入人数
     * 查询考核关联的教学任务详情
     */
    @GetMapping("/related-tasks/{assessmentId}")
    public R<List<AssessmentTaskDetailVO>> getAssessmentTaskDetails(@PathVariable Long assessmentId) {
        try {
            //List<AssessmentTaskDetailVO> result = assessmentScoreService.getAssessmentTaskDetails(assessmentId);
            List<AssessmentTaskDetailVO> result = assessmentScoreService.getAssessmentTaskDetails(assessmentId);
            return R.ok(result);
        } catch (Exception e) {
            log.error("查询考核关联教学任务详情失败", e);
            return R.fail(500,"查询失败：" + e.getMessage());
        }
    }

    /**
     * 根据考核id（assessmentId）与教学任务id（taskId）查询学生课程目标成绩
     * 学生名单，学生对应课程目标的成绩列表（返回StudentScoreTargetVO格式）
     * 查询学生课程目标成绩
     */
    @GetMapping("/{assessmentId}/task/{taskId}/scores")
    public R<List<StudentScoreTargetVO>> getStudentTargetScoresByTaskId(
            @PathVariable Long assessmentId,
            @PathVariable Long taskId) {
        try {
            List<StudentScoreTargetVO> result = assessmentScoreService.getStudentTargetScoresByTaskId(assessmentId, taskId);
            log.info("查询学生课程目标成绩成功，数量: {}", result.size());
            return R.ok(result);
        } catch (Exception e) {
            log.error("查询学生课程目标成绩失败", e);
            return R.fail(500,"查询失败：" + e.getMessage());
        }
    }

    /**
     * 查询单个学生成绩详情
     */
    @GetMapping("/{assessmentId}/task/{taskId}/student/{studentId}/score")
    public R<StudentScoreDetailVO> getStudentScoreDetail(
            @PathVariable Long assessmentId,
            @PathVariable Long taskId,
            @PathVariable Long studentId) {
        try {
            StudentScoreDetailVO result = assessmentScoreService.getStudentScoreDetail(assessmentId, taskId, studentId);
            return R.ok(result);
        } catch (Exception e) {
            log.error("查询单个学生成绩详情失败", e);
            return R.fail(500,"查询失败：" + e.getMessage());
        }
    }

    /**
     * /target-scores/course/${courseId}`
     * 根据课程ID查询学生课程目标成绩
     */
    @GetMapping("/target-scores/course/{courseId}")
    public R<List<StudentCourseTargetScoreVO>> getStudentCourseTargetScores(@PathVariable Long courseId) {
        try {
            List<StudentCourseTargetScoreVO> result = assessmentScoreService.getStudentCourseTargetScores(courseId);
            return R.ok(result);
        } catch (Exception e) {
            log.error("查询学生课程目标达成度失败", e);
            return R.fail(500, "查询失败：" + e.getMessage());
        }
    }

    //===================保存成绩（课程目标和考核详情）的入口 ====================
    /**
     * 保存一组学生的不同课程目标的成绩
     */

    /**
     * 批量保存学生课程目标成绩
     *
     * @param batchSaveDTO 批量保存请求数据
     * @return 保存结果
     */
    @PostMapping("/batch-save-target-scores")
    @Validated(ValidGroup.Add.class)
    public R<BatchSaveTargetScoresResultVO> batchSaveTargetScores(
            @RequestBody @Validated(ValidGroup.Add.class) BatchSaveTargetScoresDTO batchSaveDTO) {
        try {
            // 获取当前登录教师ID（这里需要根据实际的认证方式获取）
            Long teacherId = RequestUtil.getExtendId();

            BatchSaveTargetScoresResultVO result = assessmentScoreService.batchSaveTargetScores(batchSaveDTO, teacherId);

            if (result.getSuccess()) {
                return R.ok(result);
            } else {
                return R.fail(400, String.valueOf(result));
            }

        } catch (IllegalArgumentException e) {
            log.warn("批量保存学生课程目标成绩参数错误", e);
            return R.fail(400, e.getMessage());
        } catch (Exception e) {
            log.error("批量保存学生课程目标成绩失败", e);
            return R.fail(500, "保存失败：" + e.getMessage());
        }
    }

    //================== 扩展的课程目标和考核详情的成绩导入模式接口 ==================

    /**
     * 按课程目标导入成绩，同batchSaveTargetScores功能一致，可忽略，暂没使用
     */
    @PostMapping("/import/target")
    public R<ScoreImportResultVO> importScoresByTarget(@Validated(ValidGroup.Add.class) @RequestBody ScoreTargetImportDTO importDTO,
                                                       @RequestParam Long teacherId) {
        ScoreImportResultVO result = assessmentScoreService.importScoresByTarget(importDTO, teacherId);
        return R.ok(result);
    }

    /**
     * 按考核详情导入成绩
     */
    @PostMapping("/import/detail")
    public R<ScoreImportResultVO> importScoresByDetail(@Validated(ValidGroup.Add.class) @RequestBody ScoreDetailImportDTO importDTO,
                                                       @RequestParam Long teacherId) {
        ScoreImportResultVO result = assessmentScoreService.importScoresByDetail(importDTO, teacherId);
        return R.ok(result);
    }

    /**
     * Excel导入课程目标成绩
     */
    @PostMapping("/import/target/excel/{assessmentId}/{taskId}")
    public R<ScoreImportResultVO> importTargetScoresFromExcel(@RequestParam("file") MultipartFile file,
                                                              @PathVariable Long assessmentId,
                                                              @PathVariable Long taskId) {
        try {
            ScoreImportResultVO result = assessmentScoreService.importTargetScoresFromExcel(
                    assessmentId, taskId, file.getInputStream(), RequestUtil.getExtendId());
            return R.ok(result);
        } catch (Exception e) {
            log.error("Excel导入课程目标成绩失败", e);
            return R.fail(500,"导入失败：" + e.getMessage());
        }
    }

    /**
     * Excel导入考核详情成绩
     */
    @PostMapping("/import/detail/excel")
    public R<ScoreImportResultVO> importDetailScoresFromExcel(@RequestParam("file") MultipartFile file,
                                                              @RequestParam Long assessmentId,
                                                              @RequestParam Long taskId,
                                                              @RequestParam Long teacherId) {
        try {
            ScoreImportResultVO result = assessmentScoreService.importDetailScoresFromExcel(
                    assessmentId, taskId, file.getInputStream(), teacherId);
            return R.ok(result);
        } catch (Exception e) {
            log.error("Excel导入考核详情成绩失败", e);
            return R.fail(500,"导入失败：" + e.getMessage());
        }
    }

    /**
     * Excel导入直接录入模式成绩
     */
    @PostMapping("/import/direct-entry/excel/{assessmentId}/{taskId}")
    public R<ScoreImportResultVO> importDirectEntryScoresFromExcel(@RequestParam("file") MultipartFile file,
                                                                   @PathVariable Long assessmentId,
                                                                   @PathVariable Long taskId) {
        try {
            // 获取当前登录教师ID
            Long teacherId = RequestUtil.getExtendId();
            
            ScoreImportResultVO result = assessmentScoreService.importTargetScoresFromExcel(
                    assessmentId, taskId, file.getInputStream(), teacherId);
            return R.ok(result);
        } catch (Exception e) {
            log.error("Excel导入直接录入模式成绩失败", e);
            return R.fail(500,"导入失败：" + e.getMessage());
        }
    }

    /**
     * 导出课程目标成绩模板
     */
    @GetMapping("/export/target/template/{assessmentId}/task/{taskId}")
    public void exportTargetScoreTemplate(@PathVariable Long assessmentId,
                                          @PathVariable Long taskId,
                                          //@RequestParam Long teacherId,
                                          HttpServletResponse response) throws IOException {
        // 设置响应头
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("课程目标成绩录入模板", "UTF-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");

        // 获取模板数据
        List<ScoreTargetExcelImportDTO> templateData = assessmentScoreService.exportTargetScoreTemplate(assessmentId, taskId, RequestUtil.getExtendId());

        // 写入Excel
        EasyExcel.write(response.getOutputStream(), ScoreTargetExcelImportDTO.class)
                .sheet("课程目标成绩录入模板")
                .doWrite(templateData);
    }

    /**
     * 导出考核详情成绩模板
     */
    @GetMapping("/export/detail/template")
    public void exportDetailScoreTemplate(@RequestParam Long assessmentId,
                                          @RequestParam Long taskId,
                                          @RequestParam Long teacherId,
                                          HttpServletResponse response) throws IOException {
        // 设置响应头
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("考核详情成绩录入模板", "UTF-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");

        // 获取模板数据
        List<ScoreDetailExcelImportDTO> templateData = assessmentScoreService.exportDetailScoreTemplate(assessmentId, taskId, teacherId);

        // 写入Excel
        EasyExcel.write(response.getOutputStream(), ScoreDetailExcelImportDTO.class)
                .sheet("考核详情成绩录入模板")
                .doWrite(templateData);
    }

    /**
     * 导出课程目标成绩
     */
    @GetMapping("/export/target")
    public void exportTargetScores(@RequestParam Long assessmentId,
                                   @RequestParam Long taskId,
                                   @RequestParam Long teacherId,
                                   HttpServletResponse response) throws IOException {
        // 设置响应头
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("课程目标成绩", "UTF-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");

        // 获取成绩数据
        List<ScoreTargetExcelImportDTO> scoreData = assessmentScoreService.exportTargetScores(assessmentId, taskId, teacherId);

        // 写入Excel
        EasyExcel.write(response.getOutputStream(), ScoreTargetExcelImportDTO.class)
                .sheet("课程目标成绩")
                .doWrite(scoreData);
    }

    /**
     * 导出考核详情成绩
     */
    @GetMapping("/export/detail")
    public void exportDetailScores(@RequestParam Long assessmentId,
                                   @RequestParam Long taskId,
                                   @RequestParam Long teacherId,
                                   HttpServletResponse response) throws IOException {
        // 设置响应头
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("考核详情成绩", "UTF-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");

        // 获取成绩数据
        List<ScoreDetailExcelImportDTO> scoreData = assessmentScoreService.exportDetailScores(assessmentId, taskId, teacherId);

        // 写入Excel
        EasyExcel.write(response.getOutputStream(), ScoreDetailExcelImportDTO.class)
                .sheet("考核详情成绩")
                .doWrite(scoreData);
    }




}
