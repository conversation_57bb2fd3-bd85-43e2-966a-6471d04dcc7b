package com.hnumi.obe.assessment.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hnumi.obe.assessment.dto.AssessmentDTO;
import com.hnumi.obe.assessment.dto.AssessmentPublishDTO;
import com.hnumi.obe.assessment.dto.AssessmentPublishQueryDTO;
import com.hnumi.obe.assessment.dto.AssessmentQueryDTO;
import com.hnumi.obe.assessment.service.IAssessmentContentService;
import com.hnumi.obe.assessment.service.IAssessmentService;
import com.hnumi.obe.assessment.service.IAssessmentTaskService;
import com.hnumi.obe.assessment.vo.AssessmentPublishVO;
import com.hnumi.obe.assessment.vo.AssessmentVO;
import com.hnumi.obe.assessment.vo.PublishableTaskVO;
import com.hnumi.obe.common.entity.R;
import com.hnumi.obe.common.valid.ValidGroup;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static cn.dev33.satoken.SaManager.log;

/**
 * 教学任务考核管理
 */
@RestController
@RequestMapping("/assessment")
public class AssessmentController {

    @Autowired
    private IAssessmentService assessmentService;
    @Autowired
    private IAssessmentContentService assessmentContentService;




    /**
     * 根据ID查询考核详情
     */
    @GetMapping("/{id}")
    public R<AssessmentVO> getAssessmentById(@PathVariable Long id) {
        AssessmentVO assessmentVO = assessmentService.getAssessmentById(id);
        return R.ok(assessmentVO);
    }


    /**
     * 新增考核
     */
    @PostMapping
    public R<AssessmentVO> saveAssessment(@Validated(ValidGroup.Add.class) @RequestBody AssessmentDTO assessmentDTO) {
        // 直接录入配置需要在新增考核时进行处理
        return R.ok(assessmentService.saveAssessment(assessmentDTO));
    }

    /**
     * 更新考核
     */
    @PutMapping
    public R<Boolean> updateAssessment(@Validated(ValidGroup.Update.class) @RequestBody AssessmentDTO assessmentDTO) {
        boolean result = assessmentService.updateAssessment(assessmentDTO);
        return R.ok(result);
    }





    //==================考核查询接口=================================
    /**
     * 分页查询考核列表，前端调用接口
     */
    @PostMapping("/page")
    public R<IPage<AssessmentVO>> pageAssessment(@RequestBody AssessmentQueryDTO queryDTO) {
        IPage<AssessmentVO> page = assessmentService.pageAssessment(queryDTO);
        return R.ok(page);
    }

    /**
     * 根据课程ID查询考核列表，考核年份和学期是课程进行中的学期
     */
    @GetMapping("/course/{courseId}")
    public R<List<AssessmentVO>> getAssessmentByCourseId(@PathVariable Long courseId) {
        List<AssessmentVO> assessmentList = assessmentService.getAssessmentByCourseId(courseId);
        return R.ok(assessmentList);
    }

    /**
     * 根据课程Code查询考核列表，这样会查询不同年份和学期的课程考核
     */
    @GetMapping("/course/{courseCode}")
    public R<List<AssessmentVO>> getAssessmentByCourseCode(@PathVariable String courseCode) {
        List<AssessmentVO> assessmentList = assessmentService.getAssessmentByCourseCode(courseCode);
        return R.ok(assessmentList);
    }



    //================== 考核物理删除相关接口 ==================

    /**
     * 物理删除考核及其所有关联数据
     */
    @DeleteMapping("/physical/{id}")
    public R<Boolean> physicalDeleteAssessment(@PathVariable Long id) {
        try {
            boolean result = assessmentContentService.physicalDeleteAssessment(id);
            if (result) {
                return R.ok(true);
            } else {
                return R.fail(500,"考核删除失败");
            }
        } catch (Exception e) {
            log.error("物理删除考核失败", e);
            return R.fail(500,"删除失败：" + e.getMessage());
        }
    }

    /**
     * 批量物理删除考核及其所有关联数据
     */
    @DeleteMapping("/physical/batch")
    public R<IAssessmentContentService.AssessmentDeleteResult> batchPhysicalDeleteAssessments(@RequestBody List<Long> assessmentIds) {
        try {
            IAssessmentContentService.AssessmentDeleteResult result = assessmentContentService.batchPhysicalDeleteAssessments(assessmentIds);

            if (result.getSuccessCount() == result.getTotalCount()) {
                return R.ok(result);
            } else if (result.getSuccessCount() > 0) {
                return R.ok(result);
            } else {
                return R.fail(500,"批量删除失败");
            }
        } catch (Exception e) {
            log.error("批量物理删除考核失败", e);
            return R.fail(500,"批量删除失败：" + e.getMessage());
        }
    }

    /**
     * 检查考核是否可以删除
     */
    @GetMapping("/check-deletable/{id}")
    public R<Boolean> checkAssessmentDeletable(@PathVariable Long id) {
        try {
            // 检查考核是否存在
            AssessmentVO assessment = assessmentService.getAssessmentById(id);
            if (assessment == null) {
                return R.fail(404,"考核不存在");
            }

            // 检查考核状态（可以根据业务需求添加更多检查条件）
            if (assessment.getAssessmentStatus() == 2) {
                return R.ok(false);
            }

            // 检查是否有已录入的成绩
            // 这里可以添加更多的业务规则检查

            return R.ok(true);
        } catch (Exception e) {
            log.error("检查考核删除条件失败", e);
            return R.fail(500,"检查失败：" + e.getMessage());
        }
    }

    /**
     * 删除考核
     */
    @DeleteMapping("/{id}")
    public R<Boolean> deleteAssessment(@PathVariable Long id) {
        boolean result = assessmentService.deleteAssessment(id);
        return R.ok(result);
    }

}
