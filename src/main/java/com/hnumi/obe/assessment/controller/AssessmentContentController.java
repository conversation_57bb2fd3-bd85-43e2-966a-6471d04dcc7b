package com.hnumi.obe.assessment.controller;

import com.hnumi.obe.assessment.dto.DetailEntryConfigDTO;
import com.hnumi.obe.assessment.dto.DirectEntryConfigDTO;
import com.hnumi.obe.assessment.service.IAssessmentContentService;
import com.hnumi.obe.assessment.vo.AssessmentContentDetailVO;
import com.hnumi.obe.common.entity.R;
import com.hnumi.obe.common.valid.ValidGroup;
import lombok.extern.slf4j.XSlf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static cn.dev33.satoken.SaManager.log;

/**
 * 题库控制器
 * 提供一次考核详细内容查询接口
 */
@RestController
@RequestMapping("/assessment/content")
public class AssessmentContentController {
    @Autowired
    private IAssessmentContentService assessmentContentService;


    /**
     * 查询一次考核的详细内容（题目和答案）
     * @param assessmentId 考核ID
     * @return 详细内容VO
     */
    @GetMapping("/detail/{assessmentId}")
    public R<AssessmentContentDetailVO> getAssessmentContentDetail(@PathVariable Long assessmentId) {
        return R.ok(assessmentContentService.getAssessmentContentDetail(assessmentId));
    }

//    /**
//     * 保存一组考核详情
//     */
//    @PostMapping("/direct/save")
//    public boolean saveDirectEntryConfig(@RequestBody DirectEntryConfigDTO dto) {
//        return assessmentContentService.updateDirectEntryConfig(dto);
//    }
    /**
     * 保存一组考核详情
     */
    @PostMapping("/detail/save")
    public R<Boolean> saveDirectEntryConfig(@RequestBody DetailEntryConfigDTO dto) {
        return R.ok(assessmentContentService.saveDirectEntryConfig(dto));
    }

    @PostMapping("/direct/save")
    public R<Boolean> saveDirectEntryConfig(@RequestBody DirectEntryConfigDTO directEntryConfig) {
        log.info("更新直接录入配置: {}", directEntryConfig);
        boolean result = assessmentContentService.updateDirectEntryConfig(directEntryConfig);
        return R.ok(result);
    }
    /**
     * 根据考核ID查询直接录入配置
     */
    @GetMapping("/direct/{assessmentId}")
    public R<DirectEntryConfigDTO> getDirectEntryConfig(@PathVariable Long assessmentId) {
        log.info("查询直接录入配置: {}", assessmentId);
        DirectEntryConfigDTO config = assessmentContentService.getDirectEntryConfig(assessmentId);
        return R.ok(config);
    }
}
