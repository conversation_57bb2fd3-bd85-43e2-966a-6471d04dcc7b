package com.hnumi.obe.assessment.mapstruct;

import com.hnumi.obe.assessment.entity.AssessmentScore;
import com.hnumi.obe.assessment.vo.StudentScoreTargetVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 考核成绩对象转换器
 * 
 * 使用 MapStruct 实现对象之间的转换
 * 主要功能：
 * 1. 实体类与VO之间的转换
 * 2. 集合对象的批量转换
 * 3. 自定义字段映射规则
 */
@Mapper
public interface AssessmentScoreConvert {

    AssessmentScoreConvert INSTANCE = Mappers.getMapper(AssessmentScoreConvert.class);

    /**
     * Entity转VO
     */
    StudentScoreTargetVO toVO(AssessmentScore entity);

    /**
     * Entity列表转VO列表
     */
    List<StudentScoreTargetVO> toVOList(List<AssessmentScore> entities);
}
