package com.hnumi.obe.assessment.mapstruct;

import com.hnumi.obe.assessment.entity.RepositoryQuestion;
import com.hnumi.obe.assessment.dto.DetailEntryConfigDTO.QuestionDetailDTO;
import com.hnumi.obe.assessment.vo.AssessmentContentDetailVO.QuestionDetailVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper
public interface RepositoryQuestionConvert {
    RepositoryQuestionConvert INSTANCE = Mappers.getMapper(RepositoryQuestionConvert.class);

    /**
     * RepositoryQuestion -> QuestionDetailDTO
     * 字段映射：id -> questionId
     */
    @Mapping(source = "id", target = "questionId")
    QuestionDetailDTO toQuestionDetailDTO(RepositoryQuestion entity);

    /**
     * QuestionDetailDTO -> RepositoryQuestion
     * 字段映射：questionId -> id
     */
    @Mapping(source = "questionId", target = "id")
    RepositoryQuestion toEntity(QuestionDetailDTO dto);

    /**
     * RepositoryQuestion -> QuestionDetailVO
     * 字段映射：id -> questionId
     */
    @Mapping(source = "id", target = "questionId")
    QuestionDetailVO toQuestionDetailVO(RepositoryQuestion entity);
}
