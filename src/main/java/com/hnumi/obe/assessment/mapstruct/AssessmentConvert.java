package com.hnumi.obe.assessment.mapstruct;

import com.hnumi.obe.assessment.dto.AssessmentDTO;
import com.hnumi.obe.assessment.entity.Assessment;
import com.hnumi.obe.assessment.vo.AssessmentVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 教学任务考核对象转换器
 */
@Mapper
public interface AssessmentConvert {

    AssessmentConvert INSTANCE = Mappers.getMapper(AssessmentConvert.class);

    /**
     * DTO转Entity
     */
    Assessment toEntity(AssessmentDTO dto);

    /**
     * Entity转VO
     */
    AssessmentVO toVO(Assessment entity);

    /**
     * Entity列表转VO列表
     */
    List<AssessmentVO> toVOList(List<Assessment> entities);

    /**
     * DTO列表转Entity列表
     */
    List<Assessment> toEntityList(List<AssessmentDTO> dtos);
}
