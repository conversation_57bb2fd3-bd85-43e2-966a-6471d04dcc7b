package com.hnumi.obe.assessment.mapstruct;

import com.hnumi.obe.assessment.entity.Assessment;
import com.hnumi.obe.assessment.vo.TaskAssessmentScoreVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 教学任务考核对象转换器
 * 
 * 使用 MapStruct 实现 Assessment 到 TaskAssessmentVO 的转换
 */
@Mapper
public interface TaskAssessmentConvert {

    TaskAssessmentConvert INSTANCE = Mappers.getMapper(TaskAssessmentConvert.class);

    /**
     * Assessment 转换为 TaskAssessmentVO
     * 
     * @param assessment 考核实体
     * @return 教学任务考核VO
     */
    @Mapping(source = "id", target = "assessmentId")
    @Mapping(source = "assessmentName", target = "assessmentName")
    @Mapping(source = "description", target = "description")
    @Mapping(source = "assessmentMethod", target = "assessmentMethod")
    @Mapping(target = "assessmentMethodName", expression = "java(getAssessmentMethodName(assessment.getAssessmentMethod()))")
    @Mapping(source = "assessmentWeight", target = "assessmentWeight")
    @Mapping(source = "assessmentDate", target = "assessmentDate")
    @Mapping(source = "assessmentStatus", target = "assessmentStatus")
    @Mapping(target = "assessmentStatusName", expression = "java(getAssessmentStatusName(assessment.getAssessmentStatus()))")
    @Mapping(source = "scoreType", target = "scoreType")
    @Mapping(target = "scoreTypeName", expression = "java(getScoreTypeName(assessment.getScoreType()))")
    @Mapping(source = "courseId", target = "courseId")
    @Mapping(source = "createTime", target = "createTime")
    @Mapping(source = "modifyTime", target = "modifyTime")
    @Mapping(target = "taskId", ignore = true)
    @Mapping(target = "scoredStudentCount", ignore = true)
    @Mapping(target = "totalStudentCount", ignore = true)
    @Mapping(target = "scoreProgress", ignore = true)
    TaskAssessmentScoreVO toTaskAssessmentVO(Assessment assessment);

    /**
     * 批量转换 Assessment 列表为 TaskAssessmentVO 列表
     */
    List<TaskAssessmentScoreVO> toTaskAssessmentVOList(List<Assessment> assessments);

    /**
     * 更新 TaskAssessmentVO 的任务相关信息
     * 
     * @param taskId 教学任务ID
     * @param scoredCount 已录入成绩数量
     * @param totalCount 总学生数量
     * @param taskAssessmentScoreVO 目标VO对象
     */
    default void updateTaskInfo(Long taskId, Integer scoredCount, Integer totalCount, @MappingTarget TaskAssessmentScoreVO taskAssessmentScoreVO) {
        taskAssessmentScoreVO.setTaskId(taskId);
        taskAssessmentScoreVO.setScoredStudentCount(scoredCount);
        taskAssessmentScoreVO.setTotalStudentCount(totalCount);
        
        if (totalCount != null && totalCount > 0) {
            java.math.BigDecimal progress = java.math.BigDecimal.valueOf(scoredCount != null ? scoredCount : 0)
                    .divide(java.math.BigDecimal.valueOf(totalCount), 4, java.math.RoundingMode.HALF_UP)
                    .multiply(java.math.BigDecimal.valueOf(100));
            taskAssessmentScoreVO.setScoreProgress(progress);
        } else {
            taskAssessmentScoreVO.setScoreProgress(java.math.BigDecimal.ZERO);
        }
    }

    /**
     * 获取考核方式名称
     */
    default String getAssessmentMethodName(Integer method) {
        if (method == null) return "";
        switch (method) {
            case 1: return "作业";
            case 2: return "测验";
            case 3: return "期末考试";
            case 4: return "实验";
            case 5: return "课程设计";
            case 6: return "实习";
            default: return "其他";
        }
    }

    /**
     * 获取考核状态名称
     */
    default String getAssessmentStatusName(Integer status) {
        if (status == null) return "";
        switch (status) {
            case 0: return "配置中";
            case 1: return "编辑中";
            case 2: return "进行中";
            case 3: return "已结束";
            default: return "未知";
        }
    }

    /**
     * 获取成绩录入方式名称
     */
    default String getScoreTypeName(Integer scoreType) {
        if (scoreType == null) return "";
        switch (scoreType) {
            case 0: return "直接录入";
            case 1: return "详细录入";
            default: return "未知";
        }
    }
}
