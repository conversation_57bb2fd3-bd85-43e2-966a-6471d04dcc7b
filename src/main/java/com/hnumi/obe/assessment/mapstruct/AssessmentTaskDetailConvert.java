package com.hnumi.obe.assessment.mapstruct;

import com.hnumi.obe.assessment.vo.AssessmentTaskDetailVO;
import com.hnumi.obe.assessment.vo.TeacherTaskVO;
import com.hnumi.obe.base.entity.Classes;
import com.hnumi.obe.base.entity.Student;
import com.hnumi.obe.task.entity.TaskWork;
import com.hnumi.obe.tp.entity.Course;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

/**
 * 考核教学任务详情转换器
 * 
 * 使用 MapStruct 实现教学任务到考核任务详情VO的转换
 */
@Mapper
public interface AssessmentTaskDetailConvert {

    AssessmentTaskDetailConvert INSTANCE = Mappers.getMapper(AssessmentTaskDetailConvert.class);

    /**
     * TaskWork 转换为 AssessmentTaskDetailVO
     *
     * @param taskWork 教学任务实体
     * @return 考核教学任务详情VO
     */
    @Mapping(source = "id", target = "taskId")
    @Mapping(source = "taskName", target = "taskName")
    @Mapping(source = "taskNumber", target = "taskNumber")
    @Mapping(source = "taskStatus", target = "taskStatus")
    @Mapping(source = "taskStatus", target = "taskStatusName", qualifiedByName = "getTaskStatusName")
    @Mapping(source = "courseId", target = "courseId")
    @Mapping(source = "taskYear", target = "taskYear", qualifiedByName = "convertTaskYear")
    @Mapping(source = "taskTerm", target = "taskTerm")
    @Mapping(source = "taskTerm", target = "taskTermName", qualifiedByName = "getTaskTermName")
    @Mapping(target = "courseName", ignore = true)
    @Mapping(target = "courseCode", ignore = true)
    @Mapping(target = "classes", ignore = true)
    @Mapping(target = "scoreStatistics", ignore = true)
    @Mapping(target = "submissionStatus", ignore = true)
    @Mapping(target = "publishTime", ignore = true)
    AssessmentTaskDetailVO toAssessmentTaskDetailVO(TaskWork taskWork);

    /**
     * 批量转换 TaskWork 列表为 AssessmentTaskDetailVO 列表
     */
    List<AssessmentTaskDetailVO> toAssessmentTaskDetailVOList(List<TaskWork> taskWorks);

    /**
     * 更新课程信息
     * 
     * @param course 课程实体
     * @param taskDetailVO 目标VO对象
     */
    @Mapping(source = "courseName", target = "courseName")
    @Mapping(source = "courseCode", target = "courseCode")
    void updateCourseInfo(Course course, @MappingTarget AssessmentTaskDetailVO taskDetailVO);

    /**
     * 创建班级信息
     * 
     * @param classes 班级实体
     * @param studentCount 学生总数
     * @param scoredCount 已录入成绩数
     * @return 班级信息VO
     */
    default AssessmentTaskDetailVO.TaskClassInfo createTaskClassInfo(Classes classes, Integer studentCount, Integer scoredCount) {
        if (classes == null) {
            return null;
        }

        AssessmentTaskDetailVO.TaskClassInfo classInfo = new AssessmentTaskDetailVO.TaskClassInfo();
        classInfo.setClassId(classes.getClassId());
        classInfo.setClassName(classes.getClassName());
        classInfo.setStudentCount(studentCount != null ? studentCount : 0);
        classInfo.setScoredCount(scoredCount != null ? scoredCount : 0);
        classInfo.setUnscoredCount(classInfo.getStudentCount() - classInfo.getScoredCount());

        // 计算录入进度
        if (classInfo.getStudentCount() > 0) {
            BigDecimal progress = BigDecimal.valueOf(classInfo.getScoredCount())
                    .divide(BigDecimal.valueOf(classInfo.getStudentCount()), 4, RoundingMode.HALF_UP)
                    .multiply(BigDecimal.valueOf(100));
            classInfo.setScoreProgress(progress);
        } else {
            classInfo.setScoreProgress(BigDecimal.ZERO);
        }

        return classInfo;
    }

    /**
     * 创建成绩统计信息
     * 
     * @param totalCount 总学生数
     * @param scoredCount 已录入数
     * @param averageScore 平均分
     * @param maxScore 最高分
     * @param minScore 最低分
     * @param passRate 及格率
     * @return 成绩统计信息
     */
    default AssessmentTaskDetailVO.ScoreStatistics createScoreStatistics(
            Integer totalCount, Integer scoredCount, BigDecimal averageScore,
            BigDecimal maxScore, BigDecimal minScore, BigDecimal passRate) {

        AssessmentTaskDetailVO.ScoreStatistics statistics = new AssessmentTaskDetailVO.ScoreStatistics();
        statistics.setTotalStudentCount(totalCount != null ? totalCount : 0);
        statistics.setScoredStudentCount(scoredCount != null ? scoredCount : 0);
        statistics.setUnscoredStudentCount(statistics.getTotalStudentCount() - statistics.getScoredStudentCount());

        // 计算录入进度
        if (statistics.getTotalStudentCount() > 0) {
            BigDecimal progress = BigDecimal.valueOf(statistics.getScoredStudentCount())
                    .divide(BigDecimal.valueOf(statistics.getTotalStudentCount()), 4, RoundingMode.HALF_UP)
                    .multiply(BigDecimal.valueOf(100));
            statistics.setScoreProgress(progress);
        } else {
            statistics.setScoreProgress(BigDecimal.ZERO);
        }

        statistics.setAverageScore(averageScore);
        statistics.setMaxScore(maxScore);
        statistics.setMinScore(minScore);
        statistics.setPassRate(passRate);

        return statistics;
    }

    /**
     * 确定成绩提交状态
     * 
     * @param totalCount 总学生数
     * @param scoredCount 已录入数
     * @return 提交状态
     */
    default AssessmentTaskDetailVO.ScoreSubmissionStatus determineSubmissionStatus(Integer totalCount, Integer scoredCount) {
        if (scoredCount == null || scoredCount == 0) {
            return AssessmentTaskDetailVO.ScoreSubmissionStatus.NOT_STARTED;
        } else if (scoredCount.equals(totalCount)) {
            return AssessmentTaskDetailVO.ScoreSubmissionStatus.COMPLETED;
        } else {
            return AssessmentTaskDetailVO.ScoreSubmissionStatus.IN_PROGRESS;
        }
    }

    /**
     * 转换任务年份
     */
    @Named("convertTaskYear")
    default String convertTaskYear(Integer taskYear) {
        return taskYear != null ? taskYear.toString() : "";
    }

    /**
     * 获取任务状态名称
     */
    @Named("getTaskStatusName")
    default String getTaskStatusName(Integer status) {
        if (status == null) return "";
        switch (status) {
            case 0: return "进行中";
            case 1: return "已结束";
            default: return "未知";
        }
    }

    /**
     * 获取学期名称
     */
    @Named("getTaskTermName")
    default String getTaskTermName(Integer term) {
        if (term == null) return "";
        switch (term) {
            case 1: return "第一学期";
            case 2: return "第二学期";
            case 3: return "第三学期";
            default: return "未知学期";
        }
    }
}
