package com.hnumi.obe.assessment.mapstruct;

import com.hnumi.obe.assessment.entity.AssessmentTask;
import com.hnumi.obe.assessment.vo.AssessmentPublishVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 考核与教学任务关联对象转换器
 * 
 * 使用 MapStruct 实现对象之间的转换
 * 主要功能：
 * 1. 实体类与VO之间的转换
 * 2. 集合对象的批量转换
 * 3. 自定义字段映射规则
 */
@Mapper
public interface AssessmentTaskConvert {

    AssessmentTaskConvert INSTANCE = Mappers.getMapper(AssessmentTaskConvert.class);

    /**
     * Entity列表转VO列表
     */
    List<AssessmentTask> toEntityList(List<AssessmentTask> entities);
}
