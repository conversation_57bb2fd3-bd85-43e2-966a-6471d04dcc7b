package com.hnumi.obe.assessment.mapstruct;

import com.hnumi.obe.assessment.vo.TeacherTaskVO;
import com.hnumi.obe.task.entity.TaskWork;
import com.hnumi.obe.tp.entity.Course;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 教师教学任务对象转换器
 * 
 * 使用 MapStruct 实现 TaskWork 到 TeacherTaskVO 的转换
 * 减少手动属性赋值的代码量
 */
@Mapper
public interface TeacherTaskConvert {

    TeacherTaskConvert INSTANCE = Mappers.getMapper(TeacherTaskConvert.class);

    /**
     * TaskWork 转换为 TeacherTaskVO
     * 
     * @param taskWork 教学任务实体
     * @return 教师教学任务VO
     */
    @Mapping(source = "id", target = "taskId")
    @Mapping(source = "taskName", target = "taskName")
    @Mapping(source = "courseId", target = "courseId")
    @Mapping(source = "taskNumber", target = "taskNumber")
    @Mapping(source = "taskYear", target = "taskYear")
    @Mapping(source = "taskTerm", target = "taskTerm")
    @Mapping(source = "teachWeek", target = "teachWeek")
    @Mapping(source = "weekHours", target = "weekHours")
    @Mapping(source = "totalHours", target = "totalHours")
    @Mapping(source = "taskStatus", target = "taskStatus")
    @Mapping(target = "taskStatusName", expression = "java(getTaskStatusName(taskWork.getTaskStatus()))")
    @Mapping(target = "courseName", ignore = true)
    @Mapping(target = "courseCode", ignore = true)
    @Mapping(target = "classes", ignore = true)
    @Mapping(target = "publishedAssessmentCount", ignore = true)
    TeacherTaskVO toTeacherTaskVO(TaskWork taskWork);

    /**
     * 批量转换 TaskWork 列表为 TeacherTaskVO 列表
     */
    List<TeacherTaskVO> toTeacherTaskVOList(List<TaskWork> taskWorks);

    /**
     * 更新 TeacherTaskVO 的课程信息
     * 
     * @param course 课程实体
     * @param teacherTaskVO 目标VO对象
     */
    @Mapping(source = "courseName", target = "courseName")
    @Mapping(source = "courseCode", target = "courseCode")
    void updateCourseInfo(Course course, @MappingTarget TeacherTaskVO teacherTaskVO);

    /**
     * 获取任务状态名称
     * 
     * @param status 状态码
     * @return 状态名称
     */
    default String getTaskStatusName(Integer status) {
        if (status == null) return "";
        switch (status) {
            case 0: return "进行中";
            case 1: return "已结束";
            default: return "未知";
        }
    }
}
