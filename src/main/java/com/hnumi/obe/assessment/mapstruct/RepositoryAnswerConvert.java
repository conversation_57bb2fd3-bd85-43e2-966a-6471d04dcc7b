package com.hnumi.obe.assessment.mapstruct;

import com.hnumi.obe.assessment.entity.RepositoryAnswer;
import com.hnumi.obe.assessment.dto.DetailEntryConfigDTO.AnswerDetailDTO;
import com.hnumi.obe.assessment.vo.AssessmentContentDetailVO.AnswerDetailVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper
public interface RepositoryAnswerConvert {
    RepositoryAnswerConvert INSTANCE = Mappers.getMapper(RepositoryAnswerConvert.class);

    /**
     * RepositoryAnswer -> AnswerDetailDTO
     * 字段映射：id -> answerId
     */
    @Mapping(source = "id", target = "answerId")
    AnswerDetailDTO toAnswerDetailDTO(RepositoryAnswer entity);

    /**
     * AnswerDetailDTO -> RepositoryAnswer
     * 字段映射：answerId -> id
     */
    @Mapping(source = "answerId", target = "id")
    RepositoryAnswer toEntity(AnswerDetailDTO dto);

    /**
     * RepositoryAnswer -> AnswerDetailVO
     * 字段映射：id -> answerId
     */
    @Mapping(source = "id", target = "answerId")
    AnswerDetailVO toAnswerDetailVO(RepositoryAnswer entity);
}
