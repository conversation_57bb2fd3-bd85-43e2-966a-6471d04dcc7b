package com.hnumi.obe.assessment.mapstruct;

import com.hnumi.obe.assessment.entity.AssessmentScore;
import com.hnumi.obe.assessment.entity.AssessmentScoreDetail;
import com.hnumi.obe.assessment.entity.AssessmentScoreTarget;
import com.hnumi.obe.assessment.vo.StudentScoreDetailVO;
import com.hnumi.obe.base.entity.Student;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

/**
 * 学生成绩详情转换器
 * 
 * 使用 MapStruct 实现学生成绩相关实体到VO的转换
 */
@Mapper
public interface StudentScoreDetailConvert {

    StudentScoreDetailConvert INSTANCE = Mappers.getMapper(StudentScoreDetailConvert.class);

    /**
     * Student 转换为 StudentScoreDetailVO 基础信息
     * 
     * @param student 学生实体
     * @return 学生成绩详情VO
     */
    @Mapping(source = "studentId", target = "studentId")
    @Mapping(source = "studentNumber", target = "studentNumber")
    //@Mapping(source = "user.realName", target = "studentName")
    @Mapping(source = "classId", target = "classId")
    @Mapping(target = "className", ignore = true)
    @Mapping(target = "totalScore", ignore = true)
    @Mapping(target = "fullScore", ignore = true)
    @Mapping(target = "scoreRate", ignore = true)
    @Mapping(target = "scoreGrade", ignore = true)
    @Mapping(target = "courseTargetScores", ignore = true)
    @Mapping(target = "detailScores", ignore = true)
    @Mapping(target = "entryStatus", ignore = true)
    @Mapping(target = "entryTime", ignore = true)
    @Mapping(target = "lastModifyTime", ignore = true)
    @Mapping(target = "entryUserId", ignore = true)
    @Mapping(target = "entryUserName", ignore = true)
    @Mapping(target = "remark", ignore = true)
    StudentScoreDetailVO toStudentScoreDetailVO(Student student);

    /**
     * 批量转换学生列表为学生成绩详情VO列表
     */
    List<StudentScoreDetailVO> toStudentScoreDetailVOList(List<Student> students);

    /**
     * AssessmentScoreTarget 转换为 CourseTargetScore
     * 
     * @param scoreTarget 课程目标成绩实体
     * @return 课程目标成绩VO
     */
    @Mapping(source = "courseTargetNo", target = "courseTargetNo")
    @Mapping(source = "score", target = "score")
    @Mapping(source = "poId", target = "poId")
    @Mapping(target = "courseTargetName", ignore = true)
    @Mapping(target = "fullScore", ignore = true)
    //@Mapping(target = "scoreRate", expression = "java(calculateScoreRate(scoreTarget.getScore(), null))")
    @Mapping(target = "poName", ignore = true)
    @Mapping(target = "weight", ignore = true)
    StudentScoreDetailVO.CourseTargetScore toCourseTargetScore(AssessmentScoreTarget scoreTarget);

    /**
     * 批量转换课程目标成绩
     */
    List<StudentScoreDetailVO.CourseTargetScore> toCourseTargetScoreList(List<AssessmentScoreTarget> scoreTargets);

    /**
     * AssessmentScoreDetail 转换为 AssessmentDetailScore
     * 
     * @param scoreDetail 考核详情成绩实体
     * @return 考核详情成绩VO
     */
    @Mapping(source = "repositoryAnswerId", target = "repositoryAnswerId")
    @Mapping(source = "studentAnswer", target = "studentAnswer")
    @Mapping(source = "score", target = "score")
    @Mapping(source = "questionScore", target = "questionScore")
    @Mapping(source = "courseTargetNo", target = "courseTargetNo")
    @Mapping(target = "questionNumber", ignore = true)
    @Mapping(target = "questionContent", ignore = true)
    //@Mapping(target = "scoreRate", expression = "java(calculateScoreRate(scoreDetail.getScore(), scoreDetail.getQuestionScore()))")
    @Mapping(target = "questionType", ignore = true)
    @Mapping(target = "difficultyLevel", ignore = true)
    StudentScoreDetailVO.AssessmentDetailScore toAssessmentDetailScore(AssessmentScoreDetail scoreDetail);

    /**
     * 批量转换考核详情成绩
     */
    List<StudentScoreDetailVO.AssessmentDetailScore> toAssessmentDetailScoreList(List<AssessmentScoreDetail> scoreDetails);

    /**
     * 计算得分率
     * 
     * @param score 得分
     * @param fullScore 满分
     * @return 得分率
     */
    default BigDecimal calculateScoreRate(BigDecimal score, BigDecimal fullScore) {
        if (score == null || fullScore == null || fullScore.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        return score.divide(fullScore, 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));
    }

    /**
     * 计算成绩等级
     * 
     * @param scoreRate 得分率
     * @return 成绩等级
     */
    default String calculateScoreGrade(BigDecimal scoreRate) {
        if (scoreRate == null) {
            return "未录入";
        }
        
        if (scoreRate.compareTo(BigDecimal.valueOf(90)) >= 0) {
            return "优秀";
        } else if (scoreRate.compareTo(BigDecimal.valueOf(80)) >= 0) {
            return "良好";
        } else if (scoreRate.compareTo(BigDecimal.valueOf(70)) >= 0) {
            return "中等";
        } else if (scoreRate.compareTo(BigDecimal.valueOf(60)) >= 0) {
            return "及格";
        } else {
            return "不及格";
        }
    }

    /**
     * 确定成绩录入状态
     * 
     * @param hasMainScore 是否有主成绩
     * @param hasTargetScores 是否有课程目标成绩
     * @param hasDetailScores 是否有详情成绩
     * @return 录入状态
     */
    default StudentScoreDetailVO.ScoreEntryStatus determineEntryStatus(
            boolean hasMainScore, boolean hasTargetScores, boolean hasDetailScores) {
        
        if (!hasMainScore && !hasTargetScores && !hasDetailScores) {
            return StudentScoreDetailVO.ScoreEntryStatus.NOT_ENTERED;
        } else if (hasMainScore && hasTargetScores && hasDetailScores) {
            return StudentScoreDetailVO.ScoreEntryStatus.FULLY_ENTERED;
        } else {
            return StudentScoreDetailVO.ScoreEntryStatus.PARTIALLY_ENTERED;
        }
    }

    /**
     * 更新总成绩信息
     * 
     * @param totalScore 总成绩
     * @param fullScore 满分
     * @param studentScoreVO 目标VO
     */
    default void updateTotalScoreInfo(BigDecimal totalScore, BigDecimal fullScore, StudentScoreDetailVO studentScoreVO) {
        studentScoreVO.setTotalScore(totalScore);
        studentScoreVO.setFullScore(fullScore);
        
        BigDecimal scoreRate = calculateScoreRate(totalScore, fullScore);
        studentScoreVO.setScoreRate(scoreRate);
        studentScoreVO.setScoreGrade(calculateScoreGrade(scoreRate));
    }
}
