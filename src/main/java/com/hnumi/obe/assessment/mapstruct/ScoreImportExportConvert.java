package com.hnumi.obe.assessment.mapstruct;

import com.hnumi.obe.assessment.dto.ScoreDetailExcelImportDTO;
import com.hnumi.obe.assessment.dto.ScoreTargetExcelImportDTO;
import com.hnumi.obe.assessment.entity.AssessmentScoreDetail;
import com.hnumi.obe.assessment.entity.AssessmentScoreTarget;
import com.hnumi.obe.base.entity.Student;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 成绩导入导出对象转换器
 * 
 * 使用 MapStruct 实现成绩相关对象的转换
 * 减少导入导出功能中的手动属性赋值
 */
@Mapper
public interface ScoreImportExportConvert {

    ScoreImportExportConvert INSTANCE = Mappers.getMapper(ScoreImportExportConvert.class);

    /**
     * Student 转换为 ScoreTargetExcelImportDTO（用于模板导出）
     * 
     * @param student 学生实体
     * @return 课程目标成绩Excel导入DTO
     */
    @Mapping(source = "studentNumber", target = "studentNumber")
    @Mapping(source = "studentName", target = "studentName")
    @Mapping(target = "className", ignore = true)
    @Mapping(target = "courseTargetNo", ignore = true)
    @Mapping(target = "score", ignore = true)
    @Mapping(target = "repositoryAnswerId", ignore = true)
    @Mapping(target = "remark", ignore = true)
    ScoreTargetExcelImportDTO studentToTargetTemplate(Student student);

    /**
     * Student 转换为 ScoreDetailExcelImportDTO（用于模板导出）
     * 
     * @param student 学生实体
     * @return 考核详情成绩Excel导入DTO
     */
    @Mapping(source = "studentNumber", target = "studentNumber")
    @Mapping(source = "studentName", target = "studentName")
    @Mapping(target = "className", ignore = true)
    @Mapping(target = "repositoryAnswerId", ignore = true)
    @Mapping(target = "questionNumber", ignore = true)
    @Mapping(target = "studentAnswer", ignore = true)
    @Mapping(target = "score", ignore = true)
    @Mapping(target = "questionScore", ignore = true)
    @Mapping(target = "courseTargetNo", ignore = true)
    @Mapping(target = "remark", ignore = true)
    ScoreDetailExcelImportDTO studentToDetailTemplate(Student student);

    /**
     * AssessmentScoreTarget 转换为 ScoreTargetExcelImportDTO（用于成绩导出）
     * 
     * @param scoreTarget 课程目标成绩实体
     * @return 课程目标成绩Excel导入DTO
     */
    @Mapping(target = "studentNumber", ignore = true)
    @Mapping(target = "studentName", ignore = true)
    @Mapping(target = "className", ignore = true)
    @Mapping(source = "courseTargetNo", target = "courseTargetNo")
    @Mapping(source = "score", target = "score")
    @Mapping(source = "repositoryAnswerId", target = "repositoryAnswerId")
    @Mapping(target = "remark", ignore = true)
    ScoreTargetExcelImportDTO scoreTargetToExportDTO(AssessmentScoreTarget scoreTarget);

    /**
     * AssessmentScoreDetail 转换为 ScoreDetailExcelImportDTO（用于成绩导出）
     * 
     * @param scoreDetail 考核详情成绩实体
     * @return 考核详情成绩Excel导入DTO
     */
    @Mapping(target = "studentNumber", ignore = true)
    @Mapping(target = "studentName", ignore = true)
    @Mapping(target = "className", ignore = true)
    @Mapping(source = "repositoryAnswerId", target = "repositoryAnswerId")
    @Mapping(target = "questionNumber", ignore = true)
    @Mapping(source = "studentAnswer", target = "studentAnswer")
    @Mapping(source = "score", target = "score")
    @Mapping(source = "questionScore", target = "questionScore")
    @Mapping(source = "courseTargetNo", target = "courseTargetNo")
    @Mapping(target = "remark", ignore = true)
    ScoreDetailExcelImportDTO scoreDetailToExportDTO(AssessmentScoreDetail scoreDetail);

    /**
     * 批量转换学生列表为课程目标成绩模板
     */
    List<ScoreTargetExcelImportDTO> studentsToTargetTemplates(List<Student> students);

    /**
     * 批量转换学生列表为考核详情成绩模板
     */
    List<ScoreDetailExcelImportDTO> studentsToDetailTemplates(List<Student> students);

    /**
     * 批量转换课程目标成绩为导出DTO
     */
    List<ScoreTargetExcelImportDTO> scoreTargetsToExportDTOs(List<AssessmentScoreTarget> scoreTargets);

    /**
     * 批量转换考核详情成绩为导出DTO
     */
    List<ScoreDetailExcelImportDTO> scoreDetailsToExportDTOs(List<AssessmentScoreDetail> scoreDetails);
}
