package com.hnumi.obe.assessment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hnumi.obe.assessment.entity.RepositoryAnswer;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import java.util.List;

@Mapper
public interface RepositoryAnswerMapper extends BaseMapper<RepositoryAnswer> {
    /**
     * 根据题目ID查询所有答案
     */
    @Select("SELECT * FROM obe_db.repository_answer WHERE question_id = #{questionId} AND status = 0")
    List<RepositoryAnswer> selectAnswersByQuestionId(@Param("questionId") Long questionId);
}

