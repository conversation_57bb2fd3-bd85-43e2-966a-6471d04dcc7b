package com.hnumi.obe.assessment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hnumi.obe.assessment.entity.AssessmentContent;
import com.hnumi.obe.assessment.entity.RepositoryAnswer;
import com.hnumi.obe.assessment.entity.RepositoryQuestion;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import java.util.List;

@Mapper
public interface AssessmentContentMapper extends BaseMapper<AssessmentContent> {
    /**
     * 根据考核ID查询所有关联题目ID
     */
    @Select("SELECT question_id FROM obe_db.assessment_content WHERE assessment_id = #{assessmentId}")
    List<Long> selectQuestionIdsByAssessmentId(@Param("assessmentId") Long assessmentId);

    @Select("SELECT assessment_detail FROM obe_db.assessment_content WHERE assessment_id = #{assessmentId}")
    String selectAssessmentDirectConfig(@Param("assessmentId") Long assessmentId);
}

