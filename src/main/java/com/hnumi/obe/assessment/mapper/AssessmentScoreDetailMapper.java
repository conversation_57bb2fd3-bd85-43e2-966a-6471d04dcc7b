package com.hnumi.obe.assessment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hnumi.obe.assessment.dto.StudentCourseTargetScoreFlatDTO;
import com.hnumi.obe.assessment.entity.AssessmentScoreDetail;
import com.hnumi.obe.assessment.entity.AssessmentScoreTarget;
import com.hnumi.obe.assessment.vo.StudentCourseTargetScoreVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 考核成绩详情表 Mapper 接口
 */
@Mapper
public interface AssessmentScoreDetailMapper extends BaseMapper<AssessmentScoreDetail> {

    /**
     * 根据考核ID和教学任务ID查询成绩详情列表
     *
     * @param assessmentId 考核ID
     * @param taskId 教学任务ID
     * @return 成绩详情列表
     */
    @Select("SELECT * FROM assessment_score_detail WHERE assessment_id = #{assessmentId} AND task_id = #{taskId} AND status = 0")
    List<AssessmentScoreDetail> selectByAssessmentAndTask(@Param("assessmentId") Long assessmentId,
                                                         @Param("taskId") Long taskId);

    /**
     * 根据学生ID、考核ID和题目答案ID查询成绩详情
     *
     * @param studentId 学生ID
     * @param assessmentId 考核ID
     * @param repositoryAnswerId 题目答案ID
     * @return 成绩详情
     */
    @Select("SELECT * FROM assessment_score_detail WHERE student_id = #{studentId} AND assessment_id = #{assessmentId} AND repository_answer_id = #{repositoryAnswerId} AND status = 0")
    AssessmentScoreDetail selectByStudentAndAnswer(@Param("studentId") Long studentId,
                                                  @Param("assessmentId") Long assessmentId,
                                                  @Param("repositoryAnswerId") Long repositoryAnswerId);

    /**
     * 根据考核ID和课程目标编号查询成绩详情列表
     *
     * @param assessmentId 考核ID
     * @param courseTargetNo 课程目标编号
     * @return 成绩详情列表
     */
    @Select("SELECT * FROM assessment_score_detail WHERE assessment_id = #{assessmentId} AND course_target_no = #{courseTargetNo} AND status = 0")
    List<AssessmentScoreDetail> selectByAssessmentAndTarget(@Param("assessmentId") Long assessmentId,
                                                           @Param("courseTargetNo") Integer courseTargetNo);

    /**
     * 统计学生在某个考核中的总得分（按课程目标）
     *
     * @param studentId 学生ID
     * @param assessmentId 考核ID
     * @param courseTargetNo 课程目标编号
     * @return 总得分
     */
    @Select("SELECT COALESCE(SUM(score), 0) FROM assessment_score_detail WHERE student_id = #{studentId} AND assessment_id = #{assessmentId} AND course_target_no = #{courseTargetNo} AND status = 0")
    Double sumScoreByStudentAndTarget(@Param("studentId") Long studentId,
                                     @Param("assessmentId") Long assessmentId,
                                     @Param("courseTargetNo") Integer courseTargetNo);

    /**
     * 删除学生在某个考核中的所有详情成绩
     *
     * @param studentId 学生ID
     * @param assessmentId 考核ID
     * @return 影响行数
     */
    @Select("UPDATE assessment_score_detail SET status = -1 WHERE student_id = #{studentId} AND assessment_id = #{assessmentId}")
    int deleteByStudentAndAssessment(@Param("studentId") Long studentId,
                                    @Param("assessmentId") Long assessmentId);

    /**
     * 根据考核ID、教学任务ID和学生ID查询考核详情成绩
     */
    @Select("SELECT * FROM assessment_score_detail " +
            "WHERE assessment_id = #{assessmentId} " +
            "AND task_id = #{taskId} " +
            "AND student_id = #{studentId} " +
            "AND status = 0 " +
            "ORDER BY repository_answer_id")
    List<AssessmentScoreDetail> selectByAssessmentAndStudent(@Param("assessmentId") Long assessmentId,
                                                           @Param("taskId") Long taskId,
                                                           @Param("studentId") Long studentId);

    /**
     * 根据一组考核ID，获取所有考核目标成绩列表，优化批量查询性能，避免脚本格式，直接使用IN查询
     *
     * @param assessmentIds 考核ID列表
     * @return 考核目标成绩列表
     */
    @Select("SELECT * FROM assessment_score_target WHERE assessment_id IN (${assessmentIds}) AND status = 0")
    List<AssessmentScoreTarget> selectScoreTargetsByAssessmentIds(@Param("assessmentIds") String assessmentIds);

    /**
     * 按课程目标和考核方式分组统计学生成绩，assessment_method从assessment表中获取
     * @param courseId 课程ID
     * @return 课程目标分组成绩
     */
    @Select("""
        SELECT ast.student_id,
               s.student_name,
               s.student_number,
               ast.course_target_no,
               ast.po_id,
               ast.objective_id,
               a.assessment_method,
               SUM(ast.score) AS total_score
        FROM assessment_score_target ast
        JOIN (
            SELECT id, assessment_method
            FROM assessment
            WHERE course_id = #{courseId} AND achievement=true AND status = 0
        ) a ON ast.assessment_id = a.id
        JOIN base_student s ON ast.student_id = s.student_id
        WHERE ast.course_id = #{courseId} AND ast.status = 0
        GROUP BY ast.student_id, s.student_name, s.student_number, ast.course_target_no, ast.po_id, ast.objective_id, a.assessment_method
    """)
    List<StudentCourseTargetScoreFlatDTO> groupScoreByTargetAndMethod(@Param("courseId") Long courseId);
}
