package com.hnumi.obe.assessment.mapper;

import com.hnumi.obe.assessment.vo.AssessmentTargetScoreVO;
import com.hnumi.obe.assessment.vo.CourseTargetScoreVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 课程目标达成度分析 Mapper
 */
@Mapper
public interface AchievementAnalysisMapper {

    /**
     * 按courseCode获取考核记录和对应的课程目标达成情况
     * 优化：一次查询获取所有需要的数据，避免多次查询
     */
    @Select("""
        WITH assessment_scores AS (
            SELECT a.id, a.course_id, a.assessment_name, a.assessment_method,
                   a.assessment_detail, a.assessment_weight, a.assessment_term,
                   a.assessment_year, c.course_code, c.course_target
            FROM assessment a
            INNER JOIN tp_course c ON a.course_id = c.course_id
            WHERE c.course_code = #{courseCode}
              AND a.status = 0
              AND a.assessment_status = 2
              AND a.achievement = 1
        )
        SELECT 
            ast.assessment_id,
            ast.course_id,
            ast.course_target_no,
            a.assessment_method,
            a.assessment_term,
            a.assessment_year,
            AVG(ast.score) as average_score,
            COUNT(DISTINCT ast.student_id) as student_count
        FROM assessment_score_target ast
        INNER JOIN assessment_scores a ON ast.assessment_id = a.id
        WHERE ast.status = 0
        GROUP BY ast.assessment_id, ast.course_id, ast.course_target_no,
                 a.assessment_method, a.assessment_term, a.assessment_year
    """)
    List<AssessmentTargetScoreVO> getAssessmentScoresByCode(@Param("courseCode") String courseCode);

    /**
     * 获取课程的考核权重配置
     * 优化：直接从tp_course表获取assessment_weight配置
     */
    @Select("""
        SELECT c.course_id as course_id,
               c.assessment_weight,
               c.course_target
        FROM tp_course c
        WHERE c.course_id = #{courseId}
        AND c.status = 0
    """)
    CourseTargetScoreVO.CourseConfig getCourseConfig(@Param("courseId") Long courseId);

    /**
     * 获取课程历史达成度数据
     * 优化：使用窗口函数计算同一课程目标在不同学期的达成度
     */
    @Select("""
        WITH target_scores AS (
            SELECT 
                ast.course_target_no,
                a.assessment_year,
                a.assessment_term,
                AVG(ast.score) as term_score,
                COUNT(DISTINCT ast.student_id) as student_count
            FROM assessment_score_target ast
            INNER JOIN assessment a ON ast.assessment_id = a.id
            INNER JOIN tp_course c ON a.course_id = c.course_id
            WHERE c.course_code = #{courseCode}
              AND a.achievement = 1
              AND ast.status = 0
              AND a.status = 0
            GROUP BY ast.course_target_no, a.assessment_year, a.assessment_term
        )
        SELECT *,
               AVG(term_score) OVER (
                   PARTITION BY course_target_no 
                   ORDER BY assessment_year, assessment_term
               ) as cumulative_score
        FROM target_scores
        ORDER BY course_target_no, assessment_year, assessment_term
    """)
    List<CourseTargetScoreVO> getHistoricalAchievement(@Param("courseCode") String courseCode);
}
