package com.hnumi.obe.assessment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hnumi.obe.assessment.entity.RepositoryQuestion;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import java.util.List;

@Mapper
public interface RepositoryQuestionMapper extends BaseMapper<RepositoryQuestion> {
    /**
     * 根据题目ID列表查询题目详情
     */
    @Select({"<script>",
            "SELECT * FROM obe_db.repository_question WHERE id IN ",
            "<foreach collection='questionIds' item='id' open='(' separator=',' close=')'>",
            "#{id}",
            "</foreach>",
            " AND status = 0",
            "</script>"})
    List<RepositoryQuestion> selectQuestionsByIds(@Param("questionIds") List<Long> questionIds);
}

