package com.hnumi.obe.assessment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hnumi.obe.assessment.entity.AssessmentScoreTarget;
import lombok.Data;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 考核成绩目标表 Mapper 接口
 */
@Mapper
public interface AssessmentScoreTargetMapper extends BaseMapper<AssessmentScoreTarget> {

    /**
     * 根据考核ID和教学任务ID查询成绩目标列表
     *
     * @param assessmentId 考核ID
     * @param taskId 教学任务ID
     * @return 成绩目标列表
     */
    @Select("SELECT * FROM assessment_score_target WHERE assessment_id = #{assessmentId} AND task_id = #{taskId} AND status = 0")
    List<AssessmentScoreTarget> selectByAssessmentAndTask(@Param("assessmentId") Long assessmentId,
                                                         @Param("taskId") Long taskId);

    /**
     * 根据学生ID、考核ID和课程目标编号查询成绩目标
     *
     * @param studentId 学生ID
     * @param assessmentId 考核ID
     * @param courseTargetNo 课程目标编号
     * @return 成绩目标
     */
    @Select("SELECT * FROM assessment_score_target WHERE student_id = #{studentId} AND assessment_id = #{assessmentId} AND course_target_no = #{courseTargetNo} AND status = 0")
    AssessmentScoreTarget selectByStudentAndTarget(@Param("studentId") Long studentId,
                                                  @Param("assessmentId") Long assessmentId,
                                                  @Param("courseTargetNo") Integer courseTargetNo);

    /**
     * 根据考核ID和课程目标编号查询成绩目标列表
     *
     * @param assessmentId 考核ID
     * @param courseTargetNo 课程目标编号
     * @return 成绩目标列表
     */
    @Select("SELECT * FROM assessment_score_target WHERE assessment_id = #{assessmentId} AND course_target_no = #{courseTargetNo} AND status = 0")
    List<AssessmentScoreTarget> selectByAssessmentAndTargetNo(@Param("assessmentId") Long assessmentId,
                                                             @Param("courseTargetNo") Integer courseTargetNo);

    /**
     * 统计学生在某个考核的某个课程目标中的得分
     *
     * @param studentId 学生ID
     * @param assessmentId 考核ID
     * @param courseTargetNo 课程目标编号
     * @return 得分
     */
    @Select("SELECT COALESCE(SUM(score), 0) FROM assessment_score_target WHERE student_id = #{studentId} AND assessment_id = #{assessmentId} AND course_target_no = #{courseTargetNo} AND status = 0")
    Double sumScoreByStudentAndTarget(@Param("studentId") Long studentId,
                                     @Param("assessmentId") Long assessmentId,
                                     @Param("courseTargetNo") Integer courseTargetNo);

    /**
     * 删除学生在某个考核中的所有目标成绩
     *
     * @param studentId 学生ID
     * @param assessmentId 考核ID
     * @return 影响行数
     */
    @Select("UPDATE assessment_score_target SET status = -1 WHERE student_id = #{studentId} AND assessment_id = #{assessmentId}")
    int deleteByStudentAndAssessment(@Param("studentId") Long studentId,
                                    @Param("assessmentId") Long assessmentId);

    /**
     * 根据毕业要求指标ID查询成绩目标列表
     *
     * @param poId 毕业要求指标ID
     * @param assessmentId 考核ID
     * @return 成绩目标列表
     */
    @Select("SELECT * FROM assessment_score_target WHERE po_id = #{poId} AND assessment_id = #{assessmentId} AND status = 0")
    List<AssessmentScoreTarget> selectByPoIdAndAssessment(@Param("poId") Long poId,
                                                         @Param("assessmentId") Long assessmentId);

    /**
     * 根据考核ID、教学任务ID和学生ID查询课程目标成绩
     */
    @Select("SELECT * FROM assessment_score_target " +
            "WHERE assessment_id = #{assessmentId} " +
            "AND task_id = #{taskId} " +
            "AND student_id = #{studentId} " +
            "AND status = 0 " +
            "ORDER BY course_target_no")
    List<AssessmentScoreTarget> selectByAssessmentAndStudent(@Param("assessmentId") Long assessmentId,
                                                           @Param("taskId") Long taskId,
                                                           @Param("studentId") Long studentId);

    /**
     * 批量查询多个学生的课程目标成绩
     * 使用XML映射文件实现动态SQL
     */
    List<AssessmentScoreTarget> selectByAssessmentAndStudents(@Param("assessmentId") Long assessmentId,
                                                            @Param("taskId") Long taskId,
                                                            @Param("studentIds") List<Long> studentIds);

    /**
     * 查询已录入成绩的学生数量（使用子查询验证学生属于该教学任务）
     *
     * @param assessmentId 考核ID
     * @param taskId 教学任务ID
     * @return 已录入成绩的学生数量
     */
    @Select("SELECT COUNT(DISTINCT st.student_id) " +
            "FROM assessment_score_target st " +
            "WHERE st.assessment_id = #{assessmentId} " +
            "AND st.task_id = #{taskId} " +
            "AND st.status = 0 " +
            "AND st.student_id IN (" +
            "    SELECT s.student_id FROM base_student s " +
            "    WHERE s.class_id IN (" +
            "        SELECT twc.class_id FROM task_worklist_classes twc " +
            "        WHERE twc.task_id = #{taskId}" +
            "    ) AND s.status = 0" +
            ")")
    int countScoredStudents(@Param("assessmentId") Long assessmentId,
                            @Param("taskId") Long taskId);

    /**
     * 统计班级已录入成绩的学生数量
     */
    @Select("SELECT COUNT(DISTINCT st.student_id) " +
            "FROM assessment_score_target st " +
            "INNER JOIN base_student s ON st.student_id = s.student_id " +
            "WHERE st.assessment_id = #{assessmentId} " +
            "AND st.task_id = #{taskId} " +
            "AND s.class_id = #{classId} " +
            "AND st.status = 0 AND s.status = 0")
    int countScoredStudentsByClass(@Param("assessmentId") Long assessmentId,
                                   @Param("taskId") Long taskId,
                                   @Param("classId") Long classId);

    /**
     * 获取平均分
     */
    @Select("SELECT AVG(student_scores.total_score) " +
            "FROM (" +
            "    SELECT SUM(st.score) as total_score " +
            "    FROM assessment_score_target st " +
            "    WHERE st.assessment_id = #{assessmentId} " +
            "    AND st.task_id = #{taskId} " +
            "    AND st.status = 0 " +
            "    GROUP BY st.student_id" +
            ") student_scores")
    BigDecimal getAverageScore(@Param("assessmentId") Long assessmentId,
                               @Param("taskId") Long taskId);

    /**
     * 获取最高分
     */
    @Select("SELECT MAX(student_scores.total_score) " +
            "FROM (" +
            "    SELECT SUM(st.score) as total_score " +
            "    FROM assessment_score_target st " +
            "    WHERE st.assessment_id = #{assessmentId} " +
            "    AND st.task_id = #{taskId} " +
            "    AND st.status = 0 " +
            "    GROUP BY st.student_id" +
            ") student_scores")
    BigDecimal getMaxScore(@Param("assessmentId") Long assessmentId,
                           @Param("taskId") Long taskId);

    /**
     * 获取最低分
     */
    @Select("SELECT MIN(student_scores.total_score) " +
            "FROM (" +
            "    SELECT SUM(st.score) as total_score " +
            "    FROM assessment_score_target st " +
            "    WHERE st.assessment_id = #{assessmentId} " +
            "    AND st.task_id = #{taskId} " +
            "    AND st.status = 0 " +
            "    GROUP BY st.student_id" +
            ") student_scores")
    BigDecimal getMinScore(@Param("assessmentId") Long assessmentId,
                           @Param("taskId") Long taskId);

    /**
     * 获取及格率
     */
    @Select("SELECT CASE " +
            "    WHEN COUNT(*) = 0 THEN 0 " +
            "    ELSE ROUND((SUM(CASE WHEN student_scores.total_score >= student_scores.assessment_total * 0.6 THEN 1 ELSE 0 END) / COUNT(*)) * 100, 2) " +
            "END as pass_rate " +
            "FROM (" +
            "    SELECT " +
            "        st.student_id, " +
            "        SUM(st.score) as total_score, " +
            "        COALESCE((" +
            "            SELECT SUM(CAST(JSON_UNQUOTE(JSON_EXTRACT(a.assessment_detail, CONCAT('$[', numbers.n, '].totalScore'))) AS DECIMAL(10,2))) " +
            "            FROM assessment a " +
            "            CROSS JOIN (SELECT 0 n UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5) numbers " +
            "            WHERE a.id = st.assessment_id " +
            "            AND JSON_UNQUOTE(JSON_EXTRACT(a.assessment_detail, CONCAT('$[', numbers.n, '].totalScore'))) IS NOT NULL" +
            "        ), 100) as assessment_total " +
            "    FROM assessment_score_target st " +
            "    WHERE st.assessment_id = #{assessmentId} " +
            "    AND st.task_id = #{taskId} " +
            "    AND st.status = 0 " +
            "    GROUP BY st.student_id, st.assessment_id" +
            ") student_scores")
    BigDecimal getPassRate(@Param("assessmentId") Long assessmentId,
                           @Param("taskId") Long taskId);

    /**
     * 批量查询教学任务的班级信息和学生统计
     *
     * @param taskIds 教学任务ID列表
     * @return 班级信息和学生统计列表
     */
    @Select("SELECT " +
            "    twc.task_id, " +
            "    c.class_id, " +
            "    c.class_name, " +
            "    COUNT(DISTINCT s.student_id) as student_count " +
            "FROM task_worklist_classes twc " +
            "INNER JOIN base_classes c ON twc.class_id = c.class_id " +
            "LEFT JOIN base_student s ON c.class_id = s.class_id AND s.status = 0 " +
            "WHERE FIND_IN_SET(twc.task_id, #{taskIds}) > 0 " +
            "AND c.status = 0 " +
            "GROUP BY twc.task_id, c.class_id, c.class_name " +
            "ORDER BY twc.task_id, c.class_id")
    List<AssessmentScoreMapper.TaskClassStatistics> batchSelectTaskClassStatistics(@Param("taskIds") String taskIds);

    /**
     * 批量查询班级已录入成绩数量
     *
     * @param assessmentId 考核ID
     * @param taskIds 教学任务ID列表
     * @return 班级成绩录入统计列表
     */
    @Select("SELECT " +
            "    scored_students.task_id, " +
            "    scored_students.class_id, " +
            "    COUNT(DISTINCT scored_students.student_id) as scored_count " +
            "FROM (" +
            "    SELECT DISTINCT st.task_id, s.class_id, st.student_id " +
            "    FROM assessment_score_target st " +
            "    INNER JOIN base_student s ON st.student_id = s.student_id " +
            "    WHERE st.assessment_id = #{assessmentId} " +
            "    AND FIND_IN_SET(st.task_id, #{taskIds}) > 0 " +
            "    AND st.status = 0 AND s.status = 0" +
            ") scored_students " +
            "GROUP BY scored_students.task_id, scored_students.class_id")
    List<AssessmentScoreMapper.ClassScoredStatistics> batchSelectClassScoredStatistics(@Param("assessmentId") Long assessmentId,
                                                                                       @Param("taskIds") String taskIds);

    /**
     * 批量查询教学任务的成绩统计信息（优化版本 - 使用字符串拼接）
     *
     * @param assessmentId 考核ID
     * @param taskIds 教学任务ID列表，格式："1,2,3,4"
     * @return 成绩统计信息列表
     */
    @Select("SELECT " +
            "    student_scores.task_id, " +
            "    COUNT(DISTINCT student_scores.student_id) as total_scored_students, " +
            "    ROUND(AVG(student_scores.total_score), 2) as average_score, " +
            "    MAX(student_scores.total_score) as max_score, " +
            "    MIN(student_scores.total_score) as min_score, " +
            "    ROUND(CASE " +
            "        WHEN COUNT(*) = 0 THEN 0 " +
            "        ELSE (SUM(CASE WHEN student_scores.total_score >= student_scores.assessment_total * 0.6 THEN 1 ELSE 0 END) / COUNT(*)) * 100 " +
            "    END, 2) as pass_rate " +
            "FROM (" +
            "    SELECT " +
            "        st.task_id, " +
            "        st.student_id, " +
            "        SUM(st.score) as total_score, " +
            "        COALESCE((" +
            "            SELECT SUM(CAST(JSON_UNQUOTE(JSON_EXTRACT(a.assessment_detail, CONCAT('$[', numbers.n, '].totalScore'))) AS DECIMAL(10,2))) " +
            "            FROM assessment a " +
            "            CROSS JOIN (SELECT 0 n UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5) numbers " +
            "            WHERE a.id = st.assessment_id " +
            "            AND JSON_UNQUOTE(JSON_EXTRACT(a.assessment_detail, CONCAT('$[', numbers.n, '].totalScore'))) IS NOT NULL" +
            "        ), 100) as assessment_total " +
            "    FROM assessment_score_target st " +
            "    WHERE st.assessment_id = #{assessmentId} " +
            "    AND FIND_IN_SET(st.task_id, #{taskIds}) > 0 " +
            "    AND st.status = 0 " +
            "    GROUP BY st.task_id, st.student_id, st.assessment_id" +
            ") student_scores " +
            "GROUP BY student_scores.task_id")
    List<AssessmentScoreMapper.TaskScoreStatistics> batchSelectTaskScoreStatistics(@Param("assessmentId") Long assessmentId,
                                                                                   @Param("taskIds") String taskIds);

    /**
     * 班级统计信息VO
     */
    @Data
    public static class TaskClassStatistics {
        private Long taskId;
        private Long classId;
        private String className;
        private Integer studentCount;
    }

    /**
     * 班级成绩录入统计VO
     */
    @Data
    public static class ClassScoredStatistics {
        private Long taskId;
        private Long classId;
        private Integer scoredCount;
    }

    /**
     * 教学任务成绩统计VO
     */
    @Data
    public static class TaskScoreStatistics {
        private Long taskId;
        private Integer totalScoredStudents;
        private BigDecimal averageScore;
        private BigDecimal maxScore;
        private BigDecimal minScore;
        private BigDecimal passRate;
    }

    /**
     * 超级优化：一次查询获取所有教学任务的完整统计信息
     * 使用复杂JOIN和子查询，最大化减少数据库交互
     *
     * @param assessmentId 考核ID
     * @return 完整的教学任务统计信息
     */
    @Select("SELECT " +
            // 教学任务基本信息
            "    tw.id as task_id, " +
            "    tw.course_id, " +
            "    tw.task_name, " +
            "    tw.task_number, " +
            "    tw.task_year, " +
            "    tw.task_term, " +
            "    tw.teach_week, " +
            "    tw.week_hours, " +
            "    tw.total_hours, " +
            "    tw.course_leader_id, " +
            "    tw.major_id, " +
            "    tw.plan_id, " +
            "    tw.status, " +
            "    tw.task_status, " +
            "    tw.creator, " +
            "    tw.create_time, " +
            "    tw.modifier, " +
            "    tw.modify_time, " +
            "    at.create_time as publish_time, " +
            // 学生统计信息（使用子查询）
            "    COALESCE(student_stats.total_students, 0) as total_students, " +
            "    COALESCE(score_stats.total_scored_students, 0) as total_scored_students, " +
            "    score_stats.average_score, " +
            "    score_stats.max_score, " +
            "    score_stats.min_score, " +
            "    score_stats.pass_rate " +
            "FROM assessment_task at " +
            "INNER JOIN task_worklist tw ON at.task_id = tw.id " +
            // 子查询：获取教学任务的学生总数
            "LEFT JOIN (" +
            "    SELECT " +
            "        twc.task_id, " +
            "        COUNT(DISTINCT s.student_id) as total_students " +
            "    FROM task_worklist_classes twc " +
            "    INNER JOIN base_classes c ON twc.class_id = c.class_id " +
            "    LEFT JOIN base_student s ON c.class_id = s.class_id AND s.status = 0 " +
            "    WHERE c.status = 0 " +
            "    GROUP BY twc.task_id" +
            ") student_stats ON tw.id = student_stats.task_id " +
            // 子查询：获取成绩统计信息
            "LEFT JOIN (" +
            "    SELECT " +
            "        student_scores.task_id, " +
            "        COUNT(DISTINCT student_scores.student_id) as total_scored_students, " +
            "        ROUND(AVG(student_scores.total_score), 2) as average_score, " +
            "        MAX(student_scores.total_score) as max_score, " +
            "        MIN(student_scores.total_score) as min_score, " +
            "        ROUND(CASE " +
            "            WHEN COUNT(*) = 0 THEN 0 " +
            "            ELSE (SUM(CASE WHEN student_scores.total_score >= student_scores.assessment_total * 0.6 THEN 1 ELSE 0 END) / COUNT(*)) * 100 " +
            "        END, 2) as pass_rate " +
            "    FROM (" +
            "        SELECT " +
            "            s.task_id, s.student_id, " +
            "            SUM(s.score) as total_score, " +
            "            COALESCE((" +
            "                SELECT SUM(CAST(JSON_UNQUOTE(JSON_EXTRACT(a.assessment_detail, CONCAT('$[', numbers.n, '].totalScore'))) AS DECIMAL(10,2))) " +
            "                FROM assessment a " +
            "                CROSS JOIN (SELECT 0 n UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5) numbers " +
            "                WHERE a.id = s.assessment_id " +
            "                AND JSON_UNQUOTE(JSON_EXTRACT(a.assessment_detail, CONCAT('$[', numbers.n, '].totalScore'))) IS NOT NULL" +
            "            ), 100) as assessment_total " +
            "        FROM assessment_score_target s " +
            "        WHERE s.assessment_id = #{assessmentId} AND s.status = 0 " +
            "        GROUP BY s.task_id, s.student_id, s.assessment_id" +
            "    ) student_scores " +
            "    GROUP BY student_scores.task_id" +
            ") score_stats ON tw.id = score_stats.task_id " +
            "WHERE at.assessment_id = #{assessmentId} " +
            "AND at.status = 0 AND tw.status = 0 " +
            "ORDER BY tw.task_number, tw.id")
    List<AssessmentScoreMapper.SuperOptimizedTaskDetail> selectSuperOptimizedTaskDetails(@Param("assessmentId") Long assessmentId);

    /**
     * 教学任务详情VO（包含所有统计信息）
     */
    @Data
    public static class SuperOptimizedTaskDetail {
        // 教学任务基本信息
        private Long taskId;
        private Long courseId;
        private String taskName;
        private Integer taskNumber;
        private Integer taskYear;
        private Integer taskTerm;
        private Integer teachWeek;
        private Integer weekHours;
        private Integer totalHours;
        private Long courseLeaderId;
        private Long majorId;
        private Long planId;
        private Integer status;
        private Integer taskStatus;
        private Long creator;
        private LocalDateTime createTime;
        private Long modifier;
        private LocalDateTime modifyTime;
        private LocalDateTime publishTime;

        // 统计信息
        private Integer totalStudents;
        private Integer totalScoredStudents;
        private BigDecimal averageScore;
        private BigDecimal maxScore;
        private BigDecimal minScore;
        private BigDecimal passRate;
    }

    /**
     * 根据学生ID、考核ID和题目答案ID查询成绩记录
     * 用于判断是否需要新增或更新
     */
    @Select("SELECT * FROM assessment_score_target " +
            "WHERE student_id = #{studentId} " +
            "AND assessment_id = #{assessmentId} " +
            "AND repository_answer_id = #{repositoryAnswerId} " +
            "AND status = 0")
    AssessmentScoreTarget selectByStudentAssessmentAndAnswer(@Param("studentId") Long studentId,
                                                           @Param("assessmentId") Long assessmentId,
                                                           @Param("repositoryAnswerId") Long repositoryAnswerId);

    /**
     * 批量查询现有成绩记录
     * 用于批量操作时的重复检查
     */
    @Select("SELECT * FROM assessment_score_target " +
            "WHERE assessment_id = #{assessmentId} " +
            "AND FIND_IN_SET(CONCAT(student_id, '_', objective_id), #{keys}) > 0 " +
            "AND status = 0")
    List<AssessmentScoreTarget> selectExistingRecords(@Param("assessmentId") Long assessmentId,
                                                     @Param("keys") String keys);

    /**
     * 优化版本：直接查询考核和任务相关的所有成绩记录及学生信息
     * 一次查询获取成绩数据和学生基本信息，避免多次查询
     *
     * @param assessmentId 考核ID
     * @param taskId 教学任务ID
     * @return 成绩记录列表（包含学生信息）
     */
    // 更新现有方法，使用子查询优化
    @Select("""
        SELECT 
            score_data.*,
            s.student_number,
            s.student_name,
            s.class_id,
            c.class_name
        FROM (
            SELECT *
            FROM assessment_score_target
            WHERE assessment_id = #{assessmentId}
            AND task_id = #{taskId}
            AND status = 0
        ) score_data
        INNER JOIN base_student s ON score_data.student_id = s.student_id AND s.status = 0
        LEFT JOIN base_classes c ON s.class_id = c.class_id AND c.status = 0
        ORDER BY s.student_number, score_data.course_target_no
        """)
    List<AssessmentScoreTargetWithStudent> selectScoresWithStudentInfo(@Param("assessmentId") Long assessmentId,
                                                                       @Param("taskId") Long taskId);

    /**
     * 查询任务中所有学生ID（用于补充没有成绩记录的学生）
     *
     * @param taskId 教学任务ID
     * @return 学生ID列表
     */
    @Select("""
            SELECT DISTINCT s.student_id
            FROM base_student s
            INNER JOIN base_classes c ON s.class_id = c.class_id
            INNER JOIN task_worklist_classes twc ON c.class_id = twc.class_id
            WHERE twc.task_id = #{taskId}
            AND s.status = 0
            AND c.status = 0
            """)
    List<Long> selectAllStudentIdsByTaskId(@Param("taskId") Long taskId);

    /**
     * 成绩记录与学生信息的组合VO
     */
    @Data
    public static class AssessmentScoreTargetWithStudent {
        // 成绩记录字段
        private Long id;
        private Long studentId;
        private Long assessmentId;
        private Long taskId;
        private Integer courseTargetNo;
        private BigDecimal score;
        private BigDecimal fullScore;
        private String objectiveId;
        private Long poId;
        private Long repositoryAnswerId;
        private Integer status;
        private Long creator;
        private LocalDateTime createTime;
        private Long modifier;
        private LocalDateTime modifyTime;

        // 学生信息字段
        private String studentNumber;
        private String studentName;
        private Long classId;
        private String className;
    }
}
