package com.hnumi.obe.assessment.mapper;

import lombok.Data;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 优化的考核成绩查询 Mapper
 * 参考 AchievementAnalysisMapper 的设计模式，使用复杂SQL一次性获取所有数据
 */
@Mapper
public interface OptimizedAssessmentScoreMapper {

    /**
     * 批量获取学生成绩详情（优化版）
     * 一次查询获取所有需要的数据，避免N+1查询问题
     */
    @Select("""
        WITH student_base_info AS (
            SELECT DISTINCT 
                s.student_id,
                s.student_number,
                s.student_name,
                s.class_id,
                c.class_name,
                s.status as student_status
            FROM base_student s
            LEFT JOIN base_classes c ON s.class_id = c.class_id
            WHERE s.class_id IN (
                SELECT twc.class_id 
                FROM task_worklist_classes twc 
                WHERE twc.task_id = #{taskId}
            )
            AND s.status = 0
        ),
        target_scores AS (
            SELECT
                ast.student_id,
                ast.course_target_no,
                ast.score,
                ast.full_score,
                ast.create_time,
                ast.modify_time,
                ast.creator
            FROM assessment_score_target ast
            INNER JOIN assessment a ON ast.assessment_id = a.id
            WHERE ast.assessment_id = #{assessmentId}
            AND ast.task_id = #{taskId}
            AND ast.status = 0
            AND a.status = 0
        ),
        detail_scores AS (
            SELECT
                asd.student_id,
                asd.repository_answer_id,
                asd.score as detail_score,
                asd.full_score as detail_full_score,
                rq.question_topic as question_content,
                rq.question_type,
                rq.question_score
            FROM assessment_score_detail asd
            INNER JOIN assessment a ON asd.assessment_id = a.id
            LEFT JOIN repository_answer ra ON asd.repository_answer_id = ra.id
            LEFT JOIN repository_question rq ON ra.question_id = rq.id
            WHERE asd.assessment_id = #{assessmentId}
            AND asd.task_id = #{taskId}
            AND asd.status = 0
            AND a.status = 0
        ),

        assessment_info AS (
            SELECT
                a.id as assessment_id,
                a.assessment_method,
                a.assessment_name,
                a.course_id,
                c.course_target,
                c.course_name
            FROM assessment a
            INNER JOIN tp_course c ON a.course_id = c.course_id
            WHERE a.id = #{assessmentId}
            AND a.status = 0
            AND c.status = 0
        )
        SELECT
            sbi.student_id,
            sbi.student_number,
            sbi.student_name,
            sbi.class_id,
            sbi.class_name,
            ts.course_target_no,
            ts.score as target_score,
            ts.full_score as target_full_score,
            ts.create_time as entry_time,
            ts.modify_time as last_modify_time,
            ts.creator as entry_user_id,
            ds.repository_answer_id,
            ds.detail_score,
            ds.detail_full_score,
            ds.question_content,
            ds.question_type,
            ds.question_score,
            ai.assessment_method,
            ai.assessment_name,
            ai.course_target,
            ai.course_name
        FROM student_base_info sbi
        CROSS JOIN assessment_info ai
        LEFT JOIN target_scores ts ON sbi.student_id = ts.student_id
        LEFT JOIN detail_scores ds ON sbi.student_id = ds.student_id
        ORDER BY sbi.student_number, ts.course_target_no, ds.repository_answer_id
    """)
    List<StudentScoreData> batchGetStudentScoreDetails(@Param("assessmentId") Long assessmentId,
                                                       @Param("taskId") Long taskId);

    /**
     * 批量获取学生课程目标成绩（优化版）
     */
    @Select("""
        WITH course_info AS (
            SELECT 
                a.course_id,
                c.course_name,
                c.course_target
            FROM assessment a
            INNER JOIN tp_course c ON a.course_id = c.course_id
            WHERE a.id = #{assessmentId}
        ),
        student_scores AS (
            SELECT
                s.student_id,
                s.student_number,
                s.student_name,
                ast.course_target_no,
                ast.score,
                a.assessment_method,
                a.course_id
            FROM base_student s
            INNER JOIN assessment_score_target ast ON s.student_id = ast.student_id
            INNER JOIN assessment a ON ast.assessment_id = a.id
            WHERE ast.assessment_id = #{assessmentId}
            AND ast.task_id = #{taskId}
            AND ast.status = 0
            AND s.status = 0
            AND a.status = 0
        )
        SELECT 
            ss.student_id,
            ss.student_number,
            ss.student_name,
            ss.course_target_no,
            ss.score,
            ss.assessment_method,
            ss.course_id
        FROM student_scores ss
        ORDER BY ss.student_number, ss.course_target_no
    """)
    List<CourseTargetScore> batchGetStudentCourseTargetScores(@Param("assessmentId") Long assessmentId,
                                                              @Param("taskId") Long taskId);

    /**
     * 获取目标成绩统计信息（优化版）
     */
    @Select("""
        SELECT 
            ast.course_target_no,
            COUNT(DISTINCT ast.student_id) as student_count,
            AVG(ast.score) as average_score,
            MAX(ast.score) as max_score,
            MIN(ast.score) as min_score,
            SUM(CASE WHEN ast.score >= (ast.full_score * 0.6) THEN 1 ELSE 0 END) as pass_count
        FROM assessment_score_target ast
        WHERE ast.assessment_id = #{assessmentId}
        AND ast.task_id = #{taskId}
        AND ast.status = 0
        GROUP BY ast.course_target_no
        ORDER BY ast.course_target_no
    """)
    List<TargetScoreStatistics> getTargetScoreStatistics(@Param("assessmentId") Long assessmentId,
                                                         @Param("taskId") Long taskId);

    /**
     * 优化的学生成绩数据结构
     */
    @Data
    class StudentScoreData {
        private Long studentId;
        private String studentNumber;
        private String studentName;
        private Long classId;
        private String className;
        private Integer courseTargetNo;
        private BigDecimal targetScore;
        private BigDecimal targetFullScore;
        private LocalDateTime entryTime;
        private LocalDateTime lastModifyTime;
        private Long entryUserId;
        private String courseTargetName;
        private BigDecimal courseTargetWeight;
        private Long repositoryAnswerId;
        private BigDecimal detailScore;
        private BigDecimal detailFullScore;
        private String questionContent;
        private String questionType;
        private BigDecimal questionScore;
        private String assessmentMethod;
        private String assessmentName;
        private String courseTarget;
        private String courseName;

        // Getters and Setters

    }

    /**
     * 优化的课程目标成绩数据结构
     */
    @Data
    class CourseTargetScore {
        private Long studentId;
        private String studentNumber;
        private String studentName;
        private Integer courseTargetNo;
        private BigDecimal score;
        private String assessmentMethod;
        private Long courseId;

    }

    /**
     * 目标成绩统计信息
     */
    @Data
    class TargetScoreStatistics {
        private Integer courseTargetNo;
        private Integer studentCount;
        private BigDecimal averageScore;
        private BigDecimal maxScore;
        private BigDecimal minScore;
        private Integer passCount;
    }
}
