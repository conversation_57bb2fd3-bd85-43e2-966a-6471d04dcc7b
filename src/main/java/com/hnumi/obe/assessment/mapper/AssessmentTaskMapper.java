package com.hnumi.obe.assessment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hnumi.obe.assessment.entity.AssessmentTask;
import lombok.Data;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 考核与教学任务关联表 Mapper 接口
 */
@Mapper
public interface AssessmentTaskMapper extends BaseMapper<AssessmentTask> {

    /**
     * 根据考核ID查询已发布的教学任务ID列表
     *
     * @param assessmentId 考核ID
     * @return 教学任务ID列表
     */
    @Select("SELECT task_id FROM assessment_task WHERE assessment_id = #{assessmentId} AND status = 0")
    List<Long> selectTaskIdsByAssessmentId(@Param("assessmentId") Long assessmentId);

    /**
     * 根据教学任务ID查询已发布的考核ID列表
     *
     * @param taskId 教学任务ID
     * @return 考核ID列表
     */
    @Select("SELECT assessment_id FROM assessment_task WHERE task_id = #{taskId} AND status = 0")
    List<Long> selectAssessmentIdsByTaskId(@Param("taskId") Long taskId);

    /**
     * 检查考核是否已发布给指定教学任务
     *
     * @param assessmentId 考核ID
     * @param taskId 教学任务ID
     * @return 关联记录数量
     */
    @Select("SELECT COUNT(*) FROM assessment_task WHERE assessment_id = #{assessmentId} AND task_id = #{taskId} AND status = 0")
    int countByAssessmentIdAndTaskId(@Param("assessmentId") Long assessmentId, @Param("taskId") Long taskId);

    /**
     * 批量查询考核发布时间
     *
     * @param assessmentId 考核ID
     * @param taskIds 教学任务ID列表
     * @return 发布时间信息列表
     */
    @Select("<script>" +
            "SELECT task_id, create_time as publish_time " +
            "FROM assessment_task " +
            "WHERE assessment_id = #{assessmentId} " +
            "AND task_id IN " +
            "<foreach collection='taskIds' item='taskId' open='(' separator=',' close=')'>" +
            "    #{taskId}" +
            "</foreach> " +
            "AND status = 0" +
            "</script>")
    List<AssessmentPublishTime> batchSelectPublishTimes(@Param("assessmentId") Long assessmentId,
                                                       @Param("taskIds") List<Long> taskIds);

    /**
     * 根据考核ID直接查询关联的教学任务详情（一次查询获取完整信息）
     *
     * @param assessmentId 考核ID
     * @return 教学任务详情列表（包含发布时间）
     */
    @Select("SELECT " +
            "    tw.id, " +
            "    tw.course_id, " +
            "    tw.task_name, " +
            "    tw.task_number, " +
            "    tw.task_year, " +
            "    tw.task_term, " +
            "    tw.teach_week, " +
            "    tw.week_hours, " +
            "    tw.total_hours, " +
            "    tw.course_leader_id, " +
            "    tw.major_id, " +
            "    tw.plan_id, " +
            "    tw.status, " +
            "    tw.task_status, " +
            "    tw.creator, " +
            "    tw.create_time, " +
            "    tw.modifier, " +
            "    tw.modify_time, " +
            "    at.create_time as publish_time " +
            "FROM assessment_task at " +
            "INNER JOIN task_worklist tw ON at.task_id = tw.id " +
            "WHERE at.assessment_id = #{assessmentId} " +
            "AND at.status = 0 " +
            "AND tw.status = 0 " +
            "ORDER BY tw.task_number, tw.id")
    List<TaskWorkWithPublishTime> selectTaskWorkListByAssessmentId(@Param("assessmentId") Long assessmentId);

    /**
     * 考核发布时间VO
     */
    @Data
    public static class AssessmentPublishTime {
        private Long taskId;
        private LocalDateTime publishTime;
    }

    /**
     * 教学任务详情（包含发布时间）VO
     */
    @Data
    public static class TaskWorkWithPublishTime {
        // TaskWork 基本字段
        private Long id;
        private Long courseId;
        private String taskName;
        private Integer taskNumber;
        private Integer taskYear;
        private Integer taskTerm;
        private Integer teachWeek;
        private Integer weekHours;
        private Integer totalHours;
        private Long courseLeaderId;
        private Long majorId;
        private Long planId;
        private Integer status;
        private Integer taskStatus;
        private Long creator;
        private LocalDateTime createTime;
        private Long modifier;
        private LocalDateTime modifyTime;

        // 发布时间
        private LocalDateTime publishTime;
    }
}
