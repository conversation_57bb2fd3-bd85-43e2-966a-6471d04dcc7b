package com.hnumi.obe.assessment.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hnumi.obe.assessment.entity.RepositoryAnswer;
import com.hnumi.obe.assessment.mapper.RepositoryAnswerMapper;
import com.hnumi.obe.assessment.service.IRepositoryAnswerService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2025-07-14 02:00
 */
@Service
public class RepositoryAnswerServiceImpl extends ServiceImpl<RepositoryAnswerMapper, RepositoryAnswer> implements IRepositoryAnswerService {

    @Override
    public List<RepositoryAnswer> getAnswersByQuestionId(Long questionId) {
        return baseMapper.selectAnswersByQuestionId(questionId);
    }
}
