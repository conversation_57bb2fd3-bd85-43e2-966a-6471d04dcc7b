package com.hnumi.obe.assessment.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hnumi.obe.assessment.dto.*;
import com.hnumi.obe.assessment.entity.AssessmentScoreTarget;
import com.hnumi.obe.assessment.vo.*;

import java.io.InputStream;
import java.util.List;

/**
 * 考核成绩管理 服务类
 */
public interface IAssessmentScoreService extends IService<AssessmentScoreTarget> {

    /**
     * 查询教师负责的教学任务列表
     *
     * @param queryDTO 查询条件
     * @return 教学任务列表
     */
    List<TeacherTaskVO> getTeacherTasks(TeacherTaskQueryDTO queryDTO);

    /**
     * 查询教学任务下的考核列表
     *
     * @param taskId 教学任务ID
     * @param teacherId 教师ID（用于权限验证）
     * @return 考核列表
     */
    List<TaskAssessmentScoreVO> getTaskAssessments(Long taskId, Long teacherId);


    /**
     * 验证教师是否有权限操作该教学任务
     *
     * @param taskId 教学任务ID
     * @param teacherId 教师ID
     * @return 是否有权限
     */
    boolean hasTaskPermission(Long taskId, Long teacherId);

    /**
     * 验证考核是否已发布给该教学任务
     *
     * @param assessmentId 考核ID
     * @param taskId 教学任务ID
     * @return 是否已发布
     */
    boolean isAssessmentPublished(Long assessmentId, Long taskId);

    //================== 扩展的成绩导入模式 ==================

    /**
     * 按课程目标导入成绩
     *
     * @param importDTO 课程目标成绩导入数据
     * @param teacherId 教师ID（用于权限验证）
     * @return 导入结果
     */
    ScoreImportResultVO importScoresByTarget(ScoreTargetImportDTO importDTO, Long teacherId);

    /**
     * 按考核详情导入成绩
     *
     * @param importDTO 考核详情成绩导入数据
     * @param teacherId 教师ID（用于权限验证）
     * @return 导入结果
     */
    ScoreImportResultVO importScoresByDetail(ScoreDetailImportDTO importDTO, Long teacherId);

    /**
     * Excel导入课程目标成绩
     *
     * @param assessmentId 考核ID
     * @param taskId 教学任务ID
     * @param inputStream Excel文件输入流
     * @param teacherId 教师ID（用于权限验证）
     * @return 导入结果
     */
    ScoreImportResultVO importTargetScoresFromExcel(Long assessmentId, Long taskId, InputStream inputStream, Long teacherId);

    /**
     * Excel导入考核详情成绩
     *
     * @param assessmentId 考核ID
     * @param taskId 教学任务ID
     * @param inputStream Excel文件输入流
     * @param teacherId 教师ID（用于权限验证）
     * @return 导入结果
     */
    ScoreImportResultVO importDetailScoresFromExcel(Long assessmentId, Long taskId, InputStream inputStream, Long teacherId);

    /**
     * Excel导入直接录入模式成绩
     *
     * @param assessmentId 考核ID
     * @param taskId 教学任务ID
     * @param inputStream Excel文件输入流
     * @param teacherId 教师ID（用于权限验证）
     * @return 导入结果
     */
    ScoreImportResultVO importDirectEntryScoresFromExcel(Long assessmentId, Long taskId, InputStream inputStream, Long teacherId);

    /**
     * 导出课程目标成绩模板
     *
     * @param assessmentId 考核ID
     * @param taskId 教学任务ID
     * @param teacherId 教师ID（用于权限验证）
     * @return 课程目标成绩模板数据
     */
    List<ScoreTargetExcelImportDTO> exportTargetScoreTemplate(Long assessmentId, Long taskId, Long teacherId);

    /**
     * 导出考核详情成绩模板
     *
     * @param assessmentId 考核ID
     * @param taskId 教学任务ID
     * @param teacherId 教师ID（用于权限验证）
     * @return 考核详情成绩模板数据
     */
    List<ScoreDetailExcelImportDTO> exportDetailScoreTemplate(Long assessmentId, Long taskId, Long teacherId);

    /**
     * 导出课程目标成绩
     *
     * @param assessmentId 考核ID
     * @param taskId 教学任务ID
     * @param teacherId 教师ID（用于权限验证）
     * @return 课程目标成绩数据
     */
    List<ScoreTargetExcelImportDTO> exportTargetScores(Long assessmentId, Long taskId, Long teacherId);

    /**
     * 导出考核详情成绩
     *
     * @param assessmentId 考核ID
     * @param taskId 教学任务ID
     * @param teacherId 教师ID（用于权限验证）
     * @return 考核详情成绩数据
     */
    List<ScoreDetailExcelImportDTO> exportDetailScores(Long assessmentId, Long taskId, Long teacherId);

    //================== 成绩记录删除相关方法 ==================
    /**
     * 根据考核ID物理删除所有相关成绩记录
     * 包括assessment_score、assessment_score_target、assessment_score_detail表中的记录
     *
     * @param assessmentId 考核ID
     * @return 删除的记录总数
     */
    int physicalDeleteAllScoresByAssessmentId(Long assessmentId);

    /**
     * 根据考核ID和教学任务ID物理删除相关成绩记录
     * 包括assessment_score、assessment_score_target、assessment_score_detail表中的记录
     *
     * @param assessmentId 考核ID
     * @param taskIds 教学任务ID列表，如果为空则删除所有相关记录
     * @return 删除的记录总数
     */
    int physicalDeleteScoresByAssessmentAndTasks(Long assessmentId, List<Long> taskIds);

    //================== 新增的核心业务功能接口 ==================

    /**
     * 查询考核关联的教学任务详情
     *
     * @param assessmentId 考核ID
     * @return 教学任务详情列表
     */
    List<AssessmentTaskDetailVO> getAssessmentTaskDetails(Long assessmentId);
   // List<AssessmentTaskDetailVO> getAssessmentTaskDetailsSuperOptimized(Long assessmentId);

    /**
     * 查询学生成绩详情
     *
     * @param assessmentId 考核ID
     * @param taskId 教学任务ID
     * @return 学生成绩详情列表
     */
    List<StudentScoreDetailVO> getStudentScoreDetails(Long assessmentId, Long taskId);

    /**
     * 根据教学任务ID获取学生课程目标成绩列表
     * @param assessmentId 考核ID
     * @param taskId 教学任务ID
     * @return 学生课程目标成绩列表
     */
    List<StudentScoreTargetVO> getStudentTargetScoresByTaskId(Long assessmentId, Long taskId);

    /**
     * 查询单个学生的成绩详情
     *
     * @param assessmentId 考核ID
     * @param taskId 教学任务ID
     * @param studentId 学生ID
     * @return 学生成绩详情
     */
    StudentScoreDetailVO getStudentScoreDetail(Long assessmentId, Long taskId, Long studentId);

    /**
     * 批量保存学生课程目标成绩
     *
     * @param batchSaveDTO 批量保存请求数据
     * @param teacherId 教师ID（用于权限验证）
     * @return 保存结果
     */
    BatchSaveTargetScoresResultVO batchSaveTargetScores(BatchSaveTargetScoresDTO batchSaveDTO, Long teacherId);

    /**
     * 查询学生课程目标达成度
     * @param courseId
     * @return
     */
    List<StudentCourseTargetScoreVO> getStudentCourseTargetScores(Long courseId);
}
