package com.hnumi.obe.assessment.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hnumi.obe.assessment.entity.AssessmentTask;
import com.hnumi.obe.assessment.vo.AssessmentVO;
import com.hnumi.obe.task.entity.TaskWork;

import java.math.BigDecimal;
import java.util.List;

/**
 * 考核与教学任务关联表 服务类
 */
public interface IAssessmentTaskService extends IService<AssessmentTask> {

    /**
     * 根据考核ID查询已发布的教学任务ID列表
     *
     * @param assessmentId 考核ID
     * @return 教学任务ID列表
     */
    List<Long> getTaskIdsByAssessmentId(Long assessmentId);

    /**
     * 根据教学任务ID查询已发布的考核ID列表
     *
     * @param taskId 教学任务ID
     * @return 考核ID列表
     */
    List<Long> getAssessmentIdsByTaskId(Long taskId);

    /**
     * 检查考核是否已发布给指定教学任务
     *
     * @param assessmentId 考核ID
     * @param taskId 教学任务ID
     * @return 是否已发布
     */
    boolean isPublished(Long assessmentId, Long taskId);

    /**
     * 根据考核ID直接获取关联的教学任务列表（优化版本）
     * 一次查询获取教学任务详情和发布时间，减少数据库交互次数
     *
     * @param assessmentId 考核ID
     * @return 教学任务列表
     */
    List<TaskWork> getTaskWorkListByAssessmentId(Long assessmentId);

    /**
     * 批量创建考核与教学任务的关联关系
     *
     * @param assessmentId 考核ID
     * @param taskIds 教学任务ID列表
     * @param publishNote 发布说明
     * @param creator 创建者ID
     * @return 创建成功的数量
     */
    int batchCreateAssessmentTaskRelations(Long assessmentId, List<Long> taskIds, String publishNote, Long creator);

    /**
     * 删除考核与教学任务的关联关系
     *
     * @param assessmentId 考核ID
     * @param taskIds 教学任务ID列表（为空时删除该考核的所有关联）
     * @return 删除成功的数量
     */
    int removeAssessmentTaskRelations(Long assessmentId, List<Long> taskIds);

    /**
     * 根据考核ID物理删除assessment_task表中的所有关联记录
     *
     * @param assessmentId 考核ID
     * @return 删除的记录数
     */
    int physicalDeleteByAssessmentId(Long assessmentId);

    /**
     * 根据考核ID和教学任务ID列表物理删除assessment_task表中的关联记录
     *
     * @param assessmentId 考核ID
     * @param taskIds 教学任务ID列表
     * @return 删除的记录数
     */
    int physicalDeleteByAssessmentAndTasks(Long assessmentId, List<Long> taskIds);
    
    /**
     * 计算指定教学任务中已分配的考核权重总和
     *
     * @param taskId 教学任务ID
     * @param excludeAssessmentId 需要排除的考核ID（用于更新场景）
     * @return 已分配的权重总和
     */
    BigDecimal calculateAssignedWeight(Long taskId, Long excludeAssessmentId);
    
    /**
     * 验证指定教学任务的考核权重是否有效
     *
     * @param taskId 教学任务ID
     * @param assessmentId 考核ID
     * @param weight 要设置的权重
     * @return 验证结果，true表示有效，false表示无效
     */
    boolean validateAssessmentWeight(Long taskId, Long assessmentId, BigDecimal weight);
    
    /**
     * 更新指定教学任务中指定考核的权重
     *
     * @param taskId 教学任务ID
     * @param assessmentId 考核ID
     * @param weight 新的权重值
     * @return 更新结果，true表示成功，false表示失败
     */
    boolean updateTaskAssessmentWeight(Long taskId, Long assessmentId, Double weight);
    
    /**
     * 更新指定教学任务中指定考核的权重并返回更新后的考核信息
     *
     * @param taskId 教学任务ID
     * @param assessmentId 考核ID
     * @param weight 新的权重值
     * @return 更新后的考核信息
     */
    AssessmentVO updateAndGetAssessment(Long taskId, Long assessmentId, Double weight);
}