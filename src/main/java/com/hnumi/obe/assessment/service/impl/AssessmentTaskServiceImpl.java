package com.hnumi.obe.assessment.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hnumi.obe.assessment.entity.Assessment;
import com.hnumi.obe.assessment.entity.AssessmentTask;
import com.hnumi.obe.assessment.mapper.AssessmentTaskMapper;
import com.hnumi.obe.assessment.service.IAssessmentService;
import com.hnumi.obe.assessment.service.IAssessmentTaskService;
import com.hnumi.obe.assessment.vo.AssessmentVO;
import com.hnumi.obe.task.entity.TaskWork;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 考核与教学任务关联表 服务实现类
 */
@Slf4j
@Service
public class AssessmentTaskServiceImpl extends ServiceImpl<AssessmentTaskMapper, AssessmentTask> implements IAssessmentTaskService {

    @Autowired
    private AssessmentTaskMapper assessmentTaskMapper;
    
    @Autowired
    @Lazy
    private IAssessmentService assessmentService;

    @Override
    public List<Long> getTaskIdsByAssessmentId(Long assessmentId) {
        return assessmentTaskMapper.selectTaskIdsByAssessmentId(assessmentId);
    }

    @Override
    public List<Long> getAssessmentIdsByTaskId(Long taskId) {
        return assessmentTaskMapper.selectAssessmentIdsByTaskId(taskId);
    }

    @Override
    public boolean isPublished(Long assessmentId, Long taskId) {
        return assessmentTaskMapper.countByAssessmentIdAndTaskId(assessmentId, taskId) > 0;
    }

    @Override
    public List<TaskWork> getTaskWorkListByAssessmentId(Long assessmentId) {
        if (assessmentId == null) {
            return new ArrayList<>();
        }

        try {
            // 一次查询获取教学任务详情和发布时间
            List<AssessmentTaskMapper.TaskWorkWithPublishTime> taskWorkWithPublishTimes =
                assessmentTaskMapper.selectTaskWorkListByAssessmentId(assessmentId);

            if (CollectionUtils.isEmpty(taskWorkWithPublishTimes)) {
                return new ArrayList<>();
            }

            // 转换为 TaskWork 对象列表
            List<TaskWork> taskWorks = new ArrayList<>();
            for (AssessmentTaskMapper.TaskWorkWithPublishTime taskWorkWithPublishTime : taskWorkWithPublishTimes) {
                TaskWork taskWork = new TaskWork();

                // 复制基本属性
                BeanUtils.copyProperties(taskWorkWithPublishTime, taskWork);

                // 注意：发布时间信息在这里不需要设置到 TaskWork 中，
                // 因为 TaskWork 实体本身不包含发布时间字段
                // 如果需要发布时间，可以在调用方通过其他方式获取

                taskWorks.add(taskWork);
            }

            return taskWorks;

        } catch (Exception e) {
            log.error("根据考核ID查询教学任务列表失败，assessmentId: {}", assessmentId, e);
            return new ArrayList<>();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchCreateAssessmentTaskRelations(Long assessmentId, List<Long> taskIds, String publishNote, Long creator) {
        if (CollectionUtils.isEmpty(taskIds)) {
            return 0;
        }

        List<AssessmentTask> assessmentTasks = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();

        for (Long taskId : taskIds) {
            // 检查是否已存在关联关系
            if (!isPublished(assessmentId, taskId)) {
                AssessmentTask assessmentTask = new AssessmentTask();
                assessmentTask.setAssessmentId(assessmentId);
                assessmentTask.setTaskId(taskId);
                assessmentTask.setPublishNote(publishNote);
                assessmentTask.setPublishStatus(0); // 正常发布
                assessmentTask.setStatus(0); // 正常状态
                assessmentTask.setCreator(creator);
                assessmentTask.setCreateTime(now);
                assessmentTask.setModifier(creator);
                assessmentTask.setModifyTime(now);
                
                assessmentTasks.add(assessmentTask);
            }
        }

        if (CollectionUtils.isEmpty(assessmentTasks)) {
            log.warn("所有教学任务都已发布该考核，无需重复创建关联关系");
            return 0;
        }

        boolean result = this.saveBatch(assessmentTasks);
        return result ? assessmentTasks.size() : 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int removeAssessmentTaskRelations(Long assessmentId, List<Long> taskIds) {
        LambdaUpdateWrapper<AssessmentTask> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(AssessmentTask::getAssessmentId, assessmentId);
        
        if (CollectionUtils.isNotEmpty(taskIds)) {
            updateWrapper.in(AssessmentTask::getTaskId, taskIds);
        }
        
        updateWrapper.set(AssessmentTask::getStatus, -1); // 软删除
        updateWrapper.set(AssessmentTask::getModifyTime, LocalDateTime.now());

        return this.update(updateWrapper) ? 1 : 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int physicalDeleteByAssessmentId(Long assessmentId) {
        if (assessmentId == null) {
            log.warn("物理删除assessment_task记录失败：assessmentId为空");
            return 0;
        }

        try {
            // 物理删除assessment_task表中的记录
            LambdaQueryWrapper<AssessmentTask> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(AssessmentTask::getAssessmentId, assessmentId);

            int deleteCount = (int) this.count(queryWrapper);
            this.remove(queryWrapper);


            log.info("成功物理删除assessment_task记录，assessmentId: {}, 删除记录数: {}", assessmentId, deleteCount);
            return deleteCount;

        } catch (Exception e) {
            log.error("物理删除assessment_task记录时发生异常，assessmentId: {}", assessmentId, e);
            throw new RuntimeException("物理删除考核任务关联失败：" + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int physicalDeleteByAssessmentAndTasks(Long assessmentId, List<Long> taskIds) {
        if (assessmentId == null) {
            log.warn("物理删除assessment_task记录失败：assessmentId为空");
            return 0;
        }

        if (CollectionUtils.isEmpty(taskIds)) {
            log.warn("物理删除assessment_task记录失败：taskIds为空，请使用physicalDeleteByAssessmentId方法");
            return 0;
        }

        try {
            // 物理删除指定的assessment_task记录
            LambdaQueryWrapper<AssessmentTask> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(AssessmentTask::getAssessmentId, assessmentId)
                    .in(AssessmentTask::getTaskId, taskIds);

            int deleteCount = (int) this.count(queryWrapper);
            this.remove(queryWrapper);

            log.info("成功物理删除assessment_task记录，assessmentId: {}, taskIds: {}, 删除记录数: {}",
                        assessmentId, taskIds, deleteCount);
            return deleteCount;

        } catch (Exception e) {
            log.error("物理删除assessment_task记录时发生异常，assessmentId: {}, taskIds: {}", assessmentId, taskIds, e);
            throw new RuntimeException("物理删除考核任务关联失败：" + e.getMessage(), e);
        }
    }

    /**
     * 计算指定教学任务中已分配的考核权重总和
     *
     * @param taskId 教学任务ID
     * @param excludeAssessmentId 需要排除的考核ID（用于更新场景）
     * @return 已分配的权重总和
     */
    @Override
    public BigDecimal calculateAssignedWeight(Long taskId, Long excludeAssessmentId) {
        if (taskId == null) {
            return BigDecimal.ZERO;
        }

        try {
            // 查询该教学任务下所有已发布的考核
            LambdaQueryWrapper<AssessmentTask> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(AssessmentTask::getTaskId, taskId)
                    .eq(AssessmentTask::getStatus, 0); // 只统计正常状态的记录
            
            // 如果指定了要排除的考核ID，则排除它
            if (excludeAssessmentId != null) {
                queryWrapper.ne(AssessmentTask::getAssessmentId, excludeAssessmentId);
            }
            
            List<AssessmentTask> assessmentTasks = this.list(queryWrapper);
            
            if (CollectionUtils.isEmpty(assessmentTasks)) {
                return BigDecimal.ZERO;
            }
            
            // 获取这些考核的权重总和
            BigDecimal totalWeight = BigDecimal.ZERO;
            for (AssessmentTask assessmentTask : assessmentTasks) {
                // 查询对应的考核信息
                com.hnumi.obe.assessment.entity.Assessment assessment = 
                    assessmentService.getById(assessmentTask.getAssessmentId());
                if (assessment != null && assessment.getAssessmentWeight() != null) {
                    totalWeight = totalWeight.add(assessment.getAssessmentWeight());
                }
            }
            
            return totalWeight;
        } catch (Exception e) {
            log.error("计算教学任务已分配权重时发生异常，taskId: {}, excludeAssessmentId: {}", taskId, excludeAssessmentId, e);
            return BigDecimal.ZERO;
        }
    }

    /**
     * 验证指定教学任务的考核权重是否有效
     *
     * @param taskId 教学任务ID
     * @param assessmentId 考核ID
     * @param weight 要设置的权重
     * @return 验证结果，true表示有效，false表示无效
     */
    @Override
    public boolean validateAssessmentWeight(Long taskId, Long assessmentId, BigDecimal weight) {
        if (taskId == null || weight == null) {
            return false;
        }

        try {
            // 计算已分配的权重总和（排除当前考核）
            BigDecimal assignedWeight = calculateAssignedWeight(taskId, assessmentId);
            
            // 检查总权重是否超过100%
            BigDecimal totalWeight = assignedWeight.add(weight != null ? weight : BigDecimal.ZERO);
            return totalWeight.compareTo(BigDecimal.valueOf(100)) <= 0;
        } catch (Exception e) {
            log.error("验证考核权重时发生异常，taskId: {}, assessmentId: {}, weight: {}", taskId, assessmentId, weight, e);
            return false;
        }
    }
    
    /**
     * 更新指定教学任务中指定考核的权重
     *
     * @param taskId 教学任务ID
     * @param assessmentId 考核ID
     * @param weight 新的权重值
     * @return 更新结果，true表示成功，false表示失败
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateTaskAssessmentWeight(Long taskId, Long assessmentId, Double weight) {
        if (taskId == null || assessmentId == null || weight == null) {
            log.warn("更新教学任务考核权重参数不完整，taskId: {}, assessmentId: {}, weight: {}", taskId, assessmentId, weight);
            return false;
        }
        
        try {
            // 1. 验证参数有效性
            // 检查考核是否已发布给指定教学任务
            if (!isPublished(assessmentId, taskId)) {
                log.warn("考核未发布给指定教学任务，assessmentId: {}, taskId: {}", assessmentId, taskId);
                return false;
            }
            
            // 检查权重值是否有效（0-100之间）
            if (weight < 0 || weight > 100) {
                log.warn("权重值超出有效范围[0,100]，weight: {}", weight);
                return false;
            }
            
            BigDecimal newWeight = BigDecimal.valueOf(weight);
            
            // 验证权重是否符合要求（总权重不超过100%）
            if (!validateAssessmentWeight(taskId, assessmentId, newWeight)) {
                log.warn("权重验证失败，设置后总权重将超过100%，taskId: {}, assessmentId: {}, weight: {}", taskId, assessmentId, weight);
                return false;
            }
            
            // 2. 更新考核权重
            LambdaUpdateWrapper<AssessmentTask> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(AssessmentTask::getAssessmentId, assessmentId)
                    .eq(AssessmentTask::getTaskId, taskId)
                    .set(AssessmentTask::getScoreWeight, newWeight);
            
            boolean result = this.update(updateWrapper);
            
            if (result) {
                log.info("成功更新教学任务考核权重，taskId: {}, assessmentId: {}, weight: {}", taskId, assessmentId, weight);
            } else {
                log.warn("更新教学任务考核权重失败，taskId: {}, assessmentId: {}, weight: {}", taskId, assessmentId, weight);
            }
            
            return result;
        } catch (Exception e) {
            log.error("更新教学任务考核权重时发生异常，taskId: {}, assessmentId: {}, weight: {}", taskId, assessmentId, weight, e);
            return false;
        }
    }
    
    /**
     * 更新指定教学任务中指定考核的权重并返回更新后的考核信息
     *
     * @param taskId 教学任务ID
     * @param assessmentId 考核ID
     * @param weight 新的权重值
     * @return 更新后的考核信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AssessmentVO updateAndGetAssessment(Long taskId, Long assessmentId, Double weight) {
        boolean success = updateTaskAssessmentWeight(taskId, assessmentId, weight);
        if (success) {
            return assessmentService.getAssessmentById(assessmentId);
        }
        return null;
    }


    /**
     * 根据考核ID直接获取教学任务详情（包含发布时间）
     *
     * @param assessmentId 考核ID
     * @return 教学任务详情列表（包含发布时间）
     */
    public List<AssessmentTaskMapper.TaskWorkWithPublishTime> getTaskWorkWithPublishTimeByAssessmentId(Long assessmentId) {
        if (assessmentId == null) {
            return new ArrayList<>();
        }

        try {
            return assessmentTaskMapper.selectTaskWorkListByAssessmentId(assessmentId);
        } catch (Exception e) {
            log.error("根据考核ID查询教学任务详情（含发布时间）失败，assessmentId: {}", assessmentId, e);
            return new ArrayList<>();
        }
    }

}