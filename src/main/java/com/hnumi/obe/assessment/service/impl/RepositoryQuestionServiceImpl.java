package com.hnumi.obe.assessment.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hnumi.obe.assessment.entity.RepositoryQuestion;
import com.hnumi.obe.assessment.mapper.RepositoryQuestionMapper;
import com.hnumi.obe.assessment.service.IRepositoryQuestionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
    <AUTHOR>
    @create 2025-07-14 01:58
*/

@Service
public class RepositoryQuestionServiceImpl extends ServiceImpl<RepositoryQuestionMapper, RepositoryQuestion> implements IRepositoryQuestionService {

    @Autowired
    private RepositoryQuestionMapper repositoryQuestionMapper;
    /**
     * 根据题目ID列表查询题目详情
     *
     * @param questionIds 题目ID列表
     * @return 题目详情列表
     */
    @Override
    public List<RepositoryQuestion> getQuestionsByIds(List<Long> questionIds) {
        return baseMapper.selectQuestionsByIds(questionIds);
    }
}
