package com.hnumi.obe.assessment.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hnumi.obe.assessment.dto.*;
import com.hnumi.obe.assessment.entity.*;
import com.hnumi.obe.assessment.mapper.*;
import com.hnumi.obe.assessment.mapstruct.TeacherTaskConvert;
import com.hnumi.obe.assessment.mapstruct.TaskAssessmentConvert;
import com.hnumi.obe.assessment.mapstruct.ScoreImportExportConvert;
import com.hnumi.obe.assessment.mapstruct.StudentScoreDetailConvert;
import com.hnumi.obe.assessment.service.IAssessmentScoreService;
import com.hnumi.obe.assessment.service.IAssessmentService;
import com.hnumi.obe.assessment.service.IAssessmentTaskService;
import com.hnumi.obe.assessment.vo.*;
import com.hnumi.obe.base.entity.Classes;
import com.hnumi.obe.base.entity.Student;
import com.hnumi.obe.base.service.IClassesService;
import com.hnumi.obe.base.service.IStudentService;
import com.hnumi.obe.task.service.ITaskWorkService;
import com.hnumi.obe.task.service.ITaskWorklistClassesService;
import com.hnumi.obe.task.entity.TaskWork;
import com.hnumi.obe.task.entity.TaskWorklistClasses;
import com.hnumi.obe.tp.service.ICourseService;
import com.hnumi.obe.tp.entity.Course;
import com.hnumi.obe.common.util.ExcelUtil;
import com.hnumi.obe.common.util.JSONUtil;
import com.hnumi.obe.task.entity.TaskWorklistTeachers;
import com.hnumi.obe.task.service.ITaskWorklistTeachersService;
import com.hnumi.obe.tp.vo.CourseObjectiveVO;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.usermodel.DateUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.RoundingMode;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 考核成绩管理 服务实现类
 */
@Slf4j
@Service
public class AssessmentScoreServiceImpl extends ServiceImpl<AssessmentScoreTargetMapper, AssessmentScoreTarget> implements IAssessmentScoreService {

//    @Autowired
//    private AssessmentScoreMapper assessmentScoreMapper;

//    @Autowired
//    private AssessmentMapper assessmentMapper;

    @Autowired
    private ITaskWorkService taskWorkService;

    @Autowired
    private ITaskWorklistTeachersService taskWorklistTeachersService;

    @Autowired
    private ITaskWorklistClassesService taskWorklistClassesService;

    @Autowired
    private IAssessmentService assessmentService;

    @Autowired
    private IAssessmentTaskService assessmentTaskService;

    @Autowired
    private ICourseService courseService;

    @Autowired
    private IClassesService classesService;

    @Autowired
    private IStudentService studentService;
    

    @Autowired
    private AssessmentScoreDetailMapper assessmentScoreDetailMapper;

    @Autowired
    private AssessmentScoreTargetMapper assessmentScoreTargetMapper;

    @Autowired
    private OptimizedAssessmentScoreMapper optimizedAssessmentScoreMapper;


    /**
     * 课程目标信息内部类
     */
    @Data
    public static class CourseTargetInfo {
        private Integer targetNo;
        private Long poId;
        private String targetName;
    }

    /**
     * 题目答案信息内部类
     */
    @Data
    public static class RepositoryAnswerInfo {
        private Long repositoryAnswerId;
        private String questionNumber;
        private BigDecimal questionScore;
        private Integer courseTargetNo;
    }


    @Override
    public List<TeacherTaskVO> getTeacherTasks(TeacherTaskQueryDTO queryDTO) {
        // 1. 查询教师负责的教学任务ID列表
        LambdaQueryWrapper<TaskWorklistTeachers> teacherWrapper = new LambdaQueryWrapper<>();
        teacherWrapper.eq(TaskWorklistTeachers::getTeacherId, queryDTO.getTeacherId());
        List<TaskWorklistTeachers> teacherTasks = taskWorklistTeachersService.list(teacherWrapper);

        if (CollectionUtils.isEmpty(teacherTasks)) {
            return new ArrayList<>();
        }

        List<Long> taskIds = teacherTasks.stream()
                .map(TaskWorklistTeachers::getTaskId)
                .collect(Collectors.toList());

        // 2. 查询教学任务详情
        LambdaQueryWrapper<TaskWork> taskWrapper = new LambdaQueryWrapper<>();
        taskWrapper.in(TaskWork::getId, taskIds)
                .eq(TaskWork::getStatus, 0); // 正常状态

        if (queryDTO.getCourseId() != null) {
            taskWrapper.eq(TaskWork::getCourseId, queryDTO.getCourseId());
        }
        if (queryDTO.getTaskYear() != null) {
            taskWrapper.eq(TaskWork::getTaskYear, queryDTO.getTaskYear());
        }
        if (queryDTO.getTaskTerm() != null) {
            taskWrapper.eq(TaskWork::getTaskTerm, queryDTO.getTaskTerm());
        }
        if (queryDTO.getTaskStatus() != null) {
            taskWrapper.eq(TaskWork::getTaskStatus, queryDTO.getTaskStatus());
        }

        List<TaskWork> taskWorks = taskWorkService.list(taskWrapper);
        if (CollectionUtils.isEmpty(taskWorks)) {
            return new ArrayList<>();
        }

        // 3. 获取课程信息
        List<Long> courseIds = taskWorks.stream()
                .map(TaskWork::getCourseId)
                .distinct()
                .collect(Collectors.toList());
        Map<Long, Course> courseMap = courseService.listByIds(courseIds).stream()
                .collect(Collectors.toMap(Course::getCourseId, course -> course));

        // 4. 获取班级信息
        Map<Long, List<TaskWorklistClasses>> taskClassMap = taskWorklistClassesService.getClassesMap(
                taskWorks.stream().map(TaskWork::getId).collect(Collectors.toList()));

        List<Long> classIds = taskClassMap.values().stream()
                .flatMap(List::stream)
                .map(TaskWorklistClasses::getClassId)
                .distinct()
                .collect(Collectors.toList());
        Map<Long, Classes> classMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(classIds)) {
            classMap = classesService.listByIds(classIds).stream()
                    .collect(Collectors.toMap(Classes::getClassId, classes -> classes));
        }

        // 5. 使用转换器构建返回结果
        List<TeacherTaskVO> result = TeacherTaskConvert.INSTANCE.toTeacherTaskVOList(taskWorks);

        // 6. 填充额外信息
        for (TeacherTaskVO vo : result) {
            // 设置课程信息
            Course course = courseMap.get(vo.getCourseId());
            if (course != null) {
                TeacherTaskConvert.INSTANCE.updateCourseInfo(course, vo);
            }

            // 设置班级信息
            List<TaskWorklistClasses> taskClasses = taskClassMap.get(vo.getTaskId());
            if (!CollectionUtils.isEmpty(taskClasses)) {
                List<TeacherTaskVO.TaskClassInfo> classInfos = new ArrayList<>();
                for (TaskWorklistClasses taskClass : taskClasses) {
                    Classes classes = classMap.get(taskClass.getClassId());
                    if (classes != null) {
                        TeacherTaskVO.TaskClassInfo classInfo = new TeacherTaskVO.TaskClassInfo();
                        classInfo.setClassId(classes.getClassId());
                        classInfo.setClassName(classes.getClassName());
                        classInfo.setStudentCount(classes.getStudentNumber());
                        classInfos.add(classInfo);
                    }
                }
                vo.setClasses(classInfos);
            }

            // 查询已发布的考核数量
            int publishedCount = getPublishedAssessmentCount(vo.getTaskId());
            vo.setPublishedAssessmentCount(publishedCount);
        }

        return result;
    }

    @Override
    public List<TaskAssessmentScoreVO> getTaskAssessments(Long taskId, Long teacherId) {
        // 1. 验证教师权限
        if (!hasTaskPermission(taskId, teacherId)) {
            throw new RuntimeException("无权限访问该教学任务");
        }

        // 2. 查询教学任务信息
        TaskWork taskWork = taskWorkService.getById(taskId);
        if (taskWork == null) {
            throw new RuntimeException("教学任务不存在");
        }

        // 3. 查询已发布的考核列表
        List<Assessment> assessments = getPublishedAssessments(taskId);
        if (CollectionUtils.isEmpty(assessments)) {
            return new ArrayList<>();
        }

        // 4. 使用转换器构建返回结果
        List<TaskAssessmentScoreVO> result = TaskAssessmentConvert.INSTANCE.toTaskAssessmentVOList(assessments);

        // 5. 填充任务相关信息和成绩进度
        int totalCount = getTotalStudentCount(taskId);
        for (TaskAssessmentScoreVO vo : result) {
            // 查询成绩录入进度
            int scoredCount = assessmentScoreTargetMapper.countScoredStudents(vo.getAssessmentId(), taskId);

            // 使用转换器更新任务信息
            TaskAssessmentConvert.INSTANCE.updateTaskInfo(taskId, scoredCount, totalCount, vo);
        }

        return result;
    }



    @Override
    public boolean hasTaskPermission(Long taskId, Long teacherId) {
        LambdaQueryWrapper<TaskWorklistTeachers> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaskWorklistTeachers::getTaskId, taskId)
                .eq(TaskWorklistTeachers::getTeacherId, teacherId);
        return taskWorklistTeachersService.count(wrapper) > 0;
    }

    @Override
    public boolean isAssessmentPublished(Long assessmentId, Long taskId) {
        // 查询考核信息
        Assessment assessment = assessmentService.getById(assessmentId);
        if (assessment == null) {
            return false;
        }

        // 检查是否全部发布
        if (assessment.getTaskId() != null && assessment.getTaskId() == -1) {
            // 全部发布，检查教学任务是否匹配
            return assessment.getCourseId().equals(getTaskCourseId(taskId)) &&
                    assessment.getAssessmentYear().equals(getTaskYear(taskId)) &&
                    assessment.getAssessmentTerm().equals(getTaskTerm(taskId));
        }

        // 检查部分发布
        return assessmentTaskService.isPublished(assessmentId, taskId);
    }

    // ==================== 辅助方法 ====================

    // 注意：状态名称转换方法已移至对应的Convert类中

    private int getPublishedAssessmentCount(Long taskId) {
        // 查询该教学任务已发布的考核数量
        TaskWork taskWork = taskWorkService.getById(taskId);
        if (taskWork == null) {
            return 0;
        }

        // 查询全部发布的考核
        LambdaQueryWrapper<Assessment> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Assessment::getTaskId, -1L)
                .eq(Assessment::getCourseId, taskWork.getCourseId())
                .eq(Assessment::getAssessmentYear, taskWork.getTaskYear())
                .eq(Assessment::getAssessmentTerm, taskWork.getTaskTerm())
                .eq(Assessment::getStatus, 0);
        int allPublishedCount = (int) assessmentService.count(wrapper);

        // 查询部分发布的考核
        List<Long> partialPublishedIds = assessmentTaskService.getAssessmentIdsByTaskId(taskId);

        return allPublishedCount + partialPublishedIds.size();
    }

    private List<Assessment> getPublishedAssessments(Long taskId) {
        TaskWork taskWork = taskWorkService.getById(taskId);
        if (taskWork == null) {
            return new ArrayList<>();
        }

        List<Assessment> result = new ArrayList<>();

        // 查询全部发布的考核
        LambdaQueryWrapper<Assessment> allWrapper = new LambdaQueryWrapper<>();
        allWrapper.eq(Assessment::getTaskId, -1L)
                .eq(Assessment::getCourseId, taskWork.getCourseId())
                .eq(Assessment::getAssessmentYear, taskWork.getTaskYear())
                .eq(Assessment::getAssessmentTerm, taskWork.getTaskTerm())
                .eq(Assessment::getStatus, 0);
        List<Assessment> allPublished = assessmentService.list(allWrapper);
        result.addAll(allPublished);

        // 查询部分发布的考核
        List<Long> partialPublishedIds = assessmentTaskService.getAssessmentIdsByTaskId(taskId);
        if (!CollectionUtils.isEmpty(partialPublishedIds)) {
            List<Assessment> partialPublished = assessmentService.listByIds(partialPublishedIds);
            result.addAll(partialPublished);
        }

        return result;
    }

    private int getTotalStudentCount(Long taskId) {
        // 使用子查询一次性获取教学任务的学生数量
        return studentService.getStudentCountByTaskId(taskId);
    }

    private Map<String, Student> getTaskStudentMap(Long taskId) {
        // 使用优化后的方法获取学生列表
        List<Student> students = getTaskStudents(taskId);
        return students.stream()
                .collect(Collectors.toMap(Student::getStudentNumber, student -> student));
    }

    private List<Student> getTaskStudents(Long taskId) {
        // 使用子查询一次性获取教学任务的学生列表
        return studentService.getStudentsByTaskId(taskId);
    }

    private String getStudentClassName(Long studentId) {
        Student student = studentService.getById(studentId);
        if (student != null && student.getClassId() != null) {
            Classes classes = classesService.getById(student.getClassId());
            return classes != null ? classes.getClassName() : "";
        }
        return "";
    }

    private Long getTaskCourseId(Long taskId) {
        TaskWork taskWork = taskWorkService.getById(taskId);
        return taskWork != null ? taskWork.getCourseId() : null;
    }

    private Integer getTaskYear(Long taskId) {
        TaskWork taskWork = taskWorkService.getById(taskId);
        return taskWork != null ? taskWork.getTaskYear() : null;
    }

    private Integer getTaskTerm(Long taskId) {
        TaskWork taskWork = taskWorkService.getById(taskId);
        return taskWork != null ? taskWork.getTaskTerm() : null;
    }

    //================== 扩展的成绩导入模式实现 ==================

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ScoreImportResultVO importScoresByTarget(ScoreTargetImportDTO importDTO, Long teacherId) {
        // 1. 验证权限
        if (!hasTaskPermission(importDTO.getTaskId(), teacherId)) {
            throw new RuntimeException("无权限操作该教学任务");
        }

        if (!isAssessmentPublished(importDTO.getAssessmentId(), importDTO.getTaskId())) {
            throw new RuntimeException("考核未发布给该教学任务");
        }

        // 2. 获取考核和教学任务信息
        Assessment assessment = assessmentService.getById(importDTO.getAssessmentId());
        if (assessment == null) {
            throw new RuntimeException("考核不存在");
        }

        TaskWork taskWork = taskWorkService.getById(importDTO.getTaskId());
        if (taskWork == null) {
            throw new RuntimeException("教学任务不存在");
        }

        // 3. 获取课程目标和毕业要求指标的映射关系
        Map<Integer, Long> targetPoMap = getCourseTargetPoMapping(assessment.getCourseId());

        List<AssessmentScoreTarget> scoreTargetList = new ArrayList<>();
        List<String> errorMessages = new ArrayList<>();
        int successCount = 0;
        int failCount = 0;

        LocalDateTime now = LocalDateTime.now();

        try {
            for (ScoreTargetImportDTO.StudentTargetScoreDTO studentScore : importDTO.getStudentScores()) {
                for (ScoreTargetImportDTO.CourseTargetScoreDTO targetScore : studentScore.getTargetScores()) {
                    try {
                        // 验证课程目标编号
                        Long poId = targetPoMap.get(targetScore.getCourseTargetNo());
                        if (poId == null) {
                            failCount++;
                            errorMessages.add(String.format("学生ID %d 的课程目标 %d 不存在或未关联毕业要求指标",
                                    studentScore.getStudentId(), targetScore.getCourseTargetNo()));
                            continue;
                        }

                        // 检查是否已存在记录
                        AssessmentScoreTarget existingScore = assessmentScoreTargetMapper.selectByStudentAndTarget(
                                studentScore.getStudentId(), importDTO.getAssessmentId(), targetScore.getCourseTargetNo());

                        if (existingScore != null) {
                            // 更新现有记录
                            existingScore.setScore(targetScore.getScore());
                            existingScore.setRepositoryAnswerId(targetScore.getRepositoryAnswerId());
                            existingScore.setModifier(teacherId);
                            existingScore.setModifyTime(now);
                            scoreTargetList.add(existingScore);
                        } else {
                            // 创建新记录
                            AssessmentScoreTarget newScore = new AssessmentScoreTarget();
                            newScore.setStudentId(studentScore.getStudentId());
                            newScore.setAssessmentId(importDTO.getAssessmentId());
                            newScore.setCourseTargetNo(targetScore.getCourseTargetNo());
                            newScore.setScore(targetScore.getScore());
                            newScore.setRepositoryAnswerId(targetScore.getRepositoryAnswerId());
                            newScore.setPoId(poId);
                            newScore.setTaskId(importDTO.getTaskId());
                            newScore.setCourseId(assessment.getCourseId());
                            newScore.setMajorId(getMajorIdFromTask(importDTO.getTaskId()));
                            newScore.setStatus(0);
                            newScore.setCreator(teacherId);
                            newScore.setCreateTime(now);
                            newScore.setModifier(teacherId);
                            newScore.setModifyTime(now);
                            scoreTargetList.add(newScore);
                        }
                        successCount++;
                    } catch (Exception e) {
                        failCount++;
                        errorMessages.add(String.format("学生ID %d 课程目标 %d 处理失败：%s",
                                studentScore.getStudentId(), targetScore.getCourseTargetNo(), e.getMessage()));
                    }
                }
            }

            // 4. 批量保存或更新
            if (!CollectionUtils.isEmpty(scoreTargetList)) {
                // 分别处理新增和更新
                List<AssessmentScoreTarget> newRecords = scoreTargetList.stream()
                        .filter(score -> score.getId() == null)
                        .collect(Collectors.toList());
                List<AssessmentScoreTarget> updateRecords = scoreTargetList.stream()
                        .filter(score -> score.getId() != null)
                        .collect(Collectors.toList());

                if (!CollectionUtils.isEmpty(newRecords)) {
                    newRecords.forEach(assessmentScoreTargetMapper::insert);
                }
                if (!CollectionUtils.isEmpty(updateRecords)) {
                    updateRecords.forEach(assessmentScoreTargetMapper::updateById);
                }
            }

            return ScoreImportResultVO.success(
                    successCount + failCount, successCount, failCount, 0);

        } catch (Exception e) {
            log.error("按课程目标导入成绩失败", e);
            return ScoreImportResultVO.failure(
                    successCount + failCount, Arrays.asList("导入失败：" + e.getMessage()));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ScoreImportResultVO importScoresByDetail(ScoreDetailImportDTO importDTO, Long teacherId) {
        // 1. 验证权限
        if (!hasTaskPermission(importDTO.getTaskId(), teacherId)) {
            throw new RuntimeException("无权限操作该教学任务");
        }

        if (!isAssessmentPublished(importDTO.getAssessmentId(), importDTO.getTaskId())) {
            throw new RuntimeException("考核未发布给该教学任务");
        }

        // 2. 获取考核和教学任务信息
        Assessment assessment = assessmentService.getById(importDTO.getAssessmentId());
        if (assessment == null) {
            throw new RuntimeException("考核不存在");
        }

        TaskWork taskWork = taskWorkService.getById(importDTO.getTaskId());
        if (taskWork == null) {
            throw new RuntimeException("教学任务不存在");
        }

        // 3. 获取题目答案和课程目标的映射关系
        Map<Long, RepositoryAnswerInfo> answerInfoMap = getRepositoryAnswerInfoMap(importDTO.getAssessmentId());

        // 4. 获取课程目标和毕业要求指标的映射关系
        Map<Integer, Long> targetPoMap = getCourseTargetPoMapping(assessment.getCourseId());

        List<AssessmentScoreDetail> scoreDetailList = new ArrayList<>();
        List<String> errorMessages = new ArrayList<>();
        int successCount = 0;
        int failCount = 0;

        LocalDateTime now = LocalDateTime.now();

        try {
            for (ScoreDetailImportDTO.StudentDetailScoreDTO studentScore : importDTO.getStudentScores()) {
                for (ScoreDetailImportDTO.QuestionAnswerScoreDTO answerScore : studentScore.getAnswerScores()) {
                    try {
                        // 验证题目答案ID
                        RepositoryAnswerInfo answerInfo = answerInfoMap.get(answerScore.getRepositoryAnswerId());
                        if (answerInfo == null) {
                            failCount++;
                            errorMessages.add(String.format("学生ID %d 的题目答案ID %d 不存在",
                                    studentScore.getStudentId(), answerScore.getRepositoryAnswerId()));
                            continue;
                        }

                        // 验证课程目标和毕业要求指标
                        Long poId = targetPoMap.get(answerInfo.getCourseTargetNo());
                        if (poId == null) {
                            failCount++;
                            errorMessages.add(String.format("学生ID %d 题目答案ID %d 对应的课程目标 %d 未关联毕业要求指标",
                                    studentScore.getStudentId(), answerScore.getRepositoryAnswerId(), answerInfo.getCourseTargetNo()));
                            continue;
                        }

                        // 检查是否已存在记录
                        AssessmentScoreDetail existingScore = assessmentScoreDetailMapper.selectByStudentAndAnswer(
                                studentScore.getStudentId(), importDTO.getAssessmentId(), answerScore.getRepositoryAnswerId());

                        if (existingScore != null) {
                            // 更新现有记录
                            existingScore.setStudentAnswer(answerScore.getStudentAnswer());
                            existingScore.setScore(answerScore.getScore());
                            existingScore.setQuestionScore(answerScore.getQuestionScore() != null ?
                                    answerScore.getQuestionScore() : answerInfo.getQuestionScore());
                            existingScore.setModifier(teacherId);
                            existingScore.setModifyTime(now);
                            scoreDetailList.add(existingScore);
                        } else {
                            // 创建新记录
                            AssessmentScoreDetail newScore = new AssessmentScoreDetail();
                            newScore.setStudentId(studentScore.getStudentId());
                            newScore.setAssessmentId(importDTO.getAssessmentId());
                            newScore.setRepositoryAnswerId(answerScore.getRepositoryAnswerId());
                            newScore.setStudentAnswer(answerScore.getStudentAnswer());
                            newScore.setScore(answerScore.getScore());
                            newScore.setQuestionScore(answerScore.getQuestionScore() != null ?
                                    answerScore.getQuestionScore() : answerInfo.getQuestionScore());
                            newScore.setCourseTargetNo(answerInfo.getCourseTargetNo());
                            newScore.setPoId(poId);
                            newScore.setTaskId(importDTO.getTaskId());
                            newScore.setCourseId(assessment.getCourseId());
                            newScore.setMajorId(getMajorIdFromTask(importDTO.getTaskId()));
                            newScore.setStatus(0);
                            newScore.setCreator(teacherId);
                            newScore.setCreateTime(now);
                            newScore.setModifier(teacherId);
                            newScore.setModifyTime(now);
                            scoreDetailList.add(newScore);
                        }
                        successCount++;
                    } catch (Exception e) {
                        failCount++;
                        errorMessages.add(String.format("学生ID %d 题目答案ID %d 处理失败：%s",
                                studentScore.getStudentId(), answerScore.getRepositoryAnswerId(), e.getMessage()));
                    }
                }
            }

            // 5. 批量保存或更新
            if (!CollectionUtils.isEmpty(scoreDetailList)) {
                // 分别处理新增和更新
                List<AssessmentScoreDetail> newRecords = scoreDetailList.stream()
                        .filter(score -> score.getId() == null)
                        .collect(Collectors.toList());
                List<AssessmentScoreDetail> updateRecords = scoreDetailList.stream()
                        .filter(score -> score.getId() != null)
                        .collect(Collectors.toList());

                if (!CollectionUtils.isEmpty(newRecords)) {
                    newRecords.forEach(assessmentScoreDetailMapper::insert);
                }
                if (!CollectionUtils.isEmpty(updateRecords)) {
                    updateRecords.forEach(assessmentScoreDetailMapper::updateById);
                }
            }

            return ScoreImportResultVO.success(
                    successCount + failCount, successCount, failCount, 0);

        } catch (Exception e) {
            log.error("按考核详情导入成绩失败", e);
            return ScoreImportResultVO.failure(
                    successCount + failCount, Arrays.asList("导入失败：" + e.getMessage()));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ScoreImportResultVO importTargetScoresFromExcel(Long assessmentId, Long taskId, InputStream inputStream, Long teacherId) {
        if (!isAssessmentPublished(assessmentId, taskId)) {
            throw new RuntimeException("考核未发布给该教学任务");
        }

        try {
            // 获取考核信息
            Assessment assessment = assessmentService.getById(assessmentId);
            if (assessment == null) {
                throw new RuntimeException("考核不存在");
            }

            // 获取教学任务的学生列表
            Map<String, Student> studentMap = getTaskStudentMap(taskId);

            // 获取课程目标和毕业要求指标的映射关系
            Map<Integer, Long> targetPoMap = getCourseTargetPoMapping(assessment.getCourseId());
            
            // 获取课程目标编号到objectiveId的映射关系
            Map<Integer, String> targetObjectiveIdMap = getCourseTargetObjectiveIdMapping(assessment.getCourseId());

            // 读取Excel文件并解析为二维数组
            List<List<String>> excelData = readExcelAsList(inputStream);
            
            if (CollectionUtils.isEmpty(excelData) || excelData.size() < 2) {
                return ScoreImportResultVO.failure(0, List.of("导入文件中没有有效数据"));
            }

            // 解析表头，确定课程目标列的位置
            List<String> headers = excelData.getFirst();
            Map<Integer, Integer> courseTargetColumnMap = parseCourseTargetColumns(headers);

            if (courseTargetColumnMap.isEmpty()) {
                return ScoreImportResultVO.failure(0, List.of("未找到有效的课程目标列"));
            }

            List<AssessmentScoreTarget> scoreList = new ArrayList<>();
            List<String> errorMessages = new ArrayList<>();
            List<ScoreImportResultVO.ImportedStudentInfo> successList = new ArrayList<>();
            List<ScoreImportResultVO.FailedRecordInfo> failList = new ArrayList<>();

            int successCount = 0;
            int failCount = 0;
            int skipCount = 0;

            // 处理数据行
            for (int i = 1; i < excelData.size(); i++) {
                List<String> row = excelData.get(i);
                int rowNumber = i + 1; // Excel行号

                try {
                    // 验证基本数据
                    if (row.size() < 3) {
                        failCount++;
                        addFailRecord(failList, rowNumber, "", "", "数据行格式错误，至少需要学号、姓名、班级三列");
                        continue;
                    }

                    String studentNumber = row.get(0);
                    String studentName = row.get(1);
                    String className = row.get(2);

                    // 验证学号
                    if (!StringUtils.hasText(studentNumber)) {
                        failCount++;
                        addFailRecord(failList, rowNumber, studentNumber, studentName, "学号不能为空");
                        continue;
                    }

                    // 查找学生
                    Student student = studentMap.get(studentNumber);
                    if (student == null) {
                        failCount++;
                        addFailRecord(failList, rowNumber, studentNumber, studentName, "学生不存在或不在该教学任务中");
                        continue;
                    }

                    // 处理课程目标成绩
                    boolean hasValidScore = false;
                    for (Map.Entry<Integer, Integer> entry : courseTargetColumnMap.entrySet()) {
                        Integer targetNo = entry.getKey();
                        Integer columnIndex = entry.getValue();

                        if (columnIndex < row.size()) {
                            String scoreStr = row.get(columnIndex);
                            if (StringUtils.hasText(scoreStr)) {
                                try {
                                    BigDecimal score = new BigDecimal(scoreStr);
                                    
                                    // 验证成绩范围
                                    if (score.compareTo(BigDecimal.ZERO) < 0) {
                                        failCount++;
                                        addFailRecord(failList, rowNumber, studentNumber, studentName, 
                                                String.format("课程目标%d成绩小于0: %s", targetNo, scoreStr));
                                        continue;
                                    }

                                    // 验证课程目标
                                    Long poId = targetPoMap.get(targetNo);
                                    if (poId == null) {
                                        failCount++;
                                        addFailRecord(failList, rowNumber, studentNumber, studentName, 
                                                String.format("课程目标%d不存在或未关联毕业要求指标", targetNo));
                                        continue;
                                    }

                                    // 获取objectiveId
                                    String objectiveId = targetObjectiveIdMap.get(targetNo);
                                    if (objectiveId == null) {
                                        failCount++;
                                        addFailRecord(failList, rowNumber, studentNumber, studentName, 
                                                String.format("课程目标%d缺少objectiveId信息", targetNo));
                                        continue;
                                    }

                                    // 检查是否已存在成绩
                                    AssessmentScoreTarget existingScore = assessmentScoreTargetMapper.selectByStudentAndTarget(
                                            student.getStudentId(), assessmentId, targetNo);
                                    
                                    if (existingScore != null) {
                                        // 更新现有记录
                                        existingScore.setScore(score);
                                        existingScore.setObjectiveId(objectiveId);
                                        existingScore.setModifier(teacherId);
                                        existingScore.setModifyTime(LocalDateTime.now());
                                        existingScore.setObjectiveId(objectiveId);
                                        assessmentScoreTargetMapper.updateById(existingScore);
                                    } else {
                                        // 创建新记录
                                        AssessmentScoreTarget scoreRecord = new AssessmentScoreTarget();
                                        scoreRecord.setStudentId(student.getStudentId());
                                        scoreRecord.setAssessmentId(assessmentId);
                                        scoreRecord.setCourseTargetNo(targetNo);
                                        scoreRecord.setScore(score);
                                        scoreRecord.setObjectiveId(objectiveId);
                                        scoreRecord.setPoId(poId);
                                        scoreRecord.setTaskId(taskId);
                                        scoreRecord.setCourseId(assessment.getCourseId());
                                        scoreRecord.setMajorId(getMajorIdFromTask(taskId));
                                        scoreRecord.setStatus(0);
                                        scoreRecord.setCreator(teacherId);
                                        scoreRecord.setCreateTime(LocalDateTime.now());
                                        scoreRecord.setModifier(teacherId);
                                        scoreRecord.setObjectiveId(objectiveId);
                                        scoreRecord.setModifyTime(LocalDateTime.now());
                                        scoreList.add(scoreRecord);
                                    }
                                    
                                    hasValidScore = true;
                                    
                                } catch (NumberFormatException e) {
                                    failCount++;
                                    addFailRecord(failList, rowNumber, studentNumber, studentName, 
                                            String.format("课程目标%d成绩格式错误: %s", targetNo, scoreStr));
                                }
                            }
                        }
                    }

                    if (hasValidScore) {
                        successCount++;
                        
                        // 记录成功信息
                        ScoreImportResultVO.ImportedStudentInfo successInfo = new ScoreImportResultVO.ImportedStudentInfo();
                        successInfo.setStudentNumber(studentNumber);
                        successInfo.setStudentName(studentName);
                        successInfo.setClassName(className);
                        successInfo.setScore("已导入课程目标成绩");
                        successList.add(successInfo);
                    } else {
                        skipCount++;
                    }

                } catch (Exception e) {
                    failCount++;
                    addFailRecord(failList, rowNumber, "", "", "处理失败：" + e.getMessage());
                }
            }

            // 批量保存成绩
            if (!CollectionUtils.isEmpty(scoreList)) {
                scoreList.forEach(assessmentScoreTargetMapper::insert);
            }

            // 构建返回结果
            ScoreImportResultVO result = ScoreImportResultVO.success(
                    excelData.size() - 1, successCount, failCount, skipCount);
            result.setSuccessList(successList);
            result.setFailList(failList);
            result.setErrorMessages(errorMessages);

            return result;

        } catch (Exception e) {
            log.error("Excel导入课程目标成绩失败", e);
            return ScoreImportResultVO.failure(0, Arrays.asList("导入失败：" + e.getMessage()));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ScoreImportResultVO importDirectEntryScoresFromExcel(Long assessmentId, Long taskId, InputStream inputStream, Long teacherId) {
        // 验证权限
        if (!hasTaskPermission(taskId, teacherId)) {
            throw new RuntimeException("无权限操作该教学任务");
        }

        if (!isAssessmentPublished(assessmentId, taskId)) {
            throw new RuntimeException("考核未发布给该教学任务");
        }

        try {
            // 获取考核信息
            Assessment assessment = assessmentService.getById(assessmentId);
            if (assessment == null) {
                throw new RuntimeException("考核不存在");
            }

            // 获取教学任务信息
            TaskWork taskWork = taskWorkService.getById(taskId);
            if (taskWork == null) {
                throw new RuntimeException("教学任务不存在");
            }

            // 获取教学任务的学生列表
            Map<String, Student> studentMap = getTaskStudentMap(taskId);

            // 获取课程目标和毕业要求指标的映射关系
            Map<Integer, Long> targetPoMap = getCourseTargetPoMapping(assessment.getCourseId());

            // 读取Excel文件并解析为二维数组
            List<List<String>> excelData = readExcelAsList(inputStream);
            
            if (CollectionUtils.isEmpty(excelData) || excelData.size() < 2) {
                return ScoreImportResultVO.failure(0, Arrays.asList("导入文件中没有有效数据"));
            }

            // 解析表头，确定课程目标列的位置
            List<String> headers = excelData.get(0);
            Map<Integer, Integer> courseTargetColumnMap = parseCourseTargetColumns(headers);

            if (courseTargetColumnMap.isEmpty()) {
                return ScoreImportResultVO.failure(0, Arrays.asList("未找到有效的课程目标列"));
            }

            List<AssessmentScoreTarget> scoreList = new ArrayList<>();
            List<String> errorMessages = new ArrayList<>();
            List<ScoreImportResultVO.ImportedStudentInfo> successList = new ArrayList<>();
            List<ScoreImportResultVO.FailedRecordInfo> failList = new ArrayList<>();

            int successCount = 0;
            int failCount = 0;
            int skipCount = 0;

            // 处理数据行
            for (int i = 1; i < excelData.size(); i++) {
                List<String> row = excelData.get(i);
                int rowNumber = i + 1; // Excel行号

                try {
                    // 验证基本数据
                    if (row.size() < 3) {
                        failCount++;
                        addFailRecord(failList, rowNumber, "", "", "数据行格式错误，至少需要学号、姓名、班级三列");
                        continue;
                    }

                    String studentNumber = row.get(0);
                    String studentName = row.get(1);
                    String className = row.get(2);

                    // 验证学号
                    if (!StringUtils.hasText(studentNumber)) {
                        failCount++;
                        addFailRecord(failList, rowNumber, studentNumber, studentName, "学号不能为空");
                        continue;
                    }

                    // 查找学生
                    Student student = studentMap.get(studentNumber);
                    if (student == null) {
                        failCount++;
                        addFailRecord(failList, rowNumber, studentNumber, studentName, "学生不存在或不在该教学任务中");
                        continue;
                    }

                    // 处理课程目标成绩
                    boolean hasValidScore = false;
                    for (Map.Entry<Integer, Integer> entry : courseTargetColumnMap.entrySet()) {
                        Integer targetNo = entry.getKey();
                        Integer columnIndex = entry.getValue();

                        if (columnIndex < row.size()) {
                            String scoreStr = row.get(columnIndex);
                            if (StringUtils.hasText(scoreStr)) {
                                try {
                                    BigDecimal score = new BigDecimal(scoreStr);
                                    
                                    // 验证成绩范围
                                    if (score.compareTo(BigDecimal.ZERO) < 0 || score.compareTo(new BigDecimal("100")) > 0) {
                                        failCount++;
                                        addFailRecord(failList, rowNumber, studentNumber, studentName, 
                                                String.format("课程目标%d成绩超出范围(0-100): %s", targetNo, scoreStr));
                                        continue;
                                    }

                                    // 验证课程目标
                                    Long poId = targetPoMap.get(targetNo);
                                    if (poId == null) {
                                        failCount++;
                                        addFailRecord(failList, rowNumber, studentNumber, studentName, 
                                                String.format("课程目标%d不存在或未关联毕业要求指标", targetNo));
                                        continue;
                                    }

                                    // 检查是否已存在成绩
                                    AssessmentScoreTarget existingScore = assessmentScoreTargetMapper.selectByStudentAndTarget(
                                            student.getStudentId(), assessmentId, targetNo);
                                    
                                    if (existingScore != null) {
                                        // 更新现有记录
                                        existingScore.setScore(score);
                                        existingScore.setModifier(teacherId);
                                        existingScore.setModifyTime(LocalDateTime.now());
                                        scoreList.add(existingScore);
                                    } else {
                                        // 创建新记录
                                        AssessmentScoreTarget newScore = new AssessmentScoreTarget();
                                        newScore.setStudentId(student.getStudentId());
                                        newScore.setAssessmentId(assessmentId);
                                        newScore.setCourseTargetNo(targetNo);
                                        newScore.setScore(score);
                                        newScore.setPoId(poId);
                                        newScore.setTaskId(taskId);
                                        newScore.setCourseId(assessment.getCourseId());
                                        newScore.setMajorId(getMajorIdFromTask(taskId));
                                        newScore.setStatus(0);
                                        newScore.setCreator(teacherId);
                                        newScore.setCreateTime(LocalDateTime.now());
                                        newScore.setModifier(teacherId);
                                        newScore.setModifyTime(LocalDateTime.now());
                                        scoreList.add(newScore);
                                    }
                                    
                                    hasValidScore = true;
                                    
                                } catch (NumberFormatException e) {
                                    failCount++;
                                    addFailRecord(failList, rowNumber, studentNumber, studentName, 
                                            String.format("课程目标%d成绩格式错误: %s", targetNo, scoreStr));
                                }
                            }
                        }
                    }

                    if (hasValidScore) {
                        successCount++;
                        
                        // 记录成功信息
                        ScoreImportResultVO.ImportedStudentInfo successInfo = new ScoreImportResultVO.ImportedStudentInfo();
                        successInfo.setStudentNumber(studentNumber);
                        successInfo.setStudentName(studentName);
                        successInfo.setClassName(className);
                        successInfo.setScore("已导入课程目标成绩");
                        successList.add(successInfo);
                    } else {
                        skipCount++;
                    }

                } catch (Exception e) {
                    failCount++;
                    addFailRecord(failList, rowNumber, "", "", "处理失败：" + e.getMessage());
                }
            }

            // 批量保存成绩
            if (!CollectionUtils.isEmpty(scoreList)) {
                // 分别处理新增和更新
                List<AssessmentScoreTarget> newRecords = scoreList.stream()
                        .filter(score -> score.getId() == null)
                        .collect(Collectors.toList());
                List<AssessmentScoreTarget> updateRecords = scoreList.stream()
                        .filter(score -> score.getId() != null)
                        .collect(Collectors.toList());

                if (!CollectionUtils.isEmpty(newRecords)) {
                    newRecords.forEach(assessmentScoreTargetMapper::insert);
                }
                if (!CollectionUtils.isEmpty(updateRecords)) {
                    updateRecords.forEach(assessmentScoreTargetMapper::updateById);
                }
            }

            // 构建返回结果
            ScoreImportResultVO result = ScoreImportResultVO.success(
                    excelData.size() - 1, successCount, failCount, skipCount);
            result.setSuccessList(successList);
            result.setFailList(failList);
            result.setErrorMessages(errorMessages);

            return result;

        } catch (Exception e) {
            log.error("Excel导入直接录入模式成绩失败", e);
            return ScoreImportResultVO.failure(0, Arrays.asList("导入失败：" + e.getMessage()));
        }
    }

    /**
     * 读取Excel文件为二维数组
     */
    private List<List<String>> readExcelAsList(InputStream inputStream) throws IOException {
        List<List<String>> result = new ArrayList<>();
        
        try (Workbook workbook = WorkbookFactory.create(inputStream)) {
            Sheet sheet = workbook.getSheetAt(0);
            
            for (Row row : sheet) {
                List<String> rowData = new ArrayList<>();
                for (Cell cell : row) {
                    rowData.add(getCellValueAsString(cell));
                }
                result.add(rowData);
            }
        }
        
        return result;
    }

    /**
     * 获取单元格值作为字符串
     */
    private String getCellValueAsString(Cell cell) {
        if (cell == null) {
            return "";
        }
        
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    return String.valueOf(cell.getNumericCellValue());
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return "";
        }
    }

    /**
     * 解析课程目标列位置
     */
    private Map<Integer, Integer> parseCourseTargetColumns(List<String> headers) {
        Map<Integer, Integer> columnMap = new HashMap<>();
        
        for (int i = 0; i < headers.size(); i++) {
            String header = headers.get(i);
            if (header != null && header.startsWith("课程目标")) {
                try {
                    String targetNoStr = header.substring(4); // 去掉"课程目标"前缀
                    Integer targetNo = Integer.parseInt(targetNoStr);
                    columnMap.put(targetNo, i);
                } catch (NumberFormatException e) {
                    // 忽略无法解析的列
                    log.warn("无法解析课程目标列: {}", header);
                }
            }
        }
        
        return columnMap;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ScoreImportResultVO importDetailScoresFromExcel(Long assessmentId, Long taskId, InputStream inputStream, Long teacherId) {
        // 验证权限
        if (!hasTaskPermission(taskId, teacherId)) {
            throw new RuntimeException("无权限操作该教学任务");
        }

        if (!isAssessmentPublished(assessmentId, taskId)) {
            throw new RuntimeException("考核未发布给该教学任务");
        }

        try {
            // 读取Excel文件
            List<ScoreDetailExcelImportDTO> importList = ExcelUtil.readAll(inputStream, ScoreDetailExcelImportDTO.class);

            if (CollectionUtils.isEmpty(importList)) {
                return ScoreImportResultVO.failure(0, Arrays.asList("导入文件中没有有效数据"));
            }

            // 获取教学任务的学生列表
            Map<String, Student> studentMap = getTaskStudentMap(taskId);

            // 获取考核信息
            Assessment assessment = assessmentService.getById(assessmentId);

            // 获取题目答案和课程目标的映射关系
            Map<Long, RepositoryAnswerInfo> answerInfoMap = getRepositoryAnswerInfoMap(assessmentId);

            // 获取课程目标和毕业要求指标的映射关系
            Map<Integer, Long> targetPoMap = getCourseTargetPoMapping(assessment.getCourseId());

            List<AssessmentScoreDetail> scoreList = new ArrayList<>();
            List<String> errorMessages = new ArrayList<>();
            List<ScoreImportResultVO.ImportedStudentInfo> successList = new ArrayList<>();
            List<ScoreImportResultVO.FailedRecordInfo> failList = new ArrayList<>();

            int successCount = 0;
            int failCount = 0;
            int skipCount = 0;

            for (int i = 0; i < importList.size(); i++) {
                ScoreDetailExcelImportDTO importDTO = importList.get(i);
                int rowNumber = i + 2; // Excel行号（从第2行开始）

                try {
                    // 验证数据
                    if (!StringUtils.hasText(importDTO.getStudentNumber())) {
                        failCount++;
                        addFailRecord(failList, rowNumber, importDTO.getStudentNumber(),
                                importDTO.getStudentName(), "学号不能为空");
                        continue;
                    }

                    if (importDTO.getScore() == null) {
                        failCount++;
                        addFailRecord(failList, rowNumber, importDTO.getStudentNumber(),
                                importDTO.getStudentName(), "成绩不能为空");
                        continue;
                    }

                    if (importDTO.getRepositoryAnswerId() == null) {
                        failCount++;
                        addFailRecord(failList, rowNumber, importDTO.getStudentNumber(),
                                importDTO.getStudentName(), "题目答案ID不能为空");
                        continue;
                    }

                    // 查找学生
                    Student student = studentMap.get(importDTO.getStudentNumber());
                    if (student == null) {
                        failCount++;
                        addFailRecord(failList, rowNumber, importDTO.getStudentNumber(),
                                importDTO.getStudentName(), "学生不存在或不在该教学任务中");
                        continue;
                    }

                    // 验证题目答案
                    RepositoryAnswerInfo answerInfo = answerInfoMap.get(importDTO.getRepositoryAnswerId());
                    if (answerInfo == null) {
                        failCount++;
                        addFailRecord(failList, rowNumber, importDTO.getStudentNumber(),
                                importDTO.getStudentName(), "题目答案ID不存在");
                        continue;
                    }

                    // 验证课程目标
                    Long poId = targetPoMap.get(answerInfo.getCourseTargetNo());
                    if (poId == null) {
                        failCount++;
                        addFailRecord(failList, rowNumber, importDTO.getStudentNumber(),
                                importDTO.getStudentName(), "题目对应的课程目标未关联毕业要求指标");
                        continue;
                    }

                    // 检查是否已存在成绩
                    AssessmentScoreDetail existingScore = assessmentScoreDetailMapper.selectByStudentAndAnswer(
                            student.getStudentId(), assessmentId, importDTO.getRepositoryAnswerId());
                    if (existingScore != null) {
                        skipCount++;
                        continue;
                    }
                    LocalDateTime now = LocalDateTime.now();
                    // 创建成绩记录
                    AssessmentScoreDetail score = new AssessmentScoreDetail();
                    score.setStudentId(student.getStudentId());
                    score.setAssessmentId(assessmentId);
                    score.setRepositoryAnswerId(importDTO.getRepositoryAnswerId());
                    score.setStudentAnswer(importDTO.getStudentAnswer());
                    score.setScore(importDTO.getScore());
                    score.setQuestionScore(importDTO.getQuestionScore() != null ?
                            importDTO.getQuestionScore() : answerInfo.getQuestionScore());
                    score.setCourseTargetNo(answerInfo.getCourseTargetNo());
                    score.setPoId(poId);
                    score.setTaskId(taskId);
                    score.setCourseId(assessment.getCourseId());
                    score.setMajorId(getMajorIdFromTask(taskId));
                    score.setStatus(0);
                    score.setCreator(teacherId);
                    score.setCreateTime(now);
                    score.setModifier(teacherId);
                    score.setModifyTime(now);

                    scoreList.add(score);
                    successCount++;

                    // 记录成功信息
                    ScoreImportResultVO.ImportedStudentInfo successInfo = new ScoreImportResultVO.ImportedStudentInfo();
                    successInfo.setStudentNumber(importDTO.getStudentNumber());
                    successInfo.setStudentName(importDTO.getStudentName());
                    successInfo.setClassName(importDTO.getClassName());
                    successInfo.setScore(importDTO.getScore().toString());
                    successList.add(successInfo);

                } catch (Exception e) {
                    failCount++;
                    addFailRecord(failList, rowNumber, importDTO.getStudentNumber(),
                            importDTO.getStudentName(), "处理失败：" + e.getMessage());
                }
            }

            // 批量保存成绩
            if (!CollectionUtils.isEmpty(scoreList)) {
                scoreList.forEach(assessmentScoreDetailMapper::insert);
            }

            // 构建返回结果
            ScoreImportResultVO result = ScoreImportResultVO.success(
                    importList.size(), successCount, failCount, skipCount);
            result.setSuccessList(successList);
            result.setFailList(failList);
            result.setErrorMessages(errorMessages);

            return result;

        } catch (Exception e) {
            log.error("Excel导入考核详情成绩失败", e);
            return ScoreImportResultVO.failure(0, Arrays.asList("导入失败：" + e.getMessage()));
        }
    }

    @Override
    public List<ScoreTargetExcelImportDTO> exportTargetScoreTemplate(Long assessmentId, Long taskId, Long teacherId) {
        // 验证权限
        if (!hasTaskPermission(taskId, teacherId)) {
            throw new RuntimeException("无权限访问该教学任务");
        }

        // 获取教学任务的学生列表
        List<Student> students = getTaskStudents(taskId);

        // 获取考核信息
        Assessment assessment = assessmentService.getById(assessmentId);
        if (assessment == null) {
            throw new RuntimeException("考核不存在");
        }

        // 获取课程目标列表
        List<Integer> courseTargets = getCourseTargetNumbers(assessment.getCourseId());

        List<ScoreTargetExcelImportDTO> result = new ArrayList<>();

        for (Student student : students) {
            for (Integer targetNo : courseTargets) {
                // 使用转换器创建基础DTO
                ScoreTargetExcelImportDTO dto = ScoreImportExportConvert.INSTANCE.studentToTargetTemplate(student);
                // 设置班级名称和课程目标编号
                dto.setClassName(getStudentClassName(student.getStudentId()));
                dto.setCourseTargetNo(targetNo);
                result.add(dto);
            }
        }

        return result;
    }

    @Override
    public List<ScoreDetailExcelImportDTO> exportDetailScoreTemplate(Long assessmentId, Long taskId, Long teacherId) {
        // 验证权限
        if (!hasTaskPermission(taskId, teacherId)) {
            throw new RuntimeException("无权限访问该教学任务");
        }

        // 获取教学任务的学生列表
        List<Student> students = getTaskStudents(taskId);

        // 获取考核的题目答案列表
        Map<Long, RepositoryAnswerInfo> answerInfoMap = getRepositoryAnswerInfoMap(assessmentId);

        List<ScoreDetailExcelImportDTO> result = new ArrayList<>();

        for (Student student : students) {
            for (RepositoryAnswerInfo answerInfo : answerInfoMap.values()) {
                // 使用转换器创建基础DTO
                ScoreDetailExcelImportDTO dto = ScoreImportExportConvert.INSTANCE.studentToDetailTemplate(student);
                // 设置额外信息
                dto.setClassName(getStudentClassName(student.getStudentId()));
                dto.setRepositoryAnswerId(answerInfo.getRepositoryAnswerId());
                dto.setQuestionNumber(answerInfo.getQuestionNumber());
                dto.setQuestionScore(answerInfo.getQuestionScore());
                dto.setCourseTargetNo(answerInfo.getCourseTargetNo());
                result.add(dto);
            }
        }

        return result;
    }

    @Override
    public List<ScoreTargetExcelImportDTO> exportTargetScores(Long assessmentId, Long taskId, Long teacherId) {
        // 验证权限
        if (!hasTaskPermission(taskId, teacherId)) {
            throw new RuntimeException("无权限访问该教学任务");
        }

        // 查询课程目标成绩
        List<AssessmentScoreTarget> scores = assessmentScoreTargetMapper.selectByAssessmentAndTask(assessmentId, taskId);

        return scores.stream().map(score -> {
            // 使用转换器创建基础DTO
            ScoreTargetExcelImportDTO dto = ScoreImportExportConvert.INSTANCE.scoreTargetToExportDTO(score);

            // 获取学生信息
            Student student = studentService.getById(score.getStudentId());
            if (student != null) {
                dto.setStudentNumber(student.getStudentNumber());
                dto.setStudentName(student.getStudentName());
                dto.setClassName(getStudentClassName(student.getStudentId()));
            }

            return dto;
        }).collect(Collectors.toList());
    }

    @Override
    public List<ScoreDetailExcelImportDTO> exportDetailScores(Long assessmentId, Long taskId, Long teacherId) {
        // 验证权限
        if (!hasTaskPermission(taskId, teacherId)) {
            throw new RuntimeException("无权限访问该教学任务");
        }

        // 查询考核详情成绩
        List<AssessmentScoreDetail> scores = assessmentScoreDetailMapper.selectByAssessmentAndTask(assessmentId, taskId);

        // 获取题目答案信息
        Map<Long, RepositoryAnswerInfo> answerInfoMap = getRepositoryAnswerInfoMap(assessmentId);

        return scores.stream().map(score -> {
            // 使用转换器创建基础DTO
            ScoreDetailExcelImportDTO dto = ScoreImportExportConvert.INSTANCE.scoreDetailToExportDTO(score);

            // 获取学生信息
            Student student = studentService.getById(score.getStudentId());
            if (student != null) {
                dto.setStudentNumber(student.getStudentNumber());
                dto.setStudentName(student.getStudentName());
                dto.setClassName(getStudentClassName(student.getStudentId()));
            }

            // 获取题目编号
            RepositoryAnswerInfo answerInfo = answerInfoMap.get(score.getRepositoryAnswerId());
            if (answerInfo != null) {
                dto.setQuestionNumber(answerInfo.getQuestionNumber());
            }

            return dto;
        }).collect(Collectors.toList());
    }

    // ==================== 辅助方法 ====================
    /**
     * 获取题目答案和课程目标的映射关系
     */
    private Map<Long, RepositoryAnswerInfo> getRepositoryAnswerInfoMap(Long assessmentId) {
        // 这里需要查询assessment_content表获取考核内容
        // 然后通过repository_answer表获取题目答案信息
        Map<Long, RepositoryAnswerInfo> result = new HashMap<>();

        try {
            // 查询考核内容（这里需要根据实际的service来调用）
            // List<AssessmentContent> contents = assessmentContentService.getByAssessmentId(assessmentId);
            // 然后查询repository_answer获取答案信息
            // 这里先返回空的Map，实际实现需要根据具体的service来完成
            log.warn("getRepositoryAnswerInfoMap方法需要根据实际的service来实现，assessmentId: {}", assessmentId);
        } catch (Exception e) {
            log.error("获取题目答案信息失败，assessmentId: {}", assessmentId, e);
        }

        return result;
    }


    /**
     * 获取专业ID从教学任务
     */
    private Long getMajorIdFromTask(Long taskId) {
        try {
            TaskWork taskWork = taskWorkService.getById(taskId);
            if (taskWork != null) {
                // 这里需要根据实际的数据结构来获取专业ID
                // 可能需要通过课程或班级来获取专业信息
                return 1L; // 临时返回，需要根据实际情况实现
            }
        } catch (Exception e) {
            log.warn("获取教学任务专业ID失败，taskId: {}", taskId, e);
        }
        return null;
    }

    /**
     * 添加失败记录
     */
    private void addFailRecord(List<ScoreImportResultVO.FailedRecordInfo> failList,
                               int rowNumber, String studentNumber, String studentName, String errorMessage) {
        ScoreImportResultVO.FailedRecordInfo failInfo = new ScoreImportResultVO.FailedRecordInfo();
        failInfo.setRowNumber(rowNumber);
        failInfo.setStudentNumber(studentNumber);
        failInfo.setStudentName(studentName);
        failInfo.setErrorMessage(errorMessage);
        failList.add(failInfo);
    }


    //================== 成绩记录删除相关方法实现 ==================

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int physicalDeleteAllScoresByAssessmentId(Long assessmentId) {
        if (assessmentId == null) {
            log.warn("物理删除成绩记录失败：assessmentId为空");
            return 0;
        }

        try {
            int totalDeleteCount = 0;

            // 1. 删除assessment_score表中的记录
            LambdaQueryWrapper<AssessmentScoreTarget> scoreWrapper = new LambdaQueryWrapper<>();
            scoreWrapper.eq(AssessmentScoreTarget::getAssessmentId, assessmentId);
            int scoreCount = (int) this.count(scoreWrapper);
            boolean scoreResult = this.remove(scoreWrapper);
            if (scoreResult) {
                totalDeleteCount += scoreCount;
                log.info("成功删除assessment_score记录，assessmentId: {}, 删除数量: {}", assessmentId, scoreCount);
            }

            // 2. 删除assessment_score_target表中的记录
            LambdaQueryWrapper<AssessmentScoreTarget> targetWrapper = new LambdaQueryWrapper<>();
            targetWrapper.eq(AssessmentScoreTarget::getAssessmentId, assessmentId);
            Long targetCount = assessmentScoreTargetMapper.selectCount(targetWrapper);
            boolean targetResult = assessmentScoreTargetMapper.delete(targetWrapper) > 0;
            if (targetResult && targetCount > 0) {
                totalDeleteCount += targetCount;
                log.info("成功删除assessment_score_target记录，assessmentId: {}, 删除数量: {}", assessmentId, targetCount);
            }

            // 3. 删除assessment_score_detail表中的记录
            LambdaQueryWrapper<AssessmentScoreDetail> detailWrapper = new LambdaQueryWrapper<>();
            detailWrapper.eq(AssessmentScoreDetail::getAssessmentId, assessmentId);
            Long detailCount = assessmentScoreDetailMapper.selectCount(detailWrapper);
            boolean detailResult = assessmentScoreDetailMapper.delete(detailWrapper) > 0;
            if (detailResult && detailCount > 0) {
                totalDeleteCount += detailCount;
                log.info("成功删除assessment_score_detail记录，assessmentId: {}, 删除数量: {}", assessmentId, detailCount);
            }

            log.info("成功物理删除考核相关成绩记录，assessmentId: {}, 总删除数量: {}", assessmentId, totalDeleteCount);
            return totalDeleteCount;

        } catch (Exception e) {
            log.error("物理删除考核成绩记录时发生异常，assessmentId: {}", assessmentId, e);
            throw new RuntimeException("删除考核成绩记录失败：" + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int physicalDeleteScoresByAssessmentAndTasks(Long assessmentId, List<Long> taskIds) {
        if (assessmentId == null) {
            log.warn("物理删除成绩记录失败：assessmentId为空");
            return 0;
        }

        if (CollectionUtils.isEmpty(taskIds)) {
            // 如果taskIds为空，删除所有相关记录
            return physicalDeleteAllScoresByAssessmentId(assessmentId);
        }

        try {
            int totalDeleteCount = 0;

            // 1. 删除assessment_score表中的记录
            LambdaQueryWrapper<AssessmentScoreTarget> scoreWrapper = new LambdaQueryWrapper<>();
            scoreWrapper.eq(AssessmentScoreTarget::getAssessmentId, assessmentId)
                    .in(AssessmentScoreTarget::getTaskId, taskIds);
            int scoreCount = (int) this.count(scoreWrapper);
            boolean scoreResult = this.remove(scoreWrapper);
            if (scoreResult) {
                totalDeleteCount += scoreCount;
                log.info("成功删除assessment_score记录，assessmentId: {}, taskIds: {}, 删除数量: {}",
                        assessmentId, taskIds, scoreCount);
            }

            // 2. 删除assessment_score_target表中的记录
            LambdaQueryWrapper<AssessmentScoreTarget> targetWrapper = new LambdaQueryWrapper<>();
            targetWrapper.eq(AssessmentScoreTarget::getAssessmentId, assessmentId)
                    .in(AssessmentScoreTarget::getTaskId, taskIds);
            Long targetCount = assessmentScoreTargetMapper.selectCount(targetWrapper);
            boolean targetResult = assessmentScoreTargetMapper.delete(targetWrapper) > 0;
            if (targetResult && targetCount > 0) {
                totalDeleteCount += targetCount;
                log.info("成功删除assessment_score_target记录，assessmentId: {}, taskIds: {}, 删除数量: {}",
                        assessmentId, taskIds, targetCount);
            }

            // 3. 删除assessment_score_detail表中的记录
            LambdaQueryWrapper<AssessmentScoreDetail> detailWrapper = new LambdaQueryWrapper<>();
            detailWrapper.eq(AssessmentScoreDetail::getAssessmentId, assessmentId)
                    .in(AssessmentScoreDetail::getTaskId, taskIds);
            Long detailCount = assessmentScoreDetailMapper.selectCount(detailWrapper);
            boolean detailResult = assessmentScoreDetailMapper.delete(detailWrapper) > 0;
            if (detailResult && detailCount > 0) {
                totalDeleteCount += detailCount;
                log.info("成功删除assessment_score_detail记录，assessmentId: {}, taskIds: {}, 删除数量: {}",
                        assessmentId, taskIds, detailCount);
            }

            log.info("成功物理删除指定任务的考核成绩记录，assessmentId: {}, taskIds: {}, 总删除数量: {}",
                    assessmentId, taskIds, totalDeleteCount);
            return totalDeleteCount;

        } catch (Exception e) {
            log.error("物理删除指定任务的考核成绩记录时发生异常，assessmentId: {}, taskIds: {}", assessmentId, taskIds, e);
            throw new RuntimeException("删除考核成绩记录失败：" + e.getMessage(), e);
        }
    }

    //================== 新增的与成绩录入相关的教学任务核心业务功能实现 ==================

    /**
     * 批量填充教学任务详情信息
     *
     * @param assessmentId 考核ID
     * @param taskIds 教学任务ID列表
     * @param publishTimeMap 发布时间映射
     * @param result 结果列表
     */
    private void batchFillTaskDetailsInfoOptimized(Long assessmentId, List<Long> taskIds,
                                                  Map<Long, LocalDateTime> publishTimeMap,
                                                  List<AssessmentTaskDetailVO> result) {
        try {
            // 将List<Long>转换为逗号分隔的字符串（简化SQL，避免<script>标签）
            String taskIdsStr = taskIds.stream()
                .map(String::valueOf)
                .collect(Collectors.joining(","));

            // 1. 批量查询班级信息和学生统计
            List<AssessmentScoreMapper.TaskClassStatistics> classStatistics =
                    assessmentScoreTargetMapper.batchSelectTaskClassStatistics(taskIdsStr);

            // 2. 批量查询班级已录入成绩数量
            List<AssessmentScoreMapper.ClassScoredStatistics> classScoredStatistics =
                    assessmentScoreTargetMapper.batchSelectClassScoredStatistics(assessmentId, taskIdsStr);

            // 3. 批量查询教学任务成绩统计
            List<AssessmentScoreMapper.TaskScoreStatistics> taskScoreStatistics =
                    assessmentScoreTargetMapper.batchSelectTaskScoreStatistics(assessmentId, taskIdsStr);

            // 4. 构建索引Map以提高查找效率
            Map<Long, List<AssessmentScoreMapper.TaskClassStatistics>> classStatisticsMap =
                classStatistics.stream().collect(Collectors.groupingBy(AssessmentScoreMapper.TaskClassStatistics::getTaskId));

            Map<String, Integer> classScoredMap = classScoredStatistics.stream()
                .collect(Collectors.toMap(
                    stat -> stat.getTaskId() + "_" + stat.getClassId(),
                    AssessmentScoreMapper.ClassScoredStatistics::getScoredCount
                ));

            Map<Long, AssessmentScoreMapper.TaskScoreStatistics> taskScoreMap =
                taskScoreStatistics.stream().collect(Collectors.toMap(
                    AssessmentScoreMapper.TaskScoreStatistics::getTaskId,
                    Function.identity()
                ));

            // 5. 填充每个教学任务的详情信息
            for (AssessmentTaskDetailVO taskDetailVO : result) {
                Long taskId = taskDetailVO.getTaskId();

                // 填充班级信息
                fillTaskClassInfo(taskId, classStatisticsMap, classScoredMap, taskDetailVO);

                // 填充成绩统计信息
                fillTaskScoreStatistics(taskId, taskScoreMap, taskDetailVO);

                // 设置发布时间（从已有的映射中获取，无需额外查询）
                taskDetailVO.setPublishTime(publishTimeMap.get(taskId));
            }

        } catch (Exception e) {
            log.error("批量填充教学任务详情信息失败（优化版），assessmentId: {}, taskIds: {}", assessmentId, taskIds, e);

            // 设置默认值，避免返回null
            for (AssessmentTaskDetailVO taskDetailVO : result) {
                setDefaultTaskDetailValues(taskDetailVO);
                // 设置发布时间
                taskDetailVO.setPublishTime(publishTimeMap.get(taskDetailVO.getTaskId()));
            }
        }
    }

    /**
     * 填充教学任务的班级信息
     */
    private void fillTaskClassInfo(Long taskId,
                                  Map<Long, List<AssessmentScoreMapper.TaskClassStatistics>> classStatisticsMap,
                                  Map<String, Integer> classScoredMap,
                                  AssessmentTaskDetailVO taskDetailVO) {
        List<AssessmentScoreMapper.TaskClassStatistics> taskClasses = classStatisticsMap.get(taskId);
        if (CollectionUtils.isEmpty(taskClasses)) {
            taskDetailVO.setClasses(new ArrayList<>());
            return;
        }

        List<AssessmentTaskDetailVO.TaskClassInfo> classInfos = new ArrayList<>();
        for (AssessmentScoreMapper.TaskClassStatistics classStat : taskClasses) {
            AssessmentTaskDetailVO.TaskClassInfo classInfo = new AssessmentTaskDetailVO.TaskClassInfo();
            classInfo.setClassId(classStat.getClassId());
            classInfo.setClassName(classStat.getClassName());
            classInfo.setStudentCount(classStat.getStudentCount());

            // 获取已录入成绩数量
            String key = taskId + "_" + classStat.getClassId();
            Integer scoredCount = classScoredMap.getOrDefault(key, 0);
            classInfo.setScoredCount(scoredCount);
            classInfo.setUnscoredCount(classStat.getStudentCount() - scoredCount);

            // 计算录入进度
            if (classStat.getStudentCount() > 0) {
                BigDecimal progress = BigDecimal.valueOf(scoredCount)
                    .divide(BigDecimal.valueOf(classStat.getStudentCount()), 4, RoundingMode.HALF_UP)
                    .multiply(BigDecimal.valueOf(100));
                classInfo.setScoreProgress(progress);
            } else {
                classInfo.setScoreProgress(BigDecimal.ZERO);
            }

            classInfos.add(classInfo);
        }

        taskDetailVO.setClasses(classInfos);
    }

    /**
     * 填充教学任务的成绩统计信息
     */
    private void fillTaskScoreStatistics(Long taskId,
                                       Map<Long, AssessmentScoreMapper.TaskScoreStatistics> taskScoreMap,
                                       AssessmentTaskDetailVO taskDetailVO) {
        AssessmentScoreMapper.TaskScoreStatistics scoreStat = taskScoreMap.get(taskId);

        // 计算总学生数和总已录入数
        int totalStudents = taskDetailVO.getClasses().stream()
            .mapToInt(AssessmentTaskDetailVO.TaskClassInfo::getStudentCount)
            .sum();
        int totalScoredStudents = taskDetailVO.getClasses().stream()
            .mapToInt(AssessmentTaskDetailVO.TaskClassInfo::getScoredCount)
            .sum();

        AssessmentTaskDetailVO.ScoreStatistics statistics = new AssessmentTaskDetailVO.ScoreStatistics();
        statistics.setTotalStudentCount(totalStudents);
        statistics.setScoredStudentCount(totalScoredStudents);
        statistics.setUnscoredStudentCount(totalStudents - totalScoredStudents);

        if (scoreStat != null) {
            statistics.setAverageScore(scoreStat.getAverageScore());
            statistics.setMaxScore(scoreStat.getMaxScore());
            statistics.setMinScore(scoreStat.getMinScore());
            statistics.setPassRate(scoreStat.getPassRate());
        }

        // 计算录入进度
        if (totalStudents > 0) {
            BigDecimal progress = BigDecimal.valueOf(totalScoredStudents)
                .divide(BigDecimal.valueOf(totalStudents), 4, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(100));
            statistics.setScoreProgress(progress);
        } else {
            statistics.setScoreProgress(BigDecimal.ZERO);
        }

        // 设置提交状态
        if (totalScoredStudents == 0) {
            taskDetailVO.setSubmissionStatus(AssessmentTaskDetailVO.ScoreSubmissionStatus.NOT_STARTED);
        } else if (totalScoredStudents < totalStudents) {
            taskDetailVO.setSubmissionStatus(AssessmentTaskDetailVO.ScoreSubmissionStatus.IN_PROGRESS);
        } else {
            taskDetailVO.setSubmissionStatus(AssessmentTaskDetailVO.ScoreSubmissionStatus.COMPLETED);
        }

        taskDetailVO.setScoreStatistics(statistics);
    }

    /**
     * 设置默认值
     */
    private void setDefaultTaskDetailValues(AssessmentTaskDetailVO taskDetailVO) {
        if (taskDetailVO.getClasses() == null) {
            taskDetailVO.setClasses(new ArrayList<>());
        }
        if (taskDetailVO.getScoreStatistics() == null) {
            AssessmentTaskDetailVO.ScoreStatistics defaultStats = new AssessmentTaskDetailVO.ScoreStatistics();
            defaultStats.setTotalStudentCount(0);
            defaultStats.setScoredStudentCount(0);
            defaultStats.setUnscoredStudentCount(0);
            defaultStats.setScoreProgress(BigDecimal.ZERO);
            taskDetailVO.setScoreStatistics(defaultStats);
        }
        if (taskDetailVO.getSubmissionStatus() == null) {
            taskDetailVO.setSubmissionStatus(AssessmentTaskDetailVO.ScoreSubmissionStatus.NOT_STARTED);
        }
    }



    /**
     * 优化版本：一次查询获取所有数据（性能优化）
     *
     * @param assessmentId 考核ID
     * @return 教学任务详情列表
     */
    public List<AssessmentTaskDetailVO> getAssessmentTaskDetails(Long assessmentId) {
        if (assessmentId == null) {
            throw new IllegalArgumentException("考核ID不能为空");
        }

        try {
            // 1. 一次查询获取所有数据（教学任务详情 + 基础统计信息）
            List<AssessmentScoreMapper.SuperOptimizedTaskDetail> superOptimizedDetails =
                    assessmentScoreTargetMapper.selectSuperOptimizedTaskDetails(assessmentId);

            if (CollectionUtils.isEmpty(superOptimizedDetails)) {
                return new ArrayList<>();
            }

            // 2. 提取教学任务ID列表，用于查询班级详细信息
            List<Long> taskIds = superOptimizedDetails.stream()
                .map(AssessmentScoreMapper.SuperOptimizedTaskDetail::getTaskId)
                .collect(Collectors.toList());

            // 将List<Long>转换为逗号分隔的字符串
            String taskIdsStr = taskIds.stream()
                .map(String::valueOf)
                .collect(Collectors.joining(","));

            // 3. 只需要查询班级详细信息（已经是最少的查询了）
            List<AssessmentScoreMapper.TaskClassStatistics> classStatistics =
                    assessmentScoreTargetMapper.batchSelectTaskClassStatistics(taskIdsStr);

            List<AssessmentScoreMapper.ClassScoredStatistics> classScoredStatistics =
                    assessmentScoreTargetMapper.batchSelectClassScoredStatistics(assessmentId, taskIdsStr);

            // 4. 构建索引
            Map<Long, List<AssessmentScoreMapper.TaskClassStatistics>> classStatisticsMap =
                classStatistics.stream().collect(Collectors.groupingBy(AssessmentScoreMapper.TaskClassStatistics::getTaskId));

            Map<String, Integer> classScoredMap = classScoredStatistics.stream()
                .collect(Collectors.toMap(
                    stat -> stat.getTaskId() + "_" + stat.getClassId(),
                    AssessmentScoreMapper.ClassScoredStatistics::getScoredCount
                ));

            // 5. 转换为最终结果
            List<AssessmentTaskDetailVO> result = new ArrayList<>();
            for (AssessmentScoreMapper.SuperOptimizedTaskDetail detail : superOptimizedDetails) {
                AssessmentTaskDetailVO taskDetailVO = convertSuperOptimizedDetail(detail);

                // 填充班级详细信息
                fillTaskClassInfo(detail.getTaskId(), classStatisticsMap, classScoredMap, taskDetailVO);

                result.add(taskDetailVO);
            }

            return result;

        } catch (Exception e) {
            log.error("优化查询考核关联教学任务详情失败，assessmentId: {}", assessmentId, e);
            throw new RuntimeException("查询教学任务详情失败：" + e.getMessage(), e);
        }
    }

    /**
     * 转换优化的详情对象为VO
     */
    private AssessmentTaskDetailVO convertSuperOptimizedDetail(AssessmentScoreMapper.SuperOptimizedTaskDetail detail) {
        AssessmentTaskDetailVO vo = new AssessmentTaskDetailVO();

        // 基本信息
        vo.setTaskId(detail.getTaskId());
        vo.setCourseId(detail.getCourseId());
        vo.setTaskName(detail.getTaskName());
        vo.setTaskNumber(detail.getTaskNumber());
        vo.setTaskYear(detail.getTaskYear().toString());
        vo.setTaskTerm(detail.getTaskTerm());
        vo.setPublishTime(detail.getPublishTime());

        // 成绩统计信息
        AssessmentTaskDetailVO.ScoreStatistics statistics = new AssessmentTaskDetailVO.ScoreStatistics();
        statistics.setTotalStudentCount(detail.getTotalStudents());
        statistics.setScoredStudentCount(detail.getTotalScoredStudents());
        statistics.setUnscoredStudentCount(detail.getTotalStudents() - detail.getTotalScoredStudents());
        statistics.setAverageScore(detail.getAverageScore());
        statistics.setMaxScore(detail.getMaxScore());
        statistics.setMinScore(detail.getMinScore());
        statistics.setPassRate(detail.getPassRate());

        // 计算录入进度
        if (detail.getTotalStudents() > 0) {
            BigDecimal progress = BigDecimal.valueOf(detail.getTotalScoredStudents())
                .divide(BigDecimal.valueOf(detail.getTotalStudents()), 4, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(100));
            statistics.setScoreProgress(progress);
        } else {
            statistics.setScoreProgress(BigDecimal.ZERO);
        }

        vo.setScoreStatistics(statistics);

        // 设置提交状态
        if (detail.getTotalScoredStudents() == 0) {
            vo.setSubmissionStatus(AssessmentTaskDetailVO.ScoreSubmissionStatus.NOT_STARTED);
        } else if (detail.getTotalScoredStudents() < detail.getTotalStudents()) {
            vo.setSubmissionStatus(AssessmentTaskDetailVO.ScoreSubmissionStatus.IN_PROGRESS);
        } else {
            vo.setSubmissionStatus(AssessmentTaskDetailVO.ScoreSubmissionStatus.COMPLETED);
        }

        return vo;
    }

    @Override
    public List<StudentScoreDetailVO> getStudentScoreDetails(Long assessmentId, Long taskId) {
        if (assessmentId == null || taskId == null) {
            throw new IllegalArgumentException("考核ID和教学任务ID不能为空");
        }

        try {
            // 使用优化的批量查询方法
            return getStudentScoreDetailsOptimized(assessmentId, taskId);
        } catch (Exception e) {
            log.error("查询学生成绩详情失败，assessmentId: {}, taskId: {}", assessmentId, taskId, e);
            throw new RuntimeException("查询学生成绩详情失败：" + e.getMessage(), e);
        }
    }

    /**
     * 优化版的学生成绩详情查询方法
     * 参考 AchievementAnalysisServiceImpl 的设计，使用批量查询避免N+1问题
     */
    private List<StudentScoreDetailVO> getStudentScoreDetailsOptimized(Long assessmentId, Long taskId) {
        log.info("开始优化查询学生成绩详情，assessmentId: {}, taskId: {}", assessmentId, taskId);

        try {
            // 1. 使用优化的Mapper一次性获取所有数据
            List<OptimizedAssessmentScoreMapper.StudentScoreData> optimizedData =
                optimizedAssessmentScoreMapper.batchGetStudentScoreDetails(assessmentId, taskId);

            if (CollectionUtils.isEmpty(optimizedData)) {
                log.info("未找到学生成绩数据，assessmentId: {}, taskId: {}", assessmentId, taskId);
                return new ArrayList<>();
            }

            // 2. 按学生ID分组数据
            Map<Long, List<OptimizedAssessmentScoreMapper.StudentScoreData>> studentDataMap =
                optimizedData.stream()
                    .collect(Collectors.groupingBy(OptimizedAssessmentScoreMapper.StudentScoreData::getStudentId));

            // 3. 构建结果列表
            List<StudentScoreDetailVO> result = new ArrayList<>();
            for (Map.Entry<Long, List<OptimizedAssessmentScoreMapper.StudentScoreData>> entry : studentDataMap.entrySet()) {
                StudentScoreDetailVO studentVO = buildStudentScoreDetailVO(entry.getValue());
                if (studentVO != null) {
                    result.add(studentVO);
                }
            }

            log.info("成功查询到 {} 个学生的成绩详情", result.size());
            return result;

        } catch (Exception e) {
            log.error("优化查询学生成绩详情失败，assessmentId: {}, taskId: {}", assessmentId, taskId, e);
            throw e;
        }
    }

    /**
     * 从优化查询的数据构建StudentScoreDetailVO
     */
    private StudentScoreDetailVO buildStudentScoreDetailVO(List<OptimizedAssessmentScoreMapper.StudentScoreData> studentData) {
        if (CollectionUtils.isEmpty(studentData)) {
            return null;
        }

        // 获取第一条记录作为学生基本信息
        OptimizedAssessmentScoreMapper.StudentScoreData firstRecord = studentData.get(0);

        StudentScoreDetailVO studentVO = new StudentScoreDetailVO();
        studentVO.setStudentId(firstRecord.getStudentId());
        studentVO.setStudentNumber(firstRecord.getStudentNumber());
        studentVO.setStudentName(firstRecord.getStudentName());
        studentVO.setClassId(firstRecord.getClassId());
        studentVO.setClassName(firstRecord.getClassName());

        // 暂时跳过JSON解析，使用简化处理

        // 构建课程目标成绩列表
        Map<Integer, OptimizedAssessmentScoreMapper.StudentScoreData> targetScoreMap =
            studentData.stream()
                .filter(data -> data.getCourseTargetNo() != null)
                .collect(Collectors.toMap(
                    OptimizedAssessmentScoreMapper.StudentScoreData::getCourseTargetNo,
                    data -> data,
                    (existing, replacement) -> existing // 如果有重复，保留第一个
                ));

        List<StudentScoreDetailVO.CourseTargetScore> courseTargetScores = new ArrayList<>();
        for (OptimizedAssessmentScoreMapper.StudentScoreData data : targetScoreMap.values()) {
            StudentScoreDetailVO.CourseTargetScore targetScore = new StudentScoreDetailVO.CourseTargetScore();
            targetScore.setCourseTargetNo(data.getCourseTargetNo());

            // 简化处理，直接使用默认名称
            targetScore.setCourseTargetName("课程目标" + data.getCourseTargetNo());

            targetScore.setScore(data.getTargetScore());
            targetScore.setFullScore(data.getTargetFullScore());
//            targetScore.setEntryTime(data.getEntryTime());
//            targetScore.setLastModifyTime(data.getLastModifyTime());
//            targetScore.setEntryUserName(getUserName(data.getEntryUserId()));

            // 计算得分率
            if (data.getTargetScore() != null && data.getTargetFullScore() != null &&
                data.getTargetFullScore().compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal scoreRate = data.getTargetScore().divide(data.getTargetFullScore(), 4, RoundingMode.HALF_UP)
                    .multiply(BigDecimal.valueOf(100));
                targetScore.setScoreRate(scoreRate);
            }

            courseTargetScores.add(targetScore);
        }
        studentVO.setCourseTargetScores(courseTargetScores);

        // 构建详情成绩列表
        Map<Long, OptimizedAssessmentScoreMapper.StudentScoreData> detailScoreMap =
            studentData.stream()
                .filter(data -> data.getRepositoryAnswerId() != null)
                .collect(Collectors.toMap(
                    OptimizedAssessmentScoreMapper.StudentScoreData::getRepositoryAnswerId,
                    data -> data,
                    (existing, replacement) -> existing
                ));

        List<StudentScoreDetailVO.AssessmentDetailScore> detailScores = new ArrayList<>();
        for (OptimizedAssessmentScoreMapper.StudentScoreData data : detailScoreMap.values()) {
            StudentScoreDetailVO.AssessmentDetailScore detailScore = new StudentScoreDetailVO.AssessmentDetailScore();
            detailScore.setRepositoryAnswerId(data.getRepositoryAnswerId());
            detailScore.setScore(data.getDetailScore());
            //detailScore.setFullScore(data.getDetailFullScore());
            detailScore.setQuestionContent(data.getQuestionContent());
            detailScore.setQuestionType(data.getQuestionType());
            detailScore.setQuestionScore(data.getQuestionScore());

            detailScores.add(detailScore);
        }
        studentVO.setDetailScores(detailScores);

        return studentVO;
    }

    @Override
    public StudentScoreDetailVO getStudentScoreDetail(Long assessmentId, Long taskId, Long studentId) {
        if (assessmentId == null || taskId == null || studentId == null) {
            throw new IllegalArgumentException("考核ID、教学任务ID和学生ID不能为空");
        }

        try {
            // 1. 获取学生信息
            Student student = studentService.getById(studentId);
            if (student == null) {
                throw new RuntimeException("学生不存在");
            }

            // 2. 使用转换器创建基础VO
            StudentScoreDetailVO result = StudentScoreDetailConvert.INSTANCE.toStudentScoreDetailVO(student);

            // 3. 填充成绩详情
            fillStudentScoreDetails(assessmentId, taskId, result);

            return result;

        } catch (Exception e) {
            log.error("查询单个学生成绩详情失败，assessmentId: {}, taskId: {}, studentId: {}",
                    assessmentId, taskId, studentId, e);
            throw new RuntimeException("查询学生成绩详情失败：" + e.getMessage(), e);
        }
    }

    @Override
    public List<StudentCourseTargetScoreVO> getStudentCourseTargetScores(Long courseId) {
        log.info("开始查询学生课程目标成绩，courseId: {}", courseId);

        try {
            // 使用优化的查询方法，参考 AchievementAnalysisServiceImpl 的设计
            return getStudentCourseTargetScoresOptimized(courseId);
        } catch (Exception e) {
            log.error("查询学生课程目标成绩失败，courseId: {}", courseId, e);
            throw new RuntimeException("查询学生课程目标成绩失败：" + e.getMessage(), e);
        }
    }

    /**
     * 优化版的学生课程目标成绩查询
     * 参考 AchievementAnalysisServiceImpl 的优秀实践
     */
    private List<StudentCourseTargetScoreVO> getStudentCourseTargetScoresOptimized(Long courseId) {
        // 1. 使用现有的优化查询（保持兼容性）
        List<StudentCourseTargetScoreFlatDTO> flatList = assessmentScoreDetailMapper.groupScoreByTargetAndMethod(courseId);
        if (CollectionUtils.isEmpty(flatList)) {
            log.info("未找到课程目标成绩数据，courseId: {}", courseId);
            return List.of();
        }

        // 2. 使用Stream API优化数据处理，减少嵌套循环
        Map<Long, List<StudentCourseTargetScoreFlatDTO>> studentMap = flatList.stream()
            .collect(Collectors.groupingBy(StudentCourseTargetScoreFlatDTO::getStudentId));

        List<StudentCourseTargetScoreVO> result = studentMap.entrySet().parallelStream()
            .map(this::buildStudentCourseTargetScoreVO)
            .filter(Objects::nonNull)
            .collect(Collectors.toList());

        log.info("成功查询到 {} 个学生的课程目标成绩", result.size());
        return result;
    }

    /**
     * 构建单个学生的课程目标成绩VO
     */
    private StudentCourseTargetScoreVO buildStudentCourseTargetScoreVO(Map.Entry<Long, List<StudentCourseTargetScoreFlatDTO>> entry) {
        List<StudentCourseTargetScoreFlatDTO> studentScores = entry.getValue();
        if (CollectionUtils.isEmpty(studentScores)) {
            return null;
        }

        StudentCourseTargetScoreFlatDTO firstScore = studentScores.get(0);
        StudentCourseTargetScoreVO vo = new StudentCourseTargetScoreVO();
        vo.setStudentId(String.valueOf(firstScore.getStudentId()));
        vo.setStudentName(firstScore.getStudentName());
        vo.setStudentNumber(firstScore.getStudentNumber());

        // 按课程目标分组并构建目标成绩列表
        Map<Integer, List<StudentCourseTargetScoreFlatDTO>> targetMap = studentScores.stream()
            .collect(Collectors.groupingBy(StudentCourseTargetScoreFlatDTO::getCourseTargetNo));

        List<StudentCourseTargetScoreVO.TargetScoreVO> targetScoreList = targetMap.entrySet().stream()
            .map(this::buildTargetScoreVO)
            .filter(Objects::nonNull)
            .collect(Collectors.toList());

        vo.setTargetScores(targetScoreList);
        return vo;
    }

    /**
     * 构建单个课程目标成绩VO
     */
    private StudentCourseTargetScoreVO.TargetScoreVO buildTargetScoreVO(Map.Entry<Integer, List<StudentCourseTargetScoreFlatDTO>> targetEntry) {
        List<StudentCourseTargetScoreFlatDTO> targetScores = targetEntry.getValue();
        if (CollectionUtils.isEmpty(targetScores)) {
            return null;
        }

        StudentCourseTargetScoreFlatDTO firstTarget = targetScores.get(0);
        StudentCourseTargetScoreVO.TargetScoreVO targetScoreVO = new StudentCourseTargetScoreVO.TargetScoreVO();
        targetScoreVO.setCourseTargetNo(String.valueOf(firstTarget.getCourseTargetNo()));
        targetScoreVO.setObjectiveId(firstTarget.getObjectiveId());
        targetScoreVO.setPoId(firstTarget.getPoId());
        targetScoreVO.setCourseTargetName(firstTarget.getCourseTargetName());

        // 构建考核方式成绩列表
        List<StudentCourseTargetScoreVO.MethodScoreVO> methodScoreList = targetScores.stream()
            .map(this::buildMethodScoreVO)
            .collect(Collectors.toList());
        targetScoreVO.setMethodScores(methodScoreList);

        // 计算总分
        BigDecimal totalScore = methodScoreList.stream()
            .map(StudentCourseTargetScoreVO.MethodScoreVO::getScore)
            .filter(Objects::nonNull)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        targetScoreVO.setTotalScore(totalScore);

        return targetScoreVO;
    }

    /**
     * 构建考核方式成绩VO
     */
    private StudentCourseTargetScoreVO.MethodScoreVO buildMethodScoreVO(StudentCourseTargetScoreFlatDTO ts) {
        StudentCourseTargetScoreVO.MethodScoreVO ms = new StudentCourseTargetScoreVO.MethodScoreVO();
        ms.setAssessmentMethod(ts.getAssessmentMethod());
        ms.setScore(ts.getTotalScore());
        return ms;
    }
//            vo.setTargetScores(targetScoreList);
//            result.add(vo);
//        }
//        return result;
//    }



    //================== 核心业务功能辅助方法 ==================

    /**
     * 填充学生成绩详情
     */
    private void fillStudentScoreDetails(Long assessmentId, Long taskId, StudentScoreDetailVO studentVO) {
        try {
            // 1. 获取班级名称
            if (studentVO.getClassId() != null) {
                Classes classes = classesService.getById(studentVO.getClassId());
                if (classes != null) {
                    studentVO.setClassName(classes.getClassName());
                }
            }

            // 2. 获取课程目标成绩（不再获取主成绩，因为assessment_score_target表存储的是分解成绩）

            // 3. 获取课程目标成绩
            List<AssessmentScoreTarget> targetScores = getTargetScores(assessmentId, taskId, studentVO.getStudentId());
            List<StudentScoreDetailVO.CourseTargetScore> courseTargetScores =
                    StudentScoreDetailConvert.INSTANCE.toCourseTargetScoreList(targetScores);

            // 填充课程目标名称和权重信息
            fillCourseTargetInfo(courseTargetScores, assessmentId);
            studentVO.setCourseTargetScores(courseTargetScores);

            // 4. 获取考核详情成绩
            List<AssessmentScoreDetail> detailScores = getDetailScores(assessmentId, taskId, studentVO.getStudentId());
            List<StudentScoreDetailVO.AssessmentDetailScore> assessmentDetailScores =
                    StudentScoreDetailConvert.INSTANCE.toAssessmentDetailScoreList(detailScores);

            // 填充题目信息
            fillQuestionInfo(assessmentDetailScores, assessmentId);
            studentVO.setDetailScores(assessmentDetailScores);

            // 5. 计算总成绩和状态（基于课程目标成绩）
            if (!CollectionUtils.isEmpty(targetScores)) {
                // 计算总分（所有课程目标成绩之和）
                BigDecimal totalScore = targetScores.stream()
                        .map(AssessmentScoreTarget::getScore)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                StudentScoreDetailConvert.INSTANCE.updateTotalScoreInfo(
                        totalScore, BigDecimal.valueOf(0), studentVO);

                // TODO：使用第一个成绩记录的时间和录入者信息
                AssessmentScoreTarget firstScore = targetScores.get(0);
                studentVO.setEntryTime(firstScore.getCreateTime());
                studentVO.setLastModifyTime(firstScore.getModifyTime());
                studentVO.setEntryUserId(firstScore.getCreator());
                studentVO.setEntryUserName(getUserName(firstScore.getCreator()));
            }

            // 6. 确定录入状态
            boolean hasTargetScores = !CollectionUtils.isEmpty(targetScores);
            boolean hasDetailScores = !CollectionUtils.isEmpty(detailScores);

            StudentScoreDetailVO.ScoreEntryStatus entryStatus =
                    StudentScoreDetailConvert.INSTANCE.determineEntryStatus(false, hasTargetScores, hasDetailScores);
            studentVO.setEntryStatus(entryStatus);

        } catch (Exception e) {
            log.error("填充学生成绩详情失败，assessmentId: {}, taskId: {}, studentId: {}",
                    assessmentId, taskId, studentVO.getStudentId(), e);
            // 设置默认值，避免返回null
            if (studentVO.getEntryStatus() == null) {
                studentVO.setEntryStatus(StudentScoreDetailVO.ScoreEntryStatus.NOT_ENTERED);
            }
            if (studentVO.getCourseTargetScores() == null) {
                studentVO.setCourseTargetScores(new ArrayList<>());
            }
            if (studentVO.getDetailScores() == null) {
                studentVO.setDetailScores(new ArrayList<>());
            }
        }
    }
    

    /**
     * 获取课程目标成绩（优化版）
     * 使用批量查询避免N+1问题
     */
    private List<AssessmentScoreTarget> getTargetScores(Long assessmentId, Long taskId, Long studentId) {
        return assessmentScoreTargetMapper.selectByAssessmentAndStudent(assessmentId, taskId, studentId);
    }

    /**
     * 批量获取多个学生的课程目标成绩（新增优化方法）
     */
    private Map<Long, List<AssessmentScoreTarget>> batchGetTargetScores(Long assessmentId, Long taskId, List<Long> studentIds) {
        if (CollectionUtils.isEmpty(studentIds)) {
            return new HashMap<>();
        }

        // 批量查询所有学生的成绩
        List<AssessmentScoreTarget> allScores = assessmentScoreTargetMapper.selectByAssessmentAndStudents(assessmentId, taskId, studentIds);

        // 按学生ID分组
        return allScores.stream()
            .collect(Collectors.groupingBy(AssessmentScoreTarget::getStudentId));
    }

    /**
     * 获取用户名（简化实现，实际项目中可能需要查询用户表）
     */
    private String getUserName(Long userId) {
        if (userId == null) {
            return "";
        }
        // 这里可以添加用户查询逻辑，或者使用缓存
        // 为了性能考虑，可以批量查询用户信息
        return "用户" + userId; // 简化实现
    }

    @Override
    public List<StudentScoreTargetVO> getStudentTargetScoresByTaskId(Long assessmentId, Long taskId) {
        log.info("开始查询学生课程目标成绩（优化版），assessmentId: {}, taskId: {}", assessmentId, taskId);

        try {
            // 1. 获取考核信息和课程目标总分配置
            Assessment assessment = assessmentService.getById(assessmentId);
            if (assessment == null) {
                log.warn("考核不存在，assessmentId: {}", assessmentId);
                return List.of();
            }

            // 2. 解析assessment_detail字段获取课程目标总分配置（整个考核的配置，所有学生共享）
            Map<String, BigDecimal> targetFullScoreMap = parseAssessmentDetail(assessment.getAssessmentDetail());
            log.info("解析到课程目标总分配置: {}", targetFullScoreMap);

            // 3. 计算考核总满分（一次计算，所有学生共享）
            BigDecimal assessmentTotalFullScore = targetFullScoreMap.values().stream()
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            log.info("考核总满分: {}", assessmentTotalFullScore);

            // 4. 优化：直接查询成绩记录及学生信息（一次查询）
            List<AssessmentScoreTargetMapper.AssessmentScoreTargetWithStudent> scoresWithStudents =
                assessmentScoreTargetMapper.selectScoresWithStudentInfo(assessmentId, taskId);

            log.info("查询到 {} 条成绩记录", scoresWithStudents.size());

            // 5. 按学生分组成绩记录
            Map<Long, List<AssessmentScoreTargetMapper.AssessmentScoreTargetWithStudent>> studentScoreMap =
                scoresWithStudents.stream()
                    .collect(Collectors.groupingBy(AssessmentScoreTargetMapper.AssessmentScoreTargetWithStudent::getStudentId));

            // 6. 获取任务中所有学生ID（用于补充没有成绩记录的学生）
            List<Long> allStudentIds = assessmentScoreTargetMapper.selectAllStudentIdsByTaskId(taskId);
            log.info("任务中共有 {} 个学生", allStudentIds.size());

            // 7. 构建结果列表
            List<StudentScoreTargetVO> result = new ArrayList<>();
            for (Long studentId : allStudentIds) {
                List<AssessmentScoreTargetMapper.AssessmentScoreTargetWithStudent> studentScores =
                    studentScoreMap.getOrDefault(studentId, List.of());

                StudentScoreTargetVO studentVO = buildStudentScoreTargetVOOptimized(
                    studentId,
                    studentScores,
                    targetFullScoreMap,
                    assessmentTotalFullScore
                );
                result.add(studentVO);
            }

            log.info("成功查询到 {} 个学生的课程目标成绩", result.size());
            return result;

        } catch (Exception e) {
            log.error("查询学生课程目标成绩失败，assessmentId: {}, taskId: {}", assessmentId, taskId, e);
            throw new RuntimeException("查询学生课程目标成绩失败：" + e.getMessage(), e);
        }
    }

    /**
     * 构建学生课程目标成绩VO（优化版本）
     * 基于成绩记录和学生信息构建VO，避免额外的学生信息查询
     *
     * @param studentId 学生ID
     * @param studentScores 学生成绩记录列表（包含学生信息）
     * @param targetFullScoreMap 课程目标总分配置
     * @param assessmentTotalFullScore 考核总满分
     * @return 学生课程目标成绩VO
     */
    private StudentScoreTargetVO buildStudentScoreTargetVOOptimized(
            Long studentId,
            List<AssessmentScoreTargetMapper.AssessmentScoreTargetWithStudent> studentScores,
            Map<String, BigDecimal> targetFullScoreMap,
            BigDecimal assessmentTotalFullScore) {

        StudentScoreTargetVO studentVO = new StudentScoreTargetVO();
        studentVO.setStudentId(studentId);

        // 如果有成绩记录，从第一条记录中获取学生基本信息
        if (!studentScores.isEmpty()) {
            AssessmentScoreTargetMapper.AssessmentScoreTargetWithStudent firstScore = studentScores.get(0);
            studentVO.setStudentNumber(firstScore.getStudentNumber());
            studentVO.setStudentName(firstScore.getStudentName());
            studentVO.setClassId(firstScore.getClassId());
            studentVO.setClassName(firstScore.getClassName());
        } else {
            // 如果没有成绩记录，需要查询学生基本信息
            // 这里可以优化为批量查询，但考虑到大部分学生都有成绩记录，单独查询的情况较少
            Student student = studentService.getById(studentId);
            if (student != null) {
                studentVO.setStudentNumber(student.getStudentNumber());
                studentVO.setStudentName(student.getStudentName());
                studentVO.setClassId(student.getClassId());
                // 班级名称可以通过额外查询获取，或者在后续优化中批量获取
                studentVO.setClassName(""); // 暂时设为空，避免额外查询
            }
        }

        // 构建课程目标成绩列表
        List<StudentScoreTargetVO.CourseTargetScore> targetScores = new ArrayList<>();
        BigDecimal totalScore = BigDecimal.ZERO;

        // 按课程目标编号分组成绩
        Map<String, List<AssessmentScoreTargetMapper.AssessmentScoreTargetWithStudent>> targetScoreMap =
            studentScores.stream()
                .collect(Collectors.groupingBy(AssessmentScoreTargetMapper.AssessmentScoreTargetWithStudent::getObjectiveId));

        // 遍历所有配置的课程目标
        for (Map.Entry<String, BigDecimal> entry : targetFullScoreMap.entrySet()) {
            String objectiveId = entry.getKey();
            BigDecimal fullScore = entry.getValue();

            StudentScoreTargetVO.CourseTargetScore targetScore = new StudentScoreTargetVO.CourseTargetScore();
            targetScore.setObjectiveId(objectiveId);
            targetScore.setFullScore(fullScore);

            // 计算该课程目标的得分

            List<AssessmentScoreTargetMapper.AssessmentScoreTargetWithStudent> targetScoreRecords =
                targetScoreMap.getOrDefault(objectiveId, List.of());

            BigDecimal targetTotalScore = targetScoreRecords.stream()
                .map(AssessmentScoreTargetMapper.AssessmentScoreTargetWithStudent::getScore)
                .filter(score -> score != null)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

            targetScore.setScore(targetTotalScore);
            targetScores.add(targetScore);
            totalScore = totalScore.add(targetTotalScore);
        }

        studentVO.setCourseTargetScores(targetScores);
        studentVO.setTotalScore(totalScore);
        studentVO.setFullScore(assessmentTotalFullScore);

        // 计算得分率
        if (assessmentTotalFullScore.compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal scoreRate = totalScore.divide(assessmentTotalFullScore, 4, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(100));
            studentVO.setScoreRate(scoreRate);
        } else {
            studentVO.setScoreRate(BigDecimal.ZERO);
        }

        return studentVO;
    }

//    /**
//     * 原始版本的查询方法（保留用于性能对比）
//     *
//     * @param assessmentId 考核ID
//     * @param taskId 教学任务ID
//     * @return 学生课程目标成绩列表
//     */
//    public List<StudentScoreTargetVO> getStudentTargetScoresByTaskIdOriginal(Long assessmentId, Long taskId) {
//        log.info("开始查询学生课程目标成绩（原始版），assessmentId: {}, taskId: {}", assessmentId, taskId);
//
//        try {
//            // 1. 获取考核信息和课程目标总分配置
//            Assessment assessment = assessmentService.getById(assessmentId);
//            if (assessment == null) {
//                log.warn("考核不存在，assessmentId: {}", assessmentId);
//                return List.of();
//            }
//
//            // 2. 解析assessment_detail字段获取课程目标总分配置
//            Map<String, BigDecimal> targetFullScoreMap = parseAssessmentDetail(assessment.getAssessmentDetail());
//            log.info("解析到课程目标总分配置: {}", targetFullScoreMap);
//
//            // 3. 计算考核总满分
//            BigDecimal assessmentTotalFullScore = targetFullScoreMap.values().stream()
//                .reduce(BigDecimal.ZERO, BigDecimal::add);
//            log.info("考核总满分: {}", assessmentTotalFullScore);
//
//            // 4. 获取教学任务的学生列表（原始方式）
//            List<Student> students = studentService.getStudentsByTaskId(taskId);
//            if (CollectionUtils.isEmpty(students)) {
//                log.warn("未找到学生列表，taskId: {}", taskId);
//                return List.of();
//            }
//
//            // 5. 批量获取学生课程目标成绩（原始方式）
//            List<Long> studentIds = students.stream().map(Student::getStudentId).collect(Collectors.toList());
//            Map<Long, List<AssessmentScoreTarget>> studentScoreMap = batchGetTargetScores(assessmentId, taskId, studentIds);
//
//            // 6. 构建结果列表（原始方式）
//            List<StudentScoreTargetVO> result = new ArrayList<>();
//            for (Student student : students) {
//                StudentScoreTargetVO studentVO = buildStudentScoreTargetVO(
//                    student,
//                    studentScoreMap.getOrDefault(student.getStudentId(), List.of()),
//                    targetFullScoreMap,
//                    assessmentTotalFullScore
//                );
//                result.add(studentVO);
//            }
//
//            log.info("成功查询到 {} 个学生的课程目标成绩（原始版）", result.size());
//            return result;
//
//        } catch (Exception e) {
//            log.error("查询学生课程目标成绩失败（原始版），assessmentId: {}, taskId: {}", assessmentId, taskId, e);
//            throw new RuntimeException("查询学生课程目标成绩失败：" + e.getMessage(), e);
//        }
//    }
//
//    /**
//     * 性能对比测试方法
//     *
//     * @param assessmentId 考核ID
//     * @param taskId 教学任务ID
//     * @return 性能对比结果
//     */
//    public Map<String, Object> performanceComparison(Long assessmentId, Long taskId) {
//        Map<String, Object> result = new HashMap<>();
//
//        // 测试原始版本
//        long startTime1 = System.currentTimeMillis();
//        List<StudentScoreTargetVO> result1 = getStudentTargetScoresByTaskIdOriginal(assessmentId, taskId);
//        long endTime1 = System.currentTimeMillis();
//        long originalTime = endTime1 - startTime1;
//
//        // 测试优化版本
//        long startTime2 = System.currentTimeMillis();
//        List<StudentScoreTargetVO> result2 = getStudentTargetScoresByTaskId(assessmentId, taskId);
//        long endTime2 = System.currentTimeMillis();
//        long optimizedTime = endTime2 - startTime2;
//
//        // 计算性能提升
//        double improvement = originalTime > 0 ?
//            ((double)(originalTime - optimizedTime) / originalTime) * 100 : 0;
//
//        result.put("originalTime", originalTime);
//        result.put("optimizedTime", optimizedTime);
//        result.put("improvement", String.format("%.2f%%", improvement));
//        result.put("originalCount", result1.size());
//        result.put("optimizedCount", result2.size());
//        result.put("dataConsistent", result1.size() == result2.size());
//
//        log.info("性能对比结果 - 原始版本: {}ms, 优化版本: {}ms, 提升: {}",
//                originalTime, optimizedTime, String.format("%.2f%%", improvement));
//
//        return result;
//    }

    /**
     * 解析assessment_detail字段获取课程目标总分配置
     * JSON格式：[{"number": null, "totalScore": 3.0, "courseObjectiveId": "1752993676951"}, ...]
     *
     * @param assessmentDetail JSON字符串
     * @return Map<课程目标编号, 总分> 注意：这里使用课程目标编号作为key，不是courseObjectiveId
     */
    private Map<String, BigDecimal> parseAssessmentDetail(String assessmentDetail) {
        Map<String, BigDecimal> targetFullScoreMap = new HashMap<>();

        if (assessmentDetail == null || assessmentDetail.trim().isEmpty()) {
            return targetFullScoreMap;
        }

        try {
            List<DirectEntryConfig> configs = JSONUtil.parseJson(assessmentDetail, DirectEntryConfig.class);
            for (int i = 0; i < configs.size(); i++) {
                DirectEntryConfig config = configs.get(i);
                if (config.getTotalScore() > 0) {
                    // 使用课程目标编号作为key（从1开始）
                    String targetNo = String.valueOf(i + 1);
                    targetFullScoreMap.put(targetNo, BigDecimal.valueOf(config.getTotalScore()));

                    log.debug("解析课程目标{}的总分: {}", targetNo, config.getTotalScore());
                }
            }
        } catch (Exception e) {
            log.warn("解析assessment_detail失败: {}", assessmentDetail, e);
        }

        return targetFullScoreMap;
    }

    /**
     * 构建单个学生的课程目标成绩VO
     * @param student 学生信息
     * @param targetScores 学生的课程目标成绩列表
     * @param targetFullScoreMap 课程目标总分配置（从assessment_detail解析）
     * @param assessmentTotalFullScore 考核总满分（预计算，避免重复计算）
     */
    private StudentScoreTargetVO buildStudentScoreTargetVO(Student student,
                                                          List<AssessmentScoreTarget> targetScores,
                                                          Map<String, BigDecimal> targetFullScoreMap,
                                                          BigDecimal assessmentTotalFullScore) {
        StudentScoreTargetVO studentVO = new StudentScoreTargetVO();

        // 设置学生基本信息
        studentVO.setStudentId(student.getStudentId());
        studentVO.setStudentNumber(student.getStudentNumber());
        studentVO.setStudentName(student.getStudentName());
        studentVO.setClassId(student.getClassId());

        // 构建课程目标成绩列表
        List<StudentScoreTargetVO.CourseTargetScore> targetScoreList = new ArrayList<>();

        // 按课程目标编号分组
        Map<Integer, AssessmentScoreTarget> scoreMap = targetScores.stream()
            .collect(Collectors.toMap(
                AssessmentScoreTarget::getCourseTargetNo,
                score -> score,
                (existing, replacement) -> existing
            ));

        // 计算学生总成绩
        BigDecimal totalScore = BigDecimal.ZERO;

        // 为每个课程目标创建成绩记录
        for (AssessmentScoreTarget targetScore : targetScores) {
            StudentScoreTargetVO.CourseTargetScore courseTargetScore = new StudentScoreTargetVO.CourseTargetScore();

            // 设置课程目标基本信息
            courseTargetScore.setCourseTargetNo(targetScore.getCourseTargetNo());
            courseTargetScore.setCourseTargetName("课程目标" + targetScore.getCourseTargetNo());

            // 设置成绩信息
            courseTargetScore.setScore(targetScore.getScore());

            // 从解析的配置中获取该课程目标的满分
            String objectiveId = String.valueOf(targetScore.getCourseTargetNo());
            BigDecimal targetFullScore = targetFullScoreMap.getOrDefault(objectiveId, BigDecimal.ZERO);
            courseTargetScore.setFullScore(targetFullScore);

            // 计算得分率
            if (targetScore.getScore() != null && targetFullScore.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal scoreRate = targetScore.getScore()
                    .divide(targetFullScore, 4, RoundingMode.HALF_UP)
                    .multiply(BigDecimal.valueOf(100));
                courseTargetScore.setScoreRate(scoreRate);
            }

            targetScoreList.add(courseTargetScore);

            // 累计学生总成绩
            if (targetScore.getScore() != null) {
                totalScore = totalScore.add(targetScore.getScore());
            }
        }

        // 设置课程目标成绩列表
        studentVO.setCourseTargetScores(targetScoreList);

        // 设置总成绩和总分（总分使用预计算的值，避免重复计算）
        studentVO.setTotalScore(totalScore);
        studentVO.setFullScore(assessmentTotalFullScore);

        // 计算总得分率
        if (totalScore.compareTo(BigDecimal.ZERO) > 0 && assessmentTotalFullScore.compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal totalScoreRate = totalScore.divide(assessmentTotalFullScore, 4, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(100));
            studentVO.setScoreRate(totalScoreRate);
        }
        return studentVO;
    }



    /**
     * 获取考核详情成绩
     */
    private List<AssessmentScoreDetail> getDetailScores(Long assessmentId, Long taskId, Long studentId) {
        return assessmentScoreDetailMapper.selectByAssessmentAndStudent(assessmentId, taskId, studentId);
    }

    /**
     * 填充课程目标信息
     */
    private void fillCourseTargetInfo(List<StudentScoreDetailVO.CourseTargetScore> courseTargetScores, Long assessmentId) {
        try {
            // 获取考核信息以获取课程ID
            Assessment assessment = assessmentService.getById(assessmentId);
            if (assessment == null) {
                return;
            }

            // 获取课程目标列表
            List<Integer> courseTargets = getCourseTargetNumbers(assessment.getCourseId());
            Map<Integer, String> targetNameMap = new HashMap<>();

            // 这里可以查询课程目标的详细信息，暂时简化处理
            for (Integer targetNo : courseTargets) {
                targetNameMap.put(targetNo, "课程目标" + targetNo);
            }

            // 填充课程目标信息
            for (StudentScoreDetailVO.CourseTargetScore targetScore : courseTargetScores) {
                String targetName = targetNameMap.get(targetScore.getCourseTargetNo());
                targetScore.setCourseTargetName(targetName != null ? targetName : "课程目标" + targetScore.getCourseTargetNo());

                // 设置默认满分（可以根据实际业务调整）
                if (targetScore.getFullScore() == null) {
                    targetScore.setFullScore(BigDecimal.valueOf(100));
                }

                // 重新计算得分率
                if (targetScore.getScore() != null && targetScore.getFullScore() != null) {
                    BigDecimal scoreRate = StudentScoreDetailConvert.INSTANCE.calculateScoreRate(
                            targetScore.getScore(), targetScore.getFullScore());
                    targetScore.setScoreRate(scoreRate);
                }
            }
        } catch (Exception e) {
            log.warn("填充课程目标信息失败，assessmentId: {}", assessmentId, e);
        }
    }

    /**
     * 填充题目信息
     */
    private void fillQuestionInfo(List<StudentScoreDetailVO.AssessmentDetailScore> detailScores, Long assessmentId) {
        try {
            // 获取考核的题目答案信息
            Map<Long, RepositoryAnswerInfo> answerInfoMap = getRepositoryAnswerInfoMap(assessmentId);

            for (StudentScoreDetailVO.AssessmentDetailScore detailScore : detailScores) {
                RepositoryAnswerInfo answerInfo = answerInfoMap.get(detailScore.getRepositoryAnswerId());
                if (answerInfo != null) {
                    detailScore.setQuestionNumber(answerInfo.getQuestionNumber());
//                    detailScore.setQuestionContent(truncateContent(answerInfo.getQuestionContent(), 50));
//                    detailScore.setQuestionType(answerInfo.getQuestionType());
//                    detailScore.setDifficultyLevel(answerInfo.getDifficultyLevel());
                } else {
                    detailScore.setQuestionNumber("题目" + detailScore.getRepositoryAnswerId());
                    detailScore.setQuestionContent("题目内容...");
                    detailScore.setQuestionType("未知");
                    detailScore.setDifficultyLevel("中等");
                }
            }
        } catch (Exception e) {
            log.warn("填充题目信息失败，assessmentId: {}", assessmentId, e);
            // 使用默认值
            for (StudentScoreDetailVO.AssessmentDetailScore detailScore : detailScores) {
                detailScore.setQuestionNumber("题目" + detailScore.getRepositoryAnswerId());
                detailScore.setQuestionContent("题目内容...");
                detailScore.setQuestionType("未知");
                detailScore.setDifficultyLevel("中等");
            }
        }
    }

    /**
     * 获取课程目标编号列表
     */
    private List<Integer> getCourseTargetNumbers(Long courseId) {
        try {
            // 这里应该查询课程目标表获取课程目标编号列表
            // 暂时返回默认的课程目标编号
            List<Integer> targets = new ArrayList<>();
            for (int i = 1; i <= 5; i++) {
                targets.add(i);
            }
            return targets;
        } catch (Exception e) {
            log.warn("获取课程目标编号失败，courseId: {}", courseId, e);
            return Arrays.asList(1, 2, 3, 4, 5);
        }
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public BatchSaveTargetScoresResultVO batchSaveTargetScores(BatchSaveTargetScoresDTO batchSaveDTO, Long teacherId) {
        try {
            // 1. 参数验证
            if (batchSaveDTO == null || CollectionUtils.isEmpty(batchSaveDTO.getStudentScores())) {
                return BatchSaveTargetScoresResultVO.failure("学生成绩数据不能为空");
            }

            // 2. 权限验证
            if (!hasTaskPermission(batchSaveDTO.getTaskId(), teacherId)) {
                return BatchSaveTargetScoresResultVO.failure("无权限操作该教学任务");
            }

            if (!isAssessmentPublished(batchSaveDTO.getAssessmentId(), batchSaveDTO.getTaskId())) {
                return BatchSaveTargetScoresResultVO.failure("考核未发布给该教学任务");
            }

            // 3. 获取教学任务和课程信息
            TaskWork taskWork = taskWorkService.getById(batchSaveDTO.getTaskId());
            if (taskWork == null) {
                return BatchSaveTargetScoresResultVO.failure("教学任务不存在");
            }

            // 4. 获取课程目标和毕业要求映射关系
            Map<Integer, Long> courseTargetPoMapping = getCourseTargetPoMapping(taskWork.getCourseId());

            // 5. 批量处理成绩数据
            return processBatchTargetScores(batchSaveDTO, taskWork, courseTargetPoMapping, teacherId);

        } catch (Exception e) {
            log.error("批量保存学生课程目标成绩失败", e);
            return BatchSaveTargetScoresResultVO.failure("保存失败：" + e.getMessage());
        }
    }

    /**
     * 处理批量成绩数据
     */
    private BatchSaveTargetScoresResultVO processBatchTargetScores(BatchSaveTargetScoresDTO batchSaveDTO,
                                                                  TaskWork taskWork,
                                                                  Map<Integer, Long> courseTargetPoMapping,
                                                                  Long teacherId) {
        List<BatchSaveTargetScoresResultVO.FailureDetail> failures = new ArrayList<>();
        List<AssessmentScoreTarget> toInsert = new ArrayList<>();
        List<AssessmentScoreTarget> toUpdate = new ArrayList<>();

        LocalDateTime now = LocalDateTime.now();

        // 1. 构建查询键，用于批量检查现有记录
        List<String> queryKeys = batchSaveDTO.getStudentScores().stream()
            .map(score -> score.getStudentId() + "_" + score.getObjectiveId())
            .collect(Collectors.toList());

        String keysStr = String.join(",", queryKeys);

        // 2. 批量查询现有记录
        List<AssessmentScoreTarget> existingRecords = assessmentScoreTargetMapper
            .selectExistingRecords(batchSaveDTO.getAssessmentId(), keysStr);

        Map<String, AssessmentScoreTarget> existingMap = existingRecords.stream()
            .collect(Collectors.toMap(
                record -> record.getStudentId() + "_" + record.getObjectiveId(),
                Function.identity()
            ));

        // 3. 处理每条成绩记录
        for (BatchSaveTargetScoresDTO.StudentTargetScoreDTO studentScore : batchSaveDTO.getStudentScores()) {
            try {
                // 验证课程目标
                Long poId = courseTargetPoMapping.get(studentScore.getCourseTargetNo());
                if (poId == null) {
                    failures.add(createFailureDetail(studentScore, "课程目标编号不存在或未关联毕业要求指标"));
                    continue;
                }

                String key = studentScore.getStudentId() + "_" + studentScore.getObjectiveId();
                AssessmentScoreTarget existingRecord = existingMap.get(key);

                if (existingRecord != null) {
                    // 更新现有记录
                    existingRecord.setScore(studentScore.getScore());
                    existingRecord.setCourseTargetNo(studentScore.getCourseTargetNo());
                    existingRecord.setPoId(poId);
                    existingRecord.setModifier(teacherId);
                    existingRecord.setModifyTime(now);
                    existingRecord.setObjectiveId(studentScore.getObjectiveId());
                    toUpdate.add(existingRecord);
                } else {
                    // 创建新记录
                    AssessmentScoreTarget newRecord = createNewScoreRecord(
                        batchSaveDTO, studentScore, taskWork, poId, teacherId, now);
                    toInsert.add(newRecord);
                }

            } catch (Exception e) {
                log.error("处理学生成绩记录失败", e);
                failures.add(createFailureDetail(studentScore, "处理失败：" + e.getMessage()));
            }
        }

        // 4. 批量保存数据
        try {
            if (!CollectionUtils.isEmpty(toInsert)) {
                toInsert.forEach(this::save);
            }
            if (!CollectionUtils.isEmpty(toUpdate)) {
                toUpdate.forEach(this::updateById);
            }

            // 5. 构建返回结果
            if (failures.isEmpty()) {
                return BatchSaveTargetScoresResultVO.success(
                    batchSaveDTO.getStudentScores().size(),
                    toInsert.size(),
                    toUpdate.size()
                );
            } else {
                return BatchSaveTargetScoresResultVO.partialSuccess(
                    batchSaveDTO.getStudentScores().size(),
                    toInsert.size(),
                    toUpdate.size(),
                    failures
                );
            }

        } catch (Exception e) {
            log.error("批量保存成绩数据失败", e);
            return BatchSaveTargetScoresResultVO.failure("数据保存失败：" + e.getMessage());
        }
    }

    /**
     * 创建新的成绩记录
     */
    private AssessmentScoreTarget createNewScoreRecord(BatchSaveTargetScoresDTO batchSaveDTO,
                                                      BatchSaveTargetScoresDTO.StudentTargetScoreDTO studentScore,
                                                      TaskWork taskWork,
                                                      Long poId,
                                                      Long teacherId,
                                                      LocalDateTime now) {
        AssessmentScoreTarget record = new AssessmentScoreTarget();
        record.setStudentId(studentScore.getStudentId());
        record.setAssessmentId(batchSaveDTO.getAssessmentId());
        record.setRepositoryAnswerId(studentScore.getRepositoryAnswerId());
        record.setScore(studentScore.getScore());
        record.setCourseTargetNo(studentScore.getCourseTargetNo());
        record.setPoId(poId);
        record.setTaskId(batchSaveDTO.getTaskId());
        record.setCourseId(taskWork.getCourseId());
        record.setMajorId(taskWork.getMajorId());
        record.setStatus(0);
        record.setCreator(teacherId);
        record.setCreateTime(now);
        record.setModifier(teacherId);
        record.setModifyTime(now);
        record.setObjectiveId(studentScore.getObjectiveId());
        return record;
    }

    /**
     * 创建失败详情
     */
    private BatchSaveTargetScoresResultVO.FailureDetail createFailureDetail(
            BatchSaveTargetScoresDTO.StudentTargetScoreDTO studentScore, String reason) {
        BatchSaveTargetScoresResultVO.FailureDetail detail = new BatchSaveTargetScoresResultVO.FailureDetail();
        detail.setStudentId(studentScore.getStudentId());
        detail.setCourseTargetNo(studentScore.getCourseTargetNo());
        detail.setRepositoryAnswerId(studentScore.getRepositoryAnswerId());
        detail.setReason(reason);
        return detail;
    }

    /**
     * 获取课程目标和毕业要求映射关系
     */
    private Map<Integer, Long> getCourseTargetPoMapping(Long courseId) {
        try {
            // 调用课程服务获取课程目标列表
            List<CourseObjectiveVO> courseObjectives = courseService.getCourseObjectives(courseId);


            // 构建课程目标编号到毕业要求ID的映射
            Map<Integer, Long> mapping = new HashMap<>();

            for (CourseObjectiveVO objectiveObj : courseObjectives) {
                try {
                    if (objectiveObj != null && objectiveObj.getNumber() != null) {
                        Long poId = null;

                        // 优先使用 gindicatorId
                        if (objectiveObj.getGindicatorId() != null) {
                            poId = objectiveObj.getGindicatorId();
                        }
                        // 如果 gindicatorId 为空，尝试从 po 对象中获取
                        else if (objectiveObj.getPo() != null && objectiveObj.getPo().getId() != null) {
                            poId = objectiveObj.getPo().getId();
                        }

                        if (poId != null) {
                            mapping.put(objectiveObj.getNumber(), poId);
                            log.debug("添加课程目标映射: 目标{} -> 毕业要求{}", objectiveObj.getNumber(), poId);
                        } else {
                            log.warn("课程目标{}缺少毕业要求ID信息", objectiveObj.getNumber());
                        }
                    }
                } catch (Exception e) {
                    log.warn("处理课程目标对象失败，跳过该目标，courseId: {}, objective: {}", courseId, objectiveObj, e);
                }
            }

            log.debug("获取课程目标映射关系成功，courseId: {}, mapping: {}", courseId, mapping);
            return mapping;

        } catch (Exception e) {
            log.error("获取课程目标和毕业要求映射关系失败，courseId: {}", courseId, e);
            return new HashMap<>();
        }
    }

    /**
     * 获取课程目标编号到objectiveId的映射关系
     */
    private Map<Integer, String> getCourseTargetObjectiveIdMapping(Long courseId) {
        try {
            // 调用课程服务获取课程目标列表
            List<CourseObjectiveVO> courseObjectives = courseService.getCourseObjectives(courseId);

            // 构建课程目标编号到objectiveId的映射
            Map<Integer, String> mapping = new HashMap<>();

            for (CourseObjectiveVO objectiveObj : courseObjectives) {
                try {
                    if (objectiveObj != null && objectiveObj.getNumber() != null && objectiveObj.getObjectiveId() != null) {
                        mapping.put(objectiveObj.getNumber(), objectiveObj.getObjectiveId());
                        log.debug("添加课程目标objectiveId映射: 目标{} -> objectiveId{}", objectiveObj.getNumber(), objectiveObj.getObjectiveId());
                    } else {
                        log.warn("课程目标{}缺少objectiveId信息", objectiveObj.getNumber());
                    }
                } catch (Exception e) {
                    log.warn("处理课程目标对象失败，跳过该目标，courseId: {}, objective: {}", courseId, objectiveObj, e);
                }
            }

            log.debug("获取课程目标objectiveId映射关系成功，courseId: {}, mapping: {}", courseId, mapping);
            return mapping;

        } catch (Exception e) {
            log.error("获取课程目标objectiveId映射关系失败，courseId: {}", courseId, e);
            return new HashMap<>();
        }
    }



}
