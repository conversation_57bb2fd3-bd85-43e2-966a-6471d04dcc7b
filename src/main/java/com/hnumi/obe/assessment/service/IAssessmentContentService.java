package com.hnumi.obe.assessment.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hnumi.obe.assessment.dto.DetailEntryConfigDTO;
import com.hnumi.obe.assessment.dto.DirectEntryConfigDTO;
import com.hnumi.obe.assessment.entity.AssessmentContent;
import com.hnumi.obe.assessment.vo.AssessmentContentDetailVO;

import java.util.List;

//, IService<RepositoryQuestion>,IService<RepositoryAnswer>
public interface IAssessmentContentService extends IService<AssessmentContent> {
    /**
     * 获取一次考核的详细内容（题目和答案）
     * @param assessmentId 考核ID
     * @return 详细内容VO
     */
    AssessmentContentDetailVO getAssessmentContentDetail(Long assessmentId);

    List<Long> getQuestionIdsByAssessmentId(Long assessmentId);
    /**
     * 保存一组考核详情
     */
    boolean saveDirectEntryConfig(DetailEntryConfigDTO dto);

    boolean updateDirectEntryConfig(DirectEntryConfigDTO directEntryConfigDTO);

    DirectEntryConfigDTO getDirectEntryConfig(Long assessmentId);

    int deleteByAssessmentId(Long assessmentId);




    //================== 考核物理删除相关方法 ==================
    /**
     * 物理删除考核及其所有关联数据
     * 包括：assessment、assessment_task、assessment_content、assessment_score等相关表记录
     *
     * @param assessmentId 考核ID
     * @return 删除结果
     */
    boolean physicalDeleteAssessment(Long assessmentId);

    /**
     * 批量物理删除考核及其所有关联数据
     *
     * @param assessmentIds 考核ID列表
     * @return 删除结果统计
     */
    AssessmentDeleteResult batchPhysicalDeleteAssessments(List<Long> assessmentIds);

    /**
     * 删除结果统计类
     */
    class AssessmentDeleteResult {
        private int totalCount;
        private int successCount;
        private int failCount;
        private List<String> errorMessages;
        private List<AssessmentDeleteDetail> deleteDetails;

        public AssessmentDeleteResult(int totalCount, int successCount, int failCount,
                                      List<String> errorMessages, List<AssessmentDeleteDetail> deleteDetails) {
            this.totalCount = totalCount;
            this.successCount = successCount;
            this.failCount = failCount;
            this.errorMessages = errorMessages;
            this.deleteDetails = deleteDetails;
        }

        // Getters
        public int getTotalCount() { return totalCount; }
        public int getSuccessCount() { return successCount; }
        public int getFailCount() { return failCount; }
        public List<String> getErrorMessages() { return errorMessages; }
        public List<AssessmentDeleteDetail> getDeleteDetails() { return deleteDetails; }
    }

    /**
     * 删除详情类
     */
    class AssessmentDeleteDetail {
        private Long assessmentId;
        private String assessmentName;
        private boolean success;
        private String errorMessage;
        private int deletedTaskCount;
        private int deletedContentCount;
        private int deletedScoreCount;

        public AssessmentDeleteDetail(Long assessmentId, String assessmentName, boolean success,
                                      String errorMessage, int deletedTaskCount, int deletedContentCount, int deletedScoreCount) {
            this.assessmentId = assessmentId;
            this.assessmentName = assessmentName;
            this.success = success;
            this.errorMessage = errorMessage;
            this.deletedTaskCount = deletedTaskCount;
            this.deletedContentCount = deletedContentCount;
            this.deletedScoreCount = deletedScoreCount;
        }

        // Getters
        public Long getAssessmentId() { return assessmentId; }
        public String getAssessmentName() { return assessmentName; }
        public boolean isSuccess() { return success; }
        public String getErrorMessage() { return errorMessage; }
        public int getDeletedTaskCount() { return deletedTaskCount; }
        public int getDeletedContentCount() { return deletedContentCount; }
        public int getDeletedScoreCount() { return deletedScoreCount; }
    }
}

