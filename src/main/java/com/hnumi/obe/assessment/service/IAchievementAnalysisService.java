package com.hnumi.obe.assessment.service;

import com.hnumi.obe.assessment.vo.CourseTargetScoreVO;

import java.util.List;

/**
 * 课程目标达成度分析服务接口
 */
public interface IAchievementAnalysisService {

    /**
     * 获取课程达成度分析数据
     * @param courseCode 课程代码
     * @return 课程达成度分析结果
     */
    CourseTargetScoreVO getTargetAchievementAnalysis(String courseCode);

    /**
     * 获取多个学期的课程达成度趋势分析
     * @param courseCode 课程代码
     * @return 课程达成度趋势数据
     */
    List<CourseTargetScoreVO> getTargetAchievementTrend(String courseCode);

    /**
     * 计算单个课程的达成度结果
     * @param courseId 课程ID
     * @param useWeights 是否使用权重计算
     * @return 达成度计算结果
     */
    CourseTargetScoreVO calculateCourseAchievement(Long courseId, boolean useWeights);
}
