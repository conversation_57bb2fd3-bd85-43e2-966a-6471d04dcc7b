package com.hnumi.obe.assessment.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hnumi.obe.assessment.dto.AssessmentDTO;
import com.hnumi.obe.assessment.dto.AssessmentPublishDTO;
import com.hnumi.obe.assessment.dto.AssessmentPublishQueryDTO;
import com.hnumi.obe.assessment.dto.AssessmentQueryDTO;
import com.hnumi.obe.assessment.dto.DirectEntryConfigDTO;
import com.hnumi.obe.assessment.vo.DirectEntryConfig;
import com.hnumi.obe.common.util.JSONUtil;
import com.hnumi.obe.assessment.entity.Assessment;
import com.hnumi.obe.assessment.mapper.AssessmentMapper;
import com.hnumi.obe.assessment.mapstruct.AssessmentConvert;
import com.hnumi.obe.assessment.service.IAssessmentContentService;
import com.hnumi.obe.assessment.service.IAssessmentScoreService;
import com.hnumi.obe.assessment.service.IAssessmentService;
import com.hnumi.obe.assessment.service.IAssessmentTaskService;
import com.hnumi.obe.assessment.vo.AssessmentPublishVO;
import com.hnumi.obe.assessment.vo.AssessmentVO;
import com.hnumi.obe.assessment.vo.PublishableTaskVO;
import com.hnumi.obe.base.entity.Classes;
import com.hnumi.obe.base.entity.Teacher;
import com.hnumi.obe.base.service.IClassesService;
import com.hnumi.obe.base.service.ITeacherService;
import com.hnumi.obe.task.entity.TaskWork;
import com.hnumi.obe.task.service.ITaskWorkService;
import com.hnumi.obe.task.service.ITaskWorklistClassesService;
import com.hnumi.obe.task.service.ITaskWorklistTeachersService;
import com.hnumi.obe.tp.service.ICourseService;
import com.hnumi.obe.tp.vo.AssessmentDetailVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 教学任务考核表 服务实现类
 */
@Slf4j
@Service
public class AssessmentServiceImpl extends ServiceImpl<AssessmentMapper, Assessment> implements IAssessmentService {
    @Autowired
    private ICourseService courseService;

    @Autowired
    @Lazy
    private IAssessmentTaskService assessmentTaskService;

    @Autowired
    private ITaskWorkService taskWorkService;

    @Autowired
    private ITaskWorklistClassesService taskWorklistClassesService;

    @Autowired
    private ITaskWorklistTeachersService taskWorklistTeachersService;

    @Autowired
    private IClassesService classesService;

    @Autowired
    private ITeacherService teacherService;

    //private static  final JSONUtil<DirectEntryConfig> jsonUtil = new JSONUtil<>();


    @Override
    public IPage<AssessmentVO> pageAssessment(AssessmentQueryDTO queryDTO) {
        Page<Assessment> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
        LambdaQueryWrapper<Assessment> wrapper = new LambdaQueryWrapper<>();

        // 构建查询条件
        wrapper.eq(queryDTO.getTaskId() != null, Assessment::getTaskId, queryDTO.getTaskId())
                .eq(queryDTO.getCourseId() != null, Assessment::getCourseId, queryDTO.getCourseId())
                .eq(queryDTO.getAssessmentType() != null, Assessment::getAssessmentMethod, queryDTO.getAssessmentType())
                .eq(queryDTO.getAssessmentYear() != null, Assessment::getAssessmentYear, queryDTO.getAssessmentYear())
                .eq(queryDTO.getAssessmentTerm() != null, Assessment::getAssessmentTerm, queryDTO.getAssessmentTerm())
                .eq(queryDTO.getScoreType() != null, Assessment::getScoreType, queryDTO.getScoreType())
                .like(StringUtils.hasText(queryDTO.getAssessmentName()), Assessment::getAssessmentName, queryDTO.getAssessmentName())
                .eq(Assessment::getStatus, 0)
                .orderByDesc(Assessment::getCreateTime);

        IPage<Assessment> assessmentPage = this.page(page, wrapper);

        // 转换为VO
        return assessmentPage.convert(AssessmentConvert.INSTANCE::toVO);
    }

    @Override
    public AssessmentVO getAssessmentById(Long id) {
        Assessment assessment = this.getById(id);
        AssessmentVO result = AssessmentConvert.INSTANCE.toVO(assessment);
        //JSONUtil<AssessmentDetailVO>
        result.setAssessmentDetailList( JSONUtil.parseJson(assessment.getAssessmentDetail(), DirectEntryConfig.class));
        return result;
    }

    @Override
    public AssessmentVO saveAssessment(AssessmentDTO assessmentDTO) {
        Assessment assessment = AssessmentConvert.INSTANCE.toEntity(assessmentDTO);
        this.save(assessment);
        return AssessmentConvert.INSTANCE.toVO(assessment);
    }

    @Override
    public boolean updateAssessment(AssessmentDTO assessmentDTO) {
        Assessment assessment = AssessmentConvert.INSTANCE.toEntity(assessmentDTO);
        return this.updateById(assessment);
    }

    @Override
    public boolean deleteAssessment(Long id) {
        Assessment assessment = new Assessment();
        assessment.setId(id);
        assessment.setStatus(-1);
        return this.updateById(assessment);
    }


    //==========================查询方法==========================

    @Override
    public List<AssessmentVO> getAssessmentByTaskId(Long taskId) {
        LambdaQueryWrapper<Assessment> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Assessment::getTaskId, taskId)
                .eq(Assessment::getStatus, 0)
                .orderByDesc(Assessment::getCreateTime);

        List<Assessment> assessments = this.list(wrapper);
        List<AssessmentVO> result = AssessmentConvert.INSTANCE.toVOList(assessments);
        //assessmentDetailList从JSON字段中获取,将assessments中的assessmentDetailList字段从JSON字段中获取

        for (int i = 0; i < result.size(); i++) {
            result.get(i).setAssessmentDetailList(
                    JSONUtil.parseJson(assessments.get(i).getAssessmentDetail(), DirectEntryConfig.class)
            );
        }
        return result;
    }

    @Override
    public List<AssessmentVO> getAssessmentByCourseId(Long courseId) {
        LambdaQueryWrapper<Assessment> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Assessment::getCourseId, courseId)
                .eq(Assessment::getStatus, 0)
                .orderByDesc(Assessment::getCreateTime);

        List<Assessment> assessments = this.list(wrapper);
        return AssessmentConvert.INSTANCE.toVOList(assessments);
    }

    @Override
    public List<AssessmentVO> getAssessmentByCourseCode(String courseCode) {
        // 参数校验
        if (!StringUtils.hasText(courseCode)) {
            throw new IllegalArgumentException("课程代码不能为空");
        }

        // 首先从课程表中获取课程ID
        List<Long> courseIds = courseService.getCourseIdByCode(courseCode);
        if (courseIds == null) {
            throw new RuntimeException("未找到课程代码为 " + courseCode + " 的课程");
        }

        // 构建批量查询条件，使用in条件查询一组Assessment,条件是courseId in courseIds
        LambdaQueryWrapper<Assessment> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(Assessment::getCourseId, courseIds)
                .eq(Assessment::getStatus, 0)
                .orderByDesc(Assessment::getCreateTime);

        List<Assessment> assessments = this.list(wrapper);
        return AssessmentConvert.INSTANCE.toVOList(assessments);


    }



    //================== 考核发布相关方法实现 ==================
    @Override
    public List<PublishableTaskVO> getPublishableTasks(AssessmentPublishQueryDTO queryDTO) {
        // 1. 根据考核ID获取考核信息
        Assessment assessment = this.getById(queryDTO.getAssessmentId());
        if (assessment == null) {
            throw new RuntimeException("考核不存在");
        }

        // 2. 查询匹配的教学任务
        LambdaQueryWrapper<TaskWork> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaskWork::getCourseId, assessment.getCourseId())
                .eq(TaskWork::getTaskYear, assessment.getAssessmentYear())
                .eq(TaskWork::getTaskTerm, assessment.getAssessmentTerm())
                .eq(TaskWork::getStatus, 0);

        List<TaskWork> taskWorks = taskWorkService.list(wrapper);
        if (CollectionUtils.isEmpty(taskWorks)) {
            return new ArrayList<>();
        }

        // 3. 获取已发布的教学任务ID列表
        List<Long> publishedTaskIds = new ArrayList<>();
        if (assessment.getTaskId() != null && assessment.getTaskId() == -1) {
            // 全部发布的情况，所有匹配的教学任务都已发布
            publishedTaskIds = taskWorks.stream().map(TaskWork::getId).collect(Collectors.toList());
        } else {
            // 部分发布的情况，查询关联表
            publishedTaskIds = assessmentTaskService.getTaskIdsByAssessmentId(queryDTO.getAssessmentId());
        }

        // 4. 构建返回结果
        List<PublishableTaskVO> result = new ArrayList<>();
        List<Long> taskIds = taskWorks.stream().map(TaskWork::getId).collect(Collectors.toList());

        // 获取班级信息
        Map<Long, List<Long>> taskClassMap = taskWorklistClassesService.getClassesMap(taskIds)
                .entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> entry.getValue().stream().map(tc -> tc.getClassId()).collect(Collectors.toList())
                ));

        // 获取教师信息
        Map<Long, List<Long>> taskTeacherMap = taskWorklistTeachersService.getTeachersMap(taskIds)
                .entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> entry.getValue().stream().map(tt -> tt.getTeacherId()).collect(Collectors.toList())
                ));

        for (TaskWork taskWork : taskWorks) {
            PublishableTaskVO vo = new PublishableTaskVO();
            vo.setTaskId(taskWork.getId());
            vo.setTaskName(taskWork.getTaskName());
            vo.setCourseId(taskWork.getCourseId());
            vo.setTaskNumber(taskWork.getTaskNumber());
            vo.setTaskYear(taskWork.getTaskYear());
            vo.setTaskTerm(taskWork.getTaskTerm());
            vo.setTeachWeek(taskWork.getTeachWeek());
            vo.setWeekHours(taskWork.getWeekHours());
            vo.setTotalHours(taskWork.getTotalHours());
            vo.setIsPublished(publishedTaskIds.contains(taskWork.getId()));

            // 设置班级信息
            List<Long> classIds = taskClassMap.get(taskWork.getId());
            if (!CollectionUtils.isEmpty(classIds)) {
                List<Classes> classes = classesService.listByIds(classIds);
                List<PublishableTaskVO.TaskClassInfo> classInfos = classes.stream()
                        .map(cls -> {
                            PublishableTaskVO.TaskClassInfo classInfo = new PublishableTaskVO.TaskClassInfo();
                            classInfo.setClassId(cls.getClassId());
                            classInfo.setClassName(cls.getClassName());
                            classInfo.setStudentCount(cls.getStudentNumber());
                            return classInfo;
                        }).collect(Collectors.toList());
                vo.setClasses(classInfos);
            }

            // 设置教师信息
            List<Long> teacherIds = taskTeacherMap.get(taskWork.getId());
            if (!CollectionUtils.isEmpty(teacherIds)) {
                List<Teacher> teachers = teacherService.listByIds(teacherIds);
                List<PublishableTaskVO.TaskTeacherInfo> teacherInfos = teachers.stream()
                        .map(teacher -> {
                            PublishableTaskVO.TaskTeacherInfo teacherInfo = new PublishableTaskVO.TaskTeacherInfo();
                            teacherInfo.setTeacherId(teacher.getTeacherId());
                            teacherInfo.setTeacherName(teacher.getTeacherName() );
                            teacherInfo.setRole(1); // 默认主讲教师
                            teacherInfo.setRoleName("主讲教师");
                            return teacherInfo;
                        }).collect(Collectors.toList());
                vo.setTeachers(teacherInfos);
            }

            result.add(vo);
        }

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AssessmentPublishVO publishAssessment(AssessmentPublishDTO publishDTO) {
        // 1. 验证考核是否存在
        Assessment assessment = this.getById(publishDTO.getAssessmentId());
        if (assessment == null) {
            throw new RuntimeException("考核不存在");
        }

        AssessmentPublishVO result = new AssessmentPublishVO();
        result.setAssessmentId(publishDTO.getAssessmentId());
        result.setAssessmentName(assessment.getAssessmentName());
        result.setPublishToAll(publishDTO.getPublishToAll());
        result.setPublishNote(publishDTO.getPublishNote());
        result.setPublishTime(LocalDateTime.now());

        try {
            // 3. 发布给指定教学任务
            if (CollectionUtils.isEmpty(publishDTO.getTaskIds())) {
                throw new RuntimeException("发布给指定教学任务时，教学任务ID列表不能为空");
            }

            // 重置assessment表的task_id为具体值（取第一个），并设置考核状态为进行中
            LambdaUpdateWrapper<Assessment> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(Assessment::getId, publishDTO.getAssessmentId())
                    .set(Assessment::getTaskId, publishDTO.getTaskIds().get(0))
                    .set(Assessment::getAchievement, publishDTO.getPublishToAll()) //设置是否参与达成度计算
                    .set(Assessment::getAssessmentStatus, 2) // 设置考核状态为进行中
                    .set(Assessment::getModifyTime, LocalDateTime.now());
            this.update(updateWrapper);

            // 创建关联记录
            int createdCount = assessmentTaskService.batchCreateAssessmentTaskRelations(
                    publishDTO.getAssessmentId(),
                    publishDTO.getTaskIds(),
                    publishDTO.getPublishNote(),
                    1L // TODO: 从上下文获取当前用户ID
            );

            result.setPublishedTaskCount(createdCount);
            result.setPublishedTaskIds(publishDTO.getTaskIds());

            if (createdCount > 0) {
                result.setPublishStatus(0); // 发布成功
                result.setPublishMessage(String.format("考核已成功发布给 %d 个教学任务", createdCount));
            } else {
                result.setPublishStatus(1); // 部分成功
                result.setPublishMessage("所有指定的教学任务都已发布该考核");
            }
        } catch (Exception e) {
            result.setPublishStatus(2); // 发布失败
            result.setPublishMessage("发布失败：" + e.getMessage());
            throw e; // 重新抛出异常以触发事务回滚
        }

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean unpublishAssessment(Long assessmentId, List<Long> taskIds) {
        Assessment assessment = this.getById(assessmentId);
        if (assessment == null) {
            return false;
        }

        try {
            if (CollectionUtils.isEmpty(taskIds)) {
                // 撤销所有发布
                // 1. 物理删除assessment_task表中的所有关联记录
                assessmentTaskService.physicalDeleteByAssessmentId(assessmentId);

                // 2. 重置assessment表的task_id和考核状态
                LambdaUpdateWrapper<Assessment> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(Assessment::getId, assessmentId)
                        .set(Assessment::getTaskId, 0L) // 设置为未发布状态
                        .set(Assessment::getAchievement, false)//取消参与达成度计算
                        .set(Assessment::getAssessmentStatus, 1) // 重置考核状态为编辑中
                        .set(Assessment::getModifyTime, LocalDateTime.now());
                this.update(updateWrapper);
            } else {
                // 撤销指定教学任务的发布
                // 1. 物理删除指定的assessment_task关联记录
                assessmentTaskService.physicalDeleteByAssessmentAndTasks(assessmentId, taskIds);

                // 2. 检查是否还有其他关联记录
                List<Long> remainingTaskIds = assessmentTaskService.getTaskIdsByAssessmentId(assessmentId);
                if (CollectionUtils.isEmpty(remainingTaskIds)) {
                    // 重置assessment表的task_id和考核状态
                    LambdaUpdateWrapper<Assessment> updateWrapper = new LambdaUpdateWrapper<>();
                    updateWrapper.eq(Assessment::getId, assessmentId)
                            .set(Assessment::getTaskId, 0L)
                            .set(Assessment::getAchievement, false)//取消参与达成度计算
                            .set(Assessment::getAssessmentStatus, 1) // 重置考核状态为编辑中
                            .set(Assessment::getModifyTime, LocalDateTime.now());
                    this.update(updateWrapper);
                } else {
                    log.info("部分撤销考核发布成功，仍有其他教学任务关联，保留assessment_content记录，assessmentId: {}, remainingTaskIds: {}",
                            assessmentId, remainingTaskIds);
                }
            }
            return true;
        } catch (Exception e) {
            log.error("撤销发布失败，assessmentId: {}, taskIds: {}");
            throw new RuntimeException("撤销发布失败：" + e.getMessage(), e);
        }
    }

    @Override
    public AssessmentPublishVO getPublishStatus(Long assessmentId) {
        Assessment assessment = this.getById(assessmentId);
        if (assessment == null) {
            return null;
        }

        AssessmentPublishVO result = new AssessmentPublishVO();
        result.setAssessmentId(assessmentId);
        result.setAssessmentName(assessment.getAssessmentName());

        if (assessment.getTaskId() != null && assessment.getTaskId() == -1) {
            // 全部发布
            result.setPublishToAll(true);
            result.setPublishStatus(0);
            result.setPublishMessage("已发布给全部匹配的教学任务");

            // 查询匹配的教学任务数量
            LambdaQueryWrapper<TaskWork> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(TaskWork::getCourseId, assessment.getCourseId())
                    .eq(TaskWork::getTaskYear, assessment.getAssessmentYear())
                    .eq(TaskWork::getTaskTerm, assessment.getAssessmentTerm())
                    .eq(TaskWork::getStatus, 0);
            int taskCount = (int) taskWorkService.count(wrapper);
            result.setPublishedTaskCount(taskCount);
        } else {
            // 部分发布或未发布
            List<Long> publishedTaskIds = assessmentTaskService.getTaskIdsByAssessmentId(assessmentId);
            result.setPublishToAll(false);
            result.setPublishedTaskIds(publishedTaskIds);
            result.setPublishedTaskCount(publishedTaskIds.size());

            if (publishedTaskIds.isEmpty()) {
                result.setPublishStatus(2);
                result.setPublishMessage("未发布");
            } else {
                result.setPublishStatus(0);
                result.setPublishMessage(String.format("已发布给 %d 个教学任务", publishedTaskIds.size()));
            }
        }

        return result;
    }

}
