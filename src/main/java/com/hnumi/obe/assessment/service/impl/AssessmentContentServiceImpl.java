package com.hnumi.obe.assessment.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hnumi.obe.assessment.dto.DetailEntryConfigDTO;
import com.hnumi.obe.assessment.dto.DirectEntryConfigDTO;
import com.hnumi.obe.assessment.entity.Assessment;
import com.hnumi.obe.assessment.entity.AssessmentContent;
import com.hnumi.obe.assessment.mapper.*;
import com.hnumi.obe.assessment.entity.RepositoryQuestion;
import com.hnumi.obe.assessment.entity.RepositoryAnswer;
import com.hnumi.obe.assessment.mapstruct.RepositoryAnswerConvert;
import com.hnumi.obe.assessment.mapstruct.RepositoryQuestionConvert;
import com.hnumi.obe.assessment.service.*;
import com.hnumi.obe.assessment.vo.AssessmentContentDetailVO;
import com.hnumi.obe.assessment.vo.DirectEntryConfig;
import com.hnumi.obe.common.util.JSONUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import static com.hnumi.obe.common.exception.ServiceExceptionUtil.exception;

@Service
@Slf4j
public class AssessmentContentServiceImpl extends ServiceImpl<AssessmentContentMapper, AssessmentContent> implements IAssessmentContentService {
    @Autowired
    private AssessmentContentMapper assessmentContentMapper;
    @Autowired
    private IRepositoryQuestionService repositoryQuestionService;
    @Autowired
    private IRepositoryAnswerService repositoryAnswerService;
    @Autowired
    @Lazy
    private IAssessmentService assessmentService;
    @Autowired
    private IAssessmentScoreService assessmentScoreService;
    @Autowired
    private IAssessmentTaskService assessmentTaskService;

    @Autowired
    private AssessmentMapper assessmentMapper;
    //private static  final JSONUtil<DirectEntryConfig> jsonUtil = new JSONUtil<>();
    @Override
    public List<Long> getQuestionIdsByAssessmentId(Long assessmentId) {
        return assessmentContentMapper.selectQuestionIdsByAssessmentId(assessmentId);
    }

    @Override
    public AssessmentContentDetailVO getAssessmentContentDetail(Long assessmentId) {
        AssessmentContentDetailVO detailVO = new AssessmentContentDetailVO();
        detailVO.setAssessmentId(assessmentId);
        // 1. 查询所有题目ID
        List<Long> questionIds = assessmentContentMapper.selectQuestionIdsByAssessmentId(assessmentId);
        if (questionIds == null || questionIds.isEmpty()) {
            detailVO.setQuestions(List.of());
            return detailVO;
        }
        // 2. 查询所有题目详情
        List<RepositoryQuestion> questions = repositoryQuestionService.getQuestionsByIds(questionIds);
        List<AssessmentContentDetailVO.QuestionDetailVO> questionDetailVOList = questions.stream().map(q -> {
            AssessmentContentDetailVO.QuestionDetailVO qvo = new AssessmentContentDetailVO.QuestionDetailVO();
            //BeanUtils.copyProperties(q, qvo);
            qvo = RepositoryQuestionConvert.INSTANCE.toQuestionDetailVO(q);
            // 3. 查询每个题目的所有答案
            List<RepositoryAnswer> answers = repositoryAnswerService.getAnswersByQuestionId(q.getId());
            List<AssessmentContentDetailVO.AnswerDetailVO> answerVOList = answers.stream().map(a -> {
                AssessmentContentDetailVO.AnswerDetailVO avo = new AssessmentContentDetailVO.AnswerDetailVO();
                //BeanUtils.copyProperties(a, avo);
                avo = RepositoryAnswerConvert.INSTANCE.toAnswerDetailVO(a);
                return avo;
            }).collect(Collectors.toList());
            qvo.setAnswers(answerVOList);
            return qvo;
        }).collect(Collectors.toList());
        detailVO.setQuestions(questionDetailVOList);
        log.info("获取考核内容详情: assessmentId={}, questions={}", assessmentId, questionDetailVOList);
        return detailVO;
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveDirectEntryConfig(DetailEntryConfigDTO dto) {
        log.info("保存考核详情配置: {}", dto);
        if (dto == null || dto.getAssessmentId() == null || dto.getQuestions() == null) return false;
        //修改assessnent的状态为编辑中，修改assessment的score_type字段为1，不删除，直接update
        // 1. 查找原有的考核内容
        Assessment assessment = new Assessment();
        // 2. 更新考核状态和分数类型
        assessment.setId(dto.getAssessmentId());
        assessment.setAssessmentStatus(1); // 设置为编辑中状态
        assessment.setScoreType(1); // 设置为手动评分
        // 3. 使用Lamda更新数据库
        boolean isUpdate = assessmentService.updateById(assessment);
        if (!isUpdate) {
            return false; // 更新失败
        }

       List<RepositoryQuestion> questionEntities = new ArrayList<>();
        List<RepositoryQuestion> updateQuestionEntities = new ArrayList<>();
        List<RepositoryAnswer> answerEntities = new ArrayList<>();
        List<RepositoryAnswer> updateAnswerEntities = new ArrayList<>();
        List<AssessmentContent> contentEntities = new ArrayList<>();

        // 1. 分类题目
        dto.getQuestions().forEach(qdto -> {
            RepositoryQuestion question = RepositoryQuestionConvert.INSTANCE.toEntity(qdto);
            question.setCourseId(dto.getCourseId());
            if (qdto.getQuestionId() == null) {
                questionEntities.add(question);
            } else {
                question.setId(qdto.getQuestionId());
                updateQuestionEntities.add(question);
            }
        });

        // 2. 批量插入新题目，获取主键
        repositoryQuestionService.saveBatch(questionEntities);

        // 3. 处理答案和内容
        int newIndex = 0;
        for (DetailEntryConfigDTO.QuestionDetailDTO qdto : dto.getQuestions()) {
            boolean isInsertQuestion = qdto.getQuestionId() == null;
            Long questionId = isInsertQuestion
                ? questionEntities.get(newIndex++).getId()
                : qdto.getQuestionId();

            if (qdto.getAnswers() != null) {
                for (DetailEntryConfigDTO.AnswerDetailDTO adto : qdto.getAnswers()) {
                    RepositoryAnswer answer = RepositoryAnswerConvert.INSTANCE.toEntity(adto);
                    answer.setQuestionId(questionId);
                    if (isInsertQuestion) {
                        answerEntities.add(answer);
                    } else {
                        answer.setId(adto.getAnswerId());
                        updateAnswerEntities.add(answer);
                    }
                }
            }
            if (isInsertQuestion) {
                AssessmentContent content = new AssessmentContent();
                content.setAssessmentId(dto.getAssessmentId());
                content.setQuestionId(questionId);
                contentEntities.add(content);
            }
        }

        repositoryQuestionService.updateBatchById(updateQuestionEntities);
        repositoryAnswerService.saveBatch(answerEntities);
        repositoryAnswerService.updateBatchById(updateAnswerEntities);
        this.saveBatch(contentEntities);
        return true;
    }


    @Override
    public boolean updateDirectEntryConfig(DirectEntryConfigDTO directEntryConfigDTO) {
        if (directEntryConfigDTO == null || directEntryConfigDTO.getAssessmentId() == null) {
            return false;
        }

        return assessmentService.update(
                new LambdaUpdateWrapper<Assessment>()
                        .eq(Assessment::getId, directEntryConfigDTO.getAssessmentId())
                        .set(Assessment::getAssessmentDetail, JSONUtil.convertToJson(directEntryConfigDTO.getConfigs()))
                        .set(Assessment::getScoreType, 0)
                        .set(Assessment::getAssessmentStatus, 1)
        );
    }

    @Override
    public DirectEntryConfigDTO getDirectEntryConfig(Long assessmentId) {
        if (assessmentId == null) {
            return null;
        }
        Assessment assessment = assessmentService.getById(assessmentId);
        if (assessment == null || assessment.getAssessmentDetail() == null) {
            return null;
        }
        List<DirectEntryConfig> config = JSONUtil.parseJson(assessment.getAssessmentDetail(),DirectEntryConfig.class);
        if (config == null) {
            return null;
        }
        DirectEntryConfigDTO dto = new DirectEntryConfigDTO();
        dto.setAssessmentId(assessmentId);
        dto.setConfigs(config);

        return dto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteByAssessmentId(Long assessmentId) {
        if (assessmentId == null) {
            log.warn("删除assessment_content记录失败：assessmentId为空");
            return 0;
        }

        try {
            // 物理删除assessment_content表中的记录
            LambdaQueryWrapper<AssessmentContent> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(AssessmentContent::getAssessmentId, assessmentId);

            // 先统计要删除的记录数
            int deleteCount = (int) this.count(queryWrapper);

            if (deleteCount == 0) {
                log.info("没有找到需要删除的assessment_content记录，assessmentId: {}", assessmentId);
                return 0;
            }

            // 执行物理删除
            boolean result = this.remove(queryWrapper);

            if (result) {
                log.info("成功物理删除assessment_content记录，assessmentId: {}, 删除记录数: {}", assessmentId, deleteCount);
                return deleteCount;
            } else {
                log.warn("删除assessment_content记录失败，assessmentId: {}", assessmentId);
                return 0;
            }
        } catch (Exception e) {
            log.error("删除assessment_content记录时发生异常，assessmentId: {}", assessmentId, e);
            throw new RuntimeException("删除考核内容失败：" + e.getMessage(), e);
        }
    }




    //================== 考核物理删除相关方法实现 ==================

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean physicalDeleteAssessment(Long assessmentId) {
        if (assessmentId == null) {
            log.warn("物理删除考核失败：assessmentId为空");
            return false;
        }

        // 获取考核信息用于日志记录
        Assessment assessment = assessmentService.getById(assessmentId);
        if (assessment == null) {
            log.warn("物理删除考核失败：考核不存在，assessmentId: {}", assessmentId);
            return false;
        }

        try {
            log.info("开始物理删除考核及其关联数据，assessmentId: {}, assessmentName: {}",
                    assessmentId, assessment.getAssessmentName());

            // 1. 删除成绩相关记录（assessment_score、assessment_score_target、assessment_score_detail）
            int deletedScoreCount = assessmentScoreService.physicalDeleteAllScoresByAssessmentId(assessmentId);
            log.info("删除成绩记录完成，assessmentId: {}, 删除数量: {}", assessmentId, deletedScoreCount);

            // 2. 删除考核内容记录（assessment_content）
            int deletedContentCount = this.deleteByAssessmentId(assessmentId);
            log.info("删除考核内容记录完成，assessmentId: {}, 删除数量: {}", assessmentId, deletedContentCount);

            // 3. 删除考核任务关联记录（assessment_task）
            int deletedTaskCount = assessmentTaskService.physicalDeleteByAssessmentId(assessmentId);
            log.info("删除考核任务关联记录完成，assessmentId: {}, 删除数量: {}", assessmentId, deletedTaskCount);

            // 4. 最后删除考核主记录（assessment）
            //this.removeById(assessmentId);
            int deleteAssessmentCount = assessmentMapper.deleteById(assessmentId);

            log.info("成功物理删除考核及其所有关联数据，assessmentId: {}, assessmentName: {}, " +
                            "删除成绩记录: {}, 删除内容记录: {}, 删除任务关联: {},删除考核数量: {}",
                    assessmentId, assessment.getAssessmentName(), deletedScoreCount, deletedContentCount, deletedTaskCount,deleteAssessmentCount);

            return true;

        } catch (Exception e) {
            log.error("物理删除考核时发生异常，assessmentId: {}, assessmentName: {}",
                    assessmentId, assessment.getAssessmentName(), e);
            throw new RuntimeException("删除考核失败：" + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AssessmentDeleteResult batchPhysicalDeleteAssessments(List<Long> assessmentIds) {
        if (CollectionUtils.isEmpty(assessmentIds)) {
            return new AssessmentDeleteResult(0, 0, 0,
                    Arrays.asList("考核ID列表为空"), new ArrayList<>());
        }

        int totalCount = assessmentIds.size();
        int successCount = 0;
        int failCount = 0;
        List<String> errorMessages = new ArrayList<>();
        List<AssessmentDeleteDetail> deleteDetails = new ArrayList<>();

        log.info("开始批量物理删除考核，总数量: {}, assessmentIds: {}", totalCount, assessmentIds);

        for (Long assessmentId : assessmentIds) {
            try {
                // 获取考核信息
                Assessment assessment = assessmentService.getById(assessmentId);
                String assessmentName = assessment != null ? assessment.getAssessmentName() : "未知";

                if (assessment == null) {
                    failCount++;
                    String errorMsg = String.format("考核不存在，ID: %d", assessmentId);
                    errorMessages.add(errorMsg);
                    deleteDetails.add(new AssessmentDeleteDetail(assessmentId, assessmentName, false, errorMsg, 0, 0, 0));
                    continue;
                }

                // 执行删除操作
                int deletedScoreCount = assessmentScoreService.physicalDeleteAllScoresByAssessmentId(assessmentId);
                int deletedContentCount = deleteByAssessmentId(assessmentId);
                int deletedTaskCount = assessmentTaskService.physicalDeleteByAssessmentId(assessmentId);

                boolean assessmentDeleted = assessmentService.removeById(assessmentId);
                if (!assessmentDeleted) {
                    throw exception(-1,"删除考核主记录失败");
                }

                successCount++;
                deleteDetails.add(new AssessmentDeleteDetail(assessmentId, assessmentName, true, null,
                        deletedTaskCount, deletedContentCount, deletedScoreCount));

                log.info("成功删除考核，assessmentId: {}, assessmentName: {}", assessmentId, assessmentName);

            } catch (Exception e) {
                failCount++;
                String errorMsg = String.format("删除考核失败，ID: %d, 错误: %s", assessmentId, e.getMessage());
                errorMessages.add(errorMsg);

                Assessment assessment = assessmentService.getById(assessmentId);
                String assessmentName = assessment != null ? assessment.getAssessmentName() : "未知";
                deleteDetails.add(new AssessmentDeleteDetail(assessmentId, assessmentName, false, e.getMessage(), 0, 0, 0));

                log.error("删除考核失败，assessmentId: {}", assessmentId, e);
            }
        }

        log.info("批量物理删除考核完成，总数: {}, 成功: {}, 失败: {}", totalCount, successCount, failCount);

        return new AssessmentDeleteResult(totalCount, successCount, failCount, errorMessages, deleteDetails);
    }
}

