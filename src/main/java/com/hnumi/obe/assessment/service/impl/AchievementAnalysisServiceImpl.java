package com.hnumi.obe.assessment.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.hnumi.obe.assessment.mapper.AchievementAnalysisMapper;
import com.hnumi.obe.assessment.service.IAchievementAnalysisService;
import com.hnumi.obe.assessment.vo.AssessmentTargetScoreVO;
import com.hnumi.obe.assessment.vo.CourseTargetScoreVO;
import com.hnumi.obe.common.util.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AchievementAnalysisServiceImpl implements IAchievementAnalysisService {

    @Autowired
    private AchievementAnalysisMapper achievementAnalysisMapper;

    @Override
    public CourseTargetScoreVO getTargetAchievementAnalysis(String courseCode) {
        // 优化: 一次查询获取所有数据
        List<AssessmentTargetScoreVO> assessmentScores = achievementAnalysisMapper.getAssessmentScoresByCode(courseCode);
        if (CollectionUtils.isEmpty(assessmentScores)) {
            return new CourseTargetScoreVO();
        }

        CourseTargetScoreVO result = new CourseTargetScoreVO();
        result.setCourseCode(courseCode);
        result.setRadarChartData(new CourseTargetScoreVO.RadarChartData());
        result.setBarChartData(new CourseTargetScoreVO.BarChartData());

        // 获取课程配置
        Long courseId = assessmentScores.get(0).getCourseId();
        CourseTargetScoreVO.CourseConfig courseConfig = achievementAnalysisMapper.getCourseConfig(courseId);

        // 计算加权和非加权达成度
        Map<String, Double> weightedScores = calculateWeightedScores(assessmentScores, courseConfig);
        Map<String, Double> unweightedScores = calculateUnweightedScores(assessmentScores);

        result.getRadarChartData().setCourseTargetAchievement(weightedScores);
        result.getBarChartData().setCourseTargetAchievement(unweightedScores);

        return result;
    }

    @Override
    public List<CourseTargetScoreVO> getTargetAchievementTrend(String courseCode) {
        // 优化: 使用窗口函数一次性获取历史趋势数据
        List<CourseTargetScoreVO> historicalData = achievementAnalysisMapper.getHistoricalAchievement(courseCode);

        // 按年份和学期分组处理数据
        Map<Integer, List<CourseTargetScoreVO>> yearGrouped = historicalData.stream()
                .collect(Collectors.groupingBy(CourseTargetScoreVO::getAssessmentYear));

        List<CourseTargetScoreVO> results = new ArrayList<>();
        for (Map.Entry<Integer, List<CourseTargetScoreVO>> entry : yearGrouped.entrySet()) {
            CourseTargetScoreVO yearScore = new CourseTargetScoreVO();
            yearScore.setAssessmentYear(entry.getKey());

            // 设置趋势图数据
            CourseTargetScoreVO.TrendChartData trendData = new CourseTargetScoreVO.TrendChartData();
            Map<String, Map<Integer, Double>> trends = entry.getValue().stream()
                    .collect(Collectors.groupingBy(
                            score -> score.getCourseTargetNo().toString(),
                            Collectors.groupingBy(
                                    CourseTargetScoreVO::getAssessmentTerm,
                                    Collectors.averagingDouble(score -> score.getCumulativeScore().doubleValue())
                            )
                    ));

            trendData.setCourseTargetTrends(trends);
            trendData.setYears(new ArrayList<>(yearGrouped.keySet()));
            yearScore.setTrendChartData(trendData);

            results.add(yearScore);
        }

        return results;
    }

    @Override
    public CourseTargetScoreVO calculateCourseAchievement(Long courseId, boolean useWeights) {
        // 1. 获取课程配置
        CourseTargetScoreVO.CourseConfig courseConfig = achievementAnalysisMapper.getCourseConfig(courseId);
        if (courseConfig == null) {
            return new CourseTargetScoreVO();
        }

        // 2. 获取该课程的所有考核成绩
        List<AssessmentTargetScoreVO> assessmentScores = achievementAnalysisMapper.getAssessmentScoresByCode(courseConfig.getCourseCode());
        if (CollectionUtils.isEmpty(assessmentScores)) {
            return new CourseTargetScoreVO();
        }

        // 3. 创建返回结果
        CourseTargetScoreVO result = new CourseTargetScoreVO();
        result.setCourseId(courseId);
        result.setRadarChartData(new CourseTargetScoreVO.RadarChartData());
        result.setBarChartData(new CourseTargetScoreVO.BarChartData());

        // 4. 根据是否使用权重计算达成度
        if (useWeights) {
            Map<String, Double> weightedScores = calculateWeightedScores(assessmentScores, courseConfig);
            result.getRadarChartData().setCourseTargetAchievement(weightedScores);
            result.getBarChartData().setCourseTargetAchievement(weightedScores);
        } else {
            Map<String, Double> unweightedScores = calculateUnweightedScores(assessmentScores);
            result.getRadarChartData().setCourseTargetAchievement(unweightedScores);
            result.getBarChartData().setCourseTargetAchievement(unweightedScores);
        }

        return result;
    }

    private Map<String, Double> calculateWeightedScores(
            List<AssessmentTargetScoreVO> scores,
            CourseTargetScoreVO.CourseConfig config) {

        // 修改JSON解析方式
        Map<Integer, BigDecimal> methodWeights = JSONUtil.parseObject(
                config.getAssessmentWeight(),
                new TypeReference<Map<Integer, BigDecimal>>() {}
        );

        // 按课程目标分组计算加权平均分
        return scores.stream()
                .collect(Collectors.groupingBy(
                        score -> score.getCourseTargetNo().toString(),
                        Collectors.collectingAndThen(
                                Collectors.toList(),
                                list -> calculateTargetWeightedScore(list, methodWeights)
                        )
                ));
    }

    private double calculateTargetWeightedScore(
            List<AssessmentTargetScoreVO> scores,
            Map<Integer, BigDecimal> weights) {

        BigDecimal weightedSum = BigDecimal.ZERO;
        BigDecimal weightSum = BigDecimal.ZERO;

        for (AssessmentTargetScoreVO score : scores) {
            BigDecimal weight = weights.getOrDefault(
                    score.getAssessmentMethod(),
                    BigDecimal.ONE
            );
            weightedSum = weightedSum.add(
                    BigDecimal.valueOf(score.getAverageScore())
                            .multiply(weight)
            );
            weightSum = weightSum.add(weight);
        }

        if (weightSum.compareTo(BigDecimal.ZERO) > 0) {
            return weightedSum.divide(weightSum, 2, RoundingMode.HALF_UP)
                    .doubleValue();
        }
        return 0.0;
    }

    private Map<String, Double> calculateUnweightedScores(List<AssessmentTargetScoreVO> scores) {
        return scores.stream()
                .collect(Collectors.groupingBy(
                        score -> score.getCourseTargetNo().toString(),
                        Collectors.averagingDouble(AssessmentTargetScoreVO::getAverageScore)
                ));
    }
}
