package com.hnumi.obe.assessment.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hnumi.obe.assessment.dto.AssessmentDTO;
import com.hnumi.obe.assessment.dto.AssessmentPublishDTO;
import com.hnumi.obe.assessment.dto.AssessmentPublishQueryDTO;
import com.hnumi.obe.assessment.dto.AssessmentQueryDTO;
import com.hnumi.obe.assessment.dto.DirectEntryConfigDTO;
import com.hnumi.obe.assessment.entity.Assessment;
import com.hnumi.obe.assessment.vo.AssessmentPublishVO;
import com.hnumi.obe.assessment.vo.AssessmentVO;
import com.hnumi.obe.assessment.vo.PublishableTaskVO;

import java.util.List;

/**
 * 教学任务考核表 服务类
 */
public interface IAssessmentService extends IService<Assessment> {

    /**
     * 分页查询考核列表
     *
     * @param queryDTO 查询条件
     * @return 考核列表
     */
    IPage<AssessmentVO> pageAssessment(AssessmentQueryDTO queryDTO);

    /**
     * 根据ID查询考核详情
     *
     * @param id 考核ID
     * @return 考核详情
     */
    AssessmentVO getAssessmentById(Long id);

    /**
     * 新增考核
     *
     * @param assessmentDTO 考核信息
     * @return 是否成功
     */
    AssessmentVO saveAssessment(AssessmentDTO assessmentDTO);

    /**
     * 更新考核
     *
     * @param assessmentDTO 考核信息
     * @return 是否成功
     */
    boolean updateAssessment(AssessmentDTO assessmentDTO);

    /**
     * 删除考核
     *
     * @param id 考核ID
     * @return 是否成功
     */
    boolean deleteAssessment(Long id);

    //boolean updateDirectEntryConfig(DirectEntryConfigDTO directEntryConfigDTO);

    /**
     * 获取直接录入配置
     *
     * @param assessmentId 考核ID
     * @return 直接录入配置
     */
    //DirectEntryConfigDTO getDirectEntryConfig(Long assessmentId);


    //================== 查询相关方法 ==================
    /**
     * 根据教学任务ID查询考核列表
     *
     * @param taskId 教学任务ID
     * @return 考核列表
     */
    List<AssessmentVO> getAssessmentByTaskId(Long taskId);

    /**
     * 根据课程ID查询考核列表
     *
     * @param courseId 课程ID
     * @return 考核列表
     */
    List<AssessmentVO> getAssessmentByCourseId(Long courseId);

    /**
     * 根据课程Code查询考核列表
     *
     * @param courseCode 课程ID
     * @return 考核列表
     */
    List<AssessmentVO> getAssessmentByCourseCode(String courseCode);



    //================== 考核发布相关方法 ==================
    /**
     * 查询可发布的教学任务列表
     *
     * @param queryDTO 查询条件
     * @return 可发布的教学任务列表
     */
    List<PublishableTaskVO> getPublishableTasks(AssessmentPublishQueryDTO queryDTO);

    /**
     * 发布考核到教学任务
     *
     * @param publishDTO 发布参数
     * @return 发布结果
     */
    AssessmentPublishVO publishAssessment(AssessmentPublishDTO publishDTO);

    /**
     * 撤销考核发布
     *
     * @param assessmentId 考核ID
     * @param taskIds 教学任务ID列表（为空时撤销所有发布）
     * @return 撤销结果
     */
    boolean unpublishAssessment(Long assessmentId, List<Long> taskIds);

    /**
     * 查询考核的发布状态
     *
     * @param assessmentId 考核ID
     * @return 发布状态信息
     */
    AssessmentPublishVO getPublishStatus(Long assessmentId);

}
