package com.hnumi.obe.system.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 菜单选项视图对象
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Data
public class MenuOptionVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 菜单ID
     */
    private Long id;

    /**
     * 父菜单ID
     */
    private Long pid;

    /**
     * 菜单名称
     */
    private String title;

    /**
     * 菜单图标
     */
    private String icon;

    /**
     * 菜单排序号
     */
    private Integer sort;

    /**
     * 0表示目录，1表示菜单，2表示按钮
     */
    private Integer type;

    /**
     * 子菜单列表
     */
    private List<MenuOptionVO> children;
}
