package com.hnumi.obe.system.vo;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 登录日志视图对象
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Data
public class LoginLogVO {
    
    /**
     * 日志ID
     */
    private Long id;
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 登录IP
     */
    private String ip;
    
    /**
     * 登录地点
     */
    private String location;
    
    /**
     * 浏览器类型
     */
    private String browser;
    
    /**
     * 操作系统
     */
    private String os;
    
    /**
     * 登录状态（0成功 1失败）
     */
    private Integer status;
    
    /**
     * 提示消息
     */
    private String msg;
    
    /**
     * 登录时间
     */
    private LocalDateTime loginTime;
} 