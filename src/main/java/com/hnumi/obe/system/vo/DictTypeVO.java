package com.hnumi.obe.system.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * 字典类型视图对象
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Data
public class DictTypeVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 字典类型ID
     */
    private Long id;

    /**
     * 租户ID
     */
    private Long tid;

    /**
     * 字典类型名称
     */
    private String name;

    /**
     * 字典类型编码
     */
    private String type;

    /**
     * 字典类型状态
     */
    private Integer status;

    /**
     * 备注
     */
    private String remark;
}
