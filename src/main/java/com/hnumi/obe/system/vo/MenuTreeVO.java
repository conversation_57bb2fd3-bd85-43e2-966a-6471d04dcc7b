package com.hnumi.obe.system.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 菜单树视图对象
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Data
public class MenuTreeVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 菜单ID
     */
    private Long id;

    /**
     * 父菜单ID
     */
    private Long pid;

    /**
     * 路由地址
     */
    private String path;

    /**
     * 组件名：唯一
     */
    private String name;

    /**
     * 组件
     */
    private String component;

    /**
     * 重定向到的子路由
     */
    private String redirect;
    /**
     * 0表示目录，1表示菜单，2表示按钮
     */
    private Integer type;

    /**
     * 菜单元数据
     */
    private MenuTreeMetaVO meta;

    /**
     * 子菜单列表
     */
    private List<MenuTreeVO> children;
}
