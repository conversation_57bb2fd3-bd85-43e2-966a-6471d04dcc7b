package com.hnumi.obe.system.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * 字典数据视图对象
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Data
public class DictDataVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 字典数据ID
     */
    private Long id;

    /**
     * 字典标签
     */
    private String label;

    /**
     * 字典键值
     */
    private String value;

    /**
     * 字典类型ID
     */
    private Long typeId;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * CSS样式
     */
    private String cssClass;

    /**
     * 表格回显样式
     */
    private String listClass;

    /**
     * 是否默认：1表示是，0表示否
     */
    private Integer defaul;

    /**
     * 状态：0表示正常，1表示停用
     */
    private Integer status;

    /**
     * 备注
     */
    private String remark;
}
