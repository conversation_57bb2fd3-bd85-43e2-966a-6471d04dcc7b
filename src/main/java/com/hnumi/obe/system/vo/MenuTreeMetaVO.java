package com.hnumi.obe.system.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * 菜单树元数据视图对象
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Data
public class MenuTreeMetaVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 菜单名称
     */
    private String title;

    /**
     * 菜单图标
     */
    private String icon;

    private String code;

    /**
     * 是否默认展开
     */
    private Boolean expanded;

    /**
     * 菜单排序号
     */
    private Integer orderNo;

    /**
     * 是否隐藏：默认不隐藏
     */
    private Boolean hidden;

    /**
     * 是否隐藏面包屑
     */
    private Boolean hiddenBreadcrumb;

    /**
     * 是否只显示一级菜单
     * 当多级菜单只有一个子节点时，是否只显示一级菜单
     * 注意：此配置需要设置在父节点上
     */
    private Boolean single;

    /**
     * iframe地址
     */
    private String frameSrc;

    /**
     * iframe是否新窗口打开
     */
    private Boolean frameBlank;

    /**
     * 是否开启keep-alive
     */
    private Boolean keepAlive;

    /**
     * 是否可关闭多标签页，默认false
     */
    private Boolean noClosable;

    /**
     * 是否显示列，默认true
     */
    private Boolean noColumn;

    /**
     * 徽标内容
     */
    private String badge;

    /**
     * 从字典表获取数据进行展示，对应字典表中value
     */
    private String target;

    /**
     * 激活的菜单
     */
    private String activeMenu;
}