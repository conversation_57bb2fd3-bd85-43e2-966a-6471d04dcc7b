package com.hnumi.obe.system.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 菜单信息视图对象
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Data
public class MenuVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 菜单ID
     */
    private Long id;

    /**
     * 父菜单ID
     */
    private Long pid;

    /**
     * 排序
     */
    private Long sort;

    /**
     * 菜单名称
     */
    private String title;

    /**
     * 路由地址
     */
    private String path;

    /**
     * 菜单图标
     */
    private String icon;

    /**
     * 权限标识
     */
    private String code;

    /**
     * 菜单类型：0表示目录，1表示菜单，2表示按钮
     */
    private String type;

    /**
     * 是否隐藏
     */
    private Boolean hidden;

    /**
     * 是否缓存
     */
    private Boolean cache;

    /**
     * 子菜单列表
     */
    private List<MenuVO> children;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDateTime createTime;
}
