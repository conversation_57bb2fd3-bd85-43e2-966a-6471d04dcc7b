package com.hnumi.obe.system.vo;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 操作日志视图对象
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Data
public class OperateLogVO {
    
    /**
     * 日志ID
     */
    private Long id;
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 操作类型
     */
    private String operation;
    
    /**
     * 请求方法
     */
    private String method;
    
    /**
     * 请求参数
     */
    private String params;
    
    /**
     * 执行时长(毫秒)
     */
    private Long time;
    
    /**
     * IP地址
     */
    private String ip;
    
    /**
     * 操作地点
     */
    private String location;
    
    /**
     * 操作状态（0正常 1异常）
     */
    private Integer status;
    
    /**
     * 错误消息
     */
    private String errorMsg;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
} 