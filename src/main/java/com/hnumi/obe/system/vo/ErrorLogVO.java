package com.hnumi.obe.system.vo;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 错误日志视图对象
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Data
public class ErrorLogVO {
    
    /**
     * 日志ID
     */
    private Long id;
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 错误类型
     */
    private String errorType;
    
    /**
     * 错误消息
     */
    private String errorMsg;
    
    /**
     * 请求方法
     */
    private String method;
    
    /**
     * 请求参数
     */
    private String params;
    
    /**
     * 请求IP
     */
    private String ip;
    
    /**
     * 请求地址
     */
    private String url;
    
    /**
     * 错误堆栈
     */
    private String stackTrace;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
} 