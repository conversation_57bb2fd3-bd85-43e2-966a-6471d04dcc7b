package com.hnumi.obe.system.vo;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

/**
 * 用户信息视图对象
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Data
public class UserVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    private Long id;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 用户名：用户昵称
     */
    private String username;

    /**
     * 真实姓名
     */
    private String name;

    /**
     * 性别：0表示保密，1表示男，2表示女
     */
    private Integer gender;

    /**
     * 出生日期
     */
    private LocalDate birthday;

    /**
     * 个性签名
     */
    private String introduction;

    /**
     * 头像地址
     */
    private String avatar;

    /**
     * 用户状态：0表示正常，99表示拉黑
     */
    private Integer status;

    /**
     * 用户角色列表
     */
    private List<RoleVO> roles;
}
