package com.hnumi.obe.system.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 登录用户信息视图对象
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Data
public class LoginUserVO implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;
    /**
     * 用户名
     */
    private String username;

    /**
     * 头像地址
     */
    private String avatar;

    /**
     * 角色列表，例如：Admin,Student
     */
    private List<String> roles = new ArrayList<>();

    /**
     * 权限列表，例如：user:list,user:add
     */
    private List<String> permissions = new ArrayList<>();
}
