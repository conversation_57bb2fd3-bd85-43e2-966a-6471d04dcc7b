package com.hnumi.obe.system.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hnumi.obe.system.vo.MenuOptionVO;
import com.hnumi.obe.system.vo.MenuTreeVO;
import com.hnumi.obe.system.vo.MenuVO;
import com.hnumi.obe.system.entity.Menu;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* 菜单表 Mapper 接口
*/
@Mapper
public interface MenuMapper extends BaseMapper<Menu> {
    List<MenuTreeVO> listAll();

    List<MenuTreeVO> listRoute(@Param("id") Long uid);

    List<String> listPermission(@Param("uid") Object uid);
}
