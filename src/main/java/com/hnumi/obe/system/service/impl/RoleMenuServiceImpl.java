package com.hnumi.obe.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hnumi.obe.common.entity.ResultCode;
import com.hnumi.obe.system.dto.RoleMenuUpdateDTO;
import com.hnumi.obe.system.entity.RoleMenu;
import com.hnumi.obe.system.mapper.RoleMenuMapper;
import com.hnumi.obe.system.service.IRoleMenuService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

import static com.hnumi.obe.common.exception.ServiceExceptionUtil.exception;

/**
 * 角色菜单关联表 服务实现类
 */
@Service
public class RoleMenuServiceImpl extends ServiceImpl<RoleMenuMapper, RoleMenu> implements IRoleMenuService {

    @Transactional(isolation = Isolation.SERIALIZABLE)
    @Override
    public void updateMenu(RoleMenuUpdateDTO data) {
        try {
            final List<RoleMenu> roleMenus = data.getIds().stream().map(e -> {
                final RoleMenu roleMenu = new RoleMenu();
                roleMenu.setMenuId(e);
                roleMenu.setRoleId(data.getRid());
                return roleMenu;
            }).collect(Collectors.toList());
            LambdaQueryWrapper<RoleMenu> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(RoleMenu::getRoleId, data.getRid());
            remove(wrapper);
            saveBatch(roleMenus);
        } catch (Exception e) {
            throw exception(ResultCode.ROLE_MENU_UPDATE_ERROR);
        }
    }
}
