package com.hnumi.obe.system.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hnumi.obe.common.entity.LoginSessionVO;
import com.hnumi.obe.system.dto.BatchDeleteDTO;
import com.hnumi.obe.system.dto.LoginUserDTO;
import com.hnumi.obe.system.dto.RegisterUserDTO;
import com.hnumi.obe.system.dto.UserImportDTO;
import com.hnumi.obe.system.dto.UserQueryDTO;
import com.hnumi.obe.system.dto.UserRoleAssignDTO;
import com.hnumi.obe.system.dto.UserUpdateSafeDTO;
import com.hnumi.obe.system.entity.BaseUser;
import com.hnumi.obe.system.vo.UserVO;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 基本用户信息表
 */
public interface IBaseUserService extends IService<BaseUser> {

    /**
     * 用户登录
     *
     * @param loginUserDTO 登录信息
     * @return 用户信息
     */
    BaseUser login(LoginUserDTO loginUserDTO);

    /**
     * 用户注册
     *
     * @param registerUserDTO 注册信息
     * @return 用户信息
     */
    BaseUser register(RegisterUserDTO registerUserDTO);

    /**
     * 删除用户
     *
     * @param id 用户ID
     * @return 是否成功
     */
    boolean deleteById(Long id);

    /**
     * 封禁/解封用户
     *
     * @param id 用户ID
     * @return 是否成功
     */
    boolean block(Long id);

    /**
     * 更新用户安全信息
     *
     * @param id 用户ID
     * @param userUpdateSafeDTO 更新信息
     * @return 是否成功
     */
    boolean updateSafeInfo(Long id, UserUpdateSafeDTO userUpdateSafeDTO);

    /**
     * 分页查询用户列表
     *
     * @param query 查询条件
     * @return 用户列表
     */
    IPage<UserVO> listUsers(UserQueryDTO query);

    /**
     * 批量导入用户
     *
     * @param users 用户列表
     * @return 返回导入结果
     */
    List<UserImportDTO> importUsers(List<UserImportDTO> users, boolean importPartial);

    /**
     * 分配用户角色
     *
     * @param userRoleAssignDTO 角色分配信息
     * @return 是否成功
     */
    boolean assignRoles(UserRoleAssignDTO userRoleAssignDTO);

    /**
     * 重置用户密码
     *
     * @param id 用户ID
     * @return 是否成功
     */
    boolean resetPassword(Long id);

    /**
     * 批量删除用户
     *
     * @param batchDeleteDTO 批量删除信息
     * @return 是否成功
     */
    boolean batchDelete(BatchDeleteDTO batchDeleteDTO);

    /**
     * 获取用户详情（包含角色信息）
     *
     * @param id 用户ID
     * @return 用户详情
     */
    UserVO getUserDetail(Long id);


    /**
     * 根据用户ID构建登录会话信息
     * 自动判断用户类型（教师/学生/系统用户）并构建相应的会话信息
     *
     * @param userId 用户ID
     * @return 登录会话信息，如果构建失败返回null
     */
    LoginSessionVO buildLoginSession(Long userId);

    /**
     * 根据基础用户信息构建登录会话信息
     * 自动判断用户类型（教师/学生/系统用户）并构建相应的会话信息
     *
     * @param baseUser 基础用户信息
     * @return 登录会话信息，如果构建失败返回null
     */
    LoginSessionVO buildLoginSession(BaseUser baseUser);

    /**
     * 批量获取用户
     * @param ids
     * @return
     */
    Map<Long, BaseUser> getMap(Set<Long> ids);
}
