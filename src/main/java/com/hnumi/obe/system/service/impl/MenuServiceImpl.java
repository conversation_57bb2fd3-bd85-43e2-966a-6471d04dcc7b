package com.hnumi.obe.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hnumi.obe.common.entity.ResultCode;
import com.hnumi.obe.common.util.CollectionUtil;
import com.hnumi.obe.common.util.RequestUtil;
import com.hnumi.obe.common.util.StringUtil;
import com.hnumi.obe.system.mapstruct.MenuConvert;
import com.hnumi.obe.system.vo.MenuOptionVO;
import com.hnumi.obe.system.vo.MenuTreeVO;
import com.hnumi.obe.system.vo.MenuVO;
import com.hnumi.obe.system.entity.Menu;
import com.hnumi.obe.system.mapper.MenuMapper;
import com.hnumi.obe.system.service.IMenuService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.hnumi.obe.common.exception.ServiceExceptionUtil.exception;

/**
 * 菜单表 服务实现类
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Service
public class MenuServiceImpl extends ServiceImpl<MenuMapper, Menu> implements IMenuService {
    
    @Autowired
    private MenuMapper menuMapper;

    /**
     * 删除菜单
     *
     * @param id 菜单ID
     * @return 是否删除成功
     */
    @Override
    public boolean deleteById(Long id) {
        // 检查是否存在子菜单
        LambdaQueryWrapper<Menu> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(Menu::getPid, id);
        wrapper.last("limit 1");
        List<Menu> menus = menuMapper.selectList(wrapper);
        if (CollectionUtil.isNotEmpty(menus)) {
            throw exception(ResultCode.MENU_IS_NOT_EMPTY);
        }
        return removeById(id);
    }

    /**
     * 批量删除菜单
     *
     * @param ids 菜单ID列表
     * @return 是否删除成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByIds(List<Long> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            return false;
        }
        // 检查所有菜单是否存在子菜单
        for (Long id : ids) {
            LambdaQueryWrapper<Menu> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(Menu::getPid, id);
            wrapper.last("limit 1");
            List<Menu> menus = menuMapper.selectList(wrapper);
            if (CollectionUtil.isNotEmpty(menus)) {
                throw exception(ResultCode.MENU_IS_NOT_EMPTY);
            }
        }
        // 批量删除菜单
        return removeByIds(ids);
    }

    /**
     * 获取菜单树形选项
     *
     * @return 菜单树形选项列表
     */
    @Override
    public List<MenuOptionVO> listTreeOption() {
        List<MenuOptionVO> menus = MenuConvert.INSTANCE.toOptionVO(list());
        return buildOptionTree(menus);
    }

    /**
     * 获取菜单树形结构
     *
     * @return 菜单树形结构列表
     */
    @Override
    public List<MenuTreeVO> listTree() {
        List<MenuTreeVO> menus = MenuConvert.INSTANCE.toTreeVO(list());
        return buildTree(menus);
    }

    /**
     * 获取路由菜单树形结构
     *
     * @return 路由菜单树形结构列表
     */
    @Override
    public List<MenuTreeVO> listRoute() {
        Long uid = RequestUtil.getUserId();
        List<MenuTreeVO> menus = menuMapper.listRoute(uid);
        return buildTree(menus);
    }

    /**
     * 获取用户权限列表
     *
     * @param uid 用户ID
     * @return 权限列表
     */
    @Override
    public List<String> listPermission(Object uid) {
        return menuMapper.listPermission(uid);
    }

    /**
     * 根据租户ID获取菜单选项树形结构
     *
     * @return 菜单选项树形结构列表
     */
    @Override
    public List<MenuOptionVO> listMenu() {
        List<MenuOptionVO> menus = MenuConvert.INSTANCE.toOptionVO(list());
        return buildOptionTree(menus);
    }

    /**
     * 更新菜单状态
     *
     * @param id 菜单ID
     * @param status 状态值 (0: 显示, 1: 隐藏)
     * @return 是否更新成功
     */
    @Override
    public boolean updateMenuStatus(Long id, Integer status) {
        if (id == null) {
            return false;
        }
        
        Menu menu = new Menu();
        menu.setId(id);
        menu.setHidden(status == 1); // 1表示隐藏，0表示显示
        
        return updateById(menu);
    }

    /**
     * 构建菜单树形结构
     *
     * @param menus 菜单列表
     * @return 树形结构列表
     */
    private List<MenuTreeVO> buildTree(List<MenuTreeVO> menus) {
        // 先按排序号排序
        menus.sort((a, b) -> {
            Integer sortA = a.getMeta().getOrderNo() != null ? a.getMeta().getOrderNo() : 0;
            Integer sortB = b.getMeta().getOrderNo() != null ? b.getMeta().getOrderNo() : 0;
            return sortA.compareTo(sortB);
        });

        // 获取根节点
        List<MenuTreeVO> root = menus.stream()
                .filter(menu -> menu.getPid() == null || menu.getPid() == 0)
                .collect(Collectors.toList());

        // 构建树形结构
        root.forEach(menu -> getChildren(menu, menus));
        return root;
    }

    /**
     * 构建菜单选项树形结构
     *
     * @param menus 菜单选项列表
     * @return 树形结构列表
     */
    private List<MenuOptionVO> buildOptionTree(List<MenuOptionVO> menus) {
        List<MenuOptionVO> root = menus.stream()
                .filter(menu ->  menu.getPid() == null || menu.getPid() == 0)
                .collect(Collectors.toList());
        root.forEach(menu -> getChildren(menu, menus));
        return root;
    }

    /**
     * 递归构建菜单选项子节点
     *
     * @param menu 当前菜单
     * @param menus 所有菜单列表
     */
    private void getChildren(MenuOptionVO menu, List<MenuOptionVO> menus) {
        for (MenuOptionVO child : menus) {
            if (menu.getId().equals(child.getPid())) {
                if (menu.getChildren() == null) {
                    menu.setChildren(new ArrayList<>());
                }
                getChildren(child, menus);
                menu.getChildren().add(child);
            }
        }
    }

    /**
     * 递归构建菜单树子节点
     *
     * @param menu 当前菜单
     * @param menus 所有菜单列表
     */
    private void getChildren(MenuTreeVO menu, List<MenuTreeVO> menus) {
        // 获取所有子菜单
        List<MenuTreeVO> children = menus.stream()
                .filter(child -> menu.getId() != null && menu.getId().equals(child.getPid()))
                .collect(Collectors.toList());

        // 如果有子菜单，递归处理
        if (!children.isEmpty()) {
            menu.setChildren(new ArrayList<>());
            for (MenuTreeVO child : children) {
                if (StringUtil.isBlank(child.getComponent()) && child.getType() != 2) {
                    child.setComponent("Layout");
                }
                getChildren(child, menus);
                menu.getChildren().add(child);
            }
        }
    }
}
