package com.hnumi.obe.system.service;

import com.hnumi.obe.system.vo.MenuOptionVO;
import com.hnumi.obe.system.vo.MenuTreeVO;
import com.hnumi.obe.system.vo.MenuVO;
import com.hnumi.obe.system.entity.Menu;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 菜单表 服务类
 */
public interface IMenuService extends IService<Menu> {

    boolean deleteById(Long id);

    List<MenuOptionVO> listTreeOption();

    List<MenuTreeVO> listTree();

    List<MenuTreeVO> listRoute();

    List<String> listPermission(Object uid);

    List<MenuOptionVO> listMenu();

    /**
     * 批量删除菜单
     *
     * @param ids 菜单ID列表
     * @return 是否删除成功
     */
    boolean deleteByIds(List<Long> ids);

    /**
     * 更新菜单状态
     *
     * @param id 菜单ID
     * @param status 状态值 (0: 显示, 1: 隐藏)
     * @return 是否更新成功
     */
    boolean updateMenuStatus(Long id, Integer status);

}
