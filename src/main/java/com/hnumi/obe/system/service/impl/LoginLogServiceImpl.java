package com.hnumi.obe.system.service.impl;

import com.hnumi.obe.system.entity.LoginLog;
import com.hnumi.obe.system.mapper.LoginLogMapper;
import com.hnumi.obe.system.service.ILoginLogService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * 登录日志表 服务实现类
 */
@Service
public class LoginLogServiceImpl extends ServiceImpl<LoginLogMapper, LoginLog> implements ILoginLogService {

}
