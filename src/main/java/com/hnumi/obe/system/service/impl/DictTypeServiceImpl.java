package com.hnumi.obe.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hnumi.obe.common.entity.ResultCode;
import com.hnumi.obe.common.util.CollectionUtil;
import com.hnumi.obe.system.entity.DictData;
import com.hnumi.obe.system.entity.DictType;
import com.hnumi.obe.system.mapper.DictDataMapper;
import com.hnumi.obe.system.mapper.DictTypeMapper;
import com.hnumi.obe.system.service.IDictTypeService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.hnumi.obe.common.exception.ServiceExceptionUtil.exception;

/**
 * 字典类型表 服务实现类
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Service
public class DictTypeServiceImpl extends ServiceImpl<DictTypeMapper, DictType> implements IDictTypeService {

    @Autowired
    private DictDataMapper dictDataMapper;

    /**
     * 删除字典类型
     *
     * @param id 字典类型ID
     * @return 是否删除成功
     */
    @Override
    public boolean deleteById(Long id) {
        // 检查是否存在关联的字典数据
        LambdaQueryWrapper<DictData> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(DictData::getTypeId, id);
        wrapper.last("limit 1");
        List<DictData> dictDataList = dictDataMapper.selectList(wrapper);
        if (CollectionUtil.isNotEmpty(dictDataList)) {
            throw exception(ResultCode.DICT_TYPE_IS_NOT_EMPTY);
        }
        return removeById(id);
    }
}
