package com.hnumi.obe.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hnumi.obe.base.service.IStudentService;
import com.hnumi.obe.base.service.ITeacherService;
import com.hnumi.obe.base.vo.StudentVO;
import com.hnumi.obe.base.vo.TeacherVO;
import com.hnumi.obe.common.CommonProperties;
import com.hnumi.obe.common.entity.LoginSessionVO;
import com.hnumi.obe.common.entity.ResultCode;
import com.hnumi.obe.common.util.CollectionUtil;
import com.hnumi.obe.common.util.PasswordUtil;
import com.hnumi.obe.common.util.RedisUtil;
import com.hnumi.obe.common.util.StringUtil;
import com.hnumi.obe.system.dto.BatchDeleteDTO;
import com.hnumi.obe.system.dto.LoginUserDTO;
import com.hnumi.obe.system.dto.RegisterUserDTO;
import com.hnumi.obe.system.dto.UserImportDTO;
import com.hnumi.obe.system.dto.UserQueryDTO;
import com.hnumi.obe.system.dto.UserRoleAssignDTO;
import com.hnumi.obe.system.dto.UserUpdateSafeDTO;
import com.hnumi.obe.system.entity.BaseUser;
import com.hnumi.obe.system.entity.Role;
import com.hnumi.obe.system.entity.RoleUser;
import com.hnumi.obe.system.mapper.BaseUserMapper;
import com.hnumi.obe.system.mapstruct.RoleConvert;
import com.hnumi.obe.system.mapstruct.UserConvert;
import com.hnumi.obe.system.service.IBaseUserService;
import com.hnumi.obe.system.service.IRoleService;
import com.hnumi.obe.system.service.IRoleUserService;
import com.hnumi.obe.system.vo.RoleVO;
import com.hnumi.obe.system.vo.UserVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static com.hnumi.obe.common.exception.ServiceExceptionUtil.exception;

/**
 * 基本用户信息表 服务实现类
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Service
@Slf4j
public class BaseUserServiceImpl extends ServiceImpl<BaseUserMapper, BaseUser> implements IBaseUserService {

    @Autowired
    private BaseUserMapper baseUserMapper;

    @Autowired
    private ITeacherService teacherService;

    @Autowired
    @Lazy
    private IStudentService studentService;


    @Autowired
    private IRoleService roleService;

    @Autowired
    private IRoleUserService roleUserService;

    /**
     * 用户登录
     *
     * @param loginUserDTO 登录用户信息
     * @return 用户信息
     */
    @Override
    public BaseUser login(LoginUserDTO loginUserDTO) {
        LambdaQueryWrapper<BaseUser> wrapper = Wrappers.lambdaQuery();
        switch (loginUserDTO.getType()) {
            case 1:
                wrapper.eq(BaseUser::getPhone, loginUserDTO.getUsername());
                break;
            case 2:
                wrapper.eq(BaseUser::getEmail, loginUserDTO.getUsername());
                break;
            default:
                throw exception(ResultCode.LOGIN_PARAM_ERROR);
        }
        wrapper.select(BaseUser::getId, BaseUser::getPassword, BaseUser::getSalt, BaseUser::getPhone, BaseUser::getEmail,BaseUser::getRealName, BaseUser::getGender);
        wrapper.last("limit 1");
        BaseUser user = baseUserMapper.selectOne(wrapper);
        if (user == null) {
            throw exception(ResultCode.LOGIN_USER_NOT_FOUND);
        }
        if (!PasswordUtil.verify(loginUserDTO.getPassword(), user.getPassword(), user.getSalt())) {
            throw exception(ResultCode.LOGIN_PASSWORD_ERROR);
        }
        return user;
    }

    /**
     * 更新用户安全信息
     *
     * @param id 用户ID
     * @param userUpdateSafeDTO 安全信息更新DTO
     * @return 是否更新成功
     */
    @Override
    public boolean updateSafeInfo(Long id, UserUpdateSafeDTO userUpdateSafeDTO) {
        BaseUser user = getById(id);
        if (user == null) {
            throw exception(ResultCode.USER_NOT_FOUND);
        }

        switch (userUpdateSafeDTO.getType()) {
            case 0:
                user.setUsername(userUpdateSafeDTO.getUsername());
                break;
            case 1:
                user.setPhone(userUpdateSafeDTO.getUsername());
                break;
            case 2:
                user.setEmail(userUpdateSafeDTO.getUsername());
                break;
            default:
                throw exception(ResultCode.USER_PARAM_ERROR);
        }

        String key = CommonProperties.REDIS_KEY_PREFIX_PHONE_CODE + ":update:" + userUpdateSafeDTO.getPhone();
        Object code = RedisUtil.get(key);
        if (code == null || !userUpdateSafeDTO.getCode().equals(code.toString())) {
            throw exception(ResultCode.PHONE_CODE_ERROR);
        }

        return updateById(user);
    }

    /**
     * 用户注册
     *
     * @param registerUserDTO 注册用户信息
     * @return 注册成功的用户信息
     */
    @Override
    public BaseUser register(RegisterUserDTO registerUserDTO) {
        return null;
    }

    /**
     * 删除用户
     *
     * @param id 用户ID
     * @return 是否删除成功
     */
    @Override
    public boolean deleteById(Long id) {
        BaseUser user = getById(id);
        if (user == null) {
            throw exception(ResultCode.USER_NOT_FOUND);
        }
        return removeById(id);
    }

    /**
     * 封禁/解封用户
     *
     * @param id 用户ID
     * @return 是否操作成功
     */
    @Override
    public boolean block(Long id) {
        BaseUser user = getById(id);
        if (user == null) {
            throw exception(ResultCode.USER_NOT_FOUND);
        }

        if (user.getStatus() == 0) {
            user.setStatus(99);
        } else if (user.getStatus() == 99) {
            user.setStatus(0);
        } else {
            throw exception(ResultCode.USER_STATUS_ERROR);
        }
        return updateById(user);
    }

    /**
     * 分页查询用户列表
     *
     * @param query 查询条件
     * @return 用户列表分页数据
     */
    @Override
    public IPage<UserVO> listUsers(UserQueryDTO query) {
        LambdaQueryWrapper<BaseUser> wrapper = Wrappers.lambdaQuery();
        wrapper.like(StringUtil.isNotBlank(query.getPhone()), BaseUser::getPhone, query.getPhone())
               .like(StringUtil.isNotBlank(query.getEmail()), BaseUser::getEmail, query.getEmail())
               .like(StringUtil.isNotBlank(query.getUsername()), BaseUser::getUsername, query.getUsername())
               .like(StringUtil.isNotBlank(query.getRealName()), BaseUser::getRealName, query.getRealName())
               .eq(!Objects.isNull(query.getGender()), BaseUser::getGender, query.getGender())
               .eq(!Objects.isNull(query.getStatus()), BaseUser::getStatus, query.getStatus());
        
        Page<BaseUser> page = new Page<>(query.getPage().getCurrent(), query.getPage().getSize());
        Page<BaseUser> resultPage = page(page, wrapper);
        
        List<UserVO> vos = UserConvert.INSTANCE.toVO(resultPage.getRecords());
        
        // 为每个用户查询角色信息
        for (UserVO userVO : vos) {
            List<RoleVO> roles = getUserRoles(userVO.getId());
            userVO.setRoles(roles);
        }
        
        Page<UserVO> voPage = new Page<>();
        voPage.setRecords(vos);
        voPage.setTotal(resultPage.getTotal());
        voPage.setCurrent(resultPage.getCurrent());
        voPage.setSize(resultPage.getSize());
        return voPage;
    }

    /**
     * 获取用户角色列表
     *
     * @param userId 用户ID
     * @return 角色列表
     */
    private List<RoleVO> getUserRoles(Long userId) {
        LambdaQueryWrapper<RoleUser> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(RoleUser::getUserId, userId);
        List<RoleUser> roleUsers = roleUserService.list(wrapper);
        
        if (CollectionUtil.isEmpty(roleUsers)) {
            return new ArrayList<>();
        }
        
        List<Long> roleIds = roleUsers.stream()
                .map(RoleUser::getRoleId)
                .collect(Collectors.toList());
        
        List<Role> roles = roleService.listByIds(roleIds);
        return RoleConvert.INSTANCE.toVO(roles);
    }

    /**
     * 分配用户角色
     *
     * @param userRoleAssignDTO 角色分配信息
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean assignRoles(UserRoleAssignDTO userRoleAssignDTO) {
        Long userId = userRoleAssignDTO.getUserId();
        final List<Long> roleIds = userRoleAssignDTO.getRoleIds();
        
        // 验证用户是否存在
        BaseUser user = getById(userId);
        if (user == null) {
            throw exception(ResultCode.USER_NOT_FOUND);
        }
        
        // 删除用户现有角色
        LambdaQueryWrapper<RoleUser> deleteWrapper = Wrappers.lambdaQuery();
        deleteWrapper.eq(RoleUser::getUserId, userId);
        roleUserService.remove(deleteWrapper);
        
        // 添加新角色
        if (CollectionUtil.isNotEmpty(roleIds)) {
            List<RoleUser> roleUsers = roleIds.stream()
                    .map(roleId -> {
                        RoleUser roleUser = new RoleUser();
                        roleUser.setUserId(userId);
                        roleUser.setRoleId(roleId);
                        return roleUser;
                    })
                    .collect(Collectors.toList());
            
            return roleUserService.saveBatch(roleUsers);
        }
        
        return true;
    }

    /**
     * 重置用户密码
     *
     * @param id 用户ID
     * @return 是否成功
     */
    @Override
    public boolean resetPassword(Long id) {
        BaseUser user = getById(id);
        if (user == null) {
            throw exception(ResultCode.USER_NOT_FOUND);
        }
        
        // 生成新的盐值和默认密码
        String salt = PasswordUtil.generateSalt();
        String defaultPassword = PasswordUtil.DEFAULT_PASSWORD;
        String hashedPassword = PasswordUtil.hash(defaultPassword, salt);
        
        user.setSalt(salt);
        user.setPassword(hashedPassword);
        
        return updateById(user);
    }

    /**
     * 批量删除用户
     *
     * @param batchDeleteDTO 批量删除信息
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchDelete(BatchDeleteDTO batchDeleteDTO) {
        List<Long> ids = batchDeleteDTO.getIds();
        if (CollectionUtil.isEmpty(ids)) {
            return true;
        }
        
        // 删除用户角色关联
        LambdaQueryWrapper<RoleUser> roleUserWrapper = Wrappers.lambdaQuery();
        roleUserWrapper.in(RoleUser::getUserId, ids);
        roleUserService.remove(roleUserWrapper);
        
        // 删除用户
        return removeByIds(ids);
    }

    /**
     * 获取用户详情（包含角色信息）
     *
     * @param id 用户ID
     * @return 用户详情
     */
    @Override
    public UserVO getUserDetail(Long id) {
        BaseUser user = getById(id);
        if (user == null) {
            throw exception(ResultCode.USER_NOT_FOUND);
        }
        
        UserVO userVO = UserConvert.INSTANCE.toVO(user);
        List<RoleVO> roles = getUserRoles(id);
        userVO.setRoles(roles);
        
        return userVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<UserImportDTO> importUsers(List<UserImportDTO> users, boolean importPartial) {
        if (CollectionUtil.isEmpty(users)) {
            return new ArrayList<>();
        }

        // 创建一个标记对象作为返回结果的第一个元素
        UserImportDTO flagDTO = new UserImportDTO();
        flagDTO.setMessage("IMPORT_FLAG");
        List<UserImportDTO> result = new ArrayList<>();
        result.add(flagDTO);

        List<BaseUser> baseUsers = UserConvert.INSTANCE.toEntityImport(users);
        // 设置初始化密码盒盐值
        baseUsers.forEach(user -> {
            user.setSalt(PasswordUtil.generateSalt());
            user.setPassword(PasswordUtil.hash(StringUtil.setNotBlank(user.getPassword(), PasswordUtil.DEFAULT_PASSWORD), user.getSalt()));
        });

        // 分批处理，每批1000条
        int batchSize = 1000;
        boolean hasError = false;
        for (int i = 0; i < baseUsers.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, baseUsers.size());
            List<BaseUser> batch = baseUsers.subList(i, endIndex);
            List<UserImportDTO> dtos = users.subList(i, endIndex);
            
            try {
                // 收集当前批次的手机号和邮箱
                List<String> phones = batch.stream()
                        .map(BaseUser::getPhone)
                        .filter(StringUtil::isNotBlank)
                        .toList();
                List<String> emails = batch.stream()
                        .map(BaseUser::getEmail)
                        .filter(StringUtil::isNotBlank)
                        .toList();

                boolean batchHasError = false;
                List<BaseUser> currentValidBatch = new ArrayList<>();
                List<UserImportDTO> currentValid = new ArrayList<>();

                // 批量校验手机号
                if (!phones.isEmpty()) {
                    LambdaQueryWrapper<BaseUser> phoneWrapper = Wrappers.lambdaQuery();
                    phoneWrapper.in(BaseUser::getPhone, phones);
                    List<BaseUser> existingPhones = list(phoneWrapper);
                    if (!existingPhones.isEmpty()) {
                        Set<String> existingPhoneSet = existingPhones.stream()
                                .map(BaseUser::getPhone)
                                .collect(Collectors.toSet());
                        
                        for (int j = 0; j < batch.size(); j++) {
                            BaseUser user = batch.get(j);
                            if (existingPhoneSet.contains(user.getPhone())) {
                                dtos.get(j).setMessage("手机号已存在：" + user.getPhone());
                                batchHasError = true;
                            } else {
                                currentValidBatch.add(user);
                                currentValid.add(dtos.get(j));
                            }
                        }
                    } else {
                        currentValidBatch.addAll(batch);
                        currentValid.addAll(dtos);
                    }
                }

                // 批量校验邮箱
                if (!emails.isEmpty()) {
                    LambdaQueryWrapper<BaseUser> emailWrapper = Wrappers.lambdaQuery();
                    emailWrapper.in(BaseUser::getEmail, emails);
                    List<BaseUser> existingEmails = list(emailWrapper);
                    if (!existingEmails.isEmpty()) {
                        Set<String> existingEmailSet = existingEmails.stream()
                                .map(BaseUser::getEmail)
                                .collect(Collectors.toSet());
                        
                        for (int j = 0; j < currentValidBatch.size(); j++) {
                            BaseUser user = currentValidBatch.get(j);
                            if (existingEmailSet.contains(user.getEmail())) {
                                currentValid.get(j).setMessage("邮箱已存在：" + user.getEmail());
                                batchHasError = true;
                                currentValidBatch.remove(j);
                                currentValid.remove(j);
                                j--;
                            }
                        }
                    }
                }

                if (batchHasError) {
                    hasError = true;
                    if (!importPartial) {
                        // 如果不允许部分导入，标记所有数据为失败
                        dtos.forEach(dto -> {
                            if (dto.getMessage() == null) {
                                dto.setMessage("存在重复数据，导入终止");
                            }
                        });
                        result.addAll(dtos);
                        break;
                    }
                }

                // 保存有效数据
                if (!currentValidBatch.isEmpty()) {
                    if (saveBatch(currentValidBatch)) {
                        currentValid.forEach(dto -> {
                            if (dto.getMessage() == null) {
                                dto.setMessage("导入成功");
                            }
                        });
                    } else {
                        currentValid.forEach(dto -> {
                            if (dto.getMessage() == null) {
                                dto.setMessage("数据保存失败");
                            }
                        });
                        hasError = true;
                    }
                }

                result.addAll(dtos);
            } catch (Exception e) {
                hasError = true;
                dtos.forEach(dto -> {
                    if (dto.getMessage() == null) {
                        dto.setMessage("系统异常：" + e.getMessage());
                    }
                });
                result.addAll(dtos);
            }
        }

        // 设置导入结果标记
        flagDTO.setMessage(hasError ? "IMPORT_PARTIAL_SUCCESS" : "IMPORT_SUCCESS");
        return result;
    }

    @Override
    public LoginSessionVO buildLoginSession(Long userId) {
        if (userId == null) {
            log.warn("构建登录会话失败：用户ID为空");
            return null;
        }

        try {
            BaseUser baseUser = getById(userId);
            if (baseUser == null) {
                log.warn("构建登录会话失败：未找到用户信息，userId={}", userId);
                return null;
            }

            return buildLoginSession(baseUser);
        } catch (Exception e) {
            log.error("构建登录会话时发生异常，userId={}", userId, e);
            return null;
        }
    }

    @Override
    public LoginSessionVO buildLoginSession(BaseUser baseUser) {
        if (baseUser == null) {
            log.warn("构建登录会话失败：基础用户信息为空");
            return null;
        }

        try {
            // 优先检查是否为教师
            TeacherVO teacherVO = teacherService.getByUserId(baseUser.getId());
            if (teacherVO != null) {
                log.debug("用户{}被识别为教师，教师ID={}", baseUser.getId(), teacherVO.getId());
                return LoginSessionVO.fromTeacher(teacherVO);
            }

            // 检查是否为学生
            StudentVO studentVO = studentService.getStudentByUserId(baseUser.getId());
            if (studentVO != null) {
                log.debug("用户{}被识别为学生，学生ID={}", baseUser.getId(), studentVO.getId());
                return LoginSessionVO.fromStudent(studentVO);
            }

            // 如果都不是，则认为是系统用户
            log.debug("用户{}被识别为系统用户", baseUser.getId());
            return LoginSessionVO.fromSystemUser(baseUser);

        } catch (Exception e) {
            log.error("构建登录会话时发生异常，userId={}", baseUser.getId(), e);
            // 发生异常时，尝试构建系统用户会话作为降级方案
            try {
                log.warn("尝试构建系统用户会话作为降级方案，userId={}", baseUser.getId());
                return LoginSessionVO.fromSystemUser(baseUser);
            } catch (Exception fallbackException) {
                log.error("降级方案也失败，userId={}", baseUser.getId(), fallbackException);
                return null;
            }
        }
    }

    @Override
    public Map<Long, BaseUser> getMap(Set<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Map.of();
        }
        LambdaQueryWrapper<BaseUser> queryWrapper = Wrappers.<BaseUser>lambdaQuery()
                .in(BaseUser::getId, ids)
                .eq(BaseUser::getStatus, 0);
        return list(queryWrapper).stream().collect(Collectors.toMap(BaseUser::getId, u -> u));
    }
}
