package com.hnumi.obe.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hnumi.obe.common.entity.ResultCode;
import com.hnumi.obe.system.entity.Role;
import com.hnumi.obe.system.mapper.RoleMapper;
import com.hnumi.obe.system.mapstruct.RoleConvert;
import com.hnumi.obe.system.service.IRoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.hnumi.obe.common.exception.ServiceExceptionUtil.exception;

/**
 * 角色表 服务实现类
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Service
public class RoleServiceImpl extends ServiceImpl<RoleMapper, Role> implements IRoleService {

    @Autowired
    private RoleMapper roleMapper;


    /**
     * 获取用户角色列表
     *
     * @param loginId 登录用户ID
     * @return 角色列表
     */
    @Override
    public List<String> listRole(Object loginId) {
        return roleMapper.listRole(loginId);
    }

    @Override
    public void updateRole(Role role) {
        try {
            roleMapper.updateById(role);
        } catch (Exception e) {
            throw exception(ResultCode.ROLE_UPDATE_ERROR);
        }
    }
}
