package com.hnumi.obe.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 用户角色关联实体类
 * 用于存储用户和角色的关联关系
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Data
@TableName("sys_user_role")
public class UserRole {
    
    /**
     * 关联ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 用户信息
     */
    @TableField(exist = false)
    private BaseUser user;
    
    /**
     * 角色信息
     */
    @TableField(exist = false)
    private Role role;
}
