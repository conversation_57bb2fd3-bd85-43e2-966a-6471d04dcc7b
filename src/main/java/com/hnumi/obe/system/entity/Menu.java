package com.hnumi.obe.system.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
* 菜单表
*/
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_menu")
public class Menu extends Model<Menu> {

    private static final long serialVersionUID = 1L;

    /**
     * 菜单ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 父菜单ID
     * 可以为 null，表示顶级菜单
     */
    @TableField("parent_id")
    private Long pid;

    /**
     * 路由地址
     */
    @TableField("path")
    private String path;

    /**
     * 组件名：唯一
     */
    @TableField("name")
    private String name;

    /**
     * 组件
     */
    @TableField("component")
    private String component;

    /**
     * 重定向到的子路由
     */
    @TableField("redirect")
    private String redirect;

    /**
     * 菜单名称
     */
    @TableField("title")
    private String title;

    /**
     * 菜单图标
     */
    @TableField("icon")
    private String icon;

    /**
     * 是否默认展开
     */
    @TableField("expanded")
    private Boolean expanded;

    /**
     * 菜单排序号
     */
    @TableField("order_no")
    private Integer sort;

    /**
     * 是否隐藏：默认不隐藏
     */
    @TableField("is_hidden")
    private Boolean hidden;

    /**
     * 是否隐藏面包屑
     */
    @TableField("hidden_breadcrumb")
    private Boolean hiddenBreadcrumb;

    /**
     * 是否只显示一级菜单
     * 当多级菜单只有一个子节点时，是否只显示一级菜单
     * 注意：此配置需要设置在父节点上
     */
    @TableField("single")
    private Boolean single;

    /**
     * iframe地址
     */
    @TableField("frame_src")
    private String frameSrc;

    /**
     * iframe是否新窗口打开
     */
    @TableField("frame_blank")
    private Boolean frameBlank;

    /**
     * 是否开启keep-alive
     */
    @TableField("keep_alive")
    private Boolean keepAlive;

    /**
     * 权限码
     */
    @TableField("menu_code")
    private String code;

    /**
     * 0表示目录，1表示菜单，2表示按钮
     */
    @TableField("menu_type")
    private Integer type;

    /**
     * 是否可关闭多标签页，默认false
     */
    @TableField("is_no_closable")
    private Boolean noClosable;

    /**
     * 是否显示列，默认true
     */
    @TableField("is_no_column")
    private Boolean noColumn;

    /**
     * 徽标内容
     */
    @TableField("badge")
    private String badge;

    /**
     * 从字典表获取数据进行展示，对应字典表中value
     */
    @TableField("target")
    private String target;

    @TableField("active_menu")
    private String activeMenu;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(value = "creator", fill = FieldFill.INSERT)
    private Long creator;

    /**
     * 更新时间
     */
    @TableField(value = "modify_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime modifyTime;

    /**
     * 更新人
     */
    @TableField(value = "modifier", fill = FieldFill.INSERT_UPDATE)
    private Long modifier;

    @TableLogic
    @TableField("is_deleted")
    private Integer deleted;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}