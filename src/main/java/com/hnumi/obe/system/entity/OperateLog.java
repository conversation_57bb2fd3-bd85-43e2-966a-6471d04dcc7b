package com.hnumi.obe.system.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
* 操作日志表
*/
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_operate_log")
public class OperateLog extends Model<OperateLog> {

    private static final long serialVersionUID = 1L;

    /**
     * 日志ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 操作用户
     */
    @TableField("username")
    private String username;

    /**
     * 业务名称
     */
    @TableField("business_name")
    private String businessName;

    /**
     * 操作内容
     */
    @TableField("action_content")
    private String actionContent;

    /**
     * 操作结果;0表示成功，1表示失败
     */
    @TableField("action_result")
    private Integer actionResult;

    /**
     * 请求参数
     */
    @TableField("request_param")
    private String requestParam;

    /**
     * 响应结果
     */
    @TableField("result")
    private String result;

    /**
     * 请求方法
     */
    @TableField("request_method")
    private String requestMethod;

    /**
     * 请求地址
     */
    @TableField("request_uri")
    private String requestUri;

    /**
     * 用户IP
     */
    @TableField("request_ip")
    private String requestIp;

    /**
     * 浏览器 UA
     */
    @TableField("user_agent")
    private String userAgent;

    /**
     * 浏览器信息
     */
    @TableField("browser")
    private String browser;

    /**
     * 操作系统信息
     */
    @TableField("os")
    private String os;

    /**
     * 执行时长
     */
    @TableField("duration")
    private Long duration;

    /**
     * IP所属城市
     */
    @TableField("ip_location_city")
    private String ipLocationCity;

    /**
     * IP所属省份
     */
    @TableField("ip_location_province")
    private String ipLocationProvince;

    /**
     * IP所属国家
     */
    @TableField("ip_location_country")
    private String ipLocationCountry;

    /**
     * 创建人
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private Long createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 是否删除
     */
    @TableField("is_deleted")
    @TableLogic
    private Boolean deleted;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}