package com.hnumi.obe.system.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
* 字典数据表
*/
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_dict_data")
public class DictData extends Model<DictData> {

    private static final long serialVersionUID = 1L;

    /**
     * 字典数据ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 字典标签
     */
    @TableField("label")
    private String label;

    /**
     * 字典的键值
     */
    @TableField("value")
    private String value;

    /**
     * 字典类型的ID
     */
    @TableField("type_id")
    private Long typeId;

    /**
     * 字典的排序
     */
    @TableField("sort")
    private Integer sort;

    /**
     * 该字典数据的样式集
     */
    @TableField("css_class")
    private String cssClass;

    /**
     * 该字典数据在表格回显的时候的样式集
     */
    @TableField("list_class")
    private String listClass;

    /**
     * 是否为默认：1表示是，0表示否
     */
    @TableField("is_default")
    private Integer defaul;

    /**
     * 0表示正常，9表示停用
     */
    @TableField("status")
    private Integer status;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(value = "creator", fill = FieldFill.INSERT)
    private Long creator;

    /**
     * 更新时间
     */
    @TableField(value = "modifyTime", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime modifyTime;

    /**
     * 更新人
     */
    @TableField(value = "modifier", fill = FieldFill.INSERT_UPDATE)
    private Long modifier;

    /**
     * 是否已删除
     */
    @TableField("is_deleted")
    @TableLogic
    private Boolean deleted;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}