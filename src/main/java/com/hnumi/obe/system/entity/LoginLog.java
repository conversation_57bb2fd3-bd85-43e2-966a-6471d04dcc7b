package com.hnumi.obe.system.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
* 登录日志表
*/
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_login_log")
public class LoginLog extends Model<LoginLog> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户角色
     */
    @TableField("user_role")
    private String userRole;

    /**
     * 用户名
     */
    @TableField("username")
    private String username;

    /**
     * 登录结果详情
     */
    @TableField("result")
    private String result;

    /**
     * 登录结果：0表示成功，1表示失败
     */
    @TableField("result_type")
    private Integer resultType;

    /**
     * 请求IP
     */
    @TableField("request_ip")
    private String requestIp;

    /**
     * 请求的UA
     */
    @TableField("user_agent")
    private String userAgent;

    /**
     * 浏览器类型
     */
    @TableField("browser")
    private String browser;

    /**
     * 操作系统类型
     */
    @TableField("os")
    private String os;

    /**
     * IP所在城市
     */
    @TableField("ip_location_city")
    private String ipLocationCity;

    /**
     * IP所在省份
     */
    @TableField("ip_location_province")
    private String ipLocationProvince;

    /**
     * IP所属国家
     */
    @TableField("ip_location_country")
    private String ipLocationCountry;

    /**
     * 创建人
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private Long createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 是否删除
     */
    @TableField("is_deleted")
    @TableLogic
    private Boolean deleted;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}