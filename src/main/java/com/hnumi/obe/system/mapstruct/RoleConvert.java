package com.hnumi.obe.system.mapstruct;

import com.hnumi.obe.common.mapstruct.BaseConvert;
import com.hnumi.obe.system.dto.RoleDTO;
import com.hnumi.obe.system.dto.RoleStatusUpdateDTO;
import com.hnumi.obe.system.vo.RoleVO;
import com.hnumi.obe.system.entity.Role;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = "spring")
public interface RoleConvert extends BaseConvert<RoleVO, Role> {
    RoleConvert INSTANCE = Mappers.getMapper(RoleConvert.class);

    Role toEntity(RoleDTO roleAddDTO);

    Role toEntity(RoleStatusUpdateDTO dto);
}
