package com.hnumi.obe.system.mapstruct;

import com.hnumi.obe.common.mapstruct.BaseConvert;
import com.hnumi.obe.system.dto.DictTypeDTO;
import com.hnumi.obe.system.vo.DictTypeVO;
import com.hnumi.obe.system.entity.DictType;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

/**
 * 字典类型对象转换器
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = "spring")
public interface DictTypeConvert extends BaseConvert<DictTypeVO, DictType> {
    DictTypeConvert INSTANCE = Mappers.getMapper(DictTypeConvert.class);

    /**
     * 将字典类型实体类转换为VO
     *
     * @param entity 字典类型实体类对象
     * @return 字典类型VO对象
     */
    @Override
    @Mappings({
        @Mapping(source = "title", target = "name"),
        @Mapping(source = "title", target = "type"),
        @Mapping(source = "status", target = "status")
    })
    DictTypeVO toVO(DictType entity);

    /**
     * 将DTO转换为实体类
     *
     * @param dto 字典类型DTO对象
     * @return 字典类型实体类对象
     */
    DictType toEntity(DictTypeDTO dto);
}
