package com.hnumi.obe.system.mapstruct;

import com.hnumi.obe.system.entity.OperateLog;
import com.hnumi.obe.system.entity.LoginLog;
import com.hnumi.obe.system.entity.ErrorLog;
import com.hnumi.obe.system.vo.OperateLogVO;
import com.hnumi.obe.system.vo.LoginLogVO;
import com.hnumi.obe.system.vo.ErrorLogVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 日志对象转换器
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Mapper
public interface LogConvert {
    
    LogConvert INSTANCE = Mappers.getMapper(LogConvert.class);
    
    /**
     * 操作日志实体转VO
     *
     * @param log 操作日志实体
     * @return 操作日志VO
     */
    OperateLogVO toOperateLogVO(OperateLog log);
    
    /**
     * 操作日志实体列表转VO列表
     *
     * @param logs 操作日志实体列表
     * @return 操作日志VO列表
     */
    List<OperateLogVO> toOperateLogVO(List<OperateLog> logs);
    
    /**
     * 登录日志实体转VO
     *
     * @param log 登录日志实体
     * @return 登录日志VO
     */
    LoginLogVO toLoginLogVO(LoginLog log);
    
    /**
     * 登录日志实体列表转VO列表
     *
     * @param logs 登录日志实体列表
     * @return 登录日志VO列表
     */
    List<LoginLogVO> toLoginLogVO(List<LoginLog> logs);
    
    /**
     * 错误日志实体转VO
     *
     * @param log 错误日志实体
     * @return 错误日志VO
     */
    ErrorLogVO toErrorLogVO(ErrorLog log);
    
    /**
     * 错误日志实体列表转VO列表
     *
     * @param logs 错误日志实体列表
     * @return 错误日志VO列表
     */
    List<ErrorLogVO> toErrorLogVO(List<ErrorLog> logs);
} 