package com.hnumi.obe.system.mapstruct;

import com.hnumi.obe.common.mapstruct.BaseConvert;
import com.hnumi.obe.system.dto.MenuDTO;
import com.hnumi.obe.system.vo.MenuOptionVO;
import com.hnumi.obe.system.vo.MenuTreeVO;
import com.hnumi.obe.system.vo.MenuVO;
import com.hnumi.obe.system.entity.Menu;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 菜单对象转换器
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = "spring")
public interface MenuConvert extends BaseConvert<MenuVO, Menu> {
    MenuConvert INSTANCE = Mappers.getMapper(MenuConvert.class);

    /**
     * 将DTO转换为实体类
     *
     * @param dto 菜单DTO对象
     * @return 菜单实体类对象
     */
    @Mappings({
        @Mapping(source = "type", target = "type"),
        @Mapping(target = "pid", expression = "java(dto.getPid() == null || dto.getPid() == 0 ? 0L : dto.getPid())")
    })
    Menu toEntity(MenuDTO dto);

    /**
     * 将实体类转换为VO
     *
     * @param menu 菜单实体类对象
     * @return 菜单VO对象
     */
    @Override
    MenuVO toVO(Menu menu);

    @Mappings({
            @Mapping(source = "title", target = "meta.title"),
            @Mapping(source = "icon", target = "meta.icon"),
            @Mapping(source = "expanded", target = "meta.expanded"),
            @Mapping(source = "sort", target = "meta.orderNo"),
            @Mapping(source = "hidden", target = "meta.hidden"),
            @Mapping(source = "code", target = "meta.code"),
            @Mapping(source = "single", target = "meta.single")
    })
    MenuTreeVO toTreeVO(Menu menu);

    List<MenuTreeVO> toTreeVO(List<Menu> menu);

    MenuOptionVO toOptionVO(Menu menu);

    List<MenuOptionVO> toOptionVO(List<Menu> menu);
}
