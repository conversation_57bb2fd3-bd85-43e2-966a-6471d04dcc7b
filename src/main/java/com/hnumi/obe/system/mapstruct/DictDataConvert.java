package com.hnumi.obe.system.mapstruct;

import com.hnumi.obe.common.mapstruct.BaseConvert;
import com.hnumi.obe.system.dto.DictDataDTO;
import com.hnumi.obe.system.vo.DictDataVO;
import com.hnumi.obe.system.entity.DictData;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

/**
 * 字典数据对象转换器
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = "spring")
public interface DictDataConvert extends BaseConvert<DictDataVO, DictData> {
    DictDataConvert INSTANCE = Mappers.getMapper(DictDataConvert.class);

    /**
     * 将DTO转换为实体类
     *
     * @param dto 字典数据DTO对象
     * @return 字典数据实体类对象
     */
    @Mappings({
        @Mapping(source = "typeId", target = "typeId"),
        @Mapping(source = "sort", target = "sort")
    })
    DictData toEntity(DictDataDTO dto);

    /**
     * 将实体类转换为VO
     *
     * @param entity 字典数据实体类对象
     * @return 字典数据VO对象
     */
    @Mappings({
        @Mapping(source = "defaul", target = "defaul")
    })
    DictDataVO toVO(DictData entity);
}
