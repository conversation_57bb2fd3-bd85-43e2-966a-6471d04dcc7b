package com.hnumi.obe.system.mapstruct;


import com.hnumi.obe.common.mapstruct.BaseConvert;
import com.hnumi.obe.system.dto.RegisterUserDTO;
import com.hnumi.obe.system.dto.UserImportDTO;
import com.hnumi.obe.system.dto.UserUpdateSafeDTO;
import com.hnumi.obe.system.dto.UserDTO;
import com.hnumi.obe.system.vo.LoginUserVO;
import com.hnumi.obe.system.vo.UserVO;
import com.hnumi.obe.system.entity.BaseUser;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;


@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = "spring")
public interface UserConvert extends BaseConvert<UserVO, BaseUser> {
    UserConvert INSTANCE = Mappers.getMapper(UserConvert.class);

    BaseUser toEntity(UserDTO dto);

    @Mappings({
        @Mapping(source = "name", target = "realName"),
        @Mapping(target = "gender", expression = "java(dto.getGender() == null || dto.getGender().equals(\"保密\") ? 0 : (dto.getGender().equals(\"男\") ? 1 : 2))")
    })
    BaseUser toEntity(UserImportDTO dto);

    BaseUser toEntity(UserUpdateSafeDTO userUpdateSafeDTO);

    BaseUser toEntity(RegisterUserDTO registerUserDTO);

    @Mappings({
        @Mapping(source = "realName", target = "name")
    })
    UserVO toVO(BaseUser entity);

    LoginUserVO toLoginUserVO(BaseUser entity);

    List<BaseUser> toEntityImport(List<UserImportDTO> users);

}
