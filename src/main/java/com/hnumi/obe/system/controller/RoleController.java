package com.hnumi.obe.system.controller;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hnumi.obe.common.entity.R;
import com.hnumi.obe.common.entity.ResultCode;
import com.hnumi.obe.system.dto.RoleDTO;
import com.hnumi.obe.system.dto.RoleMenuUpdateDTO;
import com.hnumi.obe.system.dto.RoleQueryDTO;
import com.hnumi.obe.system.dto.RoleStatusUpdateDTO;
import com.hnumi.obe.system.entity.Role;
import com.hnumi.obe.system.mapstruct.RoleConvert;
import com.hnumi.obe.system.service.IRoleMenuService;
import com.hnumi.obe.system.service.IRoleService;
import com.hnumi.obe.system.vo.RoleVO;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static com.hnumi.obe.common.exception.ServiceExceptionUtil.exception;

/**
 * 角色控制器
 * 处理角色相关的请求，包括角色的增删改查、状态更新、菜单权限管理等
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@RestController
@RequestMapping("/role")
public class RoleController {
    
    private static final Logger log = LoggerFactory.getLogger(RoleController.class);
    
    @Autowired
    private IRoleService roleService;
    
    @Autowired
    private IRoleMenuService roleMenuService;

    /**
     * 新增角色
     *
     * @param dto 角色信息
     * @return 新增结果
     */
    @PostMapping
    public R<Void> addRole(@Validated @RequestBody RoleDTO dto) {
        Role entity = RoleConvert.INSTANCE.toEntity(dto);
        if (!roleService.save(entity)) {
            throw exception(ResultCode.ROLE_ADD_ERROR);
        }
        return R.ok();
    }

    /**
     * 更新角色
     *
     * @param roleDTO 角色信息
     * @return 更新结果
     */
    @PutMapping
    public R<?> updateRole(@Validated @RequestBody RoleDTO roleDTO) {
        roleService.updateById(RoleConvert.INSTANCE.toEntity(roleDTO));
        return R.ok();
    }

    /**
     * 删除角色
     *
     * @param id 角色ID
     * @return 删除结果
     */
    @DeleteMapping("/{id}")
    public R<?> deleteRole(@PathVariable("id") Long id) {
        if (!roleService.removeById(id)) {
            throw exception(ResultCode.ROLE_DELETE_ERROR);
        }
        return R.ok();
    }

    /**
     * 获取角色详情
     *
     * @param id 角色ID
     * @return 角色信息
     */
    @GetMapping("/{id}")
    public R<Role> getRoleById(@PathVariable("id") Long id) {
        Role role = roleService.getById(id);
        if (role == null) {
            throw exception(ResultCode.ROLE_NOT_FOUND);
        }
        return R.ok(role);
    }

    /**
     * 分页查询角色列表
     *
     * @param query 查询条件
     * @return 角色列表
     */
    @PostMapping("/list")
    public R<IPage<RoleVO>> listRoles(@Validated @RequestBody RoleQueryDTO query) {
        try {
            IPage<Role> page = roleService.page(
                query.getPage(),
                Wrappers.<Role>lambdaQuery()
                    .like(StringUtils.isNotBlank(query.getTitle()), Role::getName, query.getTitle())
                    .like(StringUtils.isNotBlank(query.getCode()), Role::getCode, query.getCode())
                    .eq(query.getStatus() != null, Role::getStatus, query.getStatus())
                        .orderByAsc(Role::getSort)
            );
            
            IPage<RoleVO> voPage = page.convert(RoleConvert.INSTANCE::toVO);
            return R.ok(voPage);
        } catch (Exception e) {
            log.error("查询角色列表失败", e);
            return R.fail(-1, "查询角色列表失败：" + e.getMessage());
        }
    }

    /**
     * 更新角色状态
     *
     * @param dto 角色状态更新信息
     * @return 更新结果
     */
    @PutMapping("/status")
    public R<Void> updateRoleStatus(@RequestBody RoleStatusUpdateDTO dto) {
        roleService.updateById(RoleConvert.INSTANCE.toEntity(dto));
        return R.ok();
    }

    /**
     * 更新角色菜单权限
     *
     * @param data 角色菜单更新信息
     * @return 更新结果
     */
    @PutMapping("/menu")
    public R<Void> updateRoleMenu(@Validated @RequestBody RoleMenuUpdateDTO data) {
        roleMenuService.updateMenu(data);
        return R.ok();
    }
}
