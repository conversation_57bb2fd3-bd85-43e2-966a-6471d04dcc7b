package com.hnumi.obe.system.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hnumi.obe.common.entity.R;
import com.hnumi.obe.common.entity.ResultCode;
import com.hnumi.obe.common.util.CollectionUtil;
import com.hnumi.obe.system.dto.MenuDTO;
import com.hnumi.obe.system.dto.MenuQueryDTO;
import com.hnumi.obe.system.vo.MenuOptionVO;
import com.hnumi.obe.system.vo.MenuTreeVO;
import com.hnumi.obe.system.vo.MenuVO;
import com.hnumi.obe.system.entity.Menu;
import com.hnumi.obe.system.entity.RoleMenu;
import com.hnumi.obe.system.mapstruct.MenuConvert;
import com.hnumi.obe.system.service.IMenuService;
import com.hnumi.obe.system.service.IRoleMenuService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

import static com.hnumi.obe.common.exception.ServiceExceptionUtil.exception;

/**
 * 菜单控制器
 * 处理菜单相关的请求，包括菜单的增删改查、树形结构构建等
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@RestController
@RequestMapping("/menu")
public class MenuController {
    
    @Autowired
    private IMenuService menuService;
    
    @Autowired
    private IRoleMenuService roleMenuService;

    /**
     * 分页查询菜单列表
     *
     * @param query 查询条件
     * @return 菜单列表
     */
    @PostMapping("/list")
    public R<List<MenuTreeVO>> listMenus(@Validated @RequestBody(required = false) MenuQueryDTO query) {
        return R.ok(menuService.listTree());
    }

    /**
     * 新增菜单
     *
     * @param menu 菜单信息
     * @return 新增结果
     */
    @PostMapping
    public R<Void> addMenu(@Validated @RequestBody MenuDTO menu) {
        Menu entity = MenuConvert.INSTANCE.toEntity(menu);
        if (!menuService.save(entity)) {
            throw exception(ResultCode.MENU_ADD_ERROR);
        }
        return R.ok();
    }

    /**
     * 更新菜单
     *
     * @param menu 菜单信息
     * @return 更新结果
     */
    @PutMapping
    public R<Void> updateMenu(@Validated @RequestBody MenuDTO menu) {
        if (!menuService.updateById(MenuConvert.INSTANCE.toEntity(menu))) {
            throw exception(ResultCode.MENU_UPDATE_ERROR);
        }
        return R.ok();
    }

    /**
     * 根据角色ID获取菜单ID列表
     *
     * @param id 角色ID
     * @return 菜单ID列表
     */
    @GetMapping("/list/role/{id}")
    public R<List<Long>> listMenuIdByRoleId(@PathVariable("id") Long id) {
        LambdaQueryWrapper<RoleMenu> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(RoleMenu::getRoleId, id);
        return R.ok(CollectionUtil.convert(roleMenuService.list(wrapper), RoleMenu::getMenuId));
    }

    /**
     * 删除菜单
     *
     * @param id 菜单ID
     * @return 删除结果
     */
    @DeleteMapping("/{id}")
    public R<Void> deleteMenu(@PathVariable("id") Long id) {
        if (!menuService.deleteById(id)) {
            throw exception(ResultCode.MENU_DELETE_ERROR);
        }
        return R.ok();
    }

    /**
     * 批量删除菜单
     *
     * @param requestBody 包含菜单ID列表的请求体 {"ids": [1, 2, 3]}
     * @return 删除结果
     */
    @DeleteMapping
    public R<Void> batchDeleteMenu(@RequestBody Map<String, List<Long>> requestBody) {
        List<Long> ids = requestBody.get("ids");
        if (!menuService.deleteByIds(ids)) {
            throw exception(ResultCode.MENU_DELETE_ERROR);
        }
        return R.ok();
    }

    /**
     * 获取菜单详情
     *
     * @param id 菜单ID
     * @return 菜单信息
     */
    @GetMapping("/{id}")
    public R<Menu> getMenuById(@PathVariable("id") Long id) {
        Menu menu = menuService.getById(id);
        if (menu == null) {
            throw exception(ResultCode.MENU_NOT_FOUND);
        }
        return R.ok(menu);
    }

    /**
     * 获取菜单树形选项
     *
     * @return 菜单树形选项列表
     */
    @GetMapping("/list/tree")
    public R<List<MenuOptionVO>> listTreeOption() {
        return R.ok(menuService.listTreeOption());
    }

    /**
     * 获取路由菜单树形结构
     *
     * @return 路由菜单树形结构列表
     */
    @GetMapping("/route/list")
    public R<List<MenuTreeVO>> getTreeRoute() {
        return R.ok(menuService.listRoute());
    }

    /**
     * 更新菜单状态
     *
     * @param requestBody 包含菜单ID和状态的请求体 {"id": 1, "status": 0}
     * @return 更新结果
     */
    @PutMapping("/status")
    public R<Void> updateMenuStatus(@RequestBody Map<String, Object> requestBody) {
        Long id = Long.valueOf(requestBody.get("id").toString());
        Integer status = Integer.valueOf(requestBody.get("status").toString());
        
        if (!menuService.updateMenuStatus(id, status)) {
            throw exception(ResultCode.MENU_UPDATE_ERROR);
        }
        return R.ok();
    }
}
