package com.hnumi.obe.system.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hnumi.obe.common.entity.R;
import com.hnumi.obe.common.entity.ResultCode;
import com.hnumi.obe.common.util.StringUtil;
import com.hnumi.obe.system.dto.DictTypeDTO;
import com.hnumi.obe.system.dto.DictTypeQueryDTO;
import com.hnumi.obe.system.entity.DictType;
import com.hnumi.obe.system.mapstruct.DictTypeConvert;
import com.hnumi.obe.system.service.IDictTypeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;

/**
* 字典类型表 前端控制器
*
*/
@RestController
@RequestMapping("/dict/type")
@Slf4j
public class DictTypeController {
    @Autowired
    IDictTypeService dictTypeService;

    /**
     * 获取字典类型列表
     */
    @GetMapping("/list/all")
    public R<List<DictType>> list() {
        // 构建查询条件，只查询status=0的启用类型
        LambdaQueryWrapper<DictType> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(DictType::getStatus, 0);  // 只查询启用状态的类型
        
        List<DictType> list = dictTypeService.list(wrapper);
        return R.ok(list);
    }

    /**
     * 获取字典类型列表
     */
    @PostMapping("/list/page")
    public R<?> listPage(@RequestBody DictTypeQueryDTO dto) {
        // 构建查询条件，只查询status=0的启用类型
        LambdaQueryWrapper<DictType> wrapper = Wrappers.lambdaQuery();
        wrapper.like(StringUtil.isNotBlank(dto.getTitle()), DictType::getTitle, dto.getTitle())
                .like(StringUtil.isNotBlank(dto.getRemark()), DictType::getRemark, dto.getRemark())
                .eq(!Objects.isNull(dto.getStatus()),DictType::getStatus, dto.getStatus());  // 只查询启用状态的类型
        Page<DictType> page = dto.getPage();
        List<DictType> list = dictTypeService.list(page,wrapper);
        return R.ok(page.setRecords(list));
    }

    @PostMapping
    public R<?> addDictType(@RequestBody DictTypeDTO dictType) {
        //SAAS管理员tid是可以传输
        //普通管理员需要后台设置tid
        DictType type = DictTypeConvert.INSTANCE.toEntity(dictType);
        log.info(type.toString());
        log.info(dictType.toString());
        boolean flag = dictTypeService.save(type);
        return flag ? R.ok("添加字典类型成功") : R.fail(ResultCode.DICT_TYPE_ADD_ERROR);
    }

    @PutMapping
    public boolean updateDictType(@RequestBody DictTypeDTO dictType) {
        return dictTypeService.updateById(DictTypeConvert.INSTANCE.toEntity(dictType));
    }

    @DeleteMapping("/{id}")
    public R<?> deleteDictTypeById(@PathVariable("id") Long id) {
        boolean flag = dictTypeService.deleteById(id);
        return flag ? R.ok("删除字典类型成功") : R.fail(ResultCode.DICT_TYPE_ADD_ERROR);
    }
}
