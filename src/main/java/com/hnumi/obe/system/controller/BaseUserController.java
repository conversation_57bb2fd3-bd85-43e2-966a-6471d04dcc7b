package com.hnumi.obe.system.controller;

import cn.dev33.satoken.session.SaSession;
import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hnumi.obe.common.CommonProperties;
import com.hnumi.obe.common.Constants;
import com.hnumi.obe.common.entity.R;
import com.hnumi.obe.common.entity.ResultCode;
import com.hnumi.obe.common.enums.SmsTemplateCode;
import com.hnumi.obe.common.util.ExcelUtil;
import com.hnumi.obe.common.util.RedisUtil;
import com.hnumi.obe.common.util.SmsUtil;
import com.hnumi.obe.common.valid.ValidGroup;
import com.hnumi.obe.system.dto.LoginUserDTO;
import com.hnumi.obe.system.dto.RegisterUserDTO;
import com.hnumi.obe.system.dto.UserDTO;
import com.hnumi.obe.system.dto.UserImportDTO;
import com.hnumi.obe.system.dto.UserQueryDTO;
import com.hnumi.obe.system.dto.UserUpdateSafeDTO;
import com.hnumi.obe.system.dto.UserRoleAssignDTO;
import com.hnumi.obe.system.dto.BatchDeleteDTO;
import com.hnumi.obe.system.entity.BaseUser;
import com.hnumi.obe.system.mapstruct.UserConvert;
import com.hnumi.obe.system.service.IBaseUserService;
import com.hnumi.obe.common.entity.LoginSessionVO;
import com.hnumi.obe.system.vo.LoginUserVO;
import com.hnumi.obe.system.vo.LoginVO;
import com.hnumi.obe.system.vo.UserVO;
import org.springframework.beans.factory.annotation.Autowired;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

import static com.hnumi.obe.common.exception.ServiceExceptionUtil.exception;

/**
 * 用户管理
 * 处理用户相关的请求，包括登录、注册、信息管理等
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Slf4j
@RestController
@RequestMapping("/user")
public class BaseUserController {
    @Autowired
    private IBaseUserService baseUserService;


    /**
     * 用户登录
     *
     * @param loginUserDTO 登录信息
     * @return 登录结果
     */
    @PostMapping("/login")
    public R<LoginVO> login(@Validated @RequestBody LoginUserDTO loginUserDTO) {
        // 执行用户登录验证
        BaseUser user = baseUserService.login(loginUserDTO);
        
        // 创建Sa-Token登录会话
        StpUtil.login(user.getId());
        
        // 构建登录会话信息
        LoginSessionVO loginSession = baseUserService.buildLoginSession(user);
        if (loginSession == null) {
            throw exception(ResultCode.LOGIN_USER_NOT_FOUND);
        }
        
        // 验证会话信息完整性
        if (!loginSession.isValid()) {
            log.error("登录会话信息不完整，userId={}", user.getId());
            throw exception(ResultCode.LOGIN_USER_NOT_FOUND);
        }
        
        // 将登录会话信息存储到Sa-Token会话中
        SaSession session = StpUtil.getSession();
        session.set(Constants.LOGIN_USER_SESSION_KEY, loginSession);
        
        return R.ok(new LoginVO(StpUtil.getTokenValue()));
    }

    /**
     * 导入用户
     * 这里要注意，所有批量导入用户均可使用该方法，但是该方法导入的用户不具备任何权限，需要分配角色方可使用
     * 分配教师角色，则需要补充教师信息
     * 分配学生角色，则需要补充学生信息
     * 也可以单独写新的接口，用于直接导入教师或学生信息，只不过需要提取所需要的信息存放到base_user或者teacher/student表中
     *
     * @param file Excel文件
     * @return 导入结果
     */
    @PostMapping("/import")
    public R<?> importUsers(@RequestParam("file") MultipartFile file) throws IOException {
        List<UserImportDTO> users = ExcelUtil.readAll(file.getInputStream(), UserImportDTO.class);
        List<UserImportDTO> result = baseUserService.importUsers(users, false);
        
        // 获取导入结果标记
        String importFlag = result.getFirst().getMessage();
        if ("IMPORT_SUCCESS".equals(importFlag)) {
            return R.ok("导入成功");
        } else {
            // 获取实际导入的数据（跳过第一个标记元素）
            List<UserImportDTO> actualData = result.subList(1, result.size());
            // 获取导入失败的数据
            List<UserImportDTO> failData = actualData.stream()
                .filter(dto -> !"导入成功".equals(dto.getMessage()))
                .collect(Collectors.toList());
            
            return R.fail(ResultCode.USER_IMPORT_ERROR, failData);
        }
    }

    /**
     * 获取当前登录用户信息
     *
     * @return 用户信息
     */
    @GetMapping("/info")
    public R<LoginUserVO> getUserInfo() {
        long uid = StpUtil.getLoginIdAsLong();
        BaseUser user = baseUserService.getById(uid);
        if (user == null) {
            throw exception(ResultCode.USER_NOT_FOUND);
        }
        LoginUserVO vo = new LoginUserVO();
        vo.setId(uid);
        vo.setAvatar(user.getAvatar());
        vo.setUsername(user.getRealName());
        vo.setRoles(StpUtil.getRoleList());
        vo.setPermissions(StpUtil.getPermissionList());
        return R.ok(vo);
    }

    /**
     * 用户注册
     *
     * @param registerUserDTO 注册信息
     * @return 注册结果
     */
    @PostMapping("/register")
    public R<LoginVO> register(@Validated @RequestBody RegisterUserDTO registerUserDTO) {
        BaseUser user = baseUserService.register(registerUserDTO);
        StpUtil.login(user.getId());
        return R.ok(new LoginVO(StpUtil.getTokenValue()));
    }

    /**
     * 获取注册验证码
     *
     * @param phone 手机号
     * @return 发送结果
     */
    @GetMapping("/register/phone/code")
    public R<String> getRegisterPhoneCode(@RequestParam String phone) {
        String key = CommonProperties.REDIS_KEY_PREFIX_PHONE_CODE + ":register:" + phone;
        if (RedisUtil.getExpire(key) == -2) {
            String code = SmsUtil.getPhoneCode();
            SmsUtil.sendCode(phone, code, SmsTemplateCode.REGISTER);
            RedisUtil.set(key, code, 60 * 2);
            return R.ok("发送验证码成功");
        }
        throw exception(ResultCode.PHONE_CODE_REGISTER_SEND_ERROR);
    }

    /**
     * 获取修改信息验证码
     *
     * @param phone 手机号
     * @return 发送结果
     */
    @GetMapping("/update/phone/code")
    public R<String> getUpdatePhoneCode(@RequestParam String phone) {
        String key = CommonProperties.REDIS_KEY_PREFIX_PHONE_CODE + ":update:" + phone;
        if (RedisUtil.getExpire(key) == -2) {
            String code = SmsUtil.getPhoneCode();
            SmsUtil.sendCode(phone, code, SmsTemplateCode.REGISTER);
            RedisUtil.set(key, code, 60 * 100);
            return R.ok("发送验证码成功");
        }
        throw exception(ResultCode.PHONE_CODE_REGISTER_SEND_ERROR);
    }

    /**
     * 更新用户安全信息
     *
     * @param id 用户ID
     * @param userUpdateSafeDTO 更新信息
     * @return 更新结果
     */
    @PutMapping("/safe/info/{id}")
    public R<Void> updateSafeInfo(@PathVariable(value = "id") Long id, 
                                 @Validated @RequestBody UserUpdateSafeDTO userUpdateSafeDTO) {
        if (!baseUserService.updateSafeInfo(id, userUpdateSafeDTO)) {
            throw exception(ResultCode.USER_UPDATE_ERROR);
        }
        return R.ok();
    }


    /**
     * 用户退出登录
     *
     * @return 退出结果
     */
    @GetMapping("/logout")
    public R<String> logout() {
        StpUtil.logout();
        return R.ok("退出成功");
    }

    /**
     * 分页查询用户列表
     *
     * @param query 查询条件
     * @return 用户列表
     */
    @PostMapping("/list")
    public R<IPage<UserVO>> listUsers(@Validated @RequestBody UserQueryDTO query) {
        return R.ok(baseUserService.listUsers(query));
    }

    /**
     * 新增用户
     *
     * @param dto 用户信息
     * @return 新增结果
     */
    @PostMapping
    public R<Void> addUser(@Validated @RequestBody UserDTO dto) {
        if (!baseUserService.save(UserConvert.INSTANCE.toEntity(dto))) {
            throw exception(ResultCode.USER_ADD_ERROR);
        }
        return R.ok();
    }

    /**
     * 删除用户
     *
     * @param id 用户ID
     * @return 删除结果
     */
    @DeleteMapping("/{id}")
    public R<Void> deleteUser(@PathVariable Long id) {
        if (!baseUserService.deleteById(id)) {
            throw exception(ResultCode.USER_DELETE_ERROR);
        }
        return R.ok();
    }

    /**
     * 获取用户详情
     *
     * @param id 用户ID
     * @return 用户信息
     */
    @GetMapping("/{id}")
    public R<UserVO> getUserById(@PathVariable Long id) {
        return R.ok(baseUserService.getUserDetail(id));
    }

    /**
     * 更新用户信息
     *
     * @param userDTO 用户信息
     * @return 更新结果
     */
    @PutMapping()
    public R<Void> updateUser(@Validated(ValidGroup.Update.class) @RequestBody UserDTO userDTO) {
        if (!baseUserService.updateById(UserConvert.INSTANCE.toEntity(userDTO))) {
            throw exception(ResultCode.USER_UPDATE_ERROR);
        }
        return R.ok();
    }

    /**
     * 封禁/解封用户
     *
     * @param id 用户ID
     * @return 操作结果
     */
    @PutMapping("/block/{id}")
    public R<Void> blockUser(@PathVariable(value = "id") Long id) {
        if (!baseUserService.block(id)) {
            throw exception(ResultCode.USER_BLOCK_ERROR);
        }
        return R.ok();
    }

    /**
     * 分配用户角色
     *
     * @param userRoleAssignDTO 角色分配信息
     * @return 分配结果
     */
    @PutMapping("/roles")
    public R<Void> assignRoles(@Validated @RequestBody UserRoleAssignDTO userRoleAssignDTO) {
        if (!baseUserService.assignRoles(userRoleAssignDTO)) {
            throw exception(ResultCode.USER_ROLE_ASSIGN_ERROR);
        }
        return R.ok();
    }

    /**
     * 重置用户密码
     *
     * @param id 用户ID
     * @return 重置结果
     */
    @PutMapping("/reset-password/{id}")
    public R<Void> resetPassword(@PathVariable("id") Long id) {
        if (!baseUserService.resetPassword(id)) {
            throw exception(ResultCode.USER_PASSWORD_RESET_ERROR);
        }
        return R.ok();
    }

    /**
     * 批量删除用户
     *
     * @param batchDeleteDTO 批量删除信息
     * @return 删除结果
     */
    @DeleteMapping("/batch")
    public R<Void> batchDeleteUsers(@Validated @RequestBody BatchDeleteDTO batchDeleteDTO) {
        if (!baseUserService.batchDelete(batchDeleteDTO)) {
            throw exception(ResultCode.USER_BATCH_DELETE_ERROR);
        }
        return R.ok();
    }
}
