package com.hnumi.obe.system.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hnumi.obe.common.entity.R;
import com.hnumi.obe.system.dto.DictDataDTO;
import com.hnumi.obe.system.vo.DictDataVO;
import com.hnumi.obe.system.entity.DictData;
import com.hnumi.obe.system.mapstruct.DictDataConvert;
import com.hnumi.obe.system.service.IDictDataService;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
* 字典数据表 前端控制器
*/
@RestController
@RequestMapping("/dict/data")
public class DictDataController {
    @Autowired
    IDictDataService dictDataService;

    @PostMapping
    public R<Boolean> addDictData(@RequestBody DictDataDTO dictDataDTO) {
        DictData dictData = DictDataConvert.INSTANCE.toEntity(dictDataDTO);
        boolean success = dictDataService.save(dictData);
        return R.ok(success);
    }

    @PutMapping
    public R<Boolean> updateDictData(@RequestBody DictDataDTO dictDataDTO) {
        DictData dictData = DictDataConvert.INSTANCE.toEntity(dictDataDTO);
        boolean success = dictDataService.updateById(dictData);
        return R.ok(success);
    }

    @DeleteMapping("/{id}")
    public R<Boolean> deleteDictDataById(@PathVariable("id") Long id) {
        boolean success = dictDataService.removeById(id);
        return R.ok(success);
    }

    @GetMapping("/{id}")
    public R<DictDataVO> getDictData(@PathVariable("id") Long id) {
        DictData dictData = dictDataService.getById(id);
        DictDataVO dictDataVO = DictDataConvert.INSTANCE.toVO(dictData);
        return R.ok(dictDataVO);
    }

    @GetMapping("/type/{id}")
    public R<IPage<DictDataVO>> listDictDataByTypeId(
            @PathVariable("id") Long id,
            @RequestParam(value = "current", defaultValue = "1") Long current,
            @RequestParam(value = "size", defaultValue = "10") Long size) {
        
        // 创建分页对象
        Page<DictData> page = new Page<>(current, size);
        
        // 构建查询条件，只查询status=0的启用数据
        LambdaQueryWrapper<DictData> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(DictData::getTypeId, id)
                .eq(DictData::getStatus, 0)  // 只查询启用状态的数据
                .orderByAsc(DictData::getSort);
        
        // 执行分页查询
        IPage<DictData> dictDataPage = dictDataService.page(page, wrapper);
        
        // 转换为VO对象
        IPage<DictDataVO> dictDataVOPage = dictDataPage.convert(DictDataConvert.INSTANCE::toVO);
        
        return R.ok(dictDataVOPage);
    }
    @GetMapping("/type/all/{id}")
    public R<List<DictDataVO>> listAllDictDataByTypeId(@PathVariable("id") Long id) {
        // 构建查询条件，只查询status=0的启用数据
        LambdaQueryWrapper<DictData> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(DictData::getTypeId, id)
                .eq(DictData::getStatus, 0)  // 只查询启用状态的数据
                .orderByAsc(DictData::getSort);

        // 执行查询
        List<DictData> dictDataPage = dictDataService.list(wrapper);

        // 转换为VO对象
        List<DictDataVO> dictDataVO = DictDataConvert.INSTANCE.toVO(dictDataPage);

        return R.ok(dictDataVO);
    }
    @GetMapping("/list/all")
    public R<List<DictDataVO>> listAllDictData() {
        // 构建查询条件，只查询status=0的启用数据
        LambdaQueryWrapper<DictData> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(DictData::getStatus, 0)  // 只查询启用状态的数据
                .orderByAsc(DictData::getSort);
        
        List<DictData> dictDataPage = dictDataService.list(wrapper);
        // 转换为VO对象
        List<DictDataVO> dictDataVO = DictDataConvert.INSTANCE.toVO(dictDataPage);
        return R.ok(dictDataVO);
    }
}
