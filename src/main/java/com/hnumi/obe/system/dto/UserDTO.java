package com.hnumi.obe.system.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hnumi.obe.common.valid.annotation.Unique;
import com.hnumi.obe.common.valid.ValidGroup;
import com.hnumi.obe.system.entity.BaseUser;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDate;

@Data
public class UserDTO {
    private String id;
    
    @NotBlank(message = "手机号不能为空", groups = {ValidGroup.Add.class})
    private String phone;
    
    @NotBlank(message = "邮箱不能为空", groups = {ValidGroup.Add.class})
    private String email;
    
    private String username;
    
    private String realName;
    
    @NotNull(message = "性别不能为空", groups = {ValidGroup.Add.class})
    private Integer gender;
    
    private String introduction;
    
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate birthday;
}
