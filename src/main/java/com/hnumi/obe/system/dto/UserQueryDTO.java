package com.hnumi.obe.system.dto;

import com.hnumi.obe.common.entity.BasePage;
import com.hnumi.obe.system.entity.BaseUser;
import lombok.Data;
import lombok.EqualsAndHashCode;


@EqualsAndHashCode(callSuper = true)
@Data
public class UserQueryDTO extends BasePage<BaseUser> {
    private String phone;
    private String email;
    private String username;
    private String realName;
    private Integer gender;
    private Integer status;
}
