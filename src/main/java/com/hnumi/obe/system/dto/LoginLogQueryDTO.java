package com.hnumi.obe.system.dto;

import com.hnumi.obe.common.entity.BasePage;
import com.hnumi.obe.system.entity.LoginLog;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 登录日志查询DTO
 *
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class LoginLogQueryDTO extends BasePage<LoginLog> {
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 登录IP
     */
    private String loginIp;
    
    /**
     * 登录状态：0表示失败，1表示成功
     */
    private Integer status;
    
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
} 