package com.hnumi.obe.system.dto;

import com.hnumi.obe.common.entity.BasePage;
import com.hnumi.obe.system.entity.Role;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class RoleQueryDTO extends BasePage<Role> {
    /**
     * 角色名称
     */
    private String title;
    
    /**
     * 角色标识
     */
    private String code;
    
    /**
     * 状态：0-启用，1-禁用
     */
    private Integer status;
}
