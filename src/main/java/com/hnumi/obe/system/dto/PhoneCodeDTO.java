package com.hnumi.obe.system.dto;

import com.hnumi.obe.common.valid.ValidGroup;
import com.hnumi.obe.common.valid.annotation.Enum;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class PhoneCodeDTO {
    @NotBlank(message = "手机号不能为空", groups = {ValidGroup.Add.class})
    private String phone;
    
    @NotNull(message = "获取验证码的类型不能为空", groups = {ValidGroup.Add.class})
    @Enum(value = {"1", "2"}, message = "验证码类型不合规范", groups = {ValidGroup.Add.class})
    private Integer type;
    
    @NotBlank(message = "验证码不能为空", groups = {ValidGroup.ValidCode.class})
    private String code;
}
