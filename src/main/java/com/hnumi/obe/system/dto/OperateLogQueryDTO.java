package com.hnumi.obe.system.dto;

import com.hnumi.obe.common.entity.BasePage;
import com.hnumi.obe.system.entity.OperateLog;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 操作日志查询DTO
 *
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OperateLogQueryDTO extends BasePage<OperateLog> {
    
    /**
     * 操作用户
     */
    private String username;
    
    /**
     * 业务名称
     */
    private String businessName;
    
    /**
     * 请求方法
     */
    private String requestMethod;
    
    /**
     * 用户IP
     */
    private String requestIp;
    
    /**
     * 操作状态：0表示失败，1表示成功
     */
    private Integer status;
    
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
} 