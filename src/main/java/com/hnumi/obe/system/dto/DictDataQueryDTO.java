package com.hnumi.obe.system.dto;

import com.hnumi.obe.common.entity.BasePage;
import com.hnumi.obe.system.entity.DictData;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 字典数据查询DTO
 *
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DictDataQueryDTO extends BasePage<DictData> {
    
    /**
     * 字典类型ID
     */
    private Long dictTypeId;
    
    /**
     * 字典标签
     */
    private String dictLabel;
    
    /**
     * 字典键值
     */
    private String dictValue;
    
    /**
     * 状态：0表示禁用，1表示启用
     */
    private Integer status;
} 