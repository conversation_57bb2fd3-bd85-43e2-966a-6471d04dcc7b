package com.hnumi.obe.system.dto;

import com.hnumi.obe.common.valid.enums.OperateEnum;
import com.hnumi.obe.common.valid.annotation.Unique;
import com.hnumi.obe.common.valid.annotation.Enum;
import com.hnumi.obe.common.valid.ValidGroup;
import com.hnumi.obe.common.annotation.UniqueField;
import com.hnumi.obe.common.annotation.UniqueId;
import com.hnumi.obe.system.entity.Menu;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

//@Unique(clazz = Menu.class, message = "菜单组件名已存在", operate = OperateEnum.UPDATE)
@Data
public class MenuDTO {
    @NotNull(message = "菜单ID不能为空", groups = {ValidGroup.Update.class})
    //@UniqueId
    private Long id;
    
    private Long pid;
    private Long sort;
    private String code;
    
    @NotNull(message = "菜单类型不能为空", groups = {ValidGroup.Add.class})
    @Enum(value = {"0", "1", "2"}, message = "菜单类型不合规", groups = {ValidGroup.Add.class})
    private Integer type;
    
    private Boolean hidden;
    private Boolean levelHidden;
    
    @NotNull(message = "菜单名称不能为空", groups = {ValidGroup.Add.class})
    private String title;
    
    private String icon;
    private Boolean cache;
    private String badge;
    private String target;
    private String activeMenu;
    
    //@UniqueField
    @NotNull(message = "组件名不能为空", groups = {ValidGroup.Add.class})
    private String name;
    
    @NotNull(message = "路由地址不能为空", groups = {ValidGroup.Add.class})
    private String path;
    
    private String component;
    private String redirect;
}
