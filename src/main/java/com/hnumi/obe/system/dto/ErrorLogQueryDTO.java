package com.hnumi.obe.system.dto;

import com.hnumi.obe.common.entity.BasePage;
import com.hnumi.obe.system.entity.ErrorLog;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 异常日志查询DTO
 *
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ErrorLogQueryDTO extends BasePage<ErrorLog> {
    
    /**
     * 操作用户
     */
    private String username;
    
    /**
     * 业务名称
     */
    private String businessName;
    
    /**
     * 请求方法
     */
    private String requestMethod;
    
    /**
     * 用户IP
     */
    private String requestIp;
} 