package com.hnumi.obe.system.dto;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 登录日志DTO
 *
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Data
public class LoginLogDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 日志ID
     */
    private Long id;


    /**
     * 用户名
     */
    private String username;

    /**
     * 登录IP
     */
    private String loginIp;

    /**
     * IP所属城市
     */
    private String ipLocationCity;

    /**
     * IP所属省份
     */
    private String ipLocationProvince;

    /**
     * IP所属国家
     */
    private String ipLocationCountry;

    /**
     * 登录时间
     */
    private LocalDateTime loginTime;

    /**
     * 登录状态：0表示失败，1表示成功
     */
    private Integer status;

    /**
     * 失败原因
     */
    private String failReason;
} 