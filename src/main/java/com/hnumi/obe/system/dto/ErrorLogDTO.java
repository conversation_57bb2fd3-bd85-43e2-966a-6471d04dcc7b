package com.hnumi.obe.system.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 异常日志DTO
 *
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Data
public class ErrorLogDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 日志ID
     */
    private Long id;

    /**
     * 操作用户
     */
    private String username;

    /**
     * 业务名称
     */
    private String businessName;

    /**
     * 请求参数
     */
    private String requestParam;

    /**
     * 请求方法
     */
    private String requestMethod;

    /**
     * 请求地址
     */
    private String requestUri;

    /**
     * 用户IP
     */
    private String requestIp;

    /**
     * IP所属城市
     */
    private String ipLocationCity;

    /**
     * IP所属省份
     */
    private String ipLocationProvince;

    /**
     * IP所属国家
     */
    private String ipLocationCountry;
} 