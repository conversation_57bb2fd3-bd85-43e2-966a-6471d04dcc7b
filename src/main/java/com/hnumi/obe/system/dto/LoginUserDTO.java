package com.hnumi.obe.system.dto;

import com.hnumi.obe.common.valid.ValidGroup;
import com.hnumi.obe.common.valid.annotation.Enum;
import com.hnumi.obe.common.valid.annotation.Phone;
import com.hnumi.obe.system.entity.BaseUser;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class LoginUserDTO {
    @NotBlank(message = "用户名不能为空")
    private String username;
    
    @NotBlank(message = "密码不能为空")
    private String password;
    
    @NotNull(message = "登录类型不能为空")
    @Enum(value = {"1", "2"}, message = "登录类型错误")
    private Integer type;
}
