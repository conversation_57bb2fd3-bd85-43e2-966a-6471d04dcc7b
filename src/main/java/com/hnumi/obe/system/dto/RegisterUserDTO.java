package com.hnumi.obe.system.dto;

import com.hnumi.obe.common.valid.ValidGroup;
import com.hnumi.obe.common.valid.annotation.Unique;
import com.hnumi.obe.system.entity.BaseUser;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@Data
public class RegisterUserDTO {
    @NotBlank(message = "用户名不能为空", groups = {ValidGroup.Add.class})
    @Unique(message = "用户名已被占用", value = "username", clazz = BaseUser.class, groups = {ValidGroup.Add.class})
    private String username;
    
    @NotBlank(message = "密码不能为空", groups = {ValidGroup.Add.class})
    private String password;
    
    @NotBlank(message = "手机号不能为空", groups = {ValidGroup.Add.class})
    @Unique(message = "手机号已被注册", value = "phone", clazz = BaseUser.class, groups = {ValidGroup.Add.class})
    private String phone;
    
    @NotBlank(message = "验证码不能为空", groups = {ValidGroup.ValidCode.class})
    private String code;
}
