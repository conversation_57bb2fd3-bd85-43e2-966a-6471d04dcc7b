package com.hnumi.obe.system.dto;

import com.hnumi.obe.common.annotation.ExcelColumn;
import lombok.Data;

@Data
public class UserImportDTO {

    @ExcelColumn("姓名")
    private String name;
    @ExcelColumn("手机号")
    private String phone;
    @ExcelColumn("邮箱")
    private String email;
    @ExcelColumn("性别")
    private String gender;
    @ExcelColumn("初始化密码")
    private String password;

    @ExcelColumn("失败原因")
    private String message;

}
