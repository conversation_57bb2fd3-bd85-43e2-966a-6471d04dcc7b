package com.hnumi.obe.system.dto;

import com.hnumi.obe.common.valid.ValidGroup;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class RoleDTO {
    /**
     * 角色ID
     */
    private Long id;

    /**
     * 角色名称
     */
    @NotBlank(message = "角色名称不能为空", groups = {ValidGroup.Add.class})
    private String name;

    /**
     * 角色码
     */
    @NotBlank(message = "角色码不能为空", groups = {ValidGroup.Add.class})
    private String code;

    /**
     * 角色排序
     */
    private Integer sort;

    /**
     * 角色备注
     */
    private String remark;

    /**
     * 状态：0-启用，1-禁用
     */
    private Integer status;
}
