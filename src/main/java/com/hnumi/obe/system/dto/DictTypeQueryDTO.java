package com.hnumi.obe.system.dto;

import com.hnumi.obe.common.entity.BasePage;
import com.hnumi.obe.system.entity.DictType;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class DictTypeQueryDTO extends BasePage<DictType> {
    /**
     * 字典类型的名称
     */
    private String title;

    /**
     * 字典的状态：0表示正常，9表示停用
     */
    private Integer status = 0;
    /**
     * 字典类型描述
     */
    private String remark;
}
