package com.hnumi.obe.system.dto;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 字典数据DTO
 *
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Data
public class DictDataDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 字典编码
     */
    private Long id;

    /**
     * 字典类型ID
     */
    private Long typeId;

    /**
     * 字典标签
     */
    private String label;

    /**
     * 字典键值
     */
    private String value;

    /**
     * 字典排序
     */
    private Integer sort;

    /**
     * 是否默认：1表示是，0表示否
     */
    private Integer defaul;

    /**
     * 状态：0表示禁用，1表示启用
     */
    private Integer status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
} 