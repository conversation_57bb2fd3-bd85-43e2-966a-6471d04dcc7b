package com.hnumi.obe.system.dto;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * 用户角色分配DTO
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Data
public class UserRoleAssignDTO {
    
    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;
    
    /**
     * 角色ID列表
     */
    @NotNull(message = "角色ID列表不能为空")
    private List<Long> roleIds;
} 