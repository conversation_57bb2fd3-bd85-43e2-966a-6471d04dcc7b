package com.hnumi.obe.system.dto;

import com.hnumi.obe.common.valid.annotation.Unique;
import com.hnumi.obe.system.entity.BaseUser;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;



@Data
public class UserUpdateSafeDTO {
    @Unique(message = "该账户已被注册", value = "phone", clazz = BaseUser.class)
    private String username;
    private String phone;
    private Integer type;
    @NotBlank(message = "验证码不能为空")
    private String code;
}
