package com.hnumi.obe;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.generator.AutoGenerator;
import com.baomidou.mybatisplus.generator.FastAutoGenerator;
import com.baomidou.mybatisplus.generator.config.*;
import com.baomidou.mybatisplus.generator.config.builder.CustomFile;
import com.baomidou.mybatisplus.generator.config.rules.DateType;
import com.baomidou.mybatisplus.generator.config.rules.NamingStrategy;
import com.baomidou.mybatisplus.generator.engine.FreemarkerTemplateEngine;
import com.baomidou.mybatisplus.generator.fill.Column;
import com.baomidou.mybatisplus.generator.fill.Property;
import com.baomidou.mybatisplus.generator.keywords.MySqlKeyWordsHandler;
import org.apache.ibatis.annotations.Mapper;

import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.util.*;

/**
 * MyBatis-Plus 代码生成器
 * 功能说明：
 * 1. 自动生成实体类（Entity）
 * 2. 自动生成数据访问层（Mapper）
 * 3. 自动生成服务层（Service）
 * 4. 自动生成控制器（Controller）
 * 5. 自动生成数据传输对象（DTO）
 * 6. 自动生成视图对象（VO）
 * 7. 自动生成对象转换器（Convert）
 * 使用说明：
 * 1. 配置数据库连接信息（url、username、password）
 * 2. 配置生成参数（modulePath、moduleName、author等）
 * 3. 配置需要生成的表（tables）
 * 4. 运行main方法即可生成代码
 * 注意事项：
 * 1. 确保数据库连接信息正确
 * 2. 确保数据库中存在指定的表
 * 3. 生成代码前请备份已有代码
 * 4. 生成后可能需要手动调整部分代码
 */
public class MyBatisPlusCodeGen {
    private static final String url = "**************************************************************"; //数据库连接URL。格式：jdbc:mysql://主机:端口/数据库名?参数
    private static final String databaseName = "obe"; //数据库名称
    private static final String username = "root"; //数据库用户名
    private static final String password = "121384guoli"; //数据库密码
    private static final String modulePath = ""; //模块路径:针对子项目，如果是单个主项目，则写成""即可
    private static final String moduleName = "tp"; //模块名称
    private static final String author = ""; //作者信息,如果为空则使用系统用户名
    private static final String parentPackage = "com.hnumi.obe"; //父包名:生成的代码将放在此包+moduleName下
    private static final boolean enableAllTables = false; //是否生成所有表。true：生成所有表，false：只生成tables中指定的表
    //需要生成的表名列表：false：只生成tables中指定的表
    private static final List<String> tables = Arrays.asList("tp_course");
    private static final List<String> tablePrefix = Arrays.asList("tp_");

    /*
      用于获取数据库中的所有表名
     */
    static {
        List<String> allTables = new ArrayList<>();
        try {
            // 获取数据库连接
            Connection conn = DriverManager.getConnection(url, username, password);
            // 获取数据库元数据
            DatabaseMetaData md = conn.getMetaData();
            // 获取所有表名
            ResultSet rs = md.getTables(conn.getCatalog(), null, null, null);
            while (rs.next()) {
                allTables.add(rs.getString(3));
            }
            System.out.println("当前数据库中的表有：" + allTables);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        // 如果tables为空且enableAllTables为true，则生成所有表
        if (tables.isEmpty() && enableAllTables) {
            tables.addAll(allTables);
        }
    }

    /**
     * 代码生成器入口方法
     *
     *
     * @param args 命令行参数
     */
    public static void main(String[] args) {
        // 配置数据源
        DataSourceConfig dsc = new DataSourceConfig.Builder(url, username, password)
                .schema(databaseName)
                .keyWordsHandler(new MySqlKeyWordsHandler())
                .build();

        // 获取项目路径
        String projectPath = System.getProperty("user.dir");
        
        // 创建代码生成器
        FastAutoGenerator.create(url, username, password)
                .globalConfig(
                        builder -> builder.outputDir(projectPath + "/" + modulePath + "/src/main/java")
                                .author(StringUtils.isBlank(author) ? System.getProperty("user.name"):author)
                                .dateType(DateType.TIME_PACK) // 时间策略
                                .commentDate("yyyy-MM-dd") // 注释日期
                        )
                .packageConfig(
                        builder -> builder.parent(parentPackage)
                            .moduleName(moduleName)
                            .entity("entity")
                            .service("service")
                            .serviceImpl("service.impl")
                            .mapper("mapper")
                            .xml("mapper.xml")
                            .controller("controller")
                            .pathInfo(Collections.singletonMap(OutputFile.xml, projectPath + "/" + modulePath + "/src/main/resources/mapper/" + moduleName + "/"))
                )
                .strategyConfig(
                        builder -> builder.addTablePrefix(tablePrefix)
                                .addInclude(tables)
                                // 实体类策略
                                .entityBuilder()
                                .superClass(Model.class) // 设置父类
                                .disableSerialVersionUID() // 禁用生成 serialVersionUID
                                .enableChainModel() // 开启链式模型
                                .enableLombok() // 开启 lombok 模型
                                .enableRemoveIsPrefix() // 开启 Boolean 类型字段移除 is 前缀
                                .enableTableFieldAnnotation() // 开启生成实体时生成字段注解
                                .enableActiveRecord() // 开启 ActiveRecord 模型
                                .logicDeleteColumnName("is_deleted") // 逻辑删除字段
                                .naming(NamingStrategy.underline_to_camel) // 数据库表映射到实体的命名策略
                                .columnNaming(NamingStrategy.underline_to_camel) // 数据库表字段映射到实体的命名策略
                                .addTableFills(new Column("create_time", FieldFill.INSERT)) // 创建时间自动填充
                                .addTableFills(new Property("updateTime", FieldFill.INSERT_UPDATE)) // 更新时间自动填充
                                .idType(IdType.AUTO) // 主键策略
                                .javaTemplate("/templates/entity.java")
                                // Controller策略
                                .controllerBuilder()
                                .enableHyphenStyle() // 开启驼峰转连字符
                                .enableRestStyle() // 开启生成@RestController 控制器
                                .formatFileName("%sController")
                                .template("/templates/controller.java")
                                // Service策略
                                .serviceBuilder()
                                //.formatServiceFileName("%sService")
                                .formatServiceImplFileName("%sServiceImpl")
                                .serviceImplTemplate("/templates/serviceImpl.java")
                                .serviceTemplate("/templates/service.java")
                                // Mapper策略
                                .mapperBuilder()
                                .mapperAnnotation(Mapper.class)
                                .formatMapperFileName("%sMapper")
                                .mapperTemplate("/templates/mapper.java")
                                .mapperXmlTemplate("/templates/mapper.xml")

                        )
                .injectionConfig(injectConfig -> {
                    Map<String,Object> customMap = new HashMap<>();
                    injectConfig.customMap(customMap); //注入自定义属性
                    injectConfig.customFile(new CustomFile.Builder()
                            .fileName("DTO.java") //文件名称
                            .templatePath("templates/dto.java.ftl") //指定生成模板路径
                            .packageName("dto") //包名,自3.5.10开始,可通过在package里面获取自定义包全路径,低版本下无法获取,示例:package.entityDTO
                            .build())
                            .customFile(new CustomFile.Builder()
                            .fileName("VO.java") //文件名称
                            .templatePath("templates/vo.java.ftl") //指定生成模板路径
                            .packageName("vo") //包名,自3.5.10开始,可通过在package里面获取自定义包全路径,低版本下无法获取,示例:package.entityDTO
                            .build())
                            .customFile(new CustomFile.Builder()
                            .fileName("Convert.java") //文件名称
                            .templatePath("templates/convert.java.ftl") //指定生成模板路径
                            .packageName("mapstruct") //包名,自3.5.10开始,可通过在package里面获取自定义包全路径,低版本下无法获取,示例:package.entityDTO
                            .build())
                            .customFile(new CustomFile.Builder()
                            .fileName("QueryDTO.java") //文件名称
                            .templatePath("templates/query.java.ftl") //指定生成模板路径
                            .packageName("dto") //包名,自3.5.10开始,可通过在package里面获取自定义包全路径,低版本下无法获取,示例:package.entityDTO
                            .build())
                    ;
                })
                .templateEngine(new FreemarkerTemplateEngine())
                .execute();
    }
}
