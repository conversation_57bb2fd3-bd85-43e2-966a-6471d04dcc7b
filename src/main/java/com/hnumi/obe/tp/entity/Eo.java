package com.hnumi.obe.tp.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hnumi.obe.base.entity.BaseEntity;
import com.hnumi.obe.common.annotation.ExcelColumn;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
* 培养目标表
*
*/
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("tp_eo")
public class Eo extends BaseEntity {

    /**
     * 培养目标ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 培养目标标题
     */
    @ExcelColumn("培养目标标题")
    @TableField("eo_title")
    private String eoTitle;

    /**
     * 培养目标详情
     */
    @ExcelColumn("培养目标详情")
    @TableField("eo_description")
    private String eoDescription;

    private String relatedQuestions;

    /**
     * 所属专业培养计划id
     */
    @TableField("plan_id")
    private Long planId;

    /**
     * 所属专业id（冗余）
     */
    @TableField("major_id")
    private Long majorId;

    /**
     * 所属学院id（冗余）
     */
    @TableField("academy_id")
    private Long academyId;

    /**
     * 记录状态{0:正常状态；-1:删除状态；}
     */
    @TableField("status")
    private Integer status;

}