package com.hnumi.obe.tp.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hnumi.obe.base.entity.BaseEntity;
import com.hnumi.obe.common.annotation.ExcelColumn;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
* 毕业要求表
*
*/
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("tp_po")
public class Po extends BaseEntity {

    /**
     * 专业毕业要求二级指标ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 毕业要求编号
     */
    @TableField("po_number")
    private Integer poNumber;

    /**
     * 毕业要求标题
     */
    @ExcelColumn("毕业要求标题")
    @TableField("po_title")
    private String poTitle;
    /**
     * 毕业要求详情
     */
    @ExcelColumn("毕业要求详情")
    @TableField("po_description")
    private String poDescription;

    /**
     * 该记录是否为专业培养方案的毕业要求一级指标
     */
    @ExcelColumn("是否为一级指标")
    @TableField("is_requirement")
    private Boolean isRequirement;

    /**
     * 所属工程教育认证指标id
     */
    @TableField("standard_id")
    private Long standardId;

    @TableField("parent_id")
    private Long parentId;

    /**
     * 所属专业培养计划id
     */
    @TableField("plan_id")
    private Long planId;

    /**
     * 所属专业id（冗余）
     */
    @TableField("major_id")
    private Long majorId;

    /**
     * 所属学院id（冗余）
     */
    @TableField("academy_id")
    private Long academyId;

    /**
     * 记录状态{0:正常状态；-1:删除状态；}
     */
    @TableField("status")
    private Integer status;

}