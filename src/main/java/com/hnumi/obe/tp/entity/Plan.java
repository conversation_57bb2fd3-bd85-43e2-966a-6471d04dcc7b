package com.hnumi.obe.tp.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hnumi.obe.base.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 专业培养计划表
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("tp_plan")
public class Plan extends BaseEntity {

    /**
     * 专业培养计划ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 专业培养计划版本，按照年份输入
     */
    @TableField("plan_version")
    private Long planVersion;

    /**
     * 专业培养计划标题
     */
    @TableField("plan_name")
    private String planName;

    /**
     * 所属专业id
     */
    @TableField("major_id")
    private Long majorId;

    /**
     * 所属学院id
     */
    @TableField("academy_id")
    private Long academyId;

    /**
     * 标准id
     */
    @TableField("standard_id")
    private Long standardId;

    /**
     * 记录状态{0:正常状态；-1:删除状态；}
     */
    @TableField("status")
    private Integer status;

}
