package com.hnumi.obe.tp.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hnumi.obe.base.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
* 专业培养方案中的课程体系，课程要求带有版本号
*
*/
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("tp_course")
public class Course extends BaseEntity {

    @TableId(value = "course_id", type = IdType.AUTO)
    private Long courseId;

    /**
     * 课程编码，与版本号一起是唯一的，如2025_RB7001152
     */
    @TableField("course_code")
    private String courseCode;

    /**
     * 课程名称
     */
    @TableField("course_name")
    private String courseName;

    /**
     * course_leader
     */
    @TableField("course_leader")
    private Long courseLeader;

    /**
     * 课程学分
     */
    @TableField("course_credit")
    private Integer courseCredit;

    /**
     * 是否是核心课程
     */
    @TableField("course_core")
    private Boolean courseCore;

    /**
     * 是否是考试课
     */
    @TableField("course_exam")
    private Boolean courseExam;

    /**
     * 总学学时
     */
    @TableField("course_hours_total")
    private Integer courseHoursTotal;

    /**
     * 理教学时
     */
    @TableField("course_hours_theory")
    private Integer courseHoursTheory;

    /**
     * 实验学时
     */
    @TableField("course_hours_experiment")
    private Integer courseHoursExperiment;

    /**
     * 其他学时
     */
    @TableField("course_hours_other")
    private Integer courseHoursOther;

    /**
     * 课外学时
     */
    @TableField("course_hours_extracurricular")
    private Integer courseHoursExtracurricular;

    /**
     * 上课学期（1-8）
     */
    @TableField("course_semester")
    private String courseSemester;

    /**
     * 本专业课程类型（专业基础课、个性化发展课等）
     */
    @TableField("course_type1")
    private String courseType1;

    /**
     * 专业认证课程类型
     */
    @TableField("course_type2")
    private String courseType2;

    /**
     * 国标课程类别
     */
    @TableField("course_type3")
    private String courseType3;

    /**
     * 课程性质（必修、选修、限选等）
     */
    @TableField("course_nature")
    private String courseNature;

    /**
     * 课程目标，json{     targetCount:2；     targetList:[         {             targetTitle:"掌握JAVA程序设计语法，能够按照要求编写程序，运行出正确的结果。";             GraduateTargetId:"4.1"         };         {             targetTitle:"理解面向对象程序设计思想，完成软件需求的抽象分析，设计合理的软件类图。";             GraduateTargetId:"5.2"         };     ] }
     */
    @TableField("course_target")
    private String courseTarget;

    /**
     * 考核方式列表，json：[{typeId:1,typeName:"作业"}，{typeId:2,typeName:"阶段性测验"},{typeId:3,typeName:"期末考试"}]
     */
    @TableField("assessment_method")
    private String assessmentMethod;

    /**
     * 分课程目标设置的不同考核方式对应的考核权重：json
     */
    @TableField("assessment_weight")
    private String assessmentWeight;

    /**
     * 期末考试的不同考核方式的考核权重
     */
    @TableField("assessment_weight_exam")
    private String assessmentWeightExam;

    /**
     * 具体课程目标不同考核方式中考核占比要求：json
     */
    @TableField("assessment_proportion")
    private String assessmentProportion;

    /**
     * 所属培养方案的版本
     */
    @TableField("course_version")
    private Integer courseVersion;

    /**
     * 课程所属培养方案id
     */
    @TableField("plan_id")
    private Integer planId;

    /**
     * 专业编号-专业表
     */
    @TableField("major_id")
    private Integer majorId;

    /**
     * 记录状态{0:正常状态；-1:删除状态；}
     */
    @TableField("status")
    private Integer status;

    /**
     * 课程教材
     */
    @TableField("course_book")
    private String courseBook;

}