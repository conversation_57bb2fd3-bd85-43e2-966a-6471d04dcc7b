package com.hnumi.obe.tp.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hnumi.obe.base.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 课程体系支撑毕业要求矩阵实体
 * 表名: tp_po_matrix
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tp_po_matrix")
public class PoMatrix extends BaseEntity {

    /**
     * 支撑点ID (主键，自增)
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 毕业要求id (po_id)
     */
    private Long poId;

    /**
     * 课程id (course_id)
     */
    private Long courseId;

    /**
     * 支撑权重 (weight)
     */
    private BigDecimal weight;

    /**
     * 所属工程教育认证指标id (standard_id)
     */
    private Long standardId;

    /**
     * 所属专业培养计划id (plan_id)
     */
    private Long planId;

    /**
     * 所属专业id（冗余）(major_id)
     */
    private Long majorId;

    /**
     * 所属学院id（冗余）(academy_id)
     */
    private Long academyId;

    /**
     * 记录状态{0:正常状态；-1:删除状态；} (status)
     */
    private Integer status;

}
