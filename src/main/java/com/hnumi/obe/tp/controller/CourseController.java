package com.hnumi.obe.tp.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hnumi.obe.common.entity.R;
import com.hnumi.obe.common.util.RequestUtil;
import com.hnumi.obe.common.valid.ValidGroup;
import com.hnumi.obe.tp.dto.CourseAssessmentDataDTO;
import com.hnumi.obe.tp.dto.CourseBookDTO;
import com.hnumi.obe.tp.dto.CourseDTO;
import com.hnumi.obe.tp.dto.CourseQueryDTO;
import com.hnumi.obe.tp.dto.UpdateCourseLeaderDTO;
import com.hnumi.obe.tp.entity.Course;
import com.hnumi.obe.tp.mapstruct.CourseConvert;
import com.hnumi.obe.tp.service.ICourseService;
import com.hnumi.obe.tp.vo.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
* 专业培养方案中的课程体系，课程要求带有版本号
*
*/
@RestController
@RequestMapping("/tp/course")
public class CourseController {
    @Autowired
    ICourseService courseService;

    /**
     * 新建课程
     * @param dto
     * @return
     */
    @PostMapping("/add")
    public R<Object> addCourse(@Validated(ValidGroup.Add.class) @RequestBody CourseDTO dto) {
        Long userId = RequestUtil.getUserId();

        Course entity = CourseConvert.INSTANCE.toEntity(dto);
        entity.setStatus(0); // 正常状态
        return R.ok(courseService.save(entity));
    }

    /**
     * 更新课程信息
     * @param dto
     * @return
     */
    @PutMapping("/mod")
    public R<Object> updateCourse(@Validated(ValidGroup.Update.class) @RequestBody CourseDTO dto) { // Changed ValidGroup
        Long userId = RequestUtil.getUserId();

        Course entity = CourseConvert.INSTANCE.toEntity(dto);
        return R.ok(courseService.updateById(entity));
    }

    /**
     * 软删除课程
     * @param courseId
     * @return
     */
    @DeleteMapping("/del")
    public R<Object> deleteCourseById(@RequestParam(value = "courseId") Long courseId) { // Changed param name to match plan
        return R.ok(courseService.deleteById(courseId)); // This is a soft delete as per service impl
    }

    @PutMapping("/leader")
    public R<Boolean> updateCourseLeader(@RequestBody UpdateCourseLeaderDTO dto) { // Changed ValidGroup
        if (dto.courseId() == null) {
            throw new RuntimeException("course id 不能为空");
        }
        if (dto.courseLeader() == null) {
            throw new RuntimeException("course leader 不能为空");
        }
        Course course = new Course();
        course.setCourseId(dto.courseId());
        course.setCourseLeader(dto.courseLeader());

        return R.ok(courseService.updateById(course));
    }

    ///==============================课程信息查询相关接口=================================

    /**
     * 根据ID获取课程信息
     * @param id 课程ID
     * @return 课程信息
     */
    @GetMapping("/{id}")
    public R<Object> getCourseById(@PathVariable Long id) {
        Course course = courseService.getById(id);
        if (course == null || course.getStatus() == -1) { // Check for soft deleted
            return null; // Or throw a NotFoundException
        }
        return R.ok(CourseConvert.INSTANCE.toVO(course));
    }

    @GetMapping("/statistics/{majorId}/{planId}")
    public R<CourseStatisticsVO> getCourseStatistics(@PathVariable Long majorId, @PathVariable Long planId) {
        return R.ok(courseService.getCourseStatistics(majorId, planId));
    }

    @GetMapping("/instructor-stats/{majorId}/{planId}")
    public R<InstructorStatsVO> getInstructorStats(@PathVariable Long majorId, @PathVariable Long planId) {
        return R.ok(courseService.getInstructorStats(majorId, planId));
    }


    /**
     * 分页查询专业培养方案中的课程体系，课程要求带有版本号列表
     *
     * @param query 查询条件
     * @return 分页结果
     */
    @PostMapping("/list")
    public R<Page<CourseVO>> pageCourse(@RequestBody CourseQueryDTO query) {
        LambdaQueryWrapper<Course> wrapper = Wrappers.lambdaQuery(Course.class);
        
        wrapper.eq(query.getPlanId() != null, Course::getPlanId, query.getPlanId());
        wrapper.like(StringUtils.isNotBlank(query.getCourseName()), Course::getCourseName, query.getCourseName());
        wrapper.like(StringUtils.isNotBlank(query.getCourseCode()), Course::getCourseCode, query.getCourseCode());
        wrapper.eq(query.getCourseLeader() != null, Course::getCourseLeader, query.getCourseLeader());
        
        if (query.getStatus() != null) {
            wrapper.eq(Course::getStatus, query.getStatus());
        } else {
            wrapper.eq(Course::getStatus, 0); // Default to active records if status is not specified
        }
        
        wrapper.orderByDesc(Course::getCreateTime); // Default sort

        Page<Course> pageRequest = query.getPage();
        Page<Course> coursePage = courseService.page(pageRequest, wrapper);
        
        return R.ok(CourseConvert.INSTANCE.toPageVO(coursePage));
    }

    /**
     * 根据课程负责人和培养方案ID分页查询课程列表
     *
     * @param query query
     * @return 分页结果
     */
    @PostMapping("/findByLeaderAndPlan")
    public R<Page<CourseVO>> pageCoursesByLeaderAndPlan(@RequestBody CourseQueryDTO query) {
        Long userId = RequestUtil.getUserId();

        Page<Course> pageRequest = query.getPage();
        LambdaQueryWrapper<Course> wrapper = Wrappers.lambdaQuery(Course.class);
        
        wrapper.eq(Course::getPlanId, query.getPlanId());
        wrapper.eq(Course::getCourseLeader, userId);
        wrapper.eq(Course::getStatus, 0); // Default to active records
        
        wrapper.orderByDesc(Course::getCreateTime); // Default sort
        
        Page<Course> coursePage = courseService.page(pageRequest, wrapper);
        return R.ok(CourseConvert.INSTANCE.toPageVO(coursePage));
    }

    /**
     * 根据课程负责人和专业ID分页查询课程列表
     *
     * @param majorId
     * @return 课程列表
     */
    @GetMapping("/findByLeaderAndMajor/{majorId}")
    public R<Object> findCoursesByLeaderAndMajor(@PathVariable Long majorId) {

        System.out.println("Fetching courses for major ID: " + majorId);
        return R.ok(courseService.getCourseListByLeaderId(majorId));
    }

    /**
     * 根据ID获取课程的基本信息，不包括考核信息
     * @param courseId 课程ID
     * @return 课程信息
     */
    @GetMapping("/base-info/{courseId}")
    public R<CourseBaseInfoVO> getCourseBaseInfoById(@PathVariable Long courseId) {
        CourseBaseInfoVO course = courseService.getCourseBaseInfoById(courseId);
        if (course == null ) { // Check for soft deleted
            return null; // Or throw a NotFoundException
        }
        return R.ok(course);
    }

    /**
     * 根据ID获取课程的详细信息
     * @param courseId 课程ID
     * @return 课程信息
     */
    @GetMapping("/detail-info/{courseId}")
    public R<CourseVO> getCourseDetailInfoById(@PathVariable Long courseId) {
        Course course = courseService.getById(courseId);
        if (course == null || course.getStatus() == -1) { // Check for soft deleted
            return null; // Or throw a NotFoundException
        }
        return R.ok(CourseConvert.INSTANCE.toVO(course));
    }

    /**
     * 获取一个课号的课程基础信息列表
     * @return 课程基础信息列表
     */
    @GetMapping("/base-info/list/{courseCode}")
    public R<List<CourseBaseInfoVO>> findCourseBaseInfoList(@PathVariable String courseCode) {
        // 获取课程基础信息列表
        List<CourseBaseInfoVO> courseBaseInfoList = courseService.findCourseBaseInfoList(courseCode);
        if (courseBaseInfoList == null || courseBaseInfoList.isEmpty()) {
            return R.fail(404, "No courses found");
        }

        return R.ok(courseBaseInfoList);
    }

    //=============================课程目标相关接口=================================

    /**
     * 更新课程目标
     * @param courseId
     * @param target
     * @return
     */
    @PostMapping("/updateTarget/{courseId}")
    public R<Object> updateCourseTarget(@PathVariable Long courseId, @RequestBody CourseAssessmentDataDTO target) {
        Long userId = RequestUtil.getUserId();

        // 使用UpdateWrapper只更新courseTarget字段
        boolean updated = courseService.updateCourseTarget(courseId,target.getCourseObjectiveList())>0;
        // 先检查课程是否存在
        if (!updated) {
            return R.fail(500, "Course not found or has been deleted");
        }

        return updated ? R.ok(true) : R.fail(500,"Failed to update course target");
    }

    /**
     * 获取课程目标详细信息（按对象结构返回）
     * @param courseId 课程ID
     * @return 课程目标结构信息
     */
    @GetMapping("/objectives/{courseId}")
    public R<List<CourseObjectiveVO>> getCourseObjectives(@PathVariable Long courseId) {
     //   Long userId = RequestUtil.getUserId();

        // 获取课程目标列表
        List<CourseObjectiveVO> objectives = courseService.getCourseObjectives(courseId);
        if (objectives == null) {
            return R.fail(404, "Course objectives not found");
        }

        return R.ok(objectives);
    }
//=============================课程考核环节相关接口=================================

    /**
     * 获取课程的考核环节的各种占比、权重详情
     */
    @GetMapping("/assessment-detail/{courseId}")
    public R<AssessmentDetailVO> getCourseAssessmentDetail(@PathVariable Long courseId) {
        Long userId = RequestUtil.getUserId();

        AssessmentDetailVO detail = courseService.getCourseAssessmentDetail(courseId);
        System.out.println("Course Assessment Detail: " + detail);

        if (detail == null) {
            return R.fail(404, "Course assessment detail not found");
        }

        return R.ok(detail);
    }

    @GetMapping("/assessment-proportion/{courseId}")
    public R<List<AssessmentConfigVO>> getCourseAssessmentProportions(@PathVariable Long courseId) {
       // Long userId = RequestUtil.getUserId();

        List<AssessmentConfigVO> proportions = courseService.getCourseAssessmentProportions(courseId);

        if (proportions == null) {
            return R.fail(404, "Course proportions not found");
        }

        return R.ok(proportions);
    }

    /**
     * 更新课程考核权重，全更新
     * @param courseId 课程ID
     * @param assessmentWeight 考核权重
     * @return 更新结果
     */
    @PutMapping("/assessment-config/{courseId}")
    public R<Boolean> updateCourseAssessmentConfig(
            @PathVariable Long courseId,
            @RequestBody CourseAssessmentDataDTO assessmentWeight
            ) {
        System.out.println(assessmentWeight);
        boolean updated = courseService.updateCourseAssessmentConfig(courseId, assessmentWeight);

        if (!updated) {
            return R.fail(500, "Course not found or update failed");
        }

        return R.ok(true);
    }



//=============================课程教材相关接口=================================
//
//    /**
//     * 保存课程教材信息
//     * @param courseId 课程ID
//     * @param courseBookDTO 教材信息
//     * @return 保存结果
//     */
//    @PostMapping("/books/{courseId}")
//    public R<Boolean> saveCourseBooks(@PathVariable Long courseId,
//                                      @Validated(ValidGroup.Add.class) @RequestBody CourseBookDTO courseBookDTO) {
//        try {
//            boolean result = courseService.saveCourseBooks(courseId, courseBookDTO);
//            return result ? R.ok(true) : R.fail(500, "课程教材信息保存失败");
//        } catch (Exception e) {
//            return R.fail(500, "保存课程教材信息时发生错误: " + e.getMessage());
//        }
//    }

    /**
     * 更新课程教材信息
     * @param courseId 课程ID
     * @param courseBookDTO 教材信息
     * @return 更新结果
     */
    @PutMapping("/books/{courseId}")
    public R<Boolean> updateCourseBooks(@PathVariable Long courseId,
                                        @Validated(ValidGroup.Update.class) @RequestBody CourseBookDTO courseBookDTO) {
        try {
            boolean result = courseService.saveCourseBooks(courseId, courseBookDTO);
            return result ? R.ok(true) : R.fail(500, "课程教材信息更新失败");
        } catch (Exception e) {
            return R.fail(500, "更新课程教材信息时发生错误: " + e.getMessage());
        }
    }

    /**
     * 获取课程教材信息
     * @param courseId 课程ID
     * @return 教材信息
     */
    @GetMapping("/books/{courseId}")
    public R<CourseBookVO> getCourseBooks(@PathVariable Long courseId) {
        try {
            CourseBookVO courseBooks = courseService.getCourseBooks(courseId);
            if (courseBooks == null) {
                return R.fail(404, "未找到课程教材信息");
            }
            return R.ok(courseBooks);
        } catch (Exception e) {
            return R.fail(500, "获取课程教材信息时发生错误: " + e.getMessage());
        }
    }

//    /**
//     * 删除课程教材信息
//     * @param courseId 课程ID
//     * @return 删除结果
//     */
//    @DeleteMapping("/books/{courseId}")
//    public R<Boolean> deleteCourseBooks(@PathVariable Long courseId) {
//        try {
//            boolean result = courseService.deleteCourseBooks(courseId);
//            return result ? R.ok(true) : R.fail(500, "课程教材信息删除失败");
//        } catch (Exception e) {
//            return R.fail(500, "删除课程教材信息时发生错误: " + e.getMessage());
//        }
//    }


    //=============================TODO:这里是以课程为的信息为管理，访问的是course表，该方法需要切换到task，这个是访问的task_worklist表=================================
    /**
     * 获取教师学期课程统计
     * @param year 学年，可为空，默认当前年份
     * @param termType 学期类型（0: 春季, 1: 秋季），可为空，默认根据当前月份推断
     * @return 当前学期课程统计列表
     */
    @GetMapping("/teacher/semester/static/{year}/{termType}")
    public R<List<CourseCurrentSemesterVO>> getTeacherSemesterCourses(
            @PathVariable(required = false) Integer year,
            @PathVariable(required = false) Integer termType) {
        Long teacherId = RequestUtil.getExtendId();
        // 如果year为空，使用当前年份
        if (year == null) {
            year = java.time.LocalDate.now().getYear();
        }

        // 如果termType为空，根据当前月份推断
        if (termType == null) {
            // 2-8月为春季(0)，9-1月为秋季(1)
            int currentMonth = java.time.LocalDate.now().getMonthValue();
            termType = (currentMonth >= 2 && currentMonth <= 8) ? 0 : 1;
        }

        List<CourseCurrentSemesterVO> courses = courseService.getTeacherSemesterCourses(teacherId, year, termType);
        return R.ok(courses);
    }

    //=============TODO: 此部分为课程授课管理相关接口（task），后期需要切出来，本接口是课程基本信息（course!=task）=================================

    /**
     * 获取课程授课历史统计
     * @return 课程授课历史统计列表
     */
    @GetMapping("/teacher/history/static")
    public R<List<CourseTeachingHistoryVO>> getTeachingHistoryCourses() {
        Long teacherId = RequestUtil.getExtendId();
        List<CourseTeachingHistoryVO> courses = courseService.getTeachingHistoryCourses(teacherId);
        return R.ok(courses);
    }

    /**
     * 获取课程任务基本信息
     * @param courseId 课程ID
     * @param taskId 教学任务ID
     * @return 课程任务基本信息
     */
    @GetMapping("/info/{courseId}/{taskId}")
    public R<CourseTaskInfoVO> getCourseTaskInfo(@PathVariable Long courseId, @PathVariable Long taskId) {
        CourseTaskInfoVO info = courseService.getCourseTaskInfo(courseId, taskId);
        if (info == null) {
            return R.fail(404, "Course task info not found");
        }
        return R.ok(info);
    }

    /**
     * 获取课程任务统计信息
     * @param courseId 课程ID
     * @param taskId 教学任务ID
     * @return 课程任务统计信息
     */
    @GetMapping("/task/statistics/{courseId}/{taskId}")
    public R<CourseTaskStatisticsVO> getCourseTaskStatistics(@PathVariable Long courseId, @PathVariable Long taskId) {
        Long teacherId = RequestUtil.getExtendId();
        CourseTaskStatisticsVO statistics = courseService.getCourseTaskStatistics(courseId, taskId, teacherId);
        if (statistics == null) {
            return R.fail(404, "Course task statistics not found");
        }
        return R.ok(statistics);
    }

    /**
     * 获取课程任务班级信息
     * @param courseId 课程ID
     * @param taskId 教学任务ID
     * @return 课程任务班级信息列表
     */
    @GetMapping("/classes/{courseId}/{taskId}")
    public R<List<CourseTaskClassesVO>> getCourseTaskClasses(@PathVariable Long courseId, @PathVariable Long taskId) {
        Long teacherId = RequestUtil.getExtendId();
        List<CourseTaskClassesVO> classes = courseService.getCourseTaskClasses(courseId, taskId, teacherId);
        return R.ok(classes);
    }

    /**
     * 获取课程任务教师团队信息
     * @param courseId 课程ID
     * @param taskId 教学任务ID
     * @return 课程任务教师团队信息列表
     */
    @GetMapping("/team/{courseId}/{taskId}")
    public R<List<CourseTaskTeacherVO>> getCourseTaskTeachers(@PathVariable Long courseId, @PathVariable Long taskId) {
        List<CourseTaskTeacherVO> teachers = courseService.getCourseTaskTeachers(courseId, taskId);
        return R.ok(teachers);
    }



}
