package com.hnumi.obe.tp.controller;

import com.alibaba.excel.EasyExcel;
import com.hnumi.obe.common.entity.R;
import com.hnumi.obe.common.util.ExcelUtil;
import com.hnumi.obe.common.valid.ValidGroup;
import com.hnumi.obe.tp.dto.EoDTO;
import com.hnumi.obe.tp.dto.EoQueryDTO;
import com.hnumi.obe.tp.entity.Eo;
import com.hnumi.obe.tp.mapstruct.EoConvert;
import com.hnumi.obe.tp.service.IEoService;
import com.hnumi.obe.tp.vo.EoExportVO;
import com.hnumi.obe.tp.vo.EoVO;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.List;

/**
 * 培养目标表
 */
@RestController
@RequestMapping("/tp/eo")
public class EoController {
    @Autowired
    IEoService eoService;

    @PostMapping("/add")
    public R<Boolean> addEo(@Validated(ValidGroup.Add.class) @RequestBody EoDTO dto) {
        dto.setId(null);
        return R.ok(eoService.save(EoConvert.INSTANCE.toEntity(dto)));
    }

    @PutMapping("/mod")
    public R<Boolean> updateEo(@Validated(ValidGroup.Add.class) @RequestBody EoDTO dto) {
        return R.ok(eoService.updateById(EoConvert.INSTANCE.toEntity(dto)));
    }

    @DeleteMapping("/del")
    public Object deleteEoById(@RequestParam(value = "id") Long id) {
        return eoService.deleteById(id);
    }

    @GetMapping("/getList")
    public List<EoVO> getEoById(@RequestParam(value = "plan_id") Long planId) {
        return eoService.getAllByPlanId(planId);
    }

    /**
     * 导入培养目标
     * 在Eo.java里面的字段上面加上类似于这样的 @ExcelColumn("培养目标标题")代表导入的表格有这一列
     */
    @PostMapping("/import")
    public Object importEo(@RequestParam("file") MultipartFile file) throws Exception {
        InputStream  inputStream = file.getInputStream();
        List<Eo> eo = ExcelUtil.readAll(inputStream, Eo.class);
        return eoService.saveBatch(eo);
    }

    /**
     * 导出培养目标
     */
    @GetMapping("/export")
    public void exportEo(EoQueryDTO query, HttpServletResponse response) throws IOException {
        // 设置响应头
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("培养目标信息", "UTF-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
        // 获取所有培养目标信息
        List<EoExportVO> eoList = eoService.getEoList(query);
        // 写入Excel
        EasyExcel.write(response.getOutputStream(), EoExportVO.class)
                .sheet("培养目标信息")
                .doWrite(eoList);
    }
}
