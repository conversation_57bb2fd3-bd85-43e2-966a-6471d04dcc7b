package com.hnumi.obe.tp.controller;

import com.hnumi.obe.common.entity.R;
import com.hnumi.obe.tp.entity.PoMatrix;
import com.hnumi.obe.tp.service.PoMatrixService;
import com.hnumi.obe.tp.task.PoMatrixCleanupTask;
import com.hnumi.obe.tp.vo.CoursePoVO;
import com.hnumi.obe.tp.vo.PoVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
@Slf4j
@RestController
@RequestMapping("/tp/po-matrix")
public class PoMatrixController {

    @Autowired
    private PoMatrixService poMatrixService;


    @PostMapping
    public R<?> createPoMatrix(@RequestBody PoMatrix poMatrix) {
        boolean success = poMatrixService.create(poMatrix);
        return R.ok(success);
    }

    @GetMapping("/{id}")
    public R<PoMatrix> getPoMatrixById(@PathVariable("id") Long id) {
        PoMatrix poMatrix = poMatrixService.getById(id);
        return R.ok(poMatrix);
    }
    
    @GetMapping("/findByCourseAndPo")
    public R<PoMatrix> getPoMatrixByCourseAndPo(
            @RequestParam("courseId") Long courseId,
            @RequestParam("poId") Long poId) {
        PoMatrix poMatrix = poMatrixService.getByCourseIdAndPoId(courseId, poId);
        return R.ok(poMatrix);
    }

    @GetMapping("/by-plan/{planId}")
    public R<List<PoMatrix>> getPoMatrixByPlanId(@PathVariable("planId") Long planId) {
        List<PoMatrix> list = poMatrixService.getByPlanId(planId);
        return R.ok(list);
    }
    
    @PostMapping("/list")
    public R<List<PoMatrix>> listPoMatrix(@RequestBody(required = false) PoMatrix queryParams) {
        PoMatrix params = (queryParams == null) ? new PoMatrix() : queryParams;
        List<PoMatrix> list = poMatrixService.list(params);
        return R.ok(list);
    }

    @GetMapping("/po-list/{courseId}")
    public R<List<PoVO>> getPoListByCourseId(@PathVariable ("courseId") Long courseId){

        List<PoVO> list = poMatrixService.getPoListByCourseId(courseId);
        return R.ok(list);
    }

    @GetMapping("/po-tree/{courseId}")
    public R<List<CoursePoVO>> getPoTreeByCourseId(@PathVariable ("courseId") Long courseId){

        List<CoursePoVO> list = poMatrixService.getCourseIndicatorsByCourseId(courseId);
        if (list.isEmpty()) {
            return R.ok(List.of());
        }
        return R.ok(list);
    }

    @PutMapping("/{id}")
    public R<?> updatePoMatrix(@PathVariable("id") Long id, @RequestBody PoMatrix poMatrixDetails) {
        PoMatrix existingPoMatrix = poMatrixService.getById(id);
        if (existingPoMatrix == null) {
            throw new RuntimeException("资源未找到");
        }
        poMatrixDetails.setId(id); 
        
        boolean success = poMatrixService.update(poMatrixDetails);
        return R.ok(success);
    }

    @DeleteMapping("/{id}")
    public R<Boolean> deletePoMatrix(@PathVariable("id") Long id) {
        PoMatrix existingPoMatrix = poMatrixService.getById(id);
        if (existingPoMatrix == null) {
            throw new RuntimeException("资源未找到");
        }
        boolean success = poMatrixService.deleteById(id);
        return R.ok(success);
    }


    @Autowired
    private PoMatrixCleanupTask cleanupTask;

    /**
     * 手动清理所有无效的PoMatrix记录
     */
    @PostMapping("/cleanup/all")
    //@Operation(summary = "清理所有无效的PoMatrix记录", description = "清理引用已删除或不存在PO记录的PoMatrix记录")
    public R<Integer> cleanupAllInvalidRecords() {
        try {
            int cleanedCount = cleanupTask.manualCleanup();
            return R.ok( "清理完成，共清理了 " + cleanedCount + " 条无效记录");
        } catch (Exception e) {
            log.error("清理所有无效PoMatrix记录失败", e);
            return R.fail(500, "清理失败: " + e.getMessage());
        }
    }

    /**
     * 清理指定计划的无效PoMatrix记录
     */
    @PostMapping("/cleanup/plan/{planId}")
    // @Operation(summary = "清理指定计划的无效PoMatrix记录", description = "清理指定计划下引用已删除或不存在PO记录的PoMatrix记录")
    public R<Integer> cleanupInvalidRecordsByPlanId(
            //@Parameter(description = "计划ID", required = true)
            @PathVariable Long planId) {
        try {
            if (planId == null || planId <= 0) {
                return R.fail(400, "计划ID无效");
            }

            int cleanedCount = cleanupTask.cleanupByPlanId(planId);
            return R.ok( "清理完成，共清理了 " + cleanedCount + " 条无效记录");
        } catch (Exception e) {
            log.error("清理计划 {} 的无效PoMatrix记录失败", planId, e);
            return R.fail(500, "清理失败: " + e.getMessage());
        }
    }

    /**
     * 清理指定课程的无效PoMatrix记录
     */
    @PostMapping("/cleanup/course/{courseId}")
    //@Operation(summary = "清理指定课程的无效PoMatrix记录", description = "清理指定课程下引用已删除或不存在PO记录的PoMatrix记录")
    public R<Integer> cleanupInvalidRecordsByCourseId(
            // @Parameter(description = "课程ID", required = true)
            @PathVariable Long courseId) {
        try {
            if (courseId == null || courseId <= 0) {
                return R.fail(400, "课程ID无效");
            }

            int cleanedCount = poMatrixService.cleanupInvalidPoMatrixRecordsByCourseId(courseId);
            return R.ok("清理完成，共清理了 " + cleanedCount + " 条无效记录");
        } catch (Exception e) {
            log.error("清理课程 {} 的无效PoMatrix记录失败", courseId, e);
            return R.fail(500, "清理失败: " + e.getMessage());
        }
    }

    /**
     * 检查数据完整性
     */
    @GetMapping("/check/integrity")
    // @Operation(summary = "检查PoMatrix数据完整性", description = "检查PoMatrix记录中是否存在无效的PO引用")
    public R<String> checkDataIntegrity() {
        try {
            // 这里可以实现数据完整性检查逻辑
            // 返回检查报告而不执行清理
            return R.ok("数据完整性检查功能待实现");
        } catch (Exception e) {
            log.error("数据完整性检查失败", e);
            return R.fail(500, "检查失败: " + e.getMessage());
        }
    }

    /**
     * 获取清理统计信息
     */
    @GetMapping("/stats/cleanup")
    // @Operation(summary = "获取清理统计信息", description = "获取最近的数据清理统计信息")
    public R<String> getCleanupStats() {
        try {
            // 这里可以实现清理统计信息的查询
            // 例如：最近清理的记录数、清理频率等
            return R.ok("清理统计信息功能待实现");
        } catch (Exception e) {
            log.error("获取清理统计信息失败", e);
            return R.fail(500, "获取统计信息失败: " + e.getMessage());
        }
    }
}
