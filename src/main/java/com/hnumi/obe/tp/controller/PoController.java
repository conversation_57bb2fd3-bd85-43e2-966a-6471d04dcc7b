package com.hnumi.obe.tp.controller;

import com.alibaba.excel.EasyExcel;
import com.hnumi.obe.common.entity.R;
import com.hnumi.obe.common.util.ExcelUtil;
import com.hnumi.obe.common.valid.ValidGroup;
import com.hnumi.obe.tp.dto.PoDTO;
import com.hnumi.obe.tp.dto.PoQueryDTO;
import com.hnumi.obe.tp.entity.Po;
import com.hnumi.obe.tp.mapstruct.PoConvert;
import com.hnumi.obe.tp.service.IPoService;
import com.hnumi.obe.tp.vo.PoExportVO;
import com.hnumi.obe.tp.vo.PoTreeVO;
import com.hnumi.obe.tp.vo.PoVO;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.List;
import java.util.Map;

/**
 * 毕业要求表
 *
 */
@RestController
@RequestMapping("/tp/po")
public class PoController {
    @Autowired
    IPoService poService;

    @PostMapping("/add")
    public Object addPo(@Validated(ValidGroup.Add.class) @RequestBody PoDTO dto) {
        dto.setId(null);
        Po po = PoConvert.INSTANCE.toEntity(dto);
        poService.save(po);
        return po.getId();
    }

    @PutMapping("/mod")
    public Object updatePo(@Validated(ValidGroup.Add.class) @RequestBody PoDTO dto) {
        // 如果是 requirement 则不更新poNumber
        if (dto.getIsRequirement()) {
            dto.setPoNumber(null);
        }
        return poService.updateById(PoConvert.INSTANCE.toEntity(dto));
    }

    @DeleteMapping("/del")
    public R<Integer> deletePoById(@RequestParam(value = "id") Long id) {
        return R.ok(poService.deletePoById(id));
    }

    @GetMapping("/getList")
    public Object getPoListByPlanId(@RequestParam(value = "plan_id")@PathVariable Long planId) {
        return poService.getAllByPlanId(planId);
    }
    
    @GetMapping("/tree")
    public R<List<PoTreeVO>> getPoTree(@RequestParam(value = "plan_id")@PathVariable Long planId) {
        return R.ok(poService.getPoTree(planId));
    }


    /**
     * 更具poId查询毕业要求
     * @param poId 毕业要求id
     *             @return 毕业要求
     */
    @GetMapping("/getById/{poId}")
    public R<PoVO> getPoById(@PathVariable Long poId) {
        Po po = poService.getById(poId);
        if (po == null) {
            return null;
        }
        PoVO poVO = PoConvert.INSTANCE.toVO(po);
        return R.ok(poVO);
    }

    /**
     * 根据毕业要求id查询毕业要求一级指标点
     * @param planId 毕业要求id
     * @return 毕业要求一级指标点
     */
    @GetMapping("/getPoMapByPlanId/{planId}")
    public R<List<PoTreeVO>> getPoMapByPlanId(@PathVariable Long planId) {
        return R.ok(poService.getPoTree(planId));
    }

//    @GetMapping("/getRequirementById")
//    public Object getPoById(@RequestParam(value = "id") Long id) {
//        return poService.getById(id);
//    }

    /**
     * 导入毕业要求
     * 在Po.java里面的字段上面加上类似于这样的 @ExcelColumn("培养目标标题")代表导入的表格有这一列
     */
    @PostMapping("/import")
    public Object importPo(@RequestParam("file") MultipartFile file) throws Exception {
        InputStream inputStream = file.getInputStream();
        List<Po> po =  ExcelUtil.readAll(inputStream, Po.class);
        return poService.saveBatch(po);
    }

    /**
     * 导出毕业要求
     */
    @GetMapping("/export")
    public void exportPo(PoQueryDTO query, HttpServletResponse response) throws IOException {
        // 设置响应头
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("毕业要求信息", "UTF-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
        // 获取所有毕业要求信息
        List<PoExportVO> poList = poService.getPoList(query);
        //  写入Excel
        EasyExcel.write(response.getOutputStream(), PoExportVO.class)
                .sheet("毕业要求信息")
                .doWrite(poList);

    }
}
