package com.hnumi.obe.tp.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hnumi.obe.common.entity.PageResponse;
import com.hnumi.obe.common.entity.R;
import com.hnumi.obe.common.util.RequestUtil;
import com.hnumi.obe.tp.dto.PlanDTO;
import com.hnumi.obe.tp.dto.PlanQueryDTO;
import com.hnumi.obe.tp.dto.TaskQueryDTO;
import com.hnumi.obe.tp.mapstruct.PlanConvert;
import com.hnumi.obe.tp.service.IPlanService;
import com.hnumi.obe.tp.vo.PlanDetailVO;
import com.hnumi.obe.tp.vo.PlanSemesterVO;
import com.hnumi.obe.tp.vo.PlanVO;
import com.hnumi.obe.tp.vo.SemesterClassesVO;
import com.hnumi.obe.tp.vo.SemesterCourseVO;
import com.hnumi.obe.tp.vo.TaskVO;
import com.hnumi.obe.tp.vo.TeacherOptionVO;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/tp/plan")
public class PlanController {

    @Resource
    IPlanService planService;

    @GetMapping("/{id}")
    public R<PlanVO> getPlan(@PathVariable("id") Long id) {
        return R.ok(PlanConvert.INSTANCE.toVO(planService.getById(id)));
    }

    @GetMapping("/detail/{id}")
    public R<PlanDetailVO> getPlanStatistics(@PathVariable("id") Long id) {
        return R.ok(planService.planDetail(id));
    }

    @GetMapping("/list")
    public R<Page<PlanVO>> planList(PlanQueryDTO planQueryDTO) {
        Long userId = RequestUtil.getUserId();
        return R.ok(planService.planList(userId, planQueryDTO));
    }

    @PostMapping
    public R<Void> addPlan(@RequestBody PlanDTO planDTO) {
        Long userId = RequestUtil.getUserId();
        planService.addPlan(userId, planDTO);
        return R.ok();
    }

    @PutMapping("/{id}")
    public R<Void> updatePlan(@RequestBody PlanDTO planDTO, @PathVariable("id") Long id) {
        planDTO.setPlanId(id);
        Long userId = RequestUtil.getUserId();
        planService.updatePlan(userId, planDTO);
        return R.ok();
    }

    @DeleteMapping("/{planId}")
    public R<Void> deletePlanById(@PathVariable("planId") Long planId) {
        Long userId = RequestUtil.getUserId();
        planService.deletePlanByplanId(userId, planId);
        return R.ok();
    }

    @GetMapping("/{planId}/semesters")
    public R<List<PlanSemesterVO>> semesters(@PathVariable("planId") Long planId) {
        return R.ok(planService.semesters(planId));
    }

    @GetMapping("/{planId}/major/{majorId}/semester/{semesterId}/course")
    public R<List<SemesterCourseVO>> semesterCourse(
            @PathVariable("planId") Long planId,
            @PathVariable("majorId") Long majorId,
            @PathVariable("semesterId") Long semesterId) {
        return R.ok(planService.semesterCourse(majorId, planId, semesterId));
    }

    @GetMapping("/{planId}/major/{majorId}/classes")
    public R<List<SemesterClassesVO>> semesterClasses(
            @PathVariable("planId") Long planId,
            @PathVariable("majorId") Long majorId) {
        return R.ok(planService.semesterClasses(majorId, planId));
    }

//    @GetMapping("/{planId}/major/{majorId}/tasks")
//    public R<PageResponse<TaskVO>> tasks(
//            @PathVariable("planId") Long planId,
//            @PathVariable("majorId") Long majorId,
//            TaskQueryDTO queryDTO) {
//        return R.ok(planService.tasks(planId, majorId, queryDTO));
//    }

    @GetMapping("/{planId}/major/{majorId}/teachers")
    public R<List<TeacherOptionVO>> teachers(
            @PathVariable("planId") Long planId,
            @PathVariable("majorId") Long majorId) {
        return R.ok(planService.getTeacherOptions(planId, majorId));
    }

    @GetMapping("/{planId}/major/{majorId}/task-classes")
    public R<List<SemesterClassesVO>> taskClasses(
            @PathVariable("planId") Long planId,
            @PathVariable("majorId") Long majorId) {
        return R.ok(planService.getClassOptions(planId, majorId));
    }

}
