package com.hnumi.obe.tp.util;

import com.hnumi.obe.base.vo.TeacherVO;
import com.hnumi.obe.task.vo.TaskWorkDetailVO;
import com.hnumi.obe.task.vo.TaskWorkVO;
import com.hnumi.obe.tp.vo.TaskTeacherVO;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.stream.Collectors;

/**
 * TaskTeacherVO 转换工具类
 * 提供统一的转换方法，避免重复的转换逻辑
 *
 * <AUTHOR>
 * @since 2025-07-22
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class TaskTeacherVOConverter {

    /**
     * 将 TaskWorkVO.TaskTeacherVO 转换为 TaskWorkDetailVO.TaskTeacherVO
     * 
     * @param taskTeacher TaskWorkVO 中的教师信息
     * @param teacherMap 教师信息映射
     * @return TaskWorkDetailVO 中的教师信息
     */
    public static TaskWorkDetailVO.TaskTeacherVO toDetailVO(
            TaskTeacherVO taskTeacher,
            java.util.Map<Long, TeacherVO> teacherMap) {
        
        TaskWorkDetailVO.TaskTeacherVO detailVO = new TaskWorkDetailVO.TaskTeacherVO();
        detailVO.setTeacherId(taskTeacher.teacherId());
        //detailVO.setRole(taskTeacher.role());
        
        // 从教师映射中获取教师姓名
        if (taskTeacher.teacherId() != null && teacherMap.containsKey(taskTeacher.teacherId())) {
            TeacherVO teacher = teacherMap.get(taskTeacher.teacherId());
            detailVO.setTeacherName(teacher.getName());
        } else {
            detailVO.setTeacherName("未知教师");
        }
        
        // 设置角色名称
        //detailVO.setRoleName(getRoleName(taskTeacher.getRole()));
        
        return detailVO;
    }

    /**
     * 批量转换 TaskWorkVO.TaskTeacherVO 列表为 TaskWorkDetailVO.TaskTeacherVO 列表
     * 
     * @param taskTeachers TaskWorkVO 中的教师信息列表
     * @param teacherMap 教师信息映射
     * @return TaskWorkDetailVO 中的教师信息列表
     */
    public static List<TaskWorkDetailVO.TaskTeacherVO> toDetailVOList(
            List<TaskTeacherVO> taskTeachers,
            java.util.Map<Long, TeacherVO> teacherMap) {
        
        return taskTeachers.stream()
                .map(taskTeacher -> toDetailVO(taskTeacher, teacherMap))
                .collect(Collectors.toList());
    }

//    /**
//     * 将 TaskWorkDetailVO.TaskTeacherVO 转换为 TaskWorkVO.TaskTeacherVO
//     *
//     * @param detailTeacher TaskWorkDetailVO 中的教师信息
//     * @return TaskWorkVO 中的教师信息
//     */
//    public static TaskTeacherVO toWorkVO(TaskWorkDetailVO.TaskTeacherVO detailTeacher) {
//        TaskTeacherVO workVO = new TaskTeacherVO(
//                detailTeacher.getTeacherId(),
//                detailTeacher.getTeacherName(),
//                detailTeacher.getTeacherId() // 使用教师ID作为唯一标识
//        );
//        workVO.setTeacherId(detailTeacher.getTeacherId());
//        workVO.setRole(detailTeacher.getRole());
//        return workVO;
//    }

//    /**
//     * 批量转换 TaskWorkDetailVO.TaskTeacherVO 列表为 TaskWorkVO.TaskTeacherVO 列表
//     *
//     * @param detailTeachers TaskWorkDetailVO 中的教师信息列表
//     * @return TaskWorkVO 中的教师信息列表
//     */
//    public static List<TaskTeacherVO> toWorkVOList(
//            List<TaskWorkDetailVO.TaskTeacherVO> detailTeachers) {
//
//        return detailTeachers.stream()
//                .map(TaskTeacherVOConverter::toWorkVO)
//                .collect(Collectors.toList());
//    }

    /**
     * 将 TeacherVO 转换为 TaskTeacherVO
     * 
     * @param teacherVO 教师VO
     * @return TaskTeacherVO
     */
    public static TaskTeacherVO toTaskTeacherVO(TeacherVO teacherVO) {
        return TaskTeacherVO.from(teacherVO);
    }

    /**
     * 批量转换 TeacherVO 列表为 TaskTeacherVO 列表
     * 
     * @param teacherVOs 教师VO列表
     * @return TaskTeacherVO 列表
     */
    public static List<TaskTeacherVO> toTaskTeacherVOList(List<TeacherVO> teacherVOs) {
        return teacherVOs.stream()
                .map(TaskTeacherVO::from)
                .collect(Collectors.toList());
    }

    /**
     * 根据角色ID获取角色名称
     * 
     * @param role 角色ID
     * @return 角色名称
     */
    private static String getRoleName(Integer role) {
        if (role == null) {
            return "未知角色";
        }
        
        return switch (role) {
            case 1 -> "主讲教师";
            case 2 -> "辅导教师";
            case 3 -> "助教";
            default -> "其他角色";
        };
    }
}
