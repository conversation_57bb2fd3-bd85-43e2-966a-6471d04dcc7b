package com.hnumi.obe.tp.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.type.TypeFactory;
import com.hnumi.obe.tp.vo.CourseBookVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 课程教材JSON转换工具类
 * 用于将教材信息与JSON字符串互相转换
 */
@Slf4j
public class CourseBookJsonUtil {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 将CourseBookVO转换为JSON字符串存储到数据库
     *
     * @param courseBookVO 课程教材信息
     * @return JSON字符串
     */
    public static String toJson(CourseBookVO courseBookVO) {
        if (courseBookVO == null) {
            return null;
        }

        try {
            return objectMapper.writeValueAsString(courseBookVO);
        } catch (JsonProcessingException e) {
            log.error("课程教材信息转换为JSON失败", e);
            return null;
        }
    }

    /**
     * 将JSON字符串转换为CourseBookVO
     *
     * @param json JSON字符串
     * @return 课程教材信息
     */
    public static CourseBookVO fromJson(String json) {
        if (!StringUtils.hasText(json)) {
            return null;
        }

        try {
            return objectMapper.readValue(json, CourseBookVO.class);
        } catch (JsonProcessingException e) {
            log.error("JSON转换为课程教材信息失败：{}", json, e);
            return null;
        }
    }

    /**
     * 将教材列表转换为CourseBookVO结构
     * 自动分离主教材和参考书
     *
     * @param books 教材列表
     * @return 课程教材信息
     */
    public static CourseBookVO fromBookList(List<CourseBookVO.BookDetailVO> books) {
        if (books == null || books.isEmpty()) {
            return null;
        }

        CourseBookVO courseBookVO = new CourseBookVO();
        List<CourseBookVO.BookDetailVO> referenceBooks = new ArrayList<>();

        for (CourseBookVO.BookDetailVO book : books) {
            if (book.getType() == CourseBookVO.BookType.MAIN) {
                if (courseBookVO.getMainBook() == null) {
                    courseBookVO.setMainBook(book);
                } else {
                    log.warn("发现多本主教材，只保留第一本：{}", book.getName());
                }
            } else if (book.getType() == CourseBookVO.BookType.REFERENCE) {
                referenceBooks.add(book);
            }
        }

        courseBookVO.setReferenceBooks(referenceBooks);
        return courseBookVO;
    }

    /**
     * 将CourseBookVO转换为教材列表
     * 合并主教材和参考书为统一列表
     *
     * @param courseBookVO 课程教材信息
     * @return 教材列表
     */
    public static List<CourseBookVO.BookDetailVO> toBookList(CourseBookVO courseBookVO) {
        List<CourseBookVO.BookDetailVO> books = new ArrayList<>();

        if (courseBookVO == null) {
            return books;
        }

        // 添加主教材
        if (courseBookVO.getMainBook() != null) {
            courseBookVO.getMainBook().setType(CourseBookVO.BookType.MAIN);
            books.add(courseBookVO.getMainBook());
        }

        // 添加参考书
        if (courseBookVO.getReferenceBooks() != null) {
            for (CourseBookVO.BookDetailVO book : courseBookVO.getReferenceBooks()) {
                book.setType(CourseBookVO.BookType.REFERENCE);
                books.add(book);
            }
        }

        return books;
    }

    /**
     * 验证教材信息的完整性
     *
     * @param courseBookVO 课程教材信息
     * @return 验证结果
     */
    public static boolean validate(CourseBookVO courseBookVO) {
        if (courseBookVO == null) {
            return false;
        }

        // 验证主教材
        if (courseBookVO.getMainBook() == null) {
            log.warn("缺少主教材信息");
            return false;
        }

        if (!validateBook(courseBookVO.getMainBook())) {
            log.warn("主教材信息不完整");
            return false;
        }

        // 验证参考书（可选）
        if (courseBookVO.getReferenceBooks() != null) {
            for (CourseBookVO.BookDetailVO book : courseBookVO.getReferenceBooks()) {
                if (!validateBook(book)) {
                    log.warn("参考书信息不完整：{}", book.getName());
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * 验证单本教材信息
     *
     * @param book 教材信息
     * @return 验证结果
     */
    private static boolean validateBook(CourseBookVO.BookDetailVO book) {
        return book != null
                && StringUtils.hasText(book.getIsbn())
                && StringUtils.hasText(book.getName())
                && StringUtils.hasText(book.getAuthor())
                && StringUtils.hasText(book.getPublisher());
    }
}
