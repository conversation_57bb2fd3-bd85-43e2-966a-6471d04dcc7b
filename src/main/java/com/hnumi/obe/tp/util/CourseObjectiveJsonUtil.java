package com.hnumi.obe.tp.util;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.hnumi.obe.tp.vo.CourseObjectiveVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

import static com.fasterxml.jackson.databind.DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES;

/**
 * 课程目标JSON处理工具类
 * 专门处理课程目标相关的JSON序列化和反序列化
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
@Component
public class CourseObjectiveJsonUtil {

    private final ObjectMapper objectMapper;

    public CourseObjectiveJsonUtil() {
        this.objectMapper = new ObjectMapper();
        // 配置ObjectMapper，忽略未知属性
        this.objectMapper.configure(FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    /**
     * 将课程目标列表转换为JSON字符串
     * 
     * @param courseObjectives 课程目标列表
     * @return JSON字符串
     */
    public String convertToJson(List<CourseObjectiveVO> courseObjectives) {
        try {
            if (courseObjectives == null || courseObjectives.isEmpty()) {
                return "[]";
            }
            return objectMapper.writeValueAsString(courseObjectives);
        } catch (Exception e) {
            log.error("课程目标列表转换为JSON失败", e);
            return "[]";
        }
    }

    /**
     * 将JSON字符串解析为课程目标列表（类型安全）
     * 
     * @param json JSON字符串
     * @return 课程目标列表
     */
    public List<CourseObjectiveVO> parseJson(String json) {
        try {
            if (json == null || json.trim().isEmpty() || "null".equals(json.trim())) {
                log.debug("课程目标JSON为空，返回空列表");
                return new ArrayList<>();
            }

            // 使用明确的类型引用，避免泛型擦除
            TypeReference<List<CourseObjectiveVO>> typeReference = new TypeReference<List<CourseObjectiveVO>>() {};
            List<CourseObjectiveVO> result = objectMapper.readValue(json, typeReference);
            
            if (result == null) {
                log.warn("课程目标JSON解析结果为null，返回空列表");
                return new ArrayList<>();
            }

            log.debug("成功解析课程目标JSON，共{}个目标", result.size());
            return result;

        } catch (Exception e) {
            log.error("解析课程目标JSON失败，json: {}, error: {}", json, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 将JSON字符串解析为课程目标列表（使用JavaType，更安全）
     * 
     * @param json JSON字符串
     * @return 课程目标列表
     */
    public List<CourseObjectiveVO> parseJsonSafe(String json) {
        try {
            if (json == null || json.trim().isEmpty() || "null".equals(json.trim())) {
                return new ArrayList<>();
            }

            // 使用JavaType构造器，确保类型安全
            JavaType listType = objectMapper.getTypeFactory()
                .constructCollectionType(List.class, CourseObjectiveVO.class);
            
            List<CourseObjectiveVO> result = objectMapper.readValue(json, listType);
            
            if (result == null) {
                return new ArrayList<>();
            }

            // 验证解析结果的类型
            for (Object item : result) {
                if (!(item instanceof CourseObjectiveVO)) {
                    log.warn("解析结果包含非CourseObjectiveVO类型的对象: {}", item.getClass().getSimpleName());
                    // 可以选择抛出异常或者过滤掉非法对象
                }
            }

            return result;

        } catch (Exception e) {
            log.error("安全解析课程目标JSON失败，json: {}, error: {}", json, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 验证JSON字符串是否为有效的课程目标数组格式
     * 
     * @param json JSON字符串
     * @return 是否有效
     */
    public boolean isValidCourseObjectiveJson(String json) {
        try {
            if (json == null || json.trim().isEmpty()) {
                return false;
            }

            // 尝试解析，如果成功则认为有效
            List<CourseObjectiveVO> result = parseJsonSafe(json);
            return result != null;

        } catch (Exception e) {
            log.debug("JSON格式验证失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 获取课程目标数量（不完全解析JSON，只统计数量）
     * 
     * @param json JSON字符串
     * @return 课程目标数量
     */
    public int getCourseObjectiveCount(String json) {
        try {
            if (json == null || json.trim().isEmpty()) {
                return 0;
            }

            // 简单的数组长度统计，避免完整解析
            List<?> rawList = objectMapper.readValue(json, List.class);
            return rawList != null ? rawList.size() : 0;

        } catch (Exception e) {
            log.debug("统计课程目标数量失败: {}", e.getMessage());
            return 0;
        }
    }
}
