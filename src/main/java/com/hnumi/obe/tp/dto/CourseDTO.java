package com.hnumi.obe.tp.dto;

import com.hnumi.obe.common.valid.ValidGroup;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 专业培养方案中的课程体系，课程要求带有版本号 数据传输对象（DTO）
 * 
 * DTO（Data Transfer Object）用于服务层之间的数据传输
 * 主要用于：
 * 1. 封装请求参数
 * 2. 在不同服务层之间传递数据
 * 3. 隐藏实体类的敏感字段
 * 4. 优化数据传输结构
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CourseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotNull(message = "课程ID不能为空", groups = {ValidGroup.Update.class})
    private Long courseId;
    /**
     * 课程编码，与版本号一起是唯一的，如2025_RB7001152
     * 字段类型：String
     * 字段名称：courseCode
     * 数据库字段：course_code
     */
    @NotBlank(message = "课程编码不能为空", groups = {ValidGroup.Add.class, ValidGroup.Update.class})
    @Size(max = 50, message = "课程编码长度不能超过50个字符")
    private String courseCode;
    /**
     * 课程名称
     * 字段类型：String
     * 字段名称：courseName
     * 数据库字段：course_name
     */
    @NotBlank(message = "课程名称不能为空", groups = {ValidGroup.Add.class, ValidGroup.Update.class})
    @Size(max = 100, message = "课程名称长度不能超过100个字符")
    private String courseName;


    private Long courseLeader;
    /**
     * 课程学分
     * 字段类型：Integer
     * 字段名称：courseCredit
     * 数据库字段：course_credit
     */
    @NotNull(message = "学分不能为空", groups = {ValidGroup.Add.class, ValidGroup.Update.class})
    @Min(value = 0, message = "学分不能为负数")
    private Integer courseCredit;
    /**
     * 是否是核心课程
     * 字段类型：Boolean
     * 字段名称：courseCore
     * 数据库字段：course_core
     */
    private Boolean courseCore;
    /**
     * 是否是考试课
     * 字段类型：Boolean
     * 字段名称：courseExam
     * 数据库字段：course_exam
     */
    private Boolean courseExam;
    /**
     * 总学学时
     * 字段类型：Integer
     * 字段名称：courseHoursTotal
     * 数据库字段：course_hours_total
     */
    @Min(value = 0, message = "总学时不能为负数")
    private Integer courseHoursTotal;
    /**
     * 理教学时
     * 字段类型：Integer
     * 字段名称：courseHoursTheory
     * 数据库字段：course_hours_theory
     */
    @Min(value = 0, message = "理教学时不能为负数")
    private Integer courseHoursTheory;
    /**
     * 实验学时
     * 字段类型：Integer
     * 字段名称：courseHoursExperiment
     * 数据库字段：course_hours_experiment
     */
    @Min(value = 0, message = "实验学时不能为负数")
    private Integer courseHoursExperiment;
    /**
     * 其他学时
     * 字段类型：Integer
     * 字段名称：courseHoursOther
     * 数据库字段：course_hours_other
     */
    @Min(value = 0, message = "其他学时不能为负数")
    private Integer courseHoursOther;
    /**
     * 课外学时
     * 字段类型：Integer
     * 字段名称：courseHoursExtracurricular
     * 数据库字段：course_hours_extracurricular
     */
    @Min(value = 0, message = "课外学时不能为负数")
    private Integer courseHoursExtracurricular;
    /**
     * 上课学期（1-8）
     * 字段类型：Byte
     * 字段名称：courseSemester
     * 数据库字段：course_semester
     */
    @NotNull(message = "上课学期不能为空", groups = {ValidGroup.Add.class, ValidGroup.Update.class})
    private String courseSemester;
    /**
     * 本专业课程类型（专业基础课、个性化发展课等）
     * 字段类型：Integer
     * 字段名称：courseType1
     * 数据库字段：course_type1
     */
    @NotNull(message = "课程类别1不能为空", groups = {ValidGroup.Add.class, ValidGroup.Update.class}) // 对应前端的 category, 专业基础课等
    private String courseType1;
    /**
     * 专业认证课程类型
     * 字段类型：Integer
     * 字段名称：courseType2
     * 数据库字段：course_type2
     */
    private String courseType2;
    /**
     * 国标课程类别
     * 字段类型：Integer
     * 字段名称：courseType3
     * 数据库字段：course_type3
     */
    private String courseType3;
    /**
     * 课程性质（必修、选修、限选等）
     * 字段类型：Integer
     * 字段名称：courseNature
     * 数据库字段：course_nature
     */
    private String courseNature;
    /**
     * 课程目标，json{     targetCount:2；     targetList:[         {             targetTitle:"掌握JAVA程序设计语法，能够按照要求编写程序，运行出正确的结果。";             GraduateTargetId:"4.1"         };         {             targetTitle:"理解面向对象程序设计思想，完成软件需求的抽象分析，设计合理的软件类图。";             GraduateTargetId:"5.2"         };     ] }
     * 字段类型：String
     * 字段名称：courseTarget
     * 数据库字段：course_target
     */
    @Size(max = 2000, message = "课程目标长度不能超过2000个字符")
    private String courseTarget;
    /**
     * 考核方式列表，json：[{typeId:1,typeName:"作业"}，{typeId:2,typeName:"阶段性测验"},{typeId:3,typeName:"期末考试"}]
     * 字段类型：String
     * 字段名称：assessmentMethod
     * 数据库字段：assessment_method
     */
    @Size(max = 1000, message = "考核方式列表长度不能超过1000个字符")
    private String assessmentMethod;
    /**
     * 分课程目标设置的不同考核方式对应的考核权重：json
     * 字段类型：String
     * 字段名称：assessmentWeight
     * 数据库字段：assessment_weight
     */
    @Size(max = 1000, message = "考核权重长度不能超过1000个字符")
    private String assessmentWeight;
    /**
     * 具体课程目标不同考核方式中考核占比要求：json
     * 字段类型：String
     * 字段名称：assessmentProportion
     * 数据库字段：assessment_proportion
     */
    @Size(max = 1000, message = "考核占比要求长度不能超过1000个字符")
    private String assessmentProportion;
    /**
     * 所属培养方案的版本
     * 字段类型：Integer
     * 字段名称：courseVersion
     * 数据库字段：course_version
     */
    private Integer courseVersion;
    /**
     * 课程所属培养方案id
     * 字段类型：Integer
     * 字段名称：planId
     * 数据库字段：plan_id
     */
    @NotNull(message = "培养方案ID不能为空", groups = {ValidGroup.Add.class, ValidGroup.Update.class})
    private Integer planId;
    /**
     * 专业编号-专业表
     * 字段类型：Integer
     * 字段名称：majorId
     * 数据库字段：major_id
     */
    private Integer majorId;
    /**
     * 记录状态{0:正常状态；-1:删除状态；}
     * 字段类型：Integer
     * 字段名称：status
     * 数据库字段：status
     */
    private Integer status;

} 