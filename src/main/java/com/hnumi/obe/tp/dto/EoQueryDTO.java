package com.hnumi.obe.tp.dto;

import com.hnumi.obe.common.entity.BasePage;
import com.hnumi.obe.tp.entity.Eo;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 培养目标表 数据查询参数对象（DTO）
 * 
 * QueryDTO（Data Transfer Object）用于前端向后端查询数据封装
 * 主要用于：
 * 1. 封装请求参数
 * 2. 在不同服务层之间传递数据
 * 3. 隐藏实体类的敏感字段
 * 4. 优化数据传输结构
 */
@Data
public class EoQueryDTO extends BasePage<Eo> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 培养目标ID
     * 字段类型：Long
     * 字段名称：id
     * 数据库字段：id
     */
    private Long id;
    /**
     * 培养目标标题
     * 字段类型：String
     * 字段名称：eoTitle
     * 数据库字段：eo_title
     */
    private String eoTitle;
    /**
     * 培养目标详情
     * 字段类型：String
     * 字段名称：eoDescription
     * 数据库字段：eo_description
     */
    private String eoDescription;
    /**
     * 所属专业培养计划id
     * 字段类型：Long
     * 字段名称：planId
     * 数据库字段：plan_id
     */
    private Long planId;
    /**
     * 所属专业id（冗余）
     * 字段类型：Long
     * 字段名称：majorId
     * 数据库字段：major_id
     */
    private Long majorId;
    /**
     * 所属学院id（冗余）
     * 字段类型：Long
     * 字段名称：academyId
     * 数据库字段：academy_id
     */
    private Long academyId;
    /**
     * 记录状态{0:正常状态；-1:删除状态；}
     * 字段类型：Integer
     * 字段名称：status
     * 数据库字段：status
     */
    private Integer status;
    /**
     * 记录创建者
     * 字段类型：Long
     * 字段名称：creator
     * 数据库字段：creator
     */
    private Long creator;
    /**
     * 记录
     * 字段类型：LocalDateTime
     * 字段名称：createTime
     * 数据库字段：create_time
     */
    private LocalDateTime createTime;
    /**
     * 记录最后修改者
     * 字段类型：Long
     * 字段名称：modifier
     * 数据库字段：modifier
     */
    private Long modifier;
    /**
     * 记录最后修改时间
     * 字段类型：LocalDateTime
     * 字段名称：modifyTime
     * 数据库字段：modify_time
     */
    private LocalDateTime modifyTime;
} 