package com.hnumi.obe.tp.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 培养目标表 数据传输对象（DTO）
 * <p>
 * DTO（Data Transfer Object）用于服务层之间的数据传输
 * 主要用于：
 * 1. 封装请求参数
 * 2. 在不同服务层之间传递数据
 * 3. 隐藏实体类的敏感字段
 * 4. 优化数据传输结构
 */
@Data
public class EoDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 培养目标ID
     * 字段类型：Long
     * 字段名称：id
     * 数据库字段：id
     */
    private Long id;

    /**
     * 培养目标标题
     * 字段类型：String
     * 字段名称：eoTitle
     * 数据库字段：eo_title
     */
    private String eoTitle;

    /**
     * 培养目标详情
     * 字段类型：String
     * 字段名称：eoDescription
     * 数据库字段：eo_description
     */
    private String eoDescription;

    private String relatedQuestions;

    /**
     * 所属专业培养计划id
     * 字段类型：Long
     * 字段名称：planId
     * 数据库字段：plan_id
     */
    private Long planId;

    /**
     * 所属专业id（冗余）
     * 字段类型：Long
     * 字段名称：majorId
     * 数据库字段：major_id
     */
    private Long majorId;

    /**
     * 所属学院id（冗余）
     * 字段类型：Long
     * 字段名称：academyId
     * 数据库字段：academy_id
     */
    private Long academyId;

}