package com.hnumi.obe.tp.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 毕业要求表 数据传输对象（DTO）
 * 
 * DTO（Data Transfer Object）用于服务层之间的数据传输
 * 主要用于：
 * 1. 封装请求参数
 * 2. 在不同服务层之间传递数据
 * 3. 隐藏实体类的敏感字段
 * 4. 优化数据传输结构
 */
@Data
public class PoDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 专业毕业要求二级指标ID
     * 字段类型：Long
     * 字段名称：id
     * 数据库字段：id
     */
    private Long id;

    private Integer poNumber;
    /**
     * 毕业要求标题
     * 字段类型：String
     * 字段名称：poTitle
     * 数据库字段：po_title
     */
    private String poTitle;
    /**
     * 毕业要求详情
     * 字段类型：String
     * 字段名称：poDescription
     * 数据库字段：po_description
     */
    private String poDescription;
    /**
     * 所属工程教育认证指标id
     * 字段类型：String
     * 字段名称：standardId
     * 数据库字段：standard_id
     */
    private Long standardId;
    /**
     * 该记录是否为毕业要求一级指标
     * 字段类型：Boolean
     * 字段名称：isRequirement
     * 数据库字段：is_requirement
     * 展示说明：用于前端展示的该记录是否为毕业要求一级指标,
     */
    private Boolean isRequirement;

    private Long parentId;

    private Long requirementId;

    /**
     * 所属专业培养计划id
     * 字段类型：Long
     * 字段名称：planId
     * 数据库字段：plan_id
     */
    private Long planId;
    /**
     * 所属专业id（冗余）
     * 字段类型：Long
     * 字段名称：majorId
     * 数据库字段：major_id
     */
    private Long majorId;
    /**
     * 所属学院id（冗余）
     * 字段类型：Long
     * 字段名称：academyId
     * 数据库字段：academy_id
     */
    private Long academyId;
} 