package com.hnumi.obe.tp.dto;

import com.hnumi.obe.common.entity.BasePage;
import com.hnumi.obe.tp.vo.TaskVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TaskQueryDTO extends BasePage<TaskVO> {

    private Integer semester;

    private String taskName;

    private Long teacherId;

    private Long classId;

    private Integer taskYear;

    private Integer taskTerm;

}
