package com.hnumi.obe.tp.dto;

import com.hnumi.obe.tp.vo.*;
import lombok.Data;
import java.util.List;

/**
 * 课程考核数据传输对象 - 统一管理所有考核相关数据
 * <AUTHOR>
 * @create 2025-06-29
 */
@Data
public class CourseAssessmentDataDTO {

    private Long courseId;
    private String courseName;

    private List<AssessmentMethodVO> assessmentMethods;

    /**
     * 课程目标列表
     */
    private List<CourseObjectiveVO> courseObjectiveList;

    /**
     * 表1：课程考核各环节占比配置
     * 展示每个考核环节对不同课程目标的占比分配
     */
    private List<AssessmentConfigVO> assessmentProportions;

    /**
     * 表2：不同环节课程目标考核权重分配
     * 展示每个课程目标在不同考核环节中的权重分配
     */
    private List<AssessmentConfigVO> assessmentWeight;

    /**
     * 期末考试各考核环节权重配置
     * 展示各考核环节在期末成绩中的权重
     */
    private List<AssessmentConfigVO> finalExamWeights;
}
