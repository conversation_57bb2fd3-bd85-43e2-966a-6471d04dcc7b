package com.hnumi.obe.tp.dto;

import com.hnumi.obe.common.valid.ValidGroup;
import com.hnumi.obe.tp.vo.CourseBookVO;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 课程教材信息 DTO
 * 参考 AssessmentDTO 的设计模式
 */
@Data
public class CourseBookDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 课程ID
     */
    @NotNull(groups = {ValidGroup.Add.class, ValidGroup.Update.class}, message = "课程ID不能为空")
    private Long courseId;

    /**
     * 主教材信息
     */
    private BookDetail mainBook;

    /**
     * 参考书列表
     */
    private List<BookDetail> referenceBooks;

    /**
     * 教材详细信息
     */
    @Data
    public static class BookDetail implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 教材ISBN
         */
        @NotBlank(groups = {ValidGroup.Add.class, ValidGroup.Update.class}, message = "教材ISBN不能为空")
        private String isbn;

        /**
         * 教材名称
         */
        @NotBlank(groups = {ValidGroup.Add.class, ValidGroup.Update.class}, message = "教材名称不能为空")
        private String name;

        /**
         * 教材作者
         */
        @NotBlank(groups = {ValidGroup.Add.class, ValidGroup.Update.class}, message = "教材作者不能为空")
        private String author;

        /**
         * 教材出版社
         */
        @NotBlank(groups = {ValidGroup.Add.class, ValidGroup.Update.class}, message = "教材出版社不能为空")
        private String publisher;

        /**
         * 教材版本
         */
        private String version;

        /**
         * 教材出版时间
         */
        private String publishDate;

        /**
         * 教材类型：MAIN-主教材，REFERENCE-参考书
         */
        @NotNull(groups = {ValidGroup.Add.class, ValidGroup.Update.class}, message = "教材类型不能为空")
        private CourseBookVO.BookType type;

        /**
         * 教材简介/备注
         */
        private String description;

        /**
         * 教材价格（可选）
         */
        private Double price;

        /**
         * 教材封面URL（可选）
         */
        private String coverUrl;

        /**
         * 教材适用范围/适用对象
         */
        private String applicableScope;
    }
}
