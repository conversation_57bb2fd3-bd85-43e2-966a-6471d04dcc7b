package com.hnumi.obe.tp.mapstruct;

import com.hnumi.obe.tp.dto.CourseBookDTO;
import com.hnumi.obe.tp.vo.CourseBookVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 课程教材信息转换器
 * 用于DTO和VO之间的转换
 */
@Mapper
public interface CourseBookConvert {

    CourseBookConvert INSTANCE = Mappers.getMapper(CourseBookConvert.class);

    /**
     * DTO转VO
     *
     * @param dto 课程教材DTO
     * @return 课程教材VO
     */
    CourseBookVO toVO(CourseBookDTO dto);

    /**
     * VO转DTO
     *
     * @param vo 课程教材VO
     * @return 课程教材DTO
     */
    CourseBookDTO toDTO(CourseBookVO vo);

    /**
     * BookDetail转BookDetailVO
     *
     * @param detail 教材详情DTO中的BookDetail
     * @return 教材详情VO中的BookDetailVO
     */
    CourseBookVO.BookDetailVO toBookDetailVO(CourseBookDTO.BookDetail detail);

    /**
     * BookDetailVO转BookDetail
     *
     * @param vo 教材详情VO中的BookDetailVO
     * @return 教材详情DTO中的BookDetail
     */
    CourseBookDTO.BookDetail toBookDetail(CourseBookVO.BookDetailVO vo);
}
