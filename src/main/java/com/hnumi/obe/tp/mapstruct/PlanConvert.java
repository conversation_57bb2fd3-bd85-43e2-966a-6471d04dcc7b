package com.hnumi.obe.tp.mapstruct;

import com.hnumi.obe.common.mapstruct.BaseConvert;
import com.hnumi.obe.tp.dto.EoDTO;
import com.hnumi.obe.tp.dto.PlanDTO;
import com.hnumi.obe.tp.entity.Eo;
import com.hnumi.obe.tp.entity.Plan;
import com.hnumi.obe.tp.vo.PlanVO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface PlanConvert extends BaseConvert<PlanVO, Plan> {
    PlanConvert INSTANCE = Mappers.getMapper(PlanConvert.class);

    Plan toEntity(PlanDTO dto);
}
