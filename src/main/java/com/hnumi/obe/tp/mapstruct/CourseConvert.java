package com.hnumi.obe.tp.mapstruct;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.hnumi.obe.common.mapstruct.BaseConvert;
import com.hnumi.obe.tp.dto.CourseDTO;
import com.hnumi.obe.tp.entity.Course;
import com.hnumi.obe.tp.vo.AssessmentConfigVO;
import com.hnumi.obe.tp.vo.AssessmentMethodVO;
import com.hnumi.obe.tp.vo.CourseBaseInfoVO;
import com.hnumi.obe.tp.vo.CourseObjectiveVO;
import com.hnumi.obe.tp.vo.CourseVO;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * 专业培养方案中的课程体系，课程要求带有版本号 对象转换器
 * 
 * 使用 MapStruct 实现对象之间的转换
 * 主要功能：
 * 1. 实体类与DTO之间的转换
 * 2. 实体类与VO之间的转换
 * 3. 集合对象的批量转换
 * 4. 自定义字段映射规则
 * 
 * 使用说明：
 * 1. 通过 INSTANCE 获取转换器实例
 * 2. 调用相应的转换方法进行对象转换
 * 3. 支持自定义字段映射规则
 * 4. 支持集合对象的批量转换
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface CourseConvert extends BaseConvert<CourseVO, Course> {
    /**
     * 转换器实例
     * 使用方式：CourseConvert.INSTANCE.toVO(entity)
     */
    CourseConvert INSTANCE = Mappers.getMapper(CourseConvert.class);

    Logger log = LoggerFactory.getLogger(CourseConvert.class);
    ObjectMapper objectMapper = new ObjectMapper();

    Course toEntity(CourseDTO dto);

    CourseVO toVO(Course entity);

    Page<CourseVO> toPageVO(Page<Course> page);

    CourseBaseInfoVO toBaseInfoVO(Course course);

    void updateEntityFromDto(CourseDTO dto, @MappingTarget Course entity);



    /**
     * 自定义映射方法，将 List<CourseObjectiveVO> 转换为 JSON 字符串
     * @param value 要转换的列表
     * @return JSON 字符串
     */
    default String map(List<CourseObjectiveVO> value) {
        if (value == null || value.isEmpty()) {
            return null;
        }
        try {
            return objectMapper.writeValueAsString(value);
        } catch (JsonProcessingException e) {
            log.error("将 CourseObjectiveVO 列表转换为 JSON 字符串失败", e);
            return null;
        }
    }
}
