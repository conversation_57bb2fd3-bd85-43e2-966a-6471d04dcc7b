package com.hnumi.obe.tp.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hnumi.obe.tp.entity.Eo;
import com.hnumi.obe.tp.vo.EoVO;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 培养目标表 Mapper 接口
 */
@Mapper
public interface EoMapper extends BaseMapper<Eo> {
    @Select("select requirement_id as requirementId, eo_title as eoTitle,eo_description as eoDescription,status,create_time,modify_time from tp_eo where plan_id=#{planId} and status=0")
    List<EoVO> selectAllByPlanId(Long planId);

    @Update("UPDATE tp_eo set status = -1 where plan_id = #{planId}")
    void deleteByPalnId(Long planId);
}
