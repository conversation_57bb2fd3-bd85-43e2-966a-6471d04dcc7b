package com.hnumi.obe.tp.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hnumi.obe.tp.entity.Po;
import com.hnumi.obe.tp.vo.PoVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
* 毕业要求表 Mapper 接口
*
*/
@Mapper
public interface PoMapper extends BaseMapper<Po> {

    @Select("select po_title as poTitle,po_description as poDescription,status,create_time,modify_time from tp_po where plan_id=#{planId}")
    List<PoVO> selectAllByPlanId(@Param("planId") Long planId);

    @Update("update tp_po set status = -1 where plan_id = #{planId}")
    void deleteByPalnId(Long planId);
}
