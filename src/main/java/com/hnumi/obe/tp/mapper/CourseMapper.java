package com.hnumi.obe.tp.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hnumi.obe.tp.entity.Course;
import com.hnumi.obe.tp.vo.CourseStatisticsVO;
import com.hnumi.obe.tp.vo.InstructorStatsVO;
import com.hnumi.obe.tp.vo.CourseCurrentSemesterVO;
import com.hnumi.obe.tp.vo.CourseTeachingHistoryVO;
import com.hnumi.obe.tp.vo.CourseTaskInfoVO;
import com.hnumi.obe.tp.vo.CourseTaskStatisticsVO;
import com.hnumi.obe.tp.vo.CourseTaskClassesVO;
import com.hnumi.obe.tp.vo.CourseTaskTeacherVO;
import com.hnumi.obe.tp.vo.CourseBaseInfoVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
* 专业培养方案中的课程体系，课程要求带有版本号 Mapper 接口
*
*/
@Mapper
public interface CourseMapper extends BaseMapper<Course> {
    @Select("""
            SELECT COALESCE(SUM(course_nature), 1)                AS requiredCourses,
                   COALESCE(SUM(course_credit), 0)              AS totalCredits,
                   COALESCE(SUM(course_hours_total), 0)         AS totalHours,
                   COALESCE(SUM(course_hours_experiment), 0)    AS practiceHours,
                   COUNT(*)                                     AS totalCourses
            FROM tp_course
            WHERE plan_id = #{planId}
              AND status = 0
            """)
    CourseStatisticsVO getCourseStatistics(@Param("planId") Long planId);


    @Select("""
            SELECT COUNT(*) AS totalCourses,
                   COUNT(CASE WHEN course_leader IS NOT NULL THEN 1 END) AS assignedCount,
                   COUNT(CASE WHEN course_leader IS NULL THEN 1 END) AS unassignedCount
            FROM tp_course
            WHERE plan_id = #{planId}
              AND status = 0
            """)
    InstructorStatsVO getInstructorStats(@Param("planId") Long planId);

    @Select("""
            SELECT course_id
            FROM tp_course
            WHERE course_code = #{courseCode}
              AND status = 0
            """)
    List<Long> selectCourseIdsByCourseCode(@Param("courseCode") String courseCode);

    /**
     * 获取教师学期课程统计
     * @param teacherId 教师ID
     * @param year 学年
     * @param termType 学期类型（0: 春季, 1: 秋季）
     * @return 当前学期课程统计列表
     */
    List<CourseCurrentSemesterVO> getTeacherSemesterCourses(@Param("teacherId") Long teacherId, @Param("year") Integer year, @Param("termType") Integer termType);
    /**
     * 获取课程授课历史统计
     * @param teacherId 教师ID
     * @return 课程授课历史统计列表
     */
    List<CourseTeachingHistoryVO> getTeachingHistoryCourses(@Param("teacherId") Long teacherId);

    /**
     * 获取课程任务基本信息
     * @param courseId 课程ID
     * @param taskId 教学任务ID
     * @return 课程任务基本信息
     */
    CourseTaskInfoVO getCourseTaskInfo(@Param("courseId") Long courseId, @Param("taskId") Long taskId);

    /**
     * 获取课程任务统计信息
     * @param courseId 课程ID
     * @param taskId 教学任务ID
     * @param teacherId 教师ID
     * @return 课程任务统计信息
     */
    CourseTaskStatisticsVO getCourseTaskStatistics(@Param("courseId") Long courseId, @Param("taskId") Long taskId, @Param("teacherId") Long teacherId);

    /**
     * 获取课程任务班级信息
     * @param courseId 课程ID
     * @param taskId 教学任务ID
     * @param teacherId 教师ID
     * @return 课程任务班级信息列表
     */
    List<CourseTaskClassesVO> getCourseTaskClasses(@Param("courseId") Long courseId, @Param("taskId") Long taskId, @Param("teacherId") Long teacherId);

    /**
     * 获取课程任务教师团队信息
     * @param courseId 课程ID
     * @param taskId 教学任务ID
     * @return 课程任务教师团队信息列表
     */
    List<CourseTaskTeacherVO> getCourseTaskTeachers(@Param("courseId") Long courseId, @Param("taskId") Long taskId);

    /**
     * 根据ID查询课程基础信息
     *
     * @param courseId 课程ID
     * @return 课程基础信息
     */
    Course selectCourseBaseInfoById(@Param("courseId") Long courseId);

    /**
     * 根据条件查询课程基础信息列表
     *
     * @param courseId     课程ID
     * @param courseCode   课程代码
     * @param courseName   课程名称
     * @param courseLeader 课程负责人
     * @param status       状态
     * @return 课程基础信息列表
     */
    List<Course> selectCourseBaseInfoList(
            @Param("courseId") Long courseId,
            @Param("courseCode") String courseCode,
            @Param("courseName") String courseName,
            @Param("courseLeader") Long courseLeader,
            @Param("status") Integer status
    );
}
