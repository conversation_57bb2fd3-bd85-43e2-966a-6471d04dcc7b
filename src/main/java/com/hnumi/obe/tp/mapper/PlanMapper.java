package com.hnumi.obe.tp.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hnumi.obe.tp.dto.PlanDTO;
import com.hnumi.obe.tp.entity.Plan;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Update;

@Mapper
public interface PlanMapper extends BaseMapper<Plan> {
    @Update("update tp_plan set status = -1 where id = #{planId}")
    void deletePlanByPlanId(Long planId);
}
