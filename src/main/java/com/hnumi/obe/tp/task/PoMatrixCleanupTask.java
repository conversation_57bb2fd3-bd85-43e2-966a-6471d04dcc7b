package com.hnumi.obe.tp.task;

import com.hnumi.obe.tp.service.PoMatrixService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * PoMatrix数据清理定时任务
 * 定期清理引用已删除或不存在PO记录的PoMatrix记录
 */
@Slf4j
@Component
public class PoMatrixCleanupTask {

    @Autowired
    private PoMatrixService poMatrixService;

    /**
     * 定期清理无效的PoMatrix记录
     * 每天凌晨2点执行一次
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void cleanupInvalidPoMatrixRecords() {
        log.info("开始执行PoMatrix数据清理定时任务");
        
        try {
            // 清理所有计划的无效PoMatrix记录
            int cleanedCount = poMatrixService.cleanupInvalidPoMatrixRecords(null);
            
            if (cleanedCount > 0) {
                log.info("PoMatrix数据清理完成，共清理了 {} 条无效记录", cleanedCount);
            } else {
                log.info("PoMatrix数据清理完成，没有发现无效记录");
            }
            
        } catch (Exception e) {
            log.error("PoMatrix数据清理任务执行失败", e);
        }
    }

    /**
     * 手动触发清理任务（用于测试或紧急清理）
     * 可以通过管理接口调用
     */
    public int manualCleanup() {
        log.info("手动触发PoMatrix数据清理");
        
        try {
            int cleanedCount = poMatrixService.cleanupInvalidPoMatrixRecords(null);
            log.info("手动清理完成，共清理了 {} 条无效记录", cleanedCount);
            return cleanedCount;
        } catch (Exception e) {
            log.error("手动清理失败", e);
            throw e;
        }
    }

    /**
     * 清理指定计划的无效PoMatrix记录
     * @param planId 计划ID
     * @return 清理的记录数量
     */
    public int cleanupByPlanId(Long planId) {
        log.info("清理计划 {} 的无效PoMatrix记录", planId);
        
        try {
            int cleanedCount = poMatrixService.cleanupInvalidPoMatrixRecords(planId);
            log.info("计划 {} 清理完成，共清理了 {} 条无效记录", planId, cleanedCount);
            return cleanedCount;
        } catch (Exception e) {
            log.error("计划 {} 清理失败", planId, e);
            throw e;
        }
    }
}
