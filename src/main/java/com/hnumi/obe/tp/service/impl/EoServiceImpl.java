package com.hnumi.obe.tp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hnumi.obe.tp.dto.EoQueryDTO;
import com.hnumi.obe.tp.entity.Eo;
import com.hnumi.obe.tp.mapper.EoMapper;
import com.hnumi.obe.tp.mapstruct.EoConvert;
import com.hnumi.obe.tp.service.IEoService;
import com.hnumi.obe.tp.vo.EoExportVO;
import com.hnumi.obe.tp.vo.EoVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class EoServiceImpl extends ServiceImpl<EoMapper, Eo> implements IEoService {

    @Autowired
    private EoMapper eoMapper;

    @Override
    public Object deleteById(Long id) {
        Eo eo = new Eo();
        eo.setId(id);
        eo.setStatus(-1);
        return eoMapper.updateById(eo);
    }

    @Override
    public List<EoVO> getAllByPlanId(Long planId) {
        // 修改为使用lambda query wrapper
        QueryWrapper<Eo> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(Eo::getPlanId, planId).eq(Eo::getStatus, 0);
        return eoMapper.selectList(queryWrapper).stream().map(EoConvert.INSTANCE::toVO).collect(Collectors.toList());
    }

    @Override
    public List<EoExportVO> getEoList(EoQueryDTO query) {
        LambdaQueryWrapper<Eo> wrapper = Wrappers.lambdaQuery(Eo.class);
        /*
        wrapper.eq(!Objects.isNull(query.getId()), Eo::getId, query.getId())
                .like(StringUtil.isNotBlank(query.getEoTitle()), Eo::getEoTitle, query.getEoTitle())
                .like(StringUtil.isNotBlank(query.getEoDescription()), Eo::getEoDescription, query.getEoDescription())
                .eq(!Objects.isNull(query.getMajorId()), Eo::getMajorId, query.getMajorId())
                .eq(!Objects.isNull(query.getAcademyId()), Eo::getAcademyId, query.getAcademyId())
                .eq(!Objects.isNull(query.getPlanId()), Eo::getPlanId, query.getPlanId())
                .eq(!Objects.isNull(query.getStatus()), Eo::getStatus, query.getStatus());
          *如果模糊查询，则使用like，批量导出再加上这一段
         */
        List<Eo> data = list(wrapper);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        return data.stream().map(eo -> {
            EoExportVO eoExportVO = new EoExportVO();
            Integer status = eo.getStatus();
            //  status，创建时间，修改时间的数据状态转换
            switch (status) {
                case 0:
                    eoExportVO.setStatus("正常");
                    break;
                default:
                    eoExportVO.setStatus("删除");
                    break;
            }

            if (eo.getCreateTime() != null) {
                eoExportVO.setCreateTime(eo.getCreateTime().format(formatter));
            }
            if (eo.getModifyTime() != null) {
                eoExportVO.setModifyTime(eo.getModifyTime().format(formatter));
            }

            BeanUtils.copyProperties(eo, eoExportVO, "status", "createTime", "modifyTime");
            return eoExportVO;
        }).collect(Collectors.toList());
    }


}
