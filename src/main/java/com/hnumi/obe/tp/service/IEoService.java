package com.hnumi.obe.tp.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hnumi.obe.tp.dto.EoQueryDTO;
import com.hnumi.obe.tp.entity.Eo;
import com.hnumi.obe.tp.vo.EoExportVO;
import com.hnumi.obe.tp.vo.EoVO;

import java.util.List;

public interface IEoService extends IService<Eo> {

    Object deleteById(Long id);

    List<EoVO> getAllByPlanId(Long planId);

    List<EoExportVO> getEoList(EoQueryDTO query);
}
