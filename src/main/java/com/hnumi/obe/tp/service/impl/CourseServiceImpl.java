package com.hnumi.obe.tp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hnumi.obe.common.util.JSONUtil;
import com.hnumi.obe.common.util.RequestUtil;
import com.hnumi.obe.tp.util.CourseObjectiveJsonUtil;
import com.hnumi.obe.tp.dto.CourseAssessmentDataDTO;
import com.hnumi.obe.tp.dto.CourseBookDTO;
import com.hnumi.obe.tp.dto.CourseQueryDTO;
import com.hnumi.obe.tp.entity.Course;
import com.hnumi.obe.tp.mapper.CourseMapper;
import com.hnumi.obe.tp.mapstruct.CourseBookConvert;
import com.hnumi.obe.tp.mapstruct.CourseConvert;
import com.hnumi.obe.tp.service.ICourseService;
import com.hnumi.obe.tp.util.CourseBookJsonUtil;
import com.hnumi.obe.tp.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 专业培养方案中的课程体系，课程要求带有版本号 服务实现类 - 优化版本
 */
@Slf4j
@Service
public class CourseServiceImpl extends ServiceImpl<CourseMapper, Course> implements ICourseService {

   @Autowired
    private CourseMapper courseMapper;



    //==================================通用转换方法==================================
    /**
     * 转换为CourseBaseInfoVO的通用方法
     */
    private CourseBaseInfoVO convertToCourseBaseInfoVO(Course course) {
        CourseBaseInfoVO baseInfoVO = CourseConvert.INSTANCE.toBaseInfoVO(course);

        // 处理课程目标
        List<CourseObjectiveVO> objectives = JSONUtil.parseJson(course.getCourseTarget(), CourseObjectiveVO.class);
        baseInfoVO.setCourseObjectives(objectives);

        // 处理考核方式
        List<AssessmentMethodVO> assessmentMethods = JSONUtil.parseJson(course.getAssessmentMethod(), AssessmentMethodVO.class);
        baseInfoVO.setAssessmentMethodList(assessmentMethods);

        //处理教材信息
        CourseBookVO courseBookVO = CourseBookJsonUtil.fromJson(course.getCourseBook());
        if (courseBookVO != null) {
            baseInfoVO.setCourseBookVO(courseBookVO);
        } else {
            log.warn("课程教材信息为空，课程ID: {}", course.getCourseId());
            baseInfoVO.setCourseBookVO(new CourseBookVO());
        }

        return baseInfoVO;
    }

    /**
     * 转换为CourseVO的通用方法
     */
    private CourseVO convertToCourseVO(Course course) {
        CourseVO courseVO = CourseConvert.INSTANCE.toVO(course);

        // 处理课程目标信息
        List<CourseObjectiveVO> objectives = JSONUtil.parseJson(course.getCourseTarget(), CourseObjectiveVO.class);
        courseVO.setCourseObjectiveList(objectives);

        // 处理考核方法信息
        List<AssessmentMethodVO> assessmentMethods = JSONUtil.parseJson(course.getAssessmentMethod(), AssessmentMethodVO.class);
        courseVO.setAssessmentMethodList(assessmentMethods);

        // 处理考核权重信息
        List<AssessmentConfigVO> assessmentWeights = JSONUtil.parseJson(course.getAssessmentWeight(), AssessmentConfigVO.class);
        courseVO.setAssessmentWeightList(assessmentWeights);

        // 处理考核占比信息
        List<AssessmentConfigVO> assessmentProportions = JSONUtil.parseJson(course.getAssessmentProportion(), AssessmentConfigVO.class);
        courseVO.setAssessmentProportionList(assessmentProportions);

        // 处理教材信息
        try {
            CourseBookVO courseBookVO = getCourseBooks(course.getCourseId());
            courseVO.setCourseBookVO(courseBookVO);
        } catch (Exception e) {
            log.error("获取课程教材信息失败，课程ID: {}", course.getCourseId(), e);
            courseVO.setCourseBookVO(null);
        }

        return courseVO;
    }

    @Override
    public List<AssessmentConfigVO> getCourseAssessmentProportions(Long courseId) {
        Course course = courseMapper.selectById(courseId);
        if (course != null) {
            return JSONUtil.parseJson(course.getAssessmentWeight(), AssessmentConfigVO.class);
        }
        return List.of();
    }


    //=========================课程相关接口实现=========================

    @Override
    public Object deleteById(Long courseId) {
        Course course = new Course();
        course.setCourseId(courseId);
        course.setStatus(-1);
        return courseMapper.updateById(course);
    }

    @Override
    public String getCourseNameById(Long courseId) {
        if (courseId == null) {
            return null;
        }

        LambdaQueryWrapper<Course> wrapper = Wrappers.lambdaQuery(Course.class);
        wrapper.select(Course::getCourseName)
               .eq(Course::getCourseId, courseId)
               .eq(Course::getStatus, 0); // 只查询有效记录

        Course course = courseMapper.selectOne(wrapper);
        return course != null ? course.getCourseName() : null;
    }

    @Override
    public List<Long> getCourseIdByCode(String courseCode) {
        if (!StringUtils.hasText(courseCode)) {
            return null;
        }
        List<Long> courseIds = courseMapper.selectCourseIdsByCourseCode(courseCode);
        return courseIds;

    }

    @Override
    public List<CourseVO> getCourseListByLeaderId(Long majorId) {
        LambdaQueryWrapper<Course> wrapper = Wrappers.lambdaQuery(Course.class);
        //Teacher teacher = RequestUtil.getCurrentTeacher();
       // assert teacher != null;
        wrapper.eq(Course::getCourseLeader, RequestUtil.getExtendId());
        wrapper.eq(Course::getMajorId, majorId);
        wrapper.eq(Course::getStatus, 0); // Default to active records

        wrapper.orderByDesc(Course::getCreateTime); // Default sort
        return courseMapper.selectList(wrapper).stream()
                .map(CourseConvert.INSTANCE::toVO)
                .toList();

    }

    @Override
    public List<CourseBaseInfoVO> findCourseBaseInfoList(String courseCode) {
        log.info("开始查询课程基础信息列表，课程代码：{}", courseCode);

        try {
            // 构建查询条件
            LambdaQueryWrapper<Course> wrapper = Wrappers.lambdaQuery(Course.class);

            // 添加课程代码查询条件（支持模糊查询）
            if (StringUtils.hasText(courseCode)) {
                wrapper.like(Course::getCourseCode, courseCode.trim());
            }

            // 只查询正常状态的记录
            wrapper.eq(Course::getStatus, 0);

            // 按创建时间倒序排列
//            wrapper.orderByDesc(Course::getCreateTime);

            // 执行查询
            List<Course> courses = courseMapper.selectList(wrapper);

            if (CollectionUtils.isEmpty(courses)) {
                log.info("未找到匹配的课程信息，课程代码：{}", courseCode);
                return List.of();
            }

            // 批量转换为CourseBaseInfoVO
            List<CourseBaseInfoVO> result = courses.stream()
                .map(this::convertToCourseBaseInfoVO)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

            log.info("成功查询到 {} 条课程基础信息，课程代码：{}", result.size(), courseCode);
            return result;

        } catch (Exception e) {
            log.error("查询课程基础信息列表失败，课程代码：{}", courseCode, e);
            throw new RuntimeException("查询课程基础信息失败：" + e.getMessage(), e);
        }
    }

    @Override
    public CourseBaseInfoVO getCourseBaseInfoById(Long courseId) {
        if (courseId == null || courseId <= 0) {
            log.warn("课程ID无效：{}", courseId);
            return null;
        }

        // 使用Mapper中的方法查询课程基础信息
        Course course = courseMapper.selectById(courseId);
        if (course == null) {
            log.warn("未找到指定ID的课程信息，课程ID：{}", courseId);
            return null;
        }

        return convertToCourseBaseInfoVO(course);
    }

    @Override
    public CourseVO getCourseDetailInfoById(Long courseId) {
        if (courseId == null || courseId <= 0) {
            log.warn("课程ID无效：{}", courseId);
            return null;
        }

        // 查询课程完整信息
        Course course = courseMapper.selectById(courseId);
        if (course == null || course.getStatus() == -1) {
            log.warn("课程不存在或已删除，课程ID：{}", courseId);
            return null;
        }

        return convertToCourseVO(course);
    }


    /**
     * 获取课程统计信息
     *
     * @param majorId 专业ID
     * @param planId  培养方案ID
     * @return {@link CourseStatisticsVO}
     */
    @Override
    public CourseStatisticsVO getCourseStatistics(Long majorId, Long planId) {
        return courseMapper.getCourseStatistics(planId);
    }

    @Override
    public InstructorStatsVO getInstructorStats(Long majorId, Long planId) {
        return courseMapper.getInstructorStats(planId);
    }

    @Override
    public List<Course> getCourseListByMajorAndPlanAndSemester(Long planId, Long semesterId) {
        LambdaQueryWrapper<Course> query = Wrappers.<Course>lambdaQuery().select(Course::getCourseId, Course::getCourseName)
                //.eq(Course::getMajorId, majorId)
                .eq(Course::getPlanId, planId)
                .eq(Course::getCourseSemester, semesterId)
                .eq(Course::getStatus, 0);
        return courseMapper.selectList(query);
    }

//=========================课程目标相关接口实现=========================

    @Override
    public Integer updateCourseTarget(Long courseId, List<CourseObjectiveVO> target) {
        System.out.println("target:" + target);
        //仅仅更新课程目标，字段target，字段modifier，其他字段不更新
        Course course = new Course();
        course.setCourseId(courseId);
        course.setCourseTarget(JSONUtil.convertToJson(target));
        System.out.println("courseTarget:" + course.getCourseTarget());
        course.setModifier(RequestUtil.getUserId());
        return courseMapper.updateById(course);
    }


    private List<CourseObjectiveVO> getCourseObjectives(String objectiveJson) {
        if (objectiveJson == null || objectiveJson.isEmpty()) {
            log.warn("课程目标数据为空");
            return List.of();
        }
        // 解析课程目标数据 - 使用类型安全的方法
        List<CourseObjectiveVO> courseObjectives = JSONUtil.parseJson(objectiveJson, CourseObjectiveVO.class);
        if (courseObjectives == null || courseObjectives.isEmpty()) {
            log.warn("课程目标解析失败或无目标数据");
            return List.of();
        }
        return courseObjectives;
    }

    @Override
    public List<CourseObjectiveVO> getCourseObjectives(Long courseId) {

        // 获取课程的course_target字段
        Course course = courseMapper.selectById(courseId);
        if (course == null || course.getStatus() == -1) {
            log.error("课程不存在或已删除，课程ID: {}", courseId);
            return List.of();
        }

        return getCourseObjectives(course.getCourseTarget());

    }

    //=========================课程考核相关接口实现=========================

    @Override
    public AssessmentDetailVO getCourseAssessmentDetail(Long courseId) {
        // 从数据库中获取课程信息
        Course course = courseMapper.selectById(courseId);
        if (course == null || course.getStatus() == -1) {
            return null;
        }

        try {
            // 创建详情VO对象
            AssessmentDetailVO detailVO = new AssessmentDetailVO();
            detailVO.setCourseId(courseId);
            detailVO.setCourseName(course.getCourseName());

            //记载课程目标列表
            detailVO.setCourseObjectiveList(getCourseObjectives(courseId));

            // 解析课程考核方法数据：[{"id":"1","name":"期末考核","weight":50},{"id":"2","name":"平时考核","weight":50}]->List<AssessmentMethodVO>
            if (course.getAssessmentMethod() != null && !course.getAssessmentMethod().isEmpty()) {
                detailVO.setAssessmentMethods(JSONUtil.parseJson(course.getAssessmentMethod(), AssessmentMethodVO.class));
            }

            // 解析课程目标数据
            if (course.getCourseTarget() != null && !course.getCourseTarget().isEmpty()) {
                detailVO.setCourseObjectiveList(JSONUtil.parseJson(course.getCourseTarget(), CourseObjectiveVO.class));
            }

            // 解析期末考核权重数据
            if (course.getAssessmentWeight() != null && !course.getAssessmentWeight().isEmpty()) {
                detailVO.setFinalExamWeights(JSONUtil.parseJson(course.getAssessmentWeight(), AssessmentConfigVO.class));
            }

            // 解析考核占比数据
            if (course.getAssessmentProportion() != null && !course.getAssessmentProportion().isEmpty()) {
                detailVO.setAssessmentProportions(JSONUtil.parseJson(course.getAssessmentProportion(), AssessmentConfigVO.class));
            }

            // 解析目标权重分配数据（从考核权重JSON中提取）
            if (course.getAssessmentWeight() != null && !course.getAssessmentWeight().isEmpty()) {
                detailVO.setAssessmentWeight(JSONUtil.parseJson(course.getAssessmentWeight(), AssessmentConfigVO.class));
            }

            return detailVO;

        } catch (Exception e) {
            log.error("获取课程考核详情失败，课程ID: {}", courseId, e);
            return null;
        }
    }


    /**
     * 更新课程考核配置（使用新的数据结构）
     *
     * @param courseId       课程ID
     * @param assessmentData 考核配置数据
     * @return 更新结果
     */
    @Override
    public boolean updateCourseAssessmentConfig(Long courseId, CourseAssessmentDataDTO assessmentData) {
        try {
            // 验证课程是否存在
            Course course = courseMapper.selectById(courseId);
            if (course == null || course.getStatus() == -1) {
                log.error("课程不存在或已删除，课程ID: {}", courseId);
                return false;
            }
            // 解析考核方法详情
            String assessmentMethodJson = JSONUtil.convertToJson(assessmentData.getAssessmentMethods());
            String assessmentWeightJson = JSONUtil.convertToJson(assessmentData.getAssessmentWeight());
            String assessmentProportionJson = JSONUtil.convertToJson(assessmentData.getAssessmentProportions());
            String courseTargetJson = JSONUtil.convertToJson(assessmentData.getCourseObjectiveList());
            String finalExamWeightsJson = JSONUtil.convertToJson(assessmentData.getFinalExamWeights());

            // 更新数据库
            Course updateCourse = new Course();
            updateCourse.setCourseId(courseId);
            updateCourse.setAssessmentMethod(assessmentMethodJson);
            updateCourse.setAssessmentWeight(assessmentWeightJson);
            updateCourse.setAssessmentProportion(assessmentProportionJson);
            updateCourse.setCourseTarget(courseTargetJson);
            updateCourse.setAssessmentWeightExam(finalExamWeightsJson);
            updateCourse.setModifier(RequestUtil.getUserId());

            return courseMapper.updateById(updateCourse) > 0;

        } catch (Exception e) {
            log.error("更新课程考核配置失败，课程ID: {}", courseId, e);
            return false;
        }
    }





    // ==================== 课程教材信息业务实现 ====================

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveCourseBooks(Long courseId, CourseBookDTO courseBookDTO) {
        try {
            log.info("开始保存课程教材信息，课程ID：{}", courseId);

            // 1. 参数验证
            if (courseId == null || courseId <= 0) {
                log.warn("课程ID无效：{}", courseId);
                return false;
            }
            // 2. 检查课程是否存在
            Course existingCourse = this.getById(courseId);
            if (existingCourse == null || existingCourse.getStatus() == -1) {
                log.warn("课程不存在或已删除，课程ID：{}", courseId);
                return false;
            }
            //首先将existingCourse中的数据存储到courseBook
            // 3. DTO转VO
            CourseBookVO courseBookVO = CourseBookConvert.INSTANCE.toVO(courseBookDTO);

            // 5. 转换为JSON字符串

            String booksJson = JSONUtil.objectToJson(courseBookVO);

            // 6. 更新数据库
            Long userId = RequestUtil.getUserId();
            LambdaUpdateWrapper<Course> updateWrapper = Wrappers.lambdaUpdate(Course.class);
            updateWrapper.eq(Course::getCourseId, courseId);
            updateWrapper.set(Course::getCourseBook, booksJson);
            updateWrapper.set(Course::getModifier, userId);

            return this.update(updateWrapper);


        } catch (Exception e) {
            log.error("保存课程教材信息失败，课程ID：{}", courseId, e);
            return false;
        }
    }




    @Override
    public CourseBookVO getCourseBooks(Long courseId) {
        try {
            log.info("开始获取课程教材信息，课程ID：{}", courseId);

            // 1. 参数验证
            if (courseId == null || courseId <= 0) {
                log.warn("课程ID无效：{}", courseId);
                return null;
            }

            // 2. 查询课程信息
            Course course = this.getById(courseId);
            if (course == null || course.getStatus() == -1) {
                log.warn("课程不存在或已删除，课程ID：{}", courseId);
                return null;
            }

            // 3. 获取教材JSON字符串
            String booksJson = course.getCourseBook();
            if (booksJson == null || booksJson.trim().isEmpty()) {
                log.info("课程暂无教材信息，课程ID：{}", courseId);
                return null;
            }

            // 4. JSON转换为VO
            CourseBookVO courseBookVO = CourseBookJsonUtil.fromJson(booksJson);

            if (courseBookVO != null) {
                log.info("课程教材信息获取成功，课程ID：{}，主教材：{}",
                        courseId,
                        courseBookVO.getMainBook() != null ? courseBookVO.getMainBook().getName() : "无");
            }

            return courseBookVO;

        } catch (Exception e) {
            log.error("获取课程教材信息失败，课程ID：{}", courseId, e);
            return null;
        }
    }

// TODO: 以下接口是Taskservice，是任务类，不是课程类，应该迁移到TaskService中
    //========================课程任务相关接口实现=========================
    @Override
    public List<CourseCurrentSemesterVO> getTeacherSemesterCourses(Long teacherId, Integer year, Integer termType) {
        return courseMapper.getTeacherSemesterCourses(teacherId, year, termType);
    }

    @Override
    public List<CourseTeachingHistoryVO> getTeachingHistoryCourses(Long teacherId) {
        return courseMapper.getTeachingHistoryCourses(teacherId);
    }

    @Override
    public CourseTaskInfoVO getCourseTaskInfo(Long courseId, Long taskId) {
        // TODO: 实现课程任务基本信息查询
        // 需要关联查询tp_course和task_worklist表
        return courseMapper.getCourseTaskInfo(courseId, taskId);
    }

    @Override
    public CourseTaskStatisticsVO getCourseTaskStatistics(Long courseId, Long taskId, Long teacherId) {
        // 统计考核项目数，班级数和学生数由前端从班级数据中统计
        return courseMapper.getCourseTaskStatistics(courseId, taskId, teacherId);
    }

    @Override
    public List<CourseTaskClassesVO> getCourseTaskClasses(Long courseId, Long taskId, Long teacherId) {
        // 查询该课程任务下指定教师的班级信息
        return courseMapper.getCourseTaskClasses(courseId, taskId, teacherId);
    }

    @Override
    public List<CourseTaskTeacherVO> getCourseTaskTeachers(Long courseId, Long taskId) {
        // TODO: 实现课程任务教师团队信息查询
        // 需要关联查询task_worklist_teachers和base_teacher表
        return courseMapper.getCourseTaskTeachers(courseId, taskId);
    }
}
