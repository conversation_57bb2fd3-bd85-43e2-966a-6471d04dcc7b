package com.hnumi.obe.tp.service.impl;

import com.hnumi.obe.tp.dto.CourseBookDTO;
import com.hnumi.obe.tp.mapstruct.CourseBookConvert;
import com.hnumi.obe.tp.service.ICourseBookService;
import com.hnumi.obe.tp.util.CourseBookJsonUtil;
import com.hnumi.obe.tp.vo.CourseBookVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 课程教材服务实现类
 * 演示如何使用新的教材存储结构
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CourseBookServiceImpl implements ICourseBookService {

    // 注入你的Course相关服务或Mapper
    // private final ICourseService courseService;
    // private final CourseMapper courseMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveCourseBooks(Long courseId, CourseBookDTO courseBookDTO) {
        try {
            log.info("开始保存课程教材信息，课程ID：{}", courseId);

            // 1. 参数验证
            if (courseId == null || courseId <= 0) {
                log.warn("课程ID无效：{}", courseId);
                return false;
            }

            if (courseBookDTO == null) {
                log.warn("教材信息为空");
                return false;
            }

            // 2. DTO转VO
            CourseBookVO courseBookVO = CourseBookConvert.INSTANCE.toVO(courseBookDTO);

            // 3. 验证教材信息完整性
            if (!CourseBookJsonUtil.validate(courseBookVO)) {
                log.warn("教材信息验证失败");
                return false;
            }

            // 4. 转换为JSON字符串
            String booksJson = CourseBookJsonUtil.toJson(courseBookVO);
            if (booksJson == null) {
                log.error("教材信息转换为JSON失败");
                return false;
            }

            // 5. 保存到数据库
            // 示例SQL：UPDATE tp_course SET books_json = ? WHERE id = ?
            // courseMapper.updateBooksJson(courseId, booksJson);

            log.info("课程教材信息保存成功，课程ID：{}，主教材：{}，参考书数量：{}",
                    courseId,
                    courseBookVO.getMainBook().getName(),
                    courseBookVO.getReferenceBooks() != null ? courseBookVO.getReferenceBooks().size() : 0);

            return true;

        } catch (Exception e) {
            log.error("保存课程教材信息失败，课程ID：{}", courseId, e);
            return false;
        }
    }

    @Override
    public CourseBookVO getCourseBooks(Long courseId) {
        try {
            log.info("开始获取课程教材信息，课程ID：{}", courseId);

            // 1. 参数验证
            if (courseId == null || courseId <= 0) {
                log.warn("课程ID无效：{}", courseId);
                return null;
            }

            // 2. 从数据库获取JSON字符串
            // 示例SQL：SELECT books_json FROM tp_course WHERE id = ?
            // String booksJson = courseMapper.getBooksJsonById(courseId);
            String booksJson = ""; // 临时占位符

            // 3. JSON转换为VO
            CourseBookVO courseBookVO = CourseBookJsonUtil.fromJson(booksJson);

            if (courseBookVO != null) {
                log.info("课程教材信息获取成功，课程ID：{}，主教材：{}",
                        courseId,
                        courseBookVO.getMainBook() != null ? courseBookVO.getMainBook().getName() : "无");
            }

            return courseBookVO;

        } catch (Exception e) {
            log.error("获取课程教材信息失败，课程ID：{}", courseId, e);
            return null;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateCourseBooks(Long courseId, CourseBookDTO courseBookDTO) {
        try {
            log.info("开始更新课程教材信息，课程ID：{}", courseId);

            // 更新逻辑与保存类似
            return saveCourseBooks(courseId, courseBookDTO);

        } catch (Exception e) {
            log.error("更新课程教材信息失败，课程ID：{}", courseId, e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteCourseBooks(Long courseId) {
        try {
            log.info("开始删除课程教材信息，课程ID：{}", courseId);

            // 1. 参数验证
            if (courseId == null || courseId <= 0) {
                log.warn("课程ID无效：{}", courseId);
                return false;
            }

            // 2. 删除教材信息（设置为null或空字符串）
            // 示例SQL：UPDATE tp_course SET books_json = NULL WHERE id = ?
            // courseMapper.updateBooksJson(courseId, null);

            log.info("课程教材信息删除成功，课程ID：{}", courseId);
            return true;

        } catch (Exception e) {
            log.error("删除课程教材信息失败，课程ID：{}", courseId, e);
            return false;
        }
    }
}
