package com.hnumi.obe.tp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hnumi.obe.base.entity.Academy;
import com.hnumi.obe.base.entity.Classes;
import com.hnumi.obe.base.entity.Major;
import com.hnumi.obe.base.entity.Standard;
import com.hnumi.obe.base.entity.Teacher;
import com.hnumi.obe.base.service.IAcademyService;
import com.hnumi.obe.base.service.IClassesService;
import com.hnumi.obe.base.service.IMajorService;
import com.hnumi.obe.base.service.IStandardService;
import com.hnumi.obe.base.service.ITeacherService;
import com.hnumi.obe.common.entity.PageResponse;
import com.hnumi.obe.system.entity.BaseUser;
import com.hnumi.obe.system.service.IBaseUserService;
import com.hnumi.obe.task.entity.TaskWork;
import com.hnumi.obe.task.entity.TaskWorklistClasses;
import com.hnumi.obe.task.entity.TaskWorklistTeachers;
import com.hnumi.obe.task.mapper.TaskWorkMapper;
import com.hnumi.obe.task.service.ITaskWorklistClassesService;
import com.hnumi.obe.task.service.ITaskWorklistTeachersService;
import com.hnumi.obe.tp.dto.PlanDTO;
import com.hnumi.obe.tp.dto.PlanQueryDTO;
import com.hnumi.obe.tp.dto.TaskQueryDTO;
import com.hnumi.obe.tp.entity.Course;
import com.hnumi.obe.tp.entity.Plan;
import com.hnumi.obe.tp.entity.Po;
import com.hnumi.obe.tp.mapper.PlanMapper;
import com.hnumi.obe.tp.mapper.PoMapper;
import com.hnumi.obe.tp.mapstruct.PlanConvert;
import com.hnumi.obe.tp.service.ICourseService;
import com.hnumi.obe.tp.service.IPlanService;
import com.hnumi.obe.tp.service.IPoService;
import com.hnumi.obe.tp.vo.PlanDetailVO;
import com.hnumi.obe.tp.vo.PlanSemesterVO;
import com.hnumi.obe.tp.vo.PlanVO;
import com.hnumi.obe.tp.vo.SemesterClassesVO;
import com.hnumi.obe.tp.vo.SemesterCourseVO;
import com.hnumi.obe.tp.vo.TaskClassVO;
import com.hnumi.obe.tp.vo.TaskTeacherVO;
import com.hnumi.obe.tp.vo.TaskVO;
import com.hnumi.obe.tp.vo.TeacherOptionVO;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.text.MessageFormat;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class PlanServiceImpl extends ServiceImpl<PlanMapper, Plan> implements IPlanService {

    @Resource
    PlanMapper planMapper;

    @Resource
    ITeacherService teacherService;

    @Resource
    IMajorService majorService;

    @Resource
    IAcademyService academyService;

    @Resource
    IBaseUserService baseUserService;

    @Resource
    IStandardService standardService;

    @Resource
    PoMapper poMapper;

    @Resource
    IPoService poService;

    @Resource
    ICourseService courseService;

    @Resource
    IClassesService classesService;

    @Resource
    TaskWorkMapper taskWorkMapper;

    @Resource
    ITaskWorklistTeachersService taskWorklistTeachersService;

    @Resource
    ITaskWorklistClassesService taskWorklistClassesService;

    @Override
    public Page<PlanVO> planList(Long userId, PlanQueryDTO planQueryDTO) {
        Teacher teacher = teacherService.getByTeacherId(userId);
        if (teacher == null) {
            throw new RuntimeException("用户不存在");
        }
        Long academyId = teacher.getAcademyId();
        Long majorId = planQueryDTO.getMajorId();
        if (majorId == null) {
            throw new RuntimeException("专业不能为空");
        }
        LambdaQueryWrapper<Plan> wrapper = Wrappers.lambdaQuery(Plan.class);
        wrapper.eq(Plan::getMajorId, majorId)
                .eq(Plan::getAcademyId, academyId)
                .in(Plan::getStatus, 0, 1);

        Page<Plan> planList = planMapper.selectPage(Page.of(planQueryDTO.getCurrent(), planQueryDTO.getSize()),
                wrapper);
        List<PlanVO> vo = PlanConvert.INSTANCE.toVO(planList.getRecords());
        Page<PlanVO> result = new Page<>();
        result.setRecords(vo);
        result.setTotal(planList.getTotal());
        return result;
    }

    @Override
    public void addPlan(Long userId, PlanDTO planDTO) {

        Teacher teacher = teacherService.getByTeacherId(userId);
        if (teacher == null) {
            throw new RuntimeException("用户不存在");
        }
        final Long academyId = teacher.getAcademyId();
        Major major = majorService.getMajorByAcademyLeaderId(userId);
        if (major == null) {
            throw new RuntimeException("专业不存在");
        }
        final Long majorId = major.getMajorId();
        LambdaQueryWrapper<Plan> existWrapper = Wrappers.lambdaQuery(Plan.class);
        existWrapper
                .eq(Plan::getAcademyId, academyId)
                .eq(Plan::getMajorId, majorId)
                .eq(Plan::getPlanVersion, planDTO.getPlanVersion());
        if (planMapper.exists(existWrapper)) {
            throw new RuntimeException(
                    MessageFormat.format("当前专业已存在版本为{0}的培养方案", String.valueOf(planDTO.getPlanVersion())));
        }

        Plan plan = new Plan();
        plan.setPlanName(planDTO.getPlanName());
        plan.setPlanVersion(planDTO.getPlanVersion());
        plan.setMajorId(majorId);
        plan.setAcademyId(academyId);
        plan.setStandardId(planDTO.getStandardId());
        plan.setStatus(planDTO.getStatus());
        plan.setCreator(userId);
        plan.setModifier(userId);

        planMapper.insert(plan);

        copyStandardToPo(planDTO.getStandardId(), plan.getId(), majorId, academyId);
    }

    private void copyStandardToPo(Long standardId, Long planId, Long majorId, Long academyId) {
        List<Standard> indicators = standardService.getIndicatorsByParentId(standardId);
        List<Po> poList = indicators.stream().map(s -> {
            Po po = new Po();
            po.setPoNumber(s.getStandardNumber());
            po.setPoTitle(s.getStandardName());
            po.setPoDescription(s.getStandardDescription());
            po.setIsRequirement(true);
            po.setStandardId(standardId);
            po.setPlanId(planId);
            po.setMajorId(majorId);
            po.setAcademyId(academyId);
            po.setStatus(0);
            return po;
        }).toList();

        if (poList != null && !poList.isEmpty()) {
            poMapper.insert(poList);
        }
    }

    @Override
    public void updatePlan(Long userId, PlanDTO planDTO) {
        // 校验用户是否存在
        Teacher teacher = teacherService.getByTeacherId(userId);
        if (teacher == null) {
            throw new RuntimeException("用户不存在");
        }

        // 获取专业信息
        Major major = majorService.getMajorByAcademyLeaderId(userId);
        if (major == null) {
            throw new RuntimeException("专业不存在");
        }

        // 查询原计划数据
        Plan existingPlan = planMapper.selectById(planDTO.getPlanId());
        if (existingPlan == null) {
            throw new RuntimeException("计划不存在");
        }

        // 更新计划基本信息
        Plan newPlan = new Plan();
        newPlan.setId(existingPlan.getId());
        newPlan.setPlanName(planDTO.getPlanName());
        newPlan.setPlanVersion(planDTO.getPlanVersion());
        newPlan.setStandardId(planDTO.getStandardId());
        newPlan.setStatus(planDTO.getStatus());
        newPlan.setModifier(userId);

        // 持久化更新
        planMapper.updateById(newPlan);

        // 智能更新PO数据
        updatePoDataIntelligently(existingPlan, planDTO.getStandardId());
    }

    private void deletePoByPlanId(Long id) {
        LambdaQueryWrapper<Po> wrapper = Wrappers.lambdaQuery(Po.class);
        wrapper.eq(Po::getPlanId, id);
        poMapper.delete(wrapper);
    }

    @Override
    public void deletePlanByplanId(Long userId, Long planId) {
        // 删除培养计划以及和其关联的所有的数据，先做软删除，status置为-1
        Plan plan = new Plan();
        plan.setId(planId);
        plan.setStatus(-1);
        planMapper.updateById(plan);


    }

    @Override
    public PlanDetailVO planDetail(Long id) {
        Plan plan = getById(id);
        if (plan == null) {
            throw new RuntimeException("计划不存在");
        }

        Major major = majorService.getById(plan.getMajorId());
        if (major == null) {
            throw new RuntimeException("专业不存在");
        }

        Academy academy = academyService.getById(major.getAcademyId());
        if (academy == null) {
            throw new RuntimeException("学院不存在");
        }

        Standard standard = standardService.getById(plan.getStandardId());
        if (standard == null) {
            throw new RuntimeException("认证标准不存在");
        }

        BaseUser leader = baseUserService.getById(major.getAcademyLeaderId());

        return PlanDetailVO.from(plan, academy, major, leader, standard);
    }

    @Override
    public List<PlanSemesterVO> semesters(Long planId) {
        Plan plan = planMapper.selectById(planId);
        if (plan == null) {
            throw new RuntimeException("培养计划不存在");
        }
        Long planVersion = plan.getPlanVersion();
        List<PlanSemesterVO> list = new ArrayList<>();
        LocalDate now = LocalDate.now();

        // 从入学年份开始，生成4年8个学期
        int startYear = planVersion.intValue();

        for (int year = 0; year < 4; year++) {
            // 第一学期：9月1日 - 1月31日
            int currentYear = startYear + year;
            LocalDate firstTermStart = LocalDate.of(currentYear, 9, 1);
            LocalDate firstTermEnd = LocalDate.of(currentYear + 1, 1, 31);
            String firstTermName = MessageFormat.format("{0}-{1}学年第1学期", currentYear, currentYear + 1);
            boolean firstTermCurrent = now.isAfter(firstTermStart.minusDays(1))
                    && now.isBefore(firstTermEnd.plusDays(1));
            String firstTermYear = MessageFormat.format("{0}-{1}", currentYear, currentYear + 1);
            list.add(PlanSemesterVO.create((long) (year * 2 + 1), firstTermName, startYear, firstTermStart, firstTermEnd,
                    firstTermCurrent, firstTermYear, "1"));

            // 第二学期：2月1日 - 7月31日
            LocalDate secondTermStart = LocalDate.of(currentYear + 1, 2, 1);
            LocalDate secondTermEnd = LocalDate.of(currentYear + 1, 7, 31);
            String secondTermName = MessageFormat.format("{0}-{1}学年第2学期", currentYear, currentYear + 1);
            boolean secondTermCurrent = now.isAfter(secondTermStart.minusDays(1))
                    && now.isBefore(secondTermEnd.plusDays(1));
            String secondTermYear = MessageFormat.format("{0}-{1}", currentYear, currentYear + 1);
            list.add(PlanSemesterVO.create((long) (year * 2 + 2), secondTermName, startYear, secondTermStart, secondTermEnd,
                    secondTermCurrent, secondTermYear, "2"));
        }

        return list.reversed();
    }

    @Override
    public List<SemesterCourseVO> semesterCourse(Long majorId, Long planId, Long semesterId) {
        List<Course> courseList = courseService.getCourseListByMajorAndPlanAndSemester(planId, semesterId);
        return courseList.stream().map(SemesterCourseVO::create).toList();
    }

    @Override
    public List<SemesterClassesVO> semesterClasses(Long majorId, Long planId) {
        Plan plan = planMapper.selectById(planId);
        if (plan == null) {
            throw new RuntimeException("培养计划不存在");
        }
        Long planVersion = plan.getPlanVersion();
        List<Classes> classesList = classesService.getClassByMajorAndEntranceYear(majorId, planVersion.intValue());
        return classesList.stream().map(SemesterClassesVO::create).collect(Collectors.toList());
    }

//    @Override
//    public PageResponse<TaskVO> tasks(Long planId, Long majorId, TaskQueryDTO queryDTO) {
//        // 构建查询条件
//        LambdaQueryWrapper<TaskWork> wrapper = Wrappers.<TaskWork>lambdaQuery()
//                .eq(TaskWork::getPlanId, planId)
//                .eq(TaskWork::getMajorId, majorId)
//                .eq(TaskWork::getStatus, 0);
//
//        // 添加基本查询条件
//        if (queryDTO.getSemester() != null) {
//            wrapper.eq(TaskWork::getTaskTerm, queryDTO.getSemester());
//        }
//        if (queryDTO.getTaskYear() != null) {
//            wrapper.eq(TaskWork::getTaskYear, queryDTO.getTaskYear());
//        }
//        if (queryDTO.getTaskTerm() != null) {
//            wrapper.eq(TaskWork::getTaskTerm, queryDTO.getTaskTerm());
//        }
//        if (queryDTO.getTaskName() != null && !queryDTO.getTaskName().trim().isEmpty()) {
//            wrapper.like(TaskWork::getTaskName, queryDTO.getTaskName());
//        }
//
//        // 使用EXISTS子查询处理关联表条件
//        if (queryDTO.getTeacherId() != null) {
//            wrapper.exists(
//                    "SELECT 1 FROM task_worklist_teachers twt WHERE twt.task_id = task_worklist.id AND twt.teacher_id = {0}",
//                    queryDTO.getTeacherId());
//        }
//
//        if (queryDTO.getClassId() != null) {
//            wrapper.exists(
//                    "SELECT 1 FROM task_worklist_classes twc WHERE twc.task_id = task_worklist.id AND twc.class_id = {0}",
//                    queryDTO.getClassId());
//        }
//
//        // 执行分页查询
//        Page<TaskWork> taskWorkPage = taskWorkMapper.selectPage(
//                Page.of(queryDTO.getCurrent(), queryDTO.getSize()), wrapper);
//
//        List<TaskWork> records = taskWorkPage.getRecords();
//        long total = taskWorkPage.getTotal();
//        List<Long> list = records.stream().map(TaskWork::getId).toList();
//        Map<Long, List<TaskWorklistClasses>> classesMap = taskWorklistClassesService.getClassesMap(list);
//        Map<Long, List<TaskWorklistTeachers>> teachersMap = taskWorklistTeachersService.getTeachersMap(list);
//
//        Set<Long> classesIds = classesMap.values().stream().flatMap(Collection::stream)
//                .map(TaskWorklistClasses::getClassId).collect(Collectors.toSet());
//        Set<Long> teacherIds = teachersMap.values().stream().flatMap(Collection::stream)
//                .map(TaskWorklistTeachers::getTeacherId).collect(Collectors.toSet());
//        Map<Long, BaseUser> teacherMap = baseUserService.getMap(teacherIds);
//        Map<Long, Classes> map = classesService.getMap(classesIds);
//        List<TaskVO> result = new ArrayList<>();
//        for (TaskWork record : records) {
//            // 获取班级信息
//            List<Classes> classesList = classesMap.getOrDefault(record.getId(), new ArrayList<>()).stream()
//                    .map(t -> map.get(t.getClassId()))
//                    .filter(Objects::nonNull)
//                    .toList();
//
//            List<TaskClassVO> taskClassVOS = classesList.stream()
//                    .map(TaskClassVO::from)
//                    .toList();
//
//            List<TaskTeacherVO> taskTeacherVOS = teachersMap.getOrDefault(record.getId(), new ArrayList<>()).stream()
//                    .map(t -> teacherMap.get(t.getTeacherId()))
//                    .filter(Objects::nonNull)
//                    .map( teacher -> TaskTeacherVO::from)
//                    .toList();
//
//            // 计算总学生人数
//            int totalStudents = classesList.stream()
//                    .mapToInt(cls -> cls.getStudentNumber() != null ? cls.getStudentNumber() : 0)
//                    .sum();
//            Course course = courseService.getById(record.getCourseId());
//            TaskVO item = new TaskVO(record.getId(), record.getTaskName(), record.getCourseId(),course.getCourseName(),course.getCourseCredit(),course.getCourseHoursTotal(),
//                    taskTeacherVOS, taskClassVOS, totalStudents);
//            result.add(item);
//        }
//
//        return new PageResponse<>(result, total);
//    }

    @Override
    public List<TeacherOptionVO> getTeacherOptions(Long planId, Long majorId) {
        // 查询该专业和计划下所有教学任务
        List<Long> taskIds = taskWorkMapper.selectList(
                Wrappers.<TaskWork>lambdaQuery()
                        .eq(TaskWork::getPlanId, planId)
                        .eq(TaskWork::getMajorId, majorId)
                        .eq(TaskWork::getStatus, 0))
                .stream().map(TaskWork::getId).toList();

        if (taskIds.isEmpty()) {
            return new ArrayList<>();
        }

        // 获取所有参与教学任务的教师ID
        Set<Long> teacherIds = taskWorklistTeachersService.getTeachersMap(taskIds)
                .values().stream()
                .flatMap(Collection::stream)
                .map(TaskWorklistTeachers::getTeacherId)
                .collect(Collectors.toSet());

        // 获取教师信息并转换为VO
        Map<Long, BaseUser> teacherMap = baseUserService.getMap(teacherIds);
        return teacherMap.values().stream()
                .map(TeacherOptionVO::from)
                .sorted((a, b) -> a.name().compareTo(b.name()))
                .collect(Collectors.toList());
    }

    @Override
    public List<SemesterClassesVO> getClassOptions(Long planId, Long majorId) {
        // 查询该专业和计划下所有教学任务
        List<Long> taskIds = taskWorkMapper.selectList(
                Wrappers.<TaskWork>lambdaQuery()
                        .eq(TaskWork::getPlanId, planId)
                        .eq(TaskWork::getMajorId, majorId)
                        .eq(TaskWork::getStatus, 0))
                .stream().map(TaskWork::getId).toList();

        if (taskIds.isEmpty()) {
            return new ArrayList<>();
        }

        // 获取所有参与教学任务的班级ID
        Set<Long> classIds = taskWorklistClassesService.getClassesMap(taskIds)
                .values().stream()
                .flatMap(Collection::stream)
                .map(TaskWorklistClasses::getClassId)
                .collect(Collectors.toSet());

        // 获取班级信息并转换为VO
        Map<Long, Classes> classMap = classesService.getMap(classIds);
        return classMap.values().stream()
                .map(SemesterClassesVO::create)
                .sorted((a, b) -> a.name().compareTo(b.name()))
                .collect(Collectors.toList());
    }

    /**
     * 智能更新PO数据
     * 根据标准变更情况决定是否需要更新PO数据
     */
    private void updatePoDataIntelligently(Plan existingPlan, Long newStandardId) {
        Long currentStandardId = existingPlan.getStandardId();

        // 情况1：标准ID没有变化
        if (Objects.equals(currentStandardId, newStandardId)) {
            // 进一步检测指标是否有变化
            if (!hasIndicatorChanges(currentStandardId, existingPlan.getId())) {
                // 标准未变且指标一致，跳过PO操作
                return;
            }
        }

        // 情况2：标准变更或指标有差异，执行增量同步
        syncPoWithStandard(existingPlan.getId(), newStandardId,
                          existingPlan.getMajorId(), existingPlan.getAcademyId());
    }

    /**
     * 检测指标是否有变化
     * 比较新标准的指标ID列表与当前计划的PO ID列表
     */
    private boolean hasIndicatorChanges(Long standardId, Long planId) {
        // 获取新标准的指标ID列表
        List<Long> newIndicatorIds = standardService.getIndicatorIdsByStandardId(standardId);

        // 获取当前计划的PO ID列表
        List<Long> currentPoIds = poService.getRootPoIdsByPlanId(planId);

        // 比较两个列表是否完全一致（忽略顺序）
        // 使用HashSet进行比较，避免依赖外部工具类
        if (newIndicatorIds.size() != currentPoIds.size()) {
            return true; // 大小不同，说明有变化
        }

        Set<Long> newIndicatorSet = new HashSet<>(newIndicatorIds);
        Set<Long> currentPoSet = new HashSet<>(currentPoIds);

        return !newIndicatorSet.equals(currentPoSet);
    }

    /**
     * 增量同步PO数据与标准
     * 只对变化的部分进行增删操作，保留相同的PO记录不变
     */
    private void syncPoWithStandard(Long planId, Long newStandardId, Long majorId, Long academyId) {
        // 获取新标准的指标列表
        List<Standard> newIndicators = standardService.getIndicatorsByParentId(newStandardId);
        Set<Long> newIndicatorIds = newIndicators.stream()
                .map(Standard::getId)
                .collect(Collectors.toSet());

        // 获取当前计划的PO列表
        LambdaQueryWrapper<Po> currentPoWrapper = Wrappers.lambdaQuery(Po.class);
        currentPoWrapper.eq(Po::getPlanId, planId)
                       .eq(Po::getStatus, 0)
                       .eq(Po::getParentId, 0); // 只处理根级PO
        List<Po> currentPos = poMapper.selectList(currentPoWrapper);

        Set<Long> currentStandardIds = currentPos.stream()
                .map(Po::getStandardId)
                .collect(Collectors.toSet());

        // 识别需要删除的PO（存在于当前计划但不在新标准中）
        Set<Long> toDeleteStandardIds = new HashSet<>(currentStandardIds);
        toDeleteStandardIds.removeAll(newIndicatorIds);

        // 识别需要新增的PO（存在于新标准但不在当前计划中）
        Set<Long> toAddStandardIds = new HashSet<>(newIndicatorIds);
        toAddStandardIds.removeAll(currentStandardIds);

        // 删除不需要的PO
        if (!toDeleteStandardIds.isEmpty()) {
            deletePosByStandardIds(planId, toDeleteStandardIds);
        }

        // 新增需要的PO
        if (!toAddStandardIds.isEmpty()) {
            addPosByStandardIds(planId, newStandardId, majorId, academyId,
                               newIndicators, toAddStandardIds);
        }
    }

    /**
     * 根据标准ID删除对应的PO记录
     */
    private void deletePosByStandardIds(Long planId, Set<Long> standardIds) {
        LambdaQueryWrapper<Po> deleteWrapper = Wrappers.lambdaQuery(Po.class);
        deleteWrapper.eq(Po::getPlanId, planId)
                    .in(Po::getStandardId, standardIds)
                    .eq(Po::getStatus, 0);

        // 软删除：设置status为-1
        Po updatePo = new Po();
        updatePo.setStatus(-1);
        poMapper.update(updatePo, deleteWrapper);
    }

    /**
     * 根据标准ID新增对应的PO记录
     */
    private void addPosByStandardIds(Long planId, Long newStandardId, Long majorId, Long academyId,
                                   List<Standard> newIndicators, Set<Long> toAddStandardIds) {
        List<Po> newPoList = newIndicators.stream()
                .filter(indicator -> toAddStandardIds.contains(indicator.getId()))
                .map(indicator -> {
                    Po po = new Po();
                    po.setPoNumber(indicator.getStandardNumber());
                    po.setPoTitle(indicator.getStandardName());
                    po.setPoDescription(indicator.getStandardDescription());
                    po.setIsRequirement(true);
                    po.setStandardId(indicator.getId());
                    po.setParentId(0L); // 根级PO
                    po.setPlanId(planId);
                    po.setMajorId(majorId);
                    po.setAcademyId(academyId);
                    po.setStatus(0);
                    return po;
                }).collect(Collectors.toList());

        if (!newPoList.isEmpty()) {
            poMapper.insert(newPoList);
        }
    }

}
