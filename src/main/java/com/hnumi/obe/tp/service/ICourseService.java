package com.hnumi.obe.tp.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hnumi.obe.tp.dto.CourseAssessmentDataDTO;
import com.hnumi.obe.tp.dto.CourseQueryDTO;
import com.hnumi.obe.tp.entity.Course;
import com.hnumi.obe.tp.vo.*;
import com.hnumi.obe.tp.dto.CourseBookDTO;

import java.util.List;

/**
* 专业培养方案中的课程体系，课程要求带有版本号 服务类
*
*/
public interface ICourseService extends IService<Course> {

    Object deleteById(Long coueseId);

    /**
     * 通过专业ID获取该专业的所有课程列表？是不是应该增加课程负责人id TODO://
     * @param majorId
     * @return
     */
    List<CourseVO> getCourseListByLeaderId(Long majorId);

    /**
     * 修改课程的课程目标
     * @param courseId
     * @param target
     * @return
     */
    Integer updateCourseTarget(Long courseId, List<CourseObjectiveVO> target);

    /**
     * 获取课程考核详情
     * @param courseId 课程ID
     * @return 课程考核详情
     */
    AssessmentDetailVO getCourseAssessmentDetail(Long courseId);

    /**
     * 获取课程考核配置: 考核环节的占比
     * @param courseId
     * @return
     */
    List<AssessmentConfigVO> getCourseAssessmentProportions(Long courseId);

    /**
     * 更新课程考核配置（使用新的数据结构）
     * @param courseId 课程ID
     * @param assessmentData 考核配置数据
     * @return 更新结果
     */
    boolean updateCourseAssessmentConfig(Long courseId, CourseAssessmentDataDTO assessmentData);

    /**
     * 获取课程目标详细信息（按对象结构返回）
     * @param courseId 课程ID
     * @return 课程目标结构信息
     */
    List<CourseObjectiveVO> getCourseObjectives(Long courseId);

    /**
     * 获取课程基础信息列表
     * @param queryDTO 查询条件
     * @return 课程基础信息列表
     */
    List<CourseBaseInfoVO> findCourseBaseInfoList(String courseCode);
    /**
     * 获取课程基础信息列表
     * @param courseId 课程ID
     * @return 课程基础信息
     */
    CourseBaseInfoVO getCourseBaseInfoById(Long courseId);
    /**
     * 获取课程详细信息列表
     * @param courseId 课程ID
     * @return 课程详细信息
     */
    CourseVO getCourseDetailInfoById(Long courseId);


    CourseStatisticsVO getCourseStatistics(Long majorId, Long planId);

    InstructorStatsVO getInstructorStats(Long majorId, Long planId);

    /**
     * 根据课程ID获取课程名称
     * @param courseId
     * @return
     */
    String getCourseNameById(Long courseId);

    /**
     * 根据课程代码获取课程ID
     * @param courseCode 课程代码
     * @return 课程ID，如果未找到返回null
     */
    List<Long> getCourseIdByCode(String courseCode);

    List<Course> getCourseListByMajorAndPlanAndSemester(Long planId, Long semesterId);



//==============教材相关接口========================
    /**
     * 保存课程教材信息
     * @param courseId 课程ID
     * @param courseBookDTO 教材信息DTO
     * @return 保存结果
     */
    boolean saveCourseBooks(Long courseId, CourseBookDTO courseBookDTO);


    /**
     * 获取课程教材信息
     * @param courseId 课程ID
     * @return 教材信息VO
     */
    CourseBookVO getCourseBooks(Long courseId);

//    /**
//     * 删除课程教材信息
//     * @param courseId 课程ID
//     * @return 删除结果
//     */
//    boolean deleteCourseBooks(Long courseId);



    //=========================TODO: 需要迁移到TaskService中（课程任务相关接口）=========================
    /**
     * 获取教师学期课程统计
     * @param teacherId 教师ID
     * @param year 学年
     * @param termType 学期类型（0: 春季, 1: 秋季）
     * @return 当前学期课程统计列表
     */
    List<CourseCurrentSemesterVO> getTeacherSemesterCourses(Long teacherId, Integer year, Integer termType);

    /**
     * 获取课程授课历史统计
     * @param teacherId 教师ID
     * @return 课程授课历史统计列表
     */
    List<CourseTeachingHistoryVO> getTeachingHistoryCourses(Long teacherId);

    /**
     * 获取课程任务基本信息
     * @param courseId 课程ID
     * @param taskId 教学任务ID
     * @return 课程任务基本信息
     */
    CourseTaskInfoVO getCourseTaskInfo(Long courseId, Long taskId);

    /**
     * 获取课程任务统计信息
     * @param courseId 课程ID
     * @param taskId 教学任务ID
     * @param teacherId 教师ID
     * @return 课程任务统计信息
     */
    CourseTaskStatisticsVO getCourseTaskStatistics(Long courseId, Long taskId, Long teacherId);

    /**
     * 获取课程任务班级信息
     * @param courseId 课程ID
     * @param taskId 教学任务ID
     * @param teacherId 教师ID
     * @return 课程任务班级信息列表
     */
    List<CourseTaskClassesVO> getCourseTaskClasses(Long courseId, Long taskId, Long teacherId);

    /**
     * 获取课程任务教师团队信息
     * @param courseId 课程ID
     * @param taskId 教学任务ID
     * @return 课程任务教师团队信息列表
     */
    List<CourseTaskTeacherVO> getCourseTaskTeachers(Long courseId, Long taskId);

}