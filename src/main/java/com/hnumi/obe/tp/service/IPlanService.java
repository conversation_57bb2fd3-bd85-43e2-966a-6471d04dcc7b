package com.hnumi.obe.tp.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hnumi.obe.common.entity.PageResponse;
import com.hnumi.obe.tp.dto.PlanDTO;
import com.hnumi.obe.tp.dto.PlanQueryDTO;
import com.hnumi.obe.tp.dto.TaskQueryDTO;
import com.hnumi.obe.tp.entity.Plan;
import com.hnumi.obe.tp.vo.PlanDetailVO;
import com.hnumi.obe.tp.vo.PlanSemesterVO;
import com.hnumi.obe.tp.vo.PlanVO;
import com.hnumi.obe.tp.vo.SemesterClassesVO;
import com.hnumi.obe.tp.vo.SemesterCourseVO;
import com.hnumi.obe.tp.vo.TaskVO;
import com.hnumi.obe.tp.vo.TeacherOptionVO;

import java.util.List;

public interface IPlanService extends IService<Plan> {

    Page<PlanVO> planList(Long userId, PlanQueryDTO planQueryDTO);

    void addPlan(Long userId, PlanDTO planDTO);

    void updatePlan(Long userId, PlanDTO planDTO);

    void deletePlanByplanId(Long userId, Long planId);

    PlanDetailVO planDetail(Long id);

    List<PlanSemesterVO> semesters(Long planId);

    List<SemesterCourseVO> semesterCourse(Long majorId, Long planId, Long semesterId);

    List<SemesterClassesVO> semesterClasses(Long majorId, Long planId);

//    PageResponse<TaskVO> tasks(Long planId, Long majorId, TaskQueryDTO queryDTO);

    List<TeacherOptionVO> getTeacherOptions(Long planId, Long majorId);

    List<SemesterClassesVO> getClassOptions(Long planId, Long majorId);
}
