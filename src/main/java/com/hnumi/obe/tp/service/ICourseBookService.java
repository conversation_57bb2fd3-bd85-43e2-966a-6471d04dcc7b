package com.hnumi.obe.tp.service;

import com.hnumi.obe.tp.dto.CourseBookDTO;
import com.hnumi.obe.tp.vo.CourseBookVO;

/**
 * 课程教材服务接口
 */
public interface ICourseBookService {

    /**
     * 保存课程教材信息
     *
     * @param courseId 课程ID
     * @param courseBookDTO 教材信息
     * @return 保存结果
     */
    boolean saveCourseBooks(Long courseId, CourseBookDTO courseBookDTO);

    /**
     * 获取课程教材信息
     *
     * @param courseId 课程ID
     * @return 教材信息
     */
    CourseBookVO getCourseBooks(Long courseId);

    /**
     * 更新课程教材信息
     *
     * @param courseId 课程ID
     * @param courseBookDTO 教材信息
     * @return 更新结果
     */
    boolean updateCourseBooks(Long courseId, CourseBookDTO courseBookDTO);

    /**
     * 删除课程教材信息
     *
     * @param courseId 课程ID
     * @return 删除结果
     */
    boolean deleteCourseBooks(Long courseId);
}
