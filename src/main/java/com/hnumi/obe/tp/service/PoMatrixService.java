package com.hnumi.obe.tp.service;

import com.hnumi.obe.tp.entity.PoMatrix;
import com.hnumi.obe.tp.vo.CoursePoVO;
import com.hnumi.obe.tp.vo.PoVO;

import java.util.List;

public interface PoMatrixService {

    PoMatrix getById(Long id);

    List<PoMatrix> getByPlanId(Long planId);
    
    PoMatrix getByCourseIdAndPoId(Long courseId, Long poId);

    boolean create(PoMatrix poMatrix);

    boolean update(PoMatrix poMatrix);

    boolean deleteById(Long id);

    List<PoMatrix> list(PoMatrix queryParams); // 用于条件查询

    /**
     * 获取课程支撑的毕业要求指标列表（列表结构，只包含毕业要求，没有课程目标）
     * 但是包含目标的内容，标题和说明，不是只有id
     * 适合用于需要展示课程目标、毕业要求指标的场景
     * @param courseId
     * @return
     */
    List<PoVO> getPoListByCourseId(Long courseId);

    /**
     * 获取课支撑毕业要求指标体系（树形结构）
     * * 但是包含目标的内容，标题和说明，不是只有id
     * * 适合用于需要展示课程目标、毕业要求指标的具有层级结构的完整毕业要求指标信息的场景
     * @param courseId
     * @return
     */
    List<CoursePoVO> getCourseIndicatorsByCourseId(Long courseId); // 获取课程指标列表

    /**
     * 清理无效的PoMatrix记录
     * 删除引用已删除或不存在PO记录的PoMatrix记录
     * @param planId 计划ID，如果为null则清理所有计划的数据
     * @return 清理的记录数量
     */
    int cleanupInvalidPoMatrixRecords(Long planId);

    /**
     * 清理指定课程的无效PoMatrix记录
     * @param courseId 课程ID
     * @return 清理的记录数量
     */
    int cleanupInvalidPoMatrixRecordsByCourseId(Long courseId);
}

