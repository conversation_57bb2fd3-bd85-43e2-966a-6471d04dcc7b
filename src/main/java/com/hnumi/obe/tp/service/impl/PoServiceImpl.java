package com.hnumi.obe.tp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hnumi.obe.base.entity.Standard;
import com.hnumi.obe.base.mapper.StandardMapper;
import com.hnumi.obe.tp.dto.PoQueryDTO;
import com.hnumi.obe.tp.entity.Po;
import com.hnumi.obe.tp.mapper.PoMapper;
import com.hnumi.obe.tp.mapstruct.PoConvert;
import com.hnumi.obe.tp.service.IPoService;
import com.hnumi.obe.tp.vo.PoExportVO;
import com.hnumi.obe.tp.vo.PoTreeVO;
import com.hnumi.obe.tp.vo.PoVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 毕业要求表 服务实现类
 */
@Service
public class PoServiceImpl extends ServiceImpl<PoMapper, Po> implements IPoService {

    @Autowired
    private PoMapper poMapper;

    @Autowired
    private StandardMapper standardMapper;

    @Override
    public Integer deletePoById(Long id) {
        Po deleteParentPo = new Po();
        deleteParentPo.setStatus(-1);
        int update = poMapper.update(deleteParentPo, Wrappers.lambdaUpdate(Po.class).eq(Po::getParentId, id).eq(Po::getStatus, 0));

        Po deletePo = new Po();
        deletePo.setId(id);
        deletePo.setStatus(-1);
        return poMapper.updateById(deletePo) + update;
    }

    @Override
    public List<PoVO> getAllByPlanId(Long planId) {
        // 修改为使用lambda query wrapper
        QueryWrapper<Po> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(Po::getPlanId, planId).eq(Po::getStatus, 0);
        return poMapper.selectList(queryWrapper).stream().map(PoConvert.INSTANCE::toVO).collect(Collectors.toList());
    }

    @Override
    public List<PoExportVO> getPoList(PoQueryDTO query) {
        LambdaQueryWrapper<Po> wrapper = Wrappers.lambdaQuery(Po.class);
        List<Po> data = list(wrapper);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        return data.stream().map(po -> {
            PoExportVO poExportVO = new PoExportVO();
            Integer status = po.getStatus();
            // status，创建时间，修改时间的数据状态转换
            switch (status) {
                case 0:
                    poExportVO.setStatus("正常");
                    break;
                default:
                    poExportVO.setStatus("删除");
                    break;
            }
            if (po.getCreateTime() != null) {
                poExportVO.setCreateTime(po.getCreateTime().format(formatter));
            }
            if (po.getModifyTime() != null) {
                poExportVO.setModifyTime(po.getModifyTime().format(formatter));
            }
            BeanUtils.copyProperties(po, poExportVO, "status", "createTime", "modifyTime");
            return poExportVO;
        }).collect(Collectors.toList());
    }

    @Override
    public Map<PoVO, List<PoVO>> getPoMapByPlanId(Long planId) {
        LambdaQueryWrapper<Po> wrapper = Wrappers.lambdaQuery(Po.class)
                .eq(Po::getPlanId, planId)
                .eq(Po::getStatus, 0)
                .orderByAsc(Po::getCreateTime);

        List<Po> poList = poMapper.selectList(wrapper);
        if (poList.isEmpty()) {
            return Collections.emptyMap();
        }

        // 转换为VO对象
        List<PoVO> poVOs = poList.stream()
                .map(PoConvert.INSTANCE::toVO)
                .collect(Collectors.toList());

        // 构建id到PoVO的映射
        Map<Long, PoVO> idToPoVO = poVOs.stream()
                .collect(Collectors.toMap(PoVO::getId, vo -> vo));

        // 收集需要查询的standardIds
        Set<Long> standardIds = poVOs.stream()
                .map(PoVO::getStandardId)
                .collect(Collectors.toSet());

        // 批量查询Standard对象
        Map<Long, Standard> standardMap = new HashMap<>();
        if (!standardIds.isEmpty()) {
            standardMap = standardMapper.selectBatchIds(standardIds).stream()
                    .collect(Collectors.toMap(Standard::getId, standard -> standard));
        }

        // 构建层级Map
        Map<PoVO, List<PoVO>> resultMap = new HashMap<>();

        for (PoVO poVO : poVOs) {
            if (Boolean.TRUE.equals(poVO.getIsRequirement())) {
                // 一级指标点作为key
                resultMap.computeIfAbsent(poVO, k -> new ArrayList<>());
            } else {
                // 尝试通过parentId找到父级
                PoVO parentPoVO = Optional.ofNullable(poVO.getParentId())
                        .map(idToPoVO::get)
                        .orElse(null);

                if (parentPoVO != null) {
                    // 有父级，添加到父级的列表中
                    resultMap.computeIfAbsent(parentPoVO, k -> new ArrayList<>()).add(poVO);
                } else if (poVO.getStandardId() != null) {
                    // 没有父级，通过standardId查找
                    Standard standard = standardMap.get(poVO.getStandardId());
                    if (standard != null) {
                        // 创建基于standard的PoVO
                        PoVO standardPoVO = new PoVO();
                        standardPoVO.setId(standard.getId());
                        standardPoVO.setPoNumber(standard.getStandardNumber());
                        standardPoVO.setPoTitle(standard.getStandardName());
                        standardPoVO.setPoDescription(standard.getStandardDescription());
                        standardPoVO.setIsRequirement(true);

                        resultMap.computeIfAbsent(standardPoVO, k -> new ArrayList<>()).add(poVO);
                    }
                }
            }
        }

        return resultMap;
    }

    @Override
    public List<PoTreeVO> getPoTree(Long planId) {
        List<Po> poList = poMapper.selectList(Wrappers.lambdaQuery(Po.class)
                .eq(Po::getPlanId, planId)
                .eq(Po::getStatus, 0)
                .orderByAsc(Po::getPoNumber));

        List<PoTreeVO> result = new ArrayList<>();

        Map<Long, PoTreeVO> poTreeMap = new HashMap<>();
        for (Po po : poList) {
            PoTreeVO poTreeVO = PoTreeVO.of(po);
            poTreeMap.put(po.getId(), poTreeVO);
        }

        for (Po po : poList) {
            if (po.getParentId() != null) {
                PoTreeVO parentPoTreeVO = poTreeMap.get(po.getParentId());
                if (parentPoTreeVO != null) {
                    parentPoTreeVO.children().add(poTreeMap.get(po.getId()));
                }
            } else {
                result.add(poTreeMap.get(po.getId()));
            }
        }
        return result;
    }

    @Override
    public List<Long> getRootPoIdsByPlanId(Long planId) {
        LambdaQueryWrapper<Po> wrapper = Wrappers.lambdaQuery(Po.class);
        wrapper.eq(Po::getPlanId, planId)
               .eq(Po::getStatus, 0) // 只查询正常状态的PO
               .eq(Po::getParentId, 0) // 只查询根级PO记录
               .orderByAsc(Po::getId); // 按ID排序确保结果一致

        return this.list(wrapper).stream()
                .map(Po::getId)
                .collect(Collectors.toList());
    }
}
