package com.hnumi.obe.tp.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hnumi.obe.base.entity.Standard;
import com.hnumi.obe.base.mapper.StandardMapper;
import com.hnumi.obe.common.util.RequestUtil;
import com.hnumi.obe.tp.entity.Po;
import com.hnumi.obe.tp.entity.PoMatrix;
import com.hnumi.obe.tp.mapper.PoMapper;
import com.hnumi.obe.tp.mapper.PoMatrixMapper;
import com.hnumi.obe.tp.mapstruct.PoConvert;
import com.hnumi.obe.tp.service.PoMatrixService;
import com.hnumi.obe.tp.vo.CoursePoVO;
import com.hnumi.obe.tp.vo.PoVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class PoMatrixServiceImpl extends ServiceImpl<PoMatrixMapper, PoMatrix> implements PoMatrixService {

    @Autowired
    private PoMatrixMapper poMatrixMapper;

    @Autowired
    private PoMapper poMapper;

    @Autowired
    private StandardMapper standardMapper;


    @Override
    public PoMatrix getById(Long id) {
        return poMatrixMapper.selectById(id);
    }

    @Override
    public List<PoMatrix> getByPlanId(Long planId) {
        LambdaQueryWrapper<PoMatrix> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PoMatrix::getPlanId, planId);
        wrapper.eq(PoMatrix::getStatus, 0); // 通常只查询有效记录
        return poMatrixMapper.selectList(wrapper);
    }

    @Override
    public PoMatrix getByCourseIdAndPoId(Long courseId, Long poId) {
        LambdaQueryWrapper<PoMatrix> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PoMatrix::getCourseId, courseId)
                .eq(PoMatrix::getPoId, poId)
                .eq(PoMatrix::getStatus, 0); // 通常只查询有效记录
        return poMatrixMapper.selectOne(wrapper);
    }

    @Override
    @Transactional
    public boolean create(PoMatrix poMatrix) {
        PoMatrix existingPoMatrix = this.getByCourseIdAndPoId(poMatrix.getCourseId(), poMatrix.getPoId());

        if (existingPoMatrix != null) {
            // 记录已存在，执行更新逻辑
            // 将传入 poMatrix 的需要更新的字段复制到 existingPoMatrix
            existingPoMatrix.setWeight(poMatrix.getWeight());
            existingPoMatrix.setModifier(poMatrix.getModifier()); // 设置修改者，如果poMatrix中包含
            existingPoMatrix.setModifyTime(LocalDateTime.now());

            return poMatrixMapper.updateById(existingPoMatrix) > 0;
        } else {
            // 记录不存在，执行插入逻辑
            poMatrix.setStatus(0);
            poMatrix.setCreateTime(LocalDateTime.now());
            poMatrix.setModifyTime(LocalDateTime.now());
            poMatrix.setCreator(RequestUtil.getUserId());
            poMatrix.setModifier(RequestUtil.getUserId());
            return poMatrixMapper.insert(poMatrix) > 0;
        }
    }

    @Override
    @Transactional
    public boolean update(PoMatrix poMatrix) {
        poMatrix.setModifyTime(LocalDateTime.now());
        // poMatrix.setModifier(...); // 设置修改者，通常从当前用户上下文获取
        return poMatrixMapper.updateById(poMatrix) > 0;
    }

    @Override
    @Transactional
    public boolean deleteById(Long id) {
        return poMatrixMapper.deleteById(id) > 0;
    }

    @Override
    public List<PoMatrix> list(PoMatrix queryParams) {
        LambdaQueryWrapper<PoMatrix> wrapper = new LambdaQueryWrapper<>();
        // 根据 queryParams 中的非空字段动态构建查询条件
        if (queryParams != null) {
            if (queryParams.getPlanId() != null) {
                wrapper.eq(PoMatrix::getPlanId, queryParams.getPlanId());
            }
            if (queryParams.getCourseId() != null) {
                wrapper.eq(PoMatrix::getCourseId, queryParams.getCourseId());
            }
            if (queryParams.getPoId() != null) {
                wrapper.eq(PoMatrix::getPoId, queryParams.getPoId());
            }
            if (queryParams.getStandardId() != null) {
                wrapper.eq(PoMatrix::getStandardId, queryParams.getStandardId());
            }
            if (queryParams.getStatus() != null) {
                wrapper.eq(PoMatrix::getStatus, queryParams.getStatus());
            } else {
                wrapper.eq(PoMatrix::getStatus, 0); // 默认查询有效记录
            }
            // 可以根据需要添加更多字段的条件
        }
        return poMatrixMapper.selectList(wrapper);
    }

    @Override
    public List<PoVO> getPoListByCourseId(Long courseId) {
        // 1. 根据课程ID查询PoMatrix表，获取所有相关的poId
        LambdaQueryWrapper<PoMatrix> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PoMatrix::getCourseId, courseId)
                .eq(PoMatrix::getStatus, 0); // 只查询有效记录
        List<PoMatrix> poMatrixList = poMatrixMapper.selectList(wrapper);

        // 2. 在查询前先清理无效的PoMatrix记录
        if (!poMatrixList.isEmpty()) {
            cleanupInvalidPoMatrixRecordsByCourseId(courseId);
            // 重新查询清理后的数据
            poMatrixList = poMatrixMapper.selectList(wrapper);
        }

        // 3. 提取poId列表
        List<Long> poIds = poMatrixList.stream()
                .map(PoMatrix::getPoId)
                .distinct()
                .collect(Collectors.toList());

        if (poIds.isEmpty()) {
            return List.of();
        }

        // 4. 根据poId查询Po表，获取完整的毕业要求信息
        LambdaQueryWrapper<Po> poWrapper = new LambdaQueryWrapper<>();
        poWrapper.in(Po::getId, poIds)
                .eq(Po::getStatus, 0); // 只查询有效记录
        List<Po> poList = poMapper.selectList(poWrapper);

        // 5. 将Po实体转换为PoVO对象
        return poList.stream()
                .map(PoConvert.INSTANCE::toVO)
                .collect(Collectors.toList());
    }

    @Override
    public List<CoursePoVO> getCourseIndicatorsByCourseId(Long courseId) {
        // 1. 查询该课程下所有有效的PoMatrix记录
        LambdaQueryWrapper<PoMatrix> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PoMatrix::getCourseId, courseId)
                .eq(PoMatrix::getStatus, 0);
        List<PoMatrix> poMatrixList = poMatrixMapper.selectList(wrapper);

        // 2. 在处理前先清理无效的PoMatrix记录
        if (!poMatrixList.isEmpty()) {
            cleanupInvalidPoMatrixRecordsByCourseId(courseId);
            // 重新查询清理后的数据
            poMatrixList = poMatrixMapper.selectList(wrapper);
        }

        if (poMatrixList.isEmpty()) {
            return List.of();
        }

        // 3. 提取所有poId
        List<Long> poIds = poMatrixList.stream()
                .map(PoMatrix::getPoId)
                .distinct()
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

       Set<Long> standardIdSet = new HashSet<>();

        // 4. 查询Po表，获取PO名称等信息，同时提取standardId
        Map<Long, Po> poMap;
        if (!poIds.isEmpty()) {
            LambdaQueryWrapper<Po> poWrapper = new LambdaQueryWrapper<>();
            poWrapper.in(Po::getId, poIds)
                    .eq(Po::getStatus, 0);
            List<Po> poList = poMapper.selectList(poWrapper);
            poMap = poList.stream().collect(Collectors.toMap(Po::getId, Function.identity()));
            System.out.println("poMap: " + poMap);
            // 将Po中的parentId添加到standardIdSet中
            poList.forEach(po -> {
                if (po.getParentId() != null) {
                    standardIdSet.add(po.getParentId());
                }
            });
        } else {
            poMap = new HashMap<>();
        }
        System.out.println("standardIdSet:" + standardIdSet);
        // 5. 查询Standard表，获取Standard信息
        Map<Long, Po> standardMap;
        if (!standardIdSet.isEmpty()) {
            LambdaQueryWrapper<Po> poWrapper = new LambdaQueryWrapper<>();
            poWrapper.in(Po::getId, standardIdSet)
                    .eq(Po::getStatus, 0);
            List<Po> standardList = poMapper.selectList(poWrapper);
            System.out.println("查询到的Standard记录数: " + standardList.size());
            //standardList.forEach(s -> System.out.println("Standard ID: " + s.getId() + ", Number: " + s.getStandardNumber()));
            standardMap = standardList.stream().collect(Collectors.toMap(Po::getId, Function.identity()));
        } else {
            standardMap = new HashMap<>();
        }

        // 6. 组装CoursePoVO
        return poMatrixList.stream()
                .filter(poMatrix -> poMatrix.getPoId() != null)
                .map(poMatrix -> {
                    Po po = poMap.get(poMatrix.getPoId());
                    Po standard = standardMap.get(po.getParentId());
                    System.out.println("standardId: " + poMatrix.getStandardId() + ", standard: " + standard);

                    CoursePoVO vo = new CoursePoVO();
                    vo.setCourseId(poMatrix.getCourseId());
                    vo.setPoId(poMatrix.getPoId());
                    vo.setWeight(poMatrix.getWeight());
                    vo.setStandardId(poMatrix.getStandardId());

                    if (po != null) {
                        vo.setPoNumber(po.getPoNumber());
                        vo.setPoTitle(po.getPoTitle());
                        vo.setPoDescription(po.getPoDescription());
                    }

                    if (standard != null) {
                        vo.setStandardNumber(standard.getPoNumber());
                    }

                    return vo;
                })
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public int cleanupInvalidPoMatrixRecords(Long planId) {
        log.info("开始清理无效的PoMatrix记录，planId: {}", planId);

        // 1. 构建查询条件
        LambdaQueryWrapper<PoMatrix> matrixWrapper = new LambdaQueryWrapper<>();
        matrixWrapper.eq(PoMatrix::getStatus, 0); // 只查询有效的PoMatrix记录
        if (planId != null) {
            matrixWrapper.eq(PoMatrix::getPlanId, planId);
        }

        List<PoMatrix> poMatrixList = poMatrixMapper.selectList(matrixWrapper);
        if (poMatrixList.isEmpty()) {
            log.info("没有找到需要检查的PoMatrix记录");
            return 0;
        }

        // 2. 提取所有poId进行批量查询
        List<Long> poIds = poMatrixList.stream()
                .map(PoMatrix::getPoId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        if (poIds.isEmpty()) {
            log.info("没有找到有效的poId");
            return 0;
        }

        // 3. 批量查询有效的PO记录
        LambdaQueryWrapper<Po> poWrapper = new LambdaQueryWrapper<>();
        poWrapper.in(Po::getId, poIds)
                .eq(Po::getStatus, 0); // 只查询有效的PO记录
        List<Po> validPoList = poMapper.selectList(poWrapper);

        Set<Long> validPoIds = validPoList.stream()
                .map(Po::getId)
                .collect(Collectors.toSet());

        // 4. 找出引用无效PO的PoMatrix记录
        List<Long> invalidMatrixIds = poMatrixList.stream()
                .filter(matrix -> matrix.getPoId() == null || !validPoIds.contains(matrix.getPoId()))
                .map(PoMatrix::getId)
                .collect(Collectors.toList());

        if (invalidMatrixIds.isEmpty()) {
            log.info("没有找到需要清理的无效PoMatrix记录");
            return 0;
        }

        // 5. 物理删除无效的PoMatrix记录
        int deletedCount = poMatrixMapper.deleteBatchIds(invalidMatrixIds);

        log.info("成功清理了 {} 条无效的PoMatrix记录，删除的记录ID: {}", deletedCount, invalidMatrixIds);
        return deletedCount;
    }

    @Override
    @Transactional
    public int cleanupInvalidPoMatrixRecordsByCourseId(Long courseId) {
        log.info("开始清理课程 {} 的无效PoMatrix记录", courseId);

        if (courseId == null) {
            log.warn("courseId为空，跳过清理");
            return 0;
        }

        // 1. 查询指定课程的所有PoMatrix记录
        LambdaQueryWrapper<PoMatrix> matrixWrapper = new LambdaQueryWrapper<>();
        matrixWrapper.eq(PoMatrix::getCourseId, courseId)
                    .eq(PoMatrix::getStatus, 0);

        List<PoMatrix> poMatrixList = poMatrixMapper.selectList(matrixWrapper);
        if (poMatrixList.isEmpty()) {
            log.info("课程 {} 没有找到需要检查的PoMatrix记录", courseId);
            return 0;
        }

        // 2. 提取所有poId进行批量查询
        List<Long> poIds = poMatrixList.stream()
                .map(PoMatrix::getPoId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        if (poIds.isEmpty()) {
            log.info("课程 {} 没有找到有效的poId", courseId);
            return 0;
        }

        // 3. 批量查询有效的PO记录
        LambdaQueryWrapper<Po> poWrapper = new LambdaQueryWrapper<>();
        poWrapper.in(Po::getId, poIds)
                .eq(Po::getStatus, 0);
        List<Po> validPoList = poMapper.selectList(poWrapper);

        Set<Long> validPoIds = validPoList.stream()
                .map(Po::getId)
                .collect(Collectors.toSet());

        // 4. 找出引用无效PO的PoMatrix记录
        List<Long> invalidMatrixIds = poMatrixList.stream()
                .filter(matrix -> matrix.getPoId() == null || !validPoIds.contains(matrix.getPoId()))
                .map(PoMatrix::getId)
                .collect(Collectors.toList());

        if (invalidMatrixIds.isEmpty()) {
            log.info("课程 {} 没有找到需要清理的无效PoMatrix记录", courseId);
            return 0;
        }

        // 5. 物理删除无效的PoMatrix记录
        int deletedCount = poMatrixMapper.deleteBatchIds(invalidMatrixIds);

        log.info("成功清理了课程 {} 的 {} 条无效PoMatrix记录，删除的记录ID: {}",
                courseId, deletedCount, invalidMatrixIds);
        return deletedCount;
    }
}
