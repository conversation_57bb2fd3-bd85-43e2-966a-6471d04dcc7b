package com.hnumi.obe.tp.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hnumi.obe.tp.dto.PoQueryDTO;
import com.hnumi.obe.tp.entity.Po;
import com.hnumi.obe.tp.vo.PoExportVO;
import com.hnumi.obe.tp.vo.PoTreeVO;
import com.hnumi.obe.tp.vo.PoVO;

import java.util.List;
import java.util.Map;

/**
* 毕业要求表 服务类
*
*/
public interface IPoService extends IService<Po> {
    Integer deletePoById(Long id);

    List<PoVO> getAllByPlanId(Long planId);

    List<PoExportVO> getPoList(PoQueryDTO query);

    /**
     * 根据培养方案id查询该培养方案定制的毕业要求列表
     * @param planId 培养方案id
     *               @return 毕业要求列表，进行层级划分Map<PoVO, List<PoVO>>
     */
    Map<PoVO, List<PoVO>> getPoMapByPlanId(Long planId);

    List<PoTreeVO> getPoTree(Long planId);

    /**
     * 获取指定计划下的所有PO ID列表
     * @param planId 计划ID
     * @return PO ID列表，仅包含status=0且parentId=0的根级PO记录（按ID排序）
     */
    List<Long> getRootPoIdsByPlanId(Long planId);
}
