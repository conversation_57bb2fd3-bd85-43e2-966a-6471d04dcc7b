package com.hnumi.obe.tp.vo;

import lombok.Data;

/**
 *
 * 课程任务教师信息VO
 */
// TODO: 需要移动到task中
@Data
public class CourseTaskTeacherVO {
    /**
     * 教师ID
     */
    private Long teacherId;
    
    /**
     * 教师姓名
     */
    private String teacherName;
    
    /**
     * 教师职称
     */
    private String teacherTitle;
    
    /**
     * 所属学院
     */
    private String academyName;
    
    /**
     * 角色ID：（task_worklist_teachers表中的role字段，关联字典表数据ID）
     */
    private String roleId;
    
    /**
     * 角色名称（从字典表获取，如果没有则使用role字段的值）
     */
    private String roleName;
    
    /**
     * 邮箱
     */
    private String email;
    
    /**
     * 所教授班级数
     */
    private Integer classCount;
    
    /**
     * 所教授学生数
     */
    private Integer studentCount;
    
    /**
     * 是否为课程负责人
     */
    private Boolean isCourseLeader;
} 