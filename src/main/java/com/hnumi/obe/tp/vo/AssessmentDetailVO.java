package com.hnumi.obe.tp.vo;

import lombok.Data;

import java.util.List;

/**
 * 课程考核详情VO - 重新设计为结构化属性
 * <AUTHOR>
 * @create 2025-06-28 23:05
 */
@Data
public class AssessmentDetailVO {


    private Long courseId;
    private String courseName;
    private String courseCode;
    /**
     * 考核方式列表
     * 包含所有考核方式的详细信息
     */

    private List<AssessmentMethodVO> assessmentMethods;

    /**
     * 课程目标列表
     */
    private List<CourseObjectiveVO> courseObjectiveList;

    /**
     * 表1：课程考核各环节占比配置
     * 展示每个考核环节对不同课程目标的占比分配
     */
    private List<AssessmentConfigVO> assessmentProportions;

    /**
     * 表2：不同环节课程目标考核权重分配
     * 展示每个课程目标在不同考核环节中的权重分配
     */
    private List<AssessmentConfigVO> assessmentWeight;

    /**
     * 期末考试各考核环节权重配置
     * 展示各考核环节在期末成绩中的权重
     */
    private List<AssessmentConfigVO> finalExamWeights;

}
