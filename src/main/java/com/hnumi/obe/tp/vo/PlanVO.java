package com.hnumi.obe.tp.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PlanVO {
    private Long id;

    private Long planVersion;

    private String planName;

    private Long majorId;

    private Long academyId;

    private Long standardId;

    private Integer status;

    private Long creator;

    private LocalDateTime createTime;

    private Long modifier;

    private LocalDateTime modifyTime;
}
