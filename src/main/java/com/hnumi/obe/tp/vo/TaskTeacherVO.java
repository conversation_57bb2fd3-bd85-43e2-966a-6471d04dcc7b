package com.hnumi.obe.tp.vo;

import com.hnumi.obe.base.entity.Teacher;
import com.hnumi.obe.base.vo.TeacherVO;
import com.hnumi.obe.task.entity.TaskWorklistTeachers;

/**
 * 教学任务教师信息VO
 * 统一的教师信息展示对象，用于各种教学任务相关的业务场景
 */
public record TaskTeacherVO (
        Long id,
        String name,
        Long academyId,
        Long teacherId,
        Integer role
) {

    /**
     * 从 BaseUser 和 Teacher 实体创建 TaskTeacherVO（完整版本）
     * @param relation 教学任务与教师关系
     * @param teacher 教师实体信息
     * @return TaskTeacherVO 实例
     */
    public static TaskTeacherVO from(Teacher teacher, TaskWorklistTeachers relation) {
        return new TaskTeacherVO(
            teacher.getUserId(),
            teacher.getTeacherName(),
            teacher.getAcademyId(),
            teacher.getTeacherId(),
                relation.getRole()

        );
    }

    /**
     * 从TeacherVO 创建 TaskTeacherVO（完整版本）
     * @param teacherVO 教师信息
     *
     */
    public static TaskTeacherVO from(TeacherVO teacherVO) {
        return new TaskTeacherVO(
                teacherVO.getUser().getId(),
                teacherVO.getName() ,
                teacherVO.getAcademyId(),
                teacherVO.getId(),
                null
        );
    }


    /**
     * 从基础信息创建 TaskTeacherVO（通用构造方法）
     * @param userId 用户ID
     * @param teacherName 教师姓名
     * @param academyId 学院ID
     * @param teacherId 教师ID
     * @return TaskTeacherVO 实例
     */
    public static TaskTeacherVO of(Long userId, String teacherName, Long academyId, Long teacherId,Integer role){
        return new TaskTeacherVO(userId, teacherName, academyId, teacherId, role );
    }
}
