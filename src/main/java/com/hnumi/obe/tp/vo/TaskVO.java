package com.hnumi.obe.tp.vo;

import com.hnumi.obe.base.entity.Classes;
import com.hnumi.obe.base.vo.TeacherVO;
import com.hnumi.obe.system.entity.BaseUser;
import com.hnumi.obe.task.entity.TaskWork;
import com.hnumi.obe.tp.entity.Course;

import java.util.List;

public record TaskVO(

        Long id,
        String name,
        Long courseId,
        String courseName,
        Integer courseCredit,
        Integer courseHoursTotal,
        List<TaskTeacherVO> teachers,
        List<TaskClassVO> classes,
        Integer totalStudents) {

    public static TaskVO from(Course course, TaskWork taskWork, List<TeacherVO> teachers, List<Classes> classes) {
        int totalStudents = classes.stream()
                .mapToInt(cls -> cls.getStudentNumber() != null ? cls.getStudentNumber() : 0)
                .sum();

        return new TaskVO(taskWork.getId(), taskWork.getTaskName(),taskWork.getCourseId(),
                course.getCourseName(), course.getCourseCredit(), course.getCourseHoursTotal(),
                teachers.stream().map(TaskTeacherVO::from).toList(),
                classes.stream().map(TaskClassVO::from).toList(),
                totalStudents);
    }



}
