package com.hnumi.obe.tp.vo;

import java.time.LocalDate;

public record PlanSemesterVO(
        Long id,
        String name,
        Integer startYear,
        LocalDate start,
        LocalDate end,
        boolean current,
        Integer doorCount,
        Integer studentCount,
        Integer taskCount,
        Integer completedCount,
        String year,
        String semester
) {

    public static PlanSemesterVO create(Long id, String name, Integer startYear, LocalDate start, LocalDate end,
                                       boolean current, String year, String semester) {
        // 模拟统计数据（实际项目中应该从数据库查询）
        Integer doorCount = 3;//课程门数
        Integer studentCount = semester.equals("1") ? 135 : 45;//
        Integer taskCount = 2;
        Integer completedCount = 0;

        return new PlanSemesterVO(id, name, startYear, start, end, current,
                                 doorCount, studentCount, taskCount, completedCount, year, semester);
    }
}
