package com.hnumi.obe.tp.vo;

import com.hnumi.obe.tp.entity.Po;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public record PoTreeVO (
    Long id,
    Integer poNumber,
    String poTitle,
    String poDescription,
    Boolean isRequirement,
    List<PoTreeVO> children,
    LocalDateTime modifyTime
) {

    public static PoTreeVO of(Po po) {
        return new PoTreeVO(po.getId(), po.getPoNumber(), po.getPoTitle(), po.getPoDescription(), po.getIsRequirement(), new ArrayList<>(), po.getModifyTime());
    }

}
