package com.hnumi.obe.tp.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 课程教材信息 VO
 * 包含主教材和参考书列表
 */
@Data
public class CourseBookVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主教材信息
     */
    private BookDetailVO mainBook;

    /**
     * 参考书列表
     */
    private List<BookDetailVO> referenceBooks;

    /**
     * 教材详细信息
     */
    @Data
    public static class BookDetailVO implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 教材ISBN
         */
        private String isbn;

        /**
         * 教材名称
         */
        private String name;

        /**
         * 教材作者
         */
        private String author;

        /**
         * 教材出版社
         */
        private String publisher;

        /**
         * 教材版本
         */
        private String version;

        /**
         * 教材出版时间
         */
        private String publishDate;

        /**
         * 教材类型：MAIN-主教材，REFERENCE-参考书
         */
        private BookType type;

        /**
         * 教材简介/备注
         */
        private String description;

        /**
         * 教材价格（可选）
         */
        private Double price;

        /**
         * 教材封面URL（可选）
         */
        private String coverUrl;

        /**
         * 教材适用范围/适用对象
         */
        private String applicableScope;
    }

    /**
     * 教材类型枚举
     */
    public enum BookType {
        MAIN("主教材"),
        REFERENCE("参考书");

        private final String description;

        BookType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}

