package com.hnumi.obe.tp.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@Data
public class AssessmentConfigVO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 考核方法ID
     */
    private String methodId;
    /**
     * 考核方法名称
     */
    private String methodName;

    /**
     * 该考核方法在期末成绩中的权重（0-100）
     */
    private Double examWeight;

    private Boolean isFinalExam; // 是否为期末考试考核方法
    /**
     * 当前考核方式下的不同课程目标对应的权重（占比）配置
     */
    List<ObjectiveWeight> objectiveList;

}

@Data
class ObjectiveWeight {
    /**
     * 课程目标ID
     */
    private String objectiveId;
    /**
     * 课程目标名称
     */
    private String objectiveName;
    /**
     * 该课程目标在此考核方法中的占比（0-100）
     */
    private Double weight;

    private Double expectedScore; // 期望得分
    /**
     * 针对此课程目标在该考核方法的评价说明
     */
    private String description;
}
