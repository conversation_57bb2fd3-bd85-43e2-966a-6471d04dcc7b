package com.hnumi.obe.tp.vo;

import lombok.Data;

/**
 * 课程授课历史统计VO
 */
// TODO: 需要移动到task中
@Data
public class CourseTeachingHistoryVO {
    /**
     * 课程ID
     */
    private Long courseId;

    /**
     * 课程名称
     */
    private String courseName;

    /**
     * 课程编号
     */
    private String courseCode;

    /**
     * 课程学分
     */
    private Integer courseCredit;

    /**
     * 班级统计（该课程的总班级数）
     */
    private Integer classCount;

    /**
     * 学生数统计（该课程的总学生数）
     */
    private Integer totalStudents;

    /**
     * 学期数统计（该课程的总学期数）
     */
    private Integer semesterCount;
} 