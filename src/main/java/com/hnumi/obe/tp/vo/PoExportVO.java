package com.hnumi.obe.tp.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 培养目标导出VO
 * 用于导出培养目标到Excel表
 */
@Data
public class PoExportVO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 培养目标id
     */
    @ExcelProperty("毕业要求id")
    private Long id;

    /**
     * 培养目标标题
     */
    @ExcelProperty("毕业要求标题")
    private String poTitle;

    /**
     * 培养目标详情
     */
    @ExcelProperty("毕业要求详情")
    private String poDescription;
    /**
     * 所属工程教育认证指标id
     */
    @ExcelProperty("所属工程教育认证指标id")
    private String standardId;
    /**
     * 所属专业培养计划id
     */
    @ExcelProperty("所属专业培养计划id")
    private Long planId;
    /**
     * 所属专业id
     */
    @ExcelProperty("所属专业id")
    private Long majorId;
    /**
     * 所属学院id
     */
    @ExcelProperty("所属学院id")
    private Long academyId;

    /**
     * 记录状态{0:正常状态；-1:删除状态；}
     */
    @ExcelProperty("记录状态")
    private String status;

    /**
     * 记录创建时间
     */
    @ExcelProperty("创建时间")
    private String createTime;

    /**
     * 记录最后修改时间
     */
    @ExcelProperty("最后修改时间")
    private String modifyTime;
}