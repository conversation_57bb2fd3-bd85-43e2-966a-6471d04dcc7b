package com.hnumi.obe.tp.vo;


import lombok.Data;

import java.io.Serializable;
import java.util.List;


@Data
public class CourseBaseInfoVO implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long courseId;
    /**
     * 课程编码，与版本号一起是唯一的，如2025_RB7001152
     * 字段类型：String
     * 字段名称：courseCode
     * 数据库字段：course_code
     * 展示说明：用于前端展示的课程编码，与版本号一起是唯一的，如2025_RB7001152
     */
    private String courseCode;
    /**
     * 课程名称
     * 字段类型：String
     * 字段名称：courseName
     * 数据库字段：course_name
     * 展示说明：用于前端展示的课程名称
     */
    private String courseName;

    private Long courseLeader;
    /**
     * 课程学分
     * 字段类型：Integer
     * 字段名称：courseCredit
     * 数据库字段：course_credit
     * 展示说明：用于前端展示的课程学分
     */
    private Integer courseCredit;
    /**
     * 是否是核心课程
     * 字段类型：Boolean
     * 字段名称：courseCore
     * 数据库字段：course_core
     * 展示说明：用于前端展示的是否是核心课程
     */
    private Boolean courseCore;
    /**
     * 是否是考试课
     * 字段类型：Boolean
     * 字段名称：courseExam
     * 数据库字段：course_exam
     * 展示说明：用于前端展示的是否是考试课
     */
    private Boolean courseExam;
    /**
     * 总学学时
     * 字段类型：Integer
     * 字段名称：courseHoursTotal
     * 数据库字段：course_hours_total
     * 展示说明：用于前端展示的总学学时
     */
    private Integer courseHoursTotal;
    /**
     * 理教学时
     * 字段类型：Integer
     * 字段名称：courseHoursTheory
     * 数据库字段：course_hours_theory
     * 展示说明：用于前端展示的理教学时
     */
    private Integer courseHoursTheory;
    /**
     * 实验学时
     * 字段类型：Integer
     * 字段名称：courseHoursExperiment
     * 数据库字段：course_hours_experiment
     * 展示说明：用于前端展示的实验学时
     */
    private Integer courseHoursExperiment;
    /**
     * 其他学时
     * 字段类型：Integer
     * 字段名称：courseHoursOther
     * 数据库字段：course_hours_other
     * 展示说明：用于前端展示的其他学时
     */
    private Integer courseHoursOther;
    /**
     * 课外学时
     * 字段类型：Integer
     * 字段名称：courseHoursExtracurricular
     * 数据库字段：course_hours_extracurricular
     * 展示说明：用于前端展示的课外学时
     */
    private Integer courseHoursExtracurricular;
    /**
     * 上课学期（1-8）
     * 字段类型：Byte
     * 字段名称：courseSemester
     * 数据库字段：course_semester
     * 展示说明：用于前端展示的上课学期（1-8）
     */
    private String courseSemester;
    /**
     * 本专业课程类型（专业基础课、个性化发展课等）
     * 字段类型：Integer
     * 字段名称：courseType1
     * 数据库字段：course_type1
     * 展示说明：用于前端展示的本专业课程类型（专业基础课、个性化发展课等）
     */
    private String courseType1;
    /**
     * 专业认证课程类型
     * 字段类型：Integer
     * 字段名称：courseType2
     * 数据库字段：course_type2
     * 展示说明：用于前端展示的专业认证课程类型
     */
    private String courseType2;
    /**
     * 国标课程类别
     * 字段类型：Integer
     * 字段名称：courseType3
     * 数据库字段：course_type3
     * 展示说明：用于前端展示的国标课程类别
     */
    private String courseType3;
    /**
     * 课程性质（必修、选修、限选等）
     * 字段类型：Integer
     * 字段名称：courseNature
     * 数据库字段：course_nature
     * 展示说明：用于前端展示的课程性质（必修、选修、限选等）
     */
    private String courseNature;

    /**
     * 所属培养方案的版本
     * 字段类型：Integer
     * 字段名称：courseVersion
     * 数据库字段：course_version
     * 展示说明：用于前端展示的所属培养方案的版本
     */
    private Integer courseVersion;
    /**
     * 课程所属培养方案id
     * 字段类型：Integer
     * 字段名称：planId
     * 数据库字段：plan_id
     * 展示说明：用于前端展示的课程所属培养方案id
     */
    private Integer planId;
    /**
     * 专业编号-专业表
     * 字段类型：Integer
     * 字段名称：majorId
     * 数据库字段：major_id
     * 展示说明：用于前端展示的专业编号-专业表
     */
    private Integer majorId;

    /**
     * 课程目标列表
     * 字段类型：List<CourseObjectiveVO>
     * 字段名称：courseObjectives
     * 数据库字段：course_objectives
     * 展示说明：用于前端展示的课程目标列表
     */
    private List<CourseObjectiveVO> courseObjectives;
    private List<AssessmentMethodVO> assessmentMethodList;

    private CourseBookVO courseBookVO;


}
