package com.hnumi.obe.tp.vo;

import lombok.Data;
import lombok.ToString;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 毕业要求表 视图对象（VO）
 * 
 * VO（View Object）用于展示层，把某个指定页面（或组件）的所有数据封装起来
 * 主要用于：
 * 1. 展示层数据封装
 * 2. 数据格式转换（如日期格式化）
 * 3. 数据脱敏处理
 * 4. 前端展示优化
 */

@Data
public class PoVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 毕业要求编号
     */

    private Integer poNumber;

    /**
     * 毕业要求标题
     * 字段类型：String
     * 字段名称：poTitle
     * 数据库字段：po_title
     * 展示说明：用于前端展示的毕业要求标题
     */
    private String poTitle;
    /**
     * 毕业要求详情
     * 字段类型：String
     * 字段名称：poDescription
     * 数据库字段：po_description
     * 展示说明：用于前端展示的毕业要求详情
     */
    private String poDescription;
    /**
     * 该记录是否为毕业要求一级指标
     * 字段类型：Boolean
     * 字段名称：isRequirement
     * 数据库字段：is_requirement
     * 展示说明：用于前端展示的该记录是否为毕业要求一级指标,
     */
    private Boolean isRequirement;

    /**
     * 所属培养方案的id
     */
    private Long planId;

    /**
     * 所属专业认证标准id
     */

    private Long standardId;

    /**
     * 当前二级指标点所属的一级指标点ID
     */
    private Long parentId;
    /**
     * 记录状态{0:正常状态；-1:删除状态；}
     * 字段类型：Integer
     * 字段名称：status
     * 数据库字段：status
     * 展示说明：用于前端展示的记录状态{0:正常状态；-1:删除状态；}
     */
    private Integer status;

    /**
     * 记录
     * 字段类型：LocalDateTime
     * 字段名称：createTime
     * 数据库字段：create_time
     * 展示说明：用于前端展示的记录
     */
    private LocalDateTime createTime;

    /**
     * 记录最后修改时间
     * 字段类型：LocalDateTime
     * 字段名称：modifyTime
     * 数据库字段：modify_time
     * 展示说明：用于前端展示的记录最后修改时间
     */
    private LocalDateTime modifyTime;

    @Override
    public String toString() {
        return "PoVO{" +
                "id=" + id +
                '}';
    }
}