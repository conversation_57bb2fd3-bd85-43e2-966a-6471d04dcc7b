package com.hnumi.obe.tp.vo;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 培养目标表 视图对象（VO）
 * <p>
 * VO（View Object）用于展示层，把某个指定页面（或组件）的所有数据封装起来
 * 主要用于：
 * 1. 展示层数据封装
 * 2. 数据格式转换（如日期格式化）
 * 3. 数据脱敏处理
 * 4. 前端展示优化
 */
@Data
public class EoVO implements Serializable {

    private Long id;

    /**
     * 培养目标标题
     * 字段类型：String
     * 字段名称：eoTitle
     * 数据库字段：eo_title
     * 展示说明：用于前端展示的培养目标标题
     */

    private String eoTitle;
    /**
     * 培养目标详情
     * 字段类型：String
     * 字段名称：eoDescription
     * 数据库字段：eo_description
     * 展示说明：用于前端展示的培养目标详情
     */
    private String eoDescription;

    private String relatedQuestions;
    /**
     * 记录状态{0:正常状态；-1:删除状态；}
     * 字段类型：Integer
     * 字段名称：status
     * 数据库字段：status
     * 展示说明：用于前端展示的记录状态{0:正常状态；-1:删除状态；}
     */
    private Integer status;
    /**
     * 记录
     * 字段类型：LocalDateTime
     * 字段名称：createTime
     * 数据库字段：create_time
     * 展示说明：用于前端展示的记录
     */
    private LocalDateTime createTime;
    /**
     * 记录最后修改者
     * 字段类型：Long
     * 字段名称：modifier
     * 数据库字段：modifier
     * 展示说明：用于前端展示的记录最后修改者
     */
    private LocalDateTime modifyTime;
} 