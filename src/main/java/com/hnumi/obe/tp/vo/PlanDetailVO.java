package com.hnumi.obe.tp.vo;

import com.hnumi.obe.base.entity.Academy;
import com.hnumi.obe.base.entity.Major;
import com.hnumi.obe.base.entity.Standard;
import com.hnumi.obe.system.entity.BaseUser;
import com.hnumi.obe.tp.entity.Plan;

import java.time.LocalDateTime;

public record PlanDetailVO(
        Long id,
        String name,
        Long version,
        Long academyId,
        String academyName,
        Long majorId,
        String majorName,
        Long leaderId,
        String leaderName,
        Long standardId,
        String standardName,
        Integer status,
        LocalDateTime createTime,
        LocalDateTime modifyTime
) {

    public static PlanDetailVO from(Plan plan, Academy academy, Major major, BaseUser leader, Standard standard) {
        return new PlanDetailVO(
                plan.getId(),
                plan.getPlanName(),
                plan.getPlanVersion(),
                plan.getAcademyId(),
                academy.getAcademyName(),
                plan.getMajorId(),
                major.getMajorName(),
                leader.getId(),
                leader.getRealName(),
                standard.getId(),
                standard.getStandardName(),
                plan.getStatus(),
                plan.getCreateTime(),
                plan.getModifyTime()
        );
    }

}
