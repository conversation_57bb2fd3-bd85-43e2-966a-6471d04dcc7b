package com.hnumi.obe.tp.vo;

import lombok.Data;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2025-06-28 23:30
 */
@Data
public class CourseObjectiveVO {
    private String objectiveId; //->id
    private String objectiveName;//->title
    private Integer number;//课程目标编号
    private String description;
    private Double expectedScore;//课程目标的期望值
    private List<AssessmentMethodVO> assessmentMethods;//课程目标的考核方法列表
    private Long gindicatorId;//对应的毕业要求指标ID

    /**
     * 课程目标对应的毕业要求
     */
    private PoVO po;


}
