package com.hnumi.obe.tp.vo;

import com.hnumi.obe.base.vo.ClassesVO;
import lombok.Data;

import java.util.List;

/**
 * 当前学期课程统计VO
 */
@Data
public class CourseCurrentSemesterVO {
    /**
     * 课程ID
     */
    private Long courseId;
    private Long taskId;

    /**
     * 课程名称
     */
    private String courseName;

    /**
     * 课程编号
     */
    private String courseCode;

    /**
     * 教学计划ID
     */
    private Long planId;

    /**
     * 班级统计（该课程的总班级数）
     */
    private Integer classCount;

    /**
     * 学生数统计（该课程的总学生数）
     */
    private Integer totalStudents;

    /**
     * 教学团队人数统计（该课程的总教师数）
     */
    private Integer teacherCount;

    /**
     * 班级详情列表
     */
    private List<ClassesVO> classes;

    /**
     * 授课学期（1-8）
     */
    private Integer taskTerm;

    /**
     * 授课年份
     */
    private Integer taskYear;

    /**
     * 学期状态（true: 进行中, false: 已结课）
     */
    private Boolean isCurrentSemester;
} 