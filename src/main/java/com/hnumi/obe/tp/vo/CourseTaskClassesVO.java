package com.hnumi.obe.tp.vo;

import lombok.Data;

/**
 * 课程任务班级信息VO
 */
// TODO: 需要移动到task中
@Data
public class CourseTaskClassesVO {
    /**
     * 班级ID
     */
    private Long classId;
    
    /**
     * 班级名称
     */
    private String className;

    /**
     * 入学年份
     */
    private String entranceYear;
    
    /**
     * 学生人数
     */
    private Integer studentCount;
    
    /**
     * 教师ID
     */
    private Long teacherId;
    
    /**
     * 教师姓名
     */
    private String teacherName;
    
    /**
     * 教师职称
     */
    private String teacherTitle;
    
    /**
     * 教师所属学院
     */
    private String teacherAcademyName;
    
    /**
     * 教师角色
     */
    private String teacherRole;
    
    /**
     * 授课周数
     */
    private Integer teachWeek;
    
    /**
     * 周学时
     */
    private Integer weekHours;
    
    /**
     * 总学时
     */
    private Integer totalHours;
} 