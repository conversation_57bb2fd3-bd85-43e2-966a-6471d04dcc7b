package com.hnumi.obe.tp.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class CoursePoVO {
    private Long courseId;
    private String courseName;
    private Long poId; // 毕业要求指标ID
    private String poTitle; // 毕业要求指标名称
    private Integer poNumber; // 指标编号
    private String poDescription; // 指标描述
    private Long standardId; // 工程教育认证指标ID,如果是二级指标点，这里显示的是一级指标的ID
    private Integer standardNumber;
    private BigDecimal weight; // 指标的支撑权重
    List<CoursePoVO> children; // 子指标列表，用于树形结构展示
}
