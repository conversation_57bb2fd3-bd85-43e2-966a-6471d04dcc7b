package com.hnumi.obe.task.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 教学任务-教师关联表
 */
@Data
@Accessors(chain = true)
@TableName("task_worklist_teachers")
public class TaskWorklistTeachers {

    /**
     * 关联关系ID (独立主键)
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 教学任务ID, 关联 task_worklist(id)
     */
    @TableField("task_id")
    private Long taskId;

    /**
     * 教师ID, 关联 base_teacher(teacher_id)
     */
    @TableField("teacher_id")
    private Long teacherId;

    /**
     * 教师角色 (如: 主讲, 助教, 辅导), 由应用层定义
     */
    @TableField("role")
    private Integer role;

    /**
     * 记录创建者ID
     */
    @TableField("creator")
    private Long creator;

    /**
     * 记录创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;
}
