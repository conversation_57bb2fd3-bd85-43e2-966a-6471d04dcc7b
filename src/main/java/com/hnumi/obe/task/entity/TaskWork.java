package com.hnumi.obe.task.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hnumi.obe.base.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 教学任务信息表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("task_worklist")
public class TaskWork extends BaseEntity {

    /**
     * 教学任务ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 课程id，查tp_course -> id
     */
    @TableField("course_id")
    private Long courseId;

    /**
     * 教学任务名称, 如: 数据库原理(计2301,02班)
     */
    @TableField("task_name")
    private String taskName;

    /**
     * 课程序号，同一课程的不同授课班级
     */
    @TableField("task_number")
    private Integer taskNumber;

    /**
     * 授课年份(2023)，冗余
     */
    @TableField("task_year")
    private Integer taskYear;

    /**
     * 授课学期(1~8)，班级可以查到入学年份，对应学期
     */
    @TableField("task_term")
    private Integer taskTerm;

    /**
     * 授课周数
     */
    @TableField("teach_week")
    private Integer teachWeek;

    /**
     * 周学时
     */
    @TableField("week_hours")
    private Integer weekHours;

    /**
     * 总学时
     */
    @TableField("total_hours")
    private Integer totalHours;

    /**
     * 课程负责人id（临时负责人）
     */
    @TableField("course_leader_id")
    private Long courseLeaderId;

    /**
     * 专业id 冗余
     */
    @TableField("major_id")
    private Long majorId;

    /**
     * 计划id 冗余
     */
    @TableField("plan_id")
    private Long planId;

    /**
     * 记录状态{0:正常状态；-1:删除状态；}
     */
    @TableField("status")
    private Integer status;

    /**
     * 任务状态{0::进行中；-1:已结束；}
     */
    private Integer taskStatus;
}
