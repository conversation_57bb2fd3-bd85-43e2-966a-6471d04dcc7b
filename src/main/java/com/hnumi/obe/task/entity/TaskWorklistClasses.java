package com.hnumi.obe.task.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 教学任务-班级关联表
 */
@Data
@Accessors(chain = true)
@TableName("task_worklist_classes")
public class TaskWorklistClasses {

    /**
     * 关联关系ID (独立主键)
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 教学任务ID, 关联 task_worklist(id)
     */
    @TableField("task_id")
    private Long taskId;

    /**
     * 行政班级ID, 关联 base_classes(class_id)
     */
    @TableField("class_id")
    private Long classId;

    /**
     * 记录创建者ID
     */
    @TableField("creator")
    private Long creator;

    /**
     * 记录创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;
}
