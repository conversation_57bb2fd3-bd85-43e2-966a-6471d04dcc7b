package com.hnumi.obe.task.mapstruct;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hnumi.obe.task.dto.TaskWorkDTO;
import com.hnumi.obe.task.entity.TaskWork;
import com.hnumi.obe.task.vo.TaskWorkVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 教学任务信息表 对象转换器
 */
@Mapper
public interface TaskWorkConvert {

    TaskWorkConvert INSTANCE = Mappers.getMapper(TaskWorkConvert.class);

    /**
     * DTO转Entity
     */
    TaskWork toEntity(TaskWorkDTO dto);

    /**
     * Entity转VO
     */
    TaskWorkVO toVO(TaskWork entity);

    /**
     * Entity列表转VO列表
     */
    List<TaskWorkVO> toVOList(List<TaskWork> entityList);

    /**
     * 分页Entity转分页VO
     */
    default Page<TaskWorkVO> toPageVO(Page<TaskWork> entityPage) {
        Page<TaskWorkVO> voPage = new Page<>(entityPage.getCurrent(), entityPage.getSize(), entityPage.getTotal());
        voPage.setRecords(toVOList(entityPage.getRecords()));
        return voPage;
    }
}
