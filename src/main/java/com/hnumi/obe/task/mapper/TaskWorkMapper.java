package com.hnumi.obe.task.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hnumi.obe.task.entity.TaskWork;
import com.hnumi.obe.task.vo.CourseStaticsByTaskVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 教学任务信息表 Mapper 接口
 */
@Mapper
public interface TaskWorkMapper extends BaseMapper<TaskWork> {
    /**
     * 获取教师的所有教学任务课程统计信息
     * 使用高性能SQL查询，通过subqueries优化性能，减少数据库调用次数
     *
     * @param teacherId 教师ID
     * @return 课程统计信息列表
     */
    @Select("""
            SELECT DISTINCT
                c.course_id,
                tw.id as task_id,
                c.course_name,
                c.course_code,
                c.plan_id,
                tw.task_term,
                tw.task_year,
                CASE WHEN tw.task_status = 0 THEN 1 ELSE 0 END as is_current_semester,
                (
                    SELECT COUNT(DISTINCT twc.class_id)
                    FROM task_worklist_classes twc
                    WHERE twc.task_id = tw.id
                ) as class_count,
                (
                    SELECT COALESCE(SUM(bc.student_number), 0)
                    FROM task_worklist_classes twc
                    INNER JOIN base_classes bc ON twc.class_id = bc.class_id
                    WHERE twc.task_id = tw.id AND bc.status = 0
                ) as total_students,
                (
                    SELECT COUNT(DISTINCT twt_inner.teacher_id)
                    FROM task_worklist_teachers twt_inner
                    WHERE twt_inner.task_id = tw.id
                ) as teacher_count
            FROM task_worklist tw
            INNER JOIN tp_course c ON tw.course_id = c.course_id
            INNER JOIN task_worklist_teachers twt ON tw.id = twt.task_id
            WHERE twt.teacher_id = #{teacherId}
                AND tw.status = 0
                AND c.status = 0
            ORDER BY tw.task_year DESC, tw.task_term DESC, c.course_name
            """)
    List<CourseStaticsByTaskVO> selectCoursesByTeacherId(@Param("teacherId") Long teacherId);

    /**
     * 获取指定教学任务的班级详情列表
     * 用于单独查询班级信息，避免在主查询中造成数据重复
     *
     * @param taskId 教学任务ID
     * @return 班级详情列表
     */
    @Select("""
            SELECT
                bc.class_id,
                bc.major_id,
                bc.class_name,
                bc.entrance_year,
                bc.headteacher_id,
                bc.student_number,
                bc.class_status,
                bc.status,
                bc.creator,
                bc.create_time,
                bc.modifier,
                bc.modify_time
            FROM task_worklist_classes twc
            INNER JOIN base_classes bc ON twc.class_id = bc.class_id
            WHERE twc.task_id = #{taskId}
                AND bc.status = 0
            ORDER BY bc.class_name
            """)
    List<com.hnumi.obe.base.vo.ClassesVO> getClassesByTaskId(@Param("taskId") Long taskId);

}
