package com.hnumi.obe.task.mapper;

import com.hnumi.obe.task.vo.CourseStaticsByTaskVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * TaskWorkMapper使用示例
 * 演示如何使用高性能SQL查询获取教师的教学任务统计信息
 * 
 * <AUTHOR>
 * @create 2025-07-22
 */
@Component
public class TaskWorkMapperExample {

    @Autowired
    private TaskWorkMapper taskWorkMapper;

    /**
     * 示例1：获取教师的教学任务统计信息（不包含班级详情）
     * 高性能查询，适用于只需要统计数据的场景
     */
    public List<CourseStaticsByTaskVO> getTeachingTasksStatistics(Long teacherId) {
        return taskWorkMapper.selectCoursesByTeacherId(teacherId);
    }

    /**
     * 示例2：获取教师的教学任务统计信息（包含班级详情）
     * 需要时再查询班级详情，避免数据重复和性能问题
     */
    public List<CourseStaticsByTaskVO> getTeachingTasksWithClasses(Long teacherId) {
        List<CourseStaticsByTaskVO> results = taskWorkMapper.selectCoursesByTeacherId(teacherId);

        // 为每个任务查询班级详情
        for (CourseStaticsByTaskVO courseStats : results) {
            courseStats.setClasses(taskWorkMapper.getClassesByTaskId(courseStats.getTaskId()));
        }

        return results;
    }

    /**
     * 示例3：演示如何处理返回的数据
     */
    public void demonstrateUsage(Long teacherId) {
        List<CourseStaticsByTaskVO> results = getTeachingTasksWithClasses(teacherId);
        
        for (CourseStaticsByTaskVO courseStats : results) {
            System.out.println("课程: " + courseStats.getCourseName());
            System.out.println("课程代码: " + courseStats.getCourseCode());
            System.out.println("任务ID: " + courseStats.getTaskId());
            System.out.println("授课年份: " + courseStats.getTaskYear());
            System.out.println("授课学期: " + courseStats.getTaskTerm());
            System.out.println("班级数量: " + courseStats.getClassCount());
            System.out.println("学生总数: " + courseStats.getTotalStudents());
            System.out.println("教师数量: " + courseStats.getTeacherCount());
            System.out.println("是否当前学期: " + courseStats.getIsCurrentSemester());
            
            if (courseStats.getClasses() != null) {
                System.out.println("班级详情:");
                courseStats.getClasses().forEach(clazz -> {
                    System.out.println("  - " + clazz.getClassName() + 
                                     " (学生数: " + clazz.getStudentNumber() + ")");
                });
            }
            System.out.println("---");
        }
    }
}
