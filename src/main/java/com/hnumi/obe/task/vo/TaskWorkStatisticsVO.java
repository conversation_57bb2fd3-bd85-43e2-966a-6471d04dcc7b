package com.hnumi.obe.task.vo;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.Data;

/**
 * 教学任务统计信息 VO
 */
@Data
public class TaskWorkStatisticsVO {

    /**
     * 学年，如：2024-2025
     */
    private String academicYear;

    /**
     * 学期，如：春季学期/秋季学期
     */
    private String semester;

    /**
     * 授课年份
     */
    private Integer taskYear;

    /**
     * 授课学期
     */
    private Integer taskTerm;

    /**
     * 课程ID
     */
    private Long courseId;

    /**
     * 课程名称
     */
    private String courseName;

    /**
     * 该学期的教学任务分页列表
     */
    private Page<TaskWorkDetailVO> taskDetails;

    /**
     * 该学期班级总数量
     */
    private Integer totalClassCount;

    /**
     * 该学期学生总数
     */
    private Integer totalStudentCount;

    /**
     * 该学期任务总数
     */
    private Integer totalTaskCount;

    /**
     * 该学期教师总数
     */
    private Integer totalTeacherCount;
}
