package com.hnumi.obe.task.vo;

import lombok.Data;

import java.util.List;

/**
 * 教学任务详情 VO
 */
@Data
public class TaskWorkDetailVO {

    /**
     * 学年，如：2024-2025
     */
    private String academicYear;
    /**
     * 学期，如：春季学期/秋季学期
     */
    private String semester;


    /**
     * 教学任务id
     */
    private Long taskId;

    /**
     * 教学任务名称
     */
    private String taskName;

    /**
     * 课程序号
     */
    private Integer taskNumber;

    /**
     * 授课周数
     */
    private Integer teachWeek;

    /**
     * 周学时
     */
    private Integer weekHours;

    /**
     * 授课班级列表
     */
    private List<TaskClassVO> classes;

    /**
     * 授课教师列表
     */
    private List<TaskTeacherVO> teachers;

    /**
     * 学生总数
     */
    private Integer studentCount;

    /**
     * 班级总数
     */
    private Integer classCount;

    @Data
    public static class TaskClassVO {
        /**
         * 班级ID
         */
        private Long classId;

        /**
         * 班级名称
         */
        private String className;

        /**
         * 学生人数
         */
        private Integer studentNumber;
    }

    /**
     * 教学任务教师信息内部类
     * 包含角色信息的教师展示对象
     */
    @Data
    public static class TaskTeacherVO {
        /**
         * 教师ID
         */
        private Long teacherId;

        /**
         * 教师姓名
         */
        private String teacherName;

        /**
         * 教师角色 1:主讲教师 2:辅导教师 3:助教
         */
        private Integer role;

        /**
         * 教师角色名称
         */
        private String roleName;
    }
}
