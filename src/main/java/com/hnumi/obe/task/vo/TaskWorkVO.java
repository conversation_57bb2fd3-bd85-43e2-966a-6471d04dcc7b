package com.hnumi.obe.task.vo;

import com.hnumi.obe.base.vo.ClassesVO;
import com.hnumi.obe.tp.vo.TaskTeacherVO;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 教学任务信息表 VO
 */
@Data
public class TaskWorkVO {

    /**
     * 教学任务id（taskId）
     */
    private Long id;

    /**
     * 课程id，查tp_course -> id
     */
    private Long courseId;

    /**
     * 课程序号，同一课程的不同授课班级
     */
    private Integer taskNumber;

    /**
     * 教学任务名称, 如: 数据库原理(计2301,02班)
     */
    private String taskName;

    /**
     * 授课年份(2023)，冗余
     */
    private Integer taskYear;

    /**
     * 授课学期(1~8)，班级可以查到入学年份，对应学期
     */
    private Integer taskTerm;

    /**
     * 授课周数
     */
    private Integer teachWeek;

    /**
     * 周学时
     */
    private Integer weekHours;

    /**
     * 总学时
     */
    private Integer totalHours;

    /**
     * 课程负责人id（临时负责人）
     */
    private Long courseLeaderId;

    /**
     * 记录状态{0:正常状态；-1:删除状态；}
     */
    private Integer status;

    /**
     * 关联的班级ID列表
     */
    private List<Long> classIds;
    private List<ClassesVO> classes;

    /**
     * 关联的教师信息列表
     */
    private List<TaskTeacherVO> teachers;

    /**
     * 动态计算的学生总数
     */
    private Integer totalStudentCount;

    /**
     * 记录创建者
     */
    private Long creator;

    /**
     * 记录创建时间
     */
    private LocalDateTime createTime;

    /**
     * 记录最后修改者
     */
    private Long modifier;

    /**
     * 记录最后修改时间
     */
    private LocalDateTime modifyTime;


}
