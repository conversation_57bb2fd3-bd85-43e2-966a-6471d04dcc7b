package com.hnumi.obe.task.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hnumi.obe.task.entity.TaskWorklistClasses;

import java.util.List;
import java.util.Map;

/**
 * 教学任务-班级关联表 服务类
 */
public interface ITaskWorklistClassesService extends IService<TaskWorklistClasses> {

    /**
     * 根据教学任务ID获取关联的班级ID列表
     *
     * @param taskId 教学任务ID
     * @return 班级ID列表
     */
    List<Long> getClassIdsByTaskId(Long taskId);

    /**
     * 根据班级ID获取关联的教学任务ID列表
     *
     * @param classId 班级ID
     * @return 教学任务ID列表
     */
    List<Long> getTaskIdsByClassId(Long classId);

    /**
     * 批量保存教学任务-班级关联关系
     *
     * @param taskId   教学任务ID
     * @param classIds 班级ID列表
     * @param creator  创建者ID
     * @return 保存结果
     */
    boolean saveTaskClassRelations(Long taskId, List<Long> classIds, Long creator);

    /**
     * 删除教学任务的所有班级关联关系
     *
     * @param taskId 教学任务ID
     * @return 删除的记录数量
     */
    boolean removeByTaskId(Long taskId);

    /**
     * 统计教学任务关联的班级数量
     *
     * @param taskId 教学任务ID
     * @return 关联的班级数量
     */
    long countByTaskId(Long taskId);

    Map<Long, List<TaskWorklistClasses>> getClassesMap(List<Long> taskIds);

    /**
     * 根据教学任务ID获取关联的班级实体列表
     *
     * @param taskId 教学任务ID
     * @return 班级关联实体列表
     */
    List<TaskWorklistClasses> getByTaskId(Long taskId);

    /**
     * 清理课程与班级的重复关联关系
     * 删除指定课程ID与班级ID列表的现有关联关系
     *
     * @param courseId 课程ID
     * @param classIds 班级ID列表
     * @return 删除的记录数量
     */
    int cleanupDuplicateClassRelations(Long courseId, List<Long> classIds);

}
