package com.hnumi.obe.task.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hnumi.obe.common.entity.PageResponse;
import com.hnumi.obe.task.dto.TaskWorkDTO;
import com.hnumi.obe.task.dto.TaskWorkQueryDTO;
import com.hnumi.obe.task.entity.TaskWork;
import com.hnumi.obe.task.vo.CourseStaticsByTaskVO;
import com.hnumi.obe.task.vo.TaskWorkDetailVO;
import com.hnumi.obe.task.vo.TaskWorkStatisticsVO;
import com.hnumi.obe.task.vo.TaskWorkVO;
import com.hnumi.obe.tp.vo.TaskVO;

import java.util.List;

/**
 * 教学任务信息表 服务类
 */
public interface ITaskWorkService extends IService<TaskWork> {

    /**
     * 创建教学任务（包含关联的班级和教师）
     * @param dto 教学任务DTO
     * @return 创建结果
     */
    boolean createTaskWork(TaskWorkDTO dto);

    /**
     * 更新教学任务（包含关联的班级和教师）
     * @param dto 教学任务DTO
     * @return 更新结果
     */
    boolean updateTaskWork(TaskWorkDTO dto);

    /**
     * 软删除教学任务（同时删除关联关系）
     * @param id 教学任务ID
     * @return 删除结果
     */
    boolean deleteById(Long id);

    /**
     * 物理删除教学任务（包含所有关联关系）
     * @param taskId 教学任务ID
     * @return 删除结果
     */
    boolean deleteTaskWork(Long taskId);

    /**
     * 根据ID获取教学任务详情（包含关联的班级和教师）
     * @param id 教学任务ID
     * @return 教学任务详情
     */
    TaskWorkVO getTaskWorkDetailById(Long id);

    /**
     * 查询教学任务列表（包含关联信息），指定学期学年
     * @param courseId 课程ID
     * @param queryDTO 查询条件
     * @return 教学任务统计信息
     */
    List<TaskWorkDetailVO> getCourseTaskWorkBySemester(Long courseId, TaskWorkQueryDTO queryDTO);
    List<TaskWorkDetailVO> getCourseTaskWorkBySemester(Long courseId);
    /**
     * 分页查询教学任务列表（包含关联信息）
     * @param courseId 课程ID
     * @param queryDTO 查询条件
     * @return 教学任务统计信息（包含分页的任务详情）
     */
    Page<TaskWorkStatisticsVO> getCourseTaskWorkPage(Long courseId, TaskWorkQueryDTO queryDTO);

    /**
     * 根据教师ID获取教学任务列表
     * @param teacherId 教师ID
     * @return 教学任务列表
     */
    List<TaskWorkVO> getTasksByTeacherId(Long teacherId);

    /**
     * 根据班级ID获取教学任务列表
     * @param classId 班级ID
     * @return 教学任务列表
     */
    List<TaskWorkVO> getTasksByClassId(Long classId);

    /**
     * 根据课程ID获取教学任务列表
     * @param courseId 课程ID
     * @return 教学任务列表
     */
    List<TaskWorkVO> getTasksByCourseId(Long courseId);

    /**
     * 计算教学任务的学生总数
     * @param taskId 教学任务ID
     * @return 学生总数
     */
    Integer calculateStudentCount(Long taskId);

    /**
     * 根据课程ID获取教学任务统计信息
     * @param courseId
     * @return
     */
    List<TaskWorkStatisticsVO> getTaskWorkStatisticsByCourseId(Long courseId);

//=======================培养方案：教学计划生成相关接口=======================


    /**
     * 生成和管理教学任务列表
     * 根据培养方案信息生成完整的教学任务数据，包括课程-班级组合和现有任务状态
     *
     * @param planId 培养方案ID
     * @param termType 学期类型
     * @return 完整的教学任务列表数据
     */
    PageResponse<TaskVO> generateTaskWorkList(Long planId, Integer termType);
}
