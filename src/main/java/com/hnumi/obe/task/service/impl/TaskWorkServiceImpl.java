package com.hnumi.obe.task.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hnumi.obe.base.entity.Classes;
import com.hnumi.obe.base.service.IClassesService;
import com.hnumi.obe.base.service.ITeacherService;
import com.hnumi.obe.base.vo.ClassesVO;
import com.hnumi.obe.base.vo.TeacherVO;
import com.hnumi.obe.common.entity.PageResponse;
import com.hnumi.obe.common.util.RequestUtil;
import com.hnumi.obe.system.entity.BaseUser;
import com.hnumi.obe.system.service.IBaseUserService;
import com.hnumi.obe.task.dto.TaskWorkDTO;
import com.hnumi.obe.task.dto.TaskWorkQueryDTO;
import com.hnumi.obe.task.entity.TaskWork;
import com.hnumi.obe.task.mapper.TaskWorkMapper;
import com.hnumi.obe.task.mapstruct.TaskWorkConvert;
import com.hnumi.obe.task.service.ITaskWorkService;
import com.hnumi.obe.task.service.ITaskWorklistClassesService;
import com.hnumi.obe.task.service.ITaskWorklistTeachersService;
import com.hnumi.obe.task.vo.CourseStaticsByTaskVO;
import com.hnumi.obe.task.vo.TaskWorkDetailVO;
import com.hnumi.obe.task.vo.TaskWorkStatisticsVO;
import com.hnumi.obe.task.vo.TaskWorkVO;
import com.hnumi.obe.tp.entity.Course;
import com.hnumi.obe.tp.entity.Plan;
import com.hnumi.obe.tp.vo.TaskClassVO;
import com.hnumi.obe.tp.vo.TaskTeacherVO;
import com.hnumi.obe.tp.vo.TaskVO;
import com.hnumi.obe.tp.service.ICourseService;
import com.hnumi.obe.tp.service.IPlanService;
import com.hnumi.obe.tp.util.TaskTeacherVOConverter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;


/**
 * 教学任务信息表 服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TaskWorkServiceImpl extends ServiceImpl<TaskWorkMapper, TaskWork> implements ITaskWorkService {

    private final ITaskWorklistClassesService taskWorklistClassesService;
    private final ITaskWorklistTeachersService taskWorklistTeachersService;

    private final ICourseService courseService;
    private final IClassesService classesService;
    private final ITeacherService teacherService;
    private final IPlanService planService;
    private final IBaseUserService baseUserService;



    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createTaskWork(TaskWorkDTO dto) {
        log.info("开始创建教学任务，课程ID: {}, 任务名称: {}", dto.getCourseId(), dto.getTaskName());

        try {
            // 1. 数据验证
            validateTaskWorkData(dto);

            // 2. 清理课程-班级冲突数据
            cleanupCourseClassConflicts(dto.getCourseId(), dto.getClassIds());

            // 3. 保存教学任务主表
            TaskWork taskWork = TaskWorkConvert.INSTANCE.toEntity(dto);
            taskWork.setStatus(0); // 正常状态
            boolean saved = this.save(taskWork);

            if (!saved) {
                log.error("保存教学任务主表失败");
                return false;
            }

            // 4. 保存班级关联关系
            Long operatorId = RequestUtil.getExtendId();
            updateTaskClassRelations(taskWork.getId(), dto.getClassIds(), operatorId);

            // 5. 保存教师关联关系
            updateTaskTeacherRelations(taskWork.getId(), dto.getTeachers(), operatorId);

            log.info("成功创建教学任务，任务ID: {}", taskWork.getId());
            return true;

        } catch (Exception e) {
            log.error("创建教学任务失败，课程ID: {}, 错误信息: {}", dto.getCourseId(), e.getMessage(), e);
            throw e;
        }
    }

    // ==================== 公共方法和数据清理逻辑 ====================

    /**
     * 处理教师关联关系的公共方法
     * 复用updateTaskTeacherRelations方法，保持代码一致性
     *
     * @param taskId 任务ID
     * @param teachers 教师列表
     * @param operator 操作人
     */
    private void handleTeacherRelations(Long taskId, List<TaskWorkDTO.TaskTeacherDTO> teachers, Long operator) {
        updateTaskTeacherRelations(taskId, teachers, operator);
    }

    /**
     * 清理课程-班级冲突数据
     * @param courseId 课程ID
     * @param classIds 班级ID列表
     * @return 清理的记录数
     */
    private int cleanupCourseClassConflicts(Long courseId, List<Long> classIds) {
        if (courseId == null || CollectionUtils.isEmpty(classIds)) {
            return 0;
        }

        int cleanedCount = taskWorklistClassesService.cleanupDuplicateClassRelations(courseId, classIds);
        if (cleanedCount > 0) {
            log.info("清理了 {} 条课程ID={} 与班级的冲突关联关系", cleanedCount, courseId);
        }
        return cleanedCount;
    }

    /**
     * 验证任务数据的有效性
     * @param dto 任务DTO
     * @throws IllegalArgumentException 如果数据无效
     */
    private void validateTaskWorkData(TaskWorkDTO dto) {
        if (dto == null) {
            throw new IllegalArgumentException("教学任务数据不能为空");
        }
        if (dto.getCourseId() == null) {
            throw new IllegalArgumentException("课程ID不能为空");
        }
        if (!StringUtils.hasText(dto.getTaskName())) {
            throw new IllegalArgumentException("任务名称不能为空");
        }
    }

    /**
     * 验证更新任务数据的有效性
     * @param dto 任务DTO
     * @throws IllegalArgumentException 如果数据无效
     */
    private void validateUpdateTaskWorkData(TaskWorkDTO dto) {
        if (dto.getId() == null) {
            throw new IllegalArgumentException("修改教学任务时必须提供任务ID");
        }
        validateTaskWorkData(dto);
    }

    /**
     * 验证任务是否存在
     * @param taskId 任务ID
     * @return 存在的任务实体
     * @throws IllegalArgumentException 如果任务不存在或已被删除
     */
    private TaskWork validateTaskExists(Long taskId) {
        TaskWork existingTask = this.getById(taskId);
        if (existingTask == null || existingTask.getStatus() == -1) {
            throw new IllegalArgumentException("教学任务不存在或已被删除");
        }
        return existingTask;
    }

    /**
     * 检查任务是否还有关联的班级
     * @param taskId 任务ID
     * @return true-有关联班级，false-无关联班级
     */
    private boolean hasAssociatedClasses(Long taskId) {
        return taskWorklistClassesService.countByTaskId(taskId) > 0;
    }

    /**
     * 更新教学任务的班级关联关系
     * 采用先删除后插入的策略，确保数据一致性
     *
     * @param taskId 教学任务ID
     * @param classIds 新的班级ID列表，可以为空
     * @param operatorId 操作人ID
     */
    private void updateTaskClassRelations(Long taskId, List<Long> classIds, Long operatorId) {
        log.debug("开始更新任务ID={} 的班级关联关系，新班级列表: {}", taskId, classIds);

        // 1. 先删除该任务的所有班级关联记录
        boolean removed = taskWorklistClassesService.removeByTaskId(taskId);
        log.debug("删除任务ID={} 的原有班级关联关系，删除结果: {}", taskId, removed);

        // 2. 如果有新的班级关联，批量插入
        if (!CollectionUtils.isEmpty(classIds)) {
            boolean saved = taskWorklistClassesService.saveTaskClassRelations(taskId, classIds, operatorId);
            if (!saved) {
                log.error("批量保存班级关联关系失败，任务ID: {}, 班级列表: {}", taskId, classIds);
                throw new RuntimeException("保存班级关联关系失败");
            }
            log.info("成功更新任务ID={} 的班级关联关系，关联班级数量: {}", taskId, classIds.size());
        } else {
            log.info("任务ID={} 没有关联班级，已删除所有班级关联关系", taskId);
        }
    }

    /**
     * 更新教学任务的教师关联关系
     * 采用先删除后插入的策略，确保数据一致性
     *
     * @param taskId 教学任务ID
     * @param teachers 新的教师信息列表，可以为空
     * @param operatorId 操作人ID
     */
    private void updateTaskTeacherRelations(Long taskId, List<TaskWorkDTO.TaskTeacherDTO> teachers, Long operatorId) {
        log.debug("开始更新任务ID={} 的教师关联关系，新教师列表: {}", taskId, teachers);

        // 1. 先删除该任务的所有教师关联记录
        boolean removed = taskWorklistTeachersService.removeByTaskId(taskId);
        log.debug("删除任务ID={} 的原有教师关联关系，删除结果: {}", taskId, removed);

        // 2. 如果有新的教师关联，批量插入
        if (!CollectionUtils.isEmpty(teachers)) {
            boolean saved = taskWorklistTeachersService.saveTaskTeacherRelations(taskId, teachers, operatorId);
            if (!saved) {
                log.error("批量保存教师关联关系失败，任务ID: {}, 教师列表: {}", taskId, teachers);
                throw new RuntimeException("保存教师关联关系失败");
            }
            log.info("成功更新任务ID={} 的教师关联关系，关联教师数量: {}", taskId, teachers.size());
        } else {
            log.info("任务ID={} 没有关联教师，已删除所有教师关联关系", taskId);
        }
    }

    /**
     * 更新教学任务主表信息
     *
     * @param dto 教学任务DTO
     * @throws RuntimeException 如果更新失败
     */
    private void updateTaskWorkMainTable(TaskWorkDTO dto) {
        log.debug("开始更新教学任务主表，任务ID: {}", dto.getId());

        TaskWork taskWork = TaskWorkConvert.INSTANCE.toEntity(dto);
        boolean updated = this.updateById(taskWork);

        if (!updated) {
            log.error("更新教学任务主表失败，任务ID: {}", dto.getId());
            throw new RuntimeException("更新教学任务主表失败");
        }

        log.debug("成功更新教学任务主表，任务ID: {}", dto.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateTaskWork(TaskWorkDTO dto) {
        log.info("开始修改教学任务，任务ID: {}, 任务名称: {}", dto.getId(), dto.getTaskName());

        try {
            // 1. 数据验证
            validateUpdateTaskWorkData(dto);

            // 2. 检查任务是否存在
            TaskWork existingTask = validateTaskExists(dto.getId());

            // 3. 更新班级关联关系
            Long operatorId = RequestUtil.getExtendId();
            updateTaskClassRelations(dto.getId(), dto.getClassIds(), operatorId);

            // 4. 检查是否还有关联班级，决定是否删除任务
            if (!hasAssociatedClasses(dto.getId())) {
                log.info("任务ID={} 没有关联班级，执行物理删除", dto.getId());
                return deleteTaskWork(dto.getId());
            }

            // 5. 更新教学任务主表
            updateTaskWorkMainTable(dto);

            // 6. 更新教师关联关系
            updateTaskTeacherRelations(dto.getId(), dto.getTeachers(), operatorId);

            log.info("成功修改教学任务，任务ID: {}", dto.getId());
            return true;

        } catch (Exception e) {
            log.error("修改教学任务失败，任务ID: {}, 错误信息: {}", dto.getId(), e.getMessage(), e);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteTaskWork(Long taskId) {
        log.info("开始物理删除教学任务，任务ID: {}", taskId);

        try {
            if (taskId == null) {
                throw new IllegalArgumentException("任务ID不能为空");
            }

            // 1. 检查任务是否存在
            TaskWork existingTask = this.getById(taskId);
            if (existingTask == null) {
                log.warn("任务ID={} 不存在，跳过删除", taskId);
                return true; // 任务不存在视为删除成功
            }

            // 2. 删除班级关联关系
            taskWorklistClassesService.removeByTaskId(taskId);


            // 3. 删除教师关联关系
            taskWorklistTeachersService.removeByTaskId(taskId);


            // 4. 删除教学任务主表记录
            boolean deleted = this.removeById(taskId);
            if (!deleted) {
                log.error("删除教学任务主表失败，任务ID: {}", taskId);
                throw new RuntimeException("删除教学任务主表失败");
            }

            log.info("成功物理删除教学任务，任务ID: {}", taskId);
            return true;

        } catch (Exception e) {
            log.error("物理删除教学任务失败，任务ID: {}, 错误信息: {}", taskId, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteById(Long id) {
        // 1. 软删除教学��务主表
        LambdaUpdateWrapper<TaskWork> updateWrapper = Wrappers.lambdaUpdate(TaskWork.class);
        updateWrapper.eq(TaskWork::getId, id);
        updateWrapper.set(TaskWork::getStatus, -1);
        boolean deleted = this.update(updateWrapper);

        if (deleted) {
            // 2. 删除关联关系
            Long operatorId = RequestUtil.getExtendId();
            updateTaskClassRelations(id, null, operatorId);
            updateTaskTeacherRelations(id, null, operatorId);
        }

        return deleted;
    }

    @Override
    public TaskWorkVO getTaskWorkDetailById(Long id) {
        TaskWork taskWork = this.getById(id);
        if (taskWork == null || taskWork.getStatus() == -1) {
            return null;
        }

        TaskWorkVO vo = TaskWorkConvert.INSTANCE.toVO(taskWork);

        // 获取关联的班级ID列表
        List<Long> classIds = taskWorklistClassesService.getClassIdsByTaskId(id);
        vo.setClassIds(classIds);

        // 获取关联的教师信息列表
        List<TaskTeacherVO> teachers = taskWorklistTeachersService.getTeachersByTaskId(id);
        vo.setTeachers(teachers);

        // 计算学生总数
        Integer studentCount = calculateStudentCount(id);
        vo.setTotalStudentCount(studentCount);

        return vo;
    }

    @Override
    public Page<TaskWorkStatisticsVO> getCourseTaskWorkPage(Long courseId, TaskWorkQueryDTO queryDTO) {
        // 1. 获取该课程的所有教学任务
        LambdaQueryWrapper<TaskWork> wrapper = Wrappers.lambdaQuery(TaskWork.class);
        wrapper.eq(TaskWork::getCourseId, courseId);
        wrapper.eq(TaskWork::getStatus, 0);

        String courseName = courseService.getCourseNameById(courseId);

        // 2. 根据查询条件添加筛选
        if (queryDTO.getTaskYear() != null) {
            wrapper.eq(TaskWork::getTaskYear, queryDTO.getTaskYear());
        }
        if (queryDTO.getTaskTerm() != null) {
            wrapper.eq(TaskWork::getTaskTerm, queryDTO.getTaskTerm());
        }

        wrapper.orderByDesc(TaskWork::getTaskYear).orderByDesc(TaskWork::getTaskTerm);

        List<TaskWork> allTasks = this.list(wrapper);
        if (CollectionUtils.isEmpty(allTasks)) {
            return new Page<>(queryDTO.getPage().getCurrent(), queryDTO.getPage().getSize(), 0);
        }

        // 3. 按学年学期分组
        Map<String, List<TaskWork>> groupedTasks = allTasks.stream()
                .collect(Collectors.groupingBy(task ->
                        generateAcademicYearKey(task.getTaskYear(), task.getTaskTerm())
                ));

        // 4. 构建统计信息列表
        List<TaskWorkStatisticsVO> statisticsList = groupedTasks.entrySet().stream()
                .map(entry -> buildTaskWorkStatistics(entry.getKey(), entry.getValue(), courseId, courseName))
                .sorted((a, b) -> b.getAcademicYear().compareTo(a.getAcademicYear())) // 按学年倒序
                .collect(Collectors.toList());

        // 5. 手动分页
        Page<TaskWorkStatisticsVO> page = new Page<>(queryDTO.getPage().getCurrent(), queryDTO.getPage().getSize(), statisticsList.size());
        int start = (int) ((queryDTO.getPage().getCurrent() - 1) * queryDTO.getPage().getSize());
        int end = Math.min(start + (int) queryDTO.getPage().getSize(), statisticsList.size());
        List<TaskWorkStatisticsVO> pagedList = statisticsList.subList(start, end);
        page.setRecords(pagedList);

        return page;
    }

    @Override
    public List<TaskWorkDetailVO> getCourseTaskWorkBySemester(Long courseId, TaskWorkQueryDTO queryDTO) {
        // 验证学年和学期不能为空
        if (queryDTO.getTaskYear() == null || queryDTO.getTaskTerm() == null) {
            throw new IllegalArgumentException("学年和学期不能为空");
        }

        // 1. 获取该课程的所有教学任务
        LambdaQueryWrapper<TaskWork> wrapper = Wrappers.lambdaQuery(TaskWork.class);
        wrapper.eq(TaskWork::getCourseId, courseId);
        wrapper.eq(TaskWork::getStatus, 0);

        // 2. 根据查询条件添加筛选
        wrapper.eq(TaskWork::getTaskYear, queryDTO.getTaskYear());
        wrapper.eq(TaskWork::getTaskTerm, queryDTO.getTaskTerm());

        wrapper.orderByDesc(TaskWork::getTaskYear).orderByDesc(TaskWork::getTaskTerm);

        List<TaskWork> allTasks = this.list(wrapper);
        if (CollectionUtils.isEmpty(allTasks)) {
            return List.of();
        }

        // 3. 转换为TaskWorkDetailVO列表并返回
        return allTasks.stream()
                .map(this::convertToTaskWorkDetailVO)
                .collect(Collectors.toList());
    }

    @Override
    public List<TaskWorkDetailVO> getCourseTaskWorkBySemester(Long courseId) {


        // 1. 获取该课程的所有教学任务
        LambdaQueryWrapper<TaskWork> wrapper = Wrappers.lambdaQuery(TaskWork.class);
        wrapper.eq(TaskWork::getCourseId, courseId);
        wrapper.eq(TaskWork::getStatus, 0);

        wrapper.orderByDesc(TaskWork::getTaskYear).orderByDesc(TaskWork::getTaskTerm);

        List<TaskWork> allTasks = this.list(wrapper);
        if (CollectionUtils.isEmpty(allTasks)) {
            return List.of();
        }

        // 3. 转换为TaskWorkDetailVO列表并返回
        return allTasks.stream()
                .map(this::convertToTaskWorkDetailVO)
                .collect(Collectors.toList());
    }

    /**
     * 创建空的统计信息
     */
    private TaskWorkStatisticsVO createEmptyStatistics(Long courseId, TaskWorkQueryDTO queryDTO) {
        TaskWorkStatisticsVO statistics = new TaskWorkStatisticsVO();
        statistics.setCourseId(courseId);
        statistics.setCourseName("未知数据"); // 临时硬编码
        statistics.setAcademicYear("");
        statistics.setSemester("");
        statistics.setTaskDetails(new Page<>(queryDTO.getPage().getCurrent(), queryDTO.getPage().getSize(), 0));
        statistics.setTotalTaskCount(0);
        statistics.setTotalClassCount(0);
        statistics.setTotalStudentCount(0);
        return statistics;
    }

    /**
     * 转换TaskWork为TaskWorkDetailVO
     */
    private TaskWorkDetailVO convertToTaskWorkDetailVO(TaskWork taskWork) {
        TaskWorkDetailVO detailVO = new TaskWorkDetailVO();
        detailVO.setTaskId(taskWork.getId());
        detailVO.setTaskName(taskWork.getTaskName());
        detailVO.setTaskNumber(taskWork.getTaskNumber());
        detailVO.setTeachWeek(taskWork.getTeachWeek());
        detailVO.setWeekHours(taskWork.getWeekHours());
        //生成学年和学期信息
        String[] academicInfo = calculateAcademicYearAndSemester(taskWork.getTaskYear(), taskWork.getTaskTerm());

        detailVO.setAcademicYear(academicInfo[0]);
        detailVO.setSemester(academicInfo[1]);

        // 获取班级信息
        detailVO.setClasses(getTaskClasses(taskWork.getId()));

        // 获取教师信息
        detailVO.setTeachers(getTaskTeachers(taskWork.getId()));

        // 计算统计信息
        detailVO.setClassCount(calculateClassCount(taskWork.getId()));
        detailVO.setStudentCount(calculateStudentCount(taskWork.getId()));

        return detailVO;
    }

   /**
 * 获取任务的班级信息（优化版 - 使用批量查询）
 */
private List<TaskWorkDetailVO.TaskClassVO> getTaskClasses(Long taskId) {
    // 获取班级ID列表
    List<Long> classIds = taskWorklistClassesService.getClassIdsByTaskId(taskId);
    if (CollectionUtils.isEmpty(classIds)) {
        return List.of();
    }

    // 批量查询班级信息
    Map<Long, ClassesVO> classesMap = classesService.getClassesInfoByIds(classIds);
    if (classesMap.isEmpty()) {
        return List.of();
    }

    // 转换为TaskWorkDetailVO.TaskClassVO
    return classIds.stream()
            .map(classId -> {
                TaskWorkDetailVO.TaskClassVO classVO = new TaskWorkDetailVO.TaskClassVO();
                classVO.setClassId(classId);

                ClassesVO classesVO = classesMap.get(classId);
                if (classesVO != null) {
                    classVO.setClassName(classesVO.getClassName() != null ? classesVO.getClassName() : "未知班级");
                    classVO.setStudentNumber(classesVO.getStudentNumber() != null ? classesVO.getStudentNumber() : 0);
                } else {
                    classVO.setClassName("未知班级");
                    classVO.setStudentNumber(0);
                }

                return classVO;
            })
            .collect(Collectors.toList());
}
    /**
     * 获取任务的教师信息（优化版 - 使用批量查询，缓存机制和错误处理）
     *
     * @param taskId 任务ID
     * @return 教师信息列表
     */
    private List<TaskWorkDetailVO.TaskTeacherVO> getTaskTeachers(Long taskId) {
        // 1. 获取教师关联信息列表
        List<TaskTeacherVO> teacherRelations = taskWorklistTeachersService.getTeachersByTaskId(taskId);
        if (teacherRelations == null || teacherRelations.isEmpty()) {
            return List.of();
        }

        // 2. 提取教师ID列表
        List<Long> teacherIds = teacherRelations.stream()
                .map(TaskTeacherVO::teacherId)
                .filter(Objects::nonNull) // 过滤可能的null值
                .distinct() // 去重
                .collect(Collectors.toList());

        if (teacherIds.isEmpty()) {
            return List.of();
        }

        // 3. 批量查询教师信息 - 使用新的优化方法
        Map<Long, TeacherVO> teacherMap = teacherService.getTeacherInfoByIds(teacherIds);

        // 4. 转换为TaskWorkDetailVO的教师信息
        return teacherRelations.stream()
                .map(relation -> {
                    TaskWorkDetailVO.TaskTeacherVO teacherVO = new TaskWorkDetailVO.TaskTeacherVO();
                    teacherVO.setTeacherId(relation.teacherId());
                    teacherVO.setRole(relation.role());

                    // 使用Map直接获取教师信息，避免多次查询
                    if (relation.teacherId() != null && teacherMap.containsKey(relation.teacherId())) {
                        TeacherVO teacher = teacherMap.get(relation.teacherId());
                        teacherVO.setTeacherName(teacher.getName());
                    } else {
                        teacherVO.setTeacherName("未知教师");
                    }

                    // 转换角色名称
                    teacherVO.setRoleName(getRoleName(relation.role()));
                    return teacherVO;
                })
                .collect(Collectors.toList());
    }

    /**
     * 根据角色代码获取角色名称
     */
    private String getRoleName(Integer role) {
        if (role == null) {
            return "未知";
        }
        switch (role) {
            case 1:
                return "主讲教师";
            case 2:
                return "辅导教师";
            case 3:
                return "助教";
            default:
                return "未知";
        }
    }

    /**
     * 计算任务的班级数量
     */
    private Integer calculateClassCount(Long taskId) {
        List<Long> classIds = taskWorklistClassesService.getClassIdsByTaskId(taskId);
        return classIds != null ? classIds.size() : 0;
    }

    @Override
    public List<TaskWorkStatisticsVO> getTaskWorkStatisticsByCourseId(Long courseId) {
        // 1. 获取该课程的所有教学任务
        LambdaQueryWrapper<TaskWork> wrapper = Wrappers.lambdaQuery(TaskWork.class);
        wrapper.eq(TaskWork::getCourseId, courseId);
        wrapper.eq(TaskWork::getStatus, 0);
        wrapper.orderByDesc(TaskWork::getTaskYear).orderByDesc(TaskWork::getTaskTerm);

        String courseName = courseService.getCourseNameById(courseId);

        List<TaskWork> taskWorks = this.list(wrapper);
        if (CollectionUtils.isEmpty(taskWorks)) {
            return List.of();
        }

        // 2. 按学年学期分组
        Map<String, List<TaskWork>> groupedTasks = taskWorks.stream()
                .collect(Collectors.groupingBy(task ->
                        generateAcademicYearKey(task.getTaskYear(), task.getTaskTerm())
                ));

        // 3. 构建统计信息
        return groupedTasks.entrySet().stream()
                .map(entry -> buildTaskWorkStatistics(entry.getKey(), entry.getValue(), courseId, courseName))
                .sorted((a, b) -> b.getAcademicYear().compareTo(a.getAcademicYear())) // 按学年倒序
                .collect(Collectors.toList());
    }

    /**
     * 计算学年和学期信息
     *
     * @param taskYear 任务年份
     * @param taskTerm 学期
     * @return 数组，[0]为学年，[1]为学期名称
     */
    private String[] calculateAcademicYearAndSemester(Integer taskYear, Integer taskTerm) {
        String academicYear;
        String semester;

        if (taskTerm == 1 || taskTerm == 3 || taskTerm == 5 || taskTerm == 7) { // 秋季学期
            academicYear = taskYear + "-" + (taskYear + 1);
            semester = "秋季学期";
        } else if (taskTerm == 2 || taskTerm == 4 || taskTerm == 6 || taskTerm == 8) { // 春季学期
            academicYear = (taskYear - 1) + "-" + taskYear;
            semester = "春季学期";
        } else {
            academicYear = taskYear + "-" + (taskYear + 1);
            semester = "未知学期";
        }

        return new String[]{academicYear, semester};
    }

    /**
     * 生成学年学期键
     */
    private String generateAcademicYearKey(Integer taskYear, Integer taskTerm) {
        String[] result = calculateAcademicYearAndSemester(taskYear, taskTerm);
        return result[0] + "_" + result[1];
    }

    /**
     * 构建教学任务统计信息
     */
    private TaskWorkStatisticsVO buildTaskWorkStatistics(String key, List<TaskWork> tasks, Long courseId, String courseName) {
        String[] parts = key.split("_");
        String academicYear = parts[0];
        String semester = parts[1];

        TaskWorkStatisticsVO statistics = new TaskWorkStatisticsVO();
        //
        statistics.setTaskYear(tasks!=null?tasks.get(0).getTaskYear(): null);
        statistics.setTaskTerm(tasks!=null?tasks.get(0).getTaskTerm(): null);
        statistics.setAcademicYear(academicYear);
        statistics.setSemester(semester);
        statistics.setCourseId(courseId);
        statistics.setCourseName(courseName);

        // 构建任务详情列表 - 使用独立的TaskWorkDetailVO
        List<TaskWorkDetailVO> taskDetails = tasks.stream()
                .map(this::convertToTaskWorkDetailVO)
                .collect(Collectors.toList());

        // 创建分页对象（这里暂时不分页，返回所有数据）
        Page<TaskWorkDetailVO> page = new Page<>(1, taskDetails.size(), taskDetails.size());
        page.setRecords(taskDetails);

        statistics.setTaskDetails(page);
        statistics.setTotalTaskCount(tasks.size());

        // 计算总的班级数量和学生数量
        int totalClassCount = taskDetails.stream()
                .mapToInt(TaskWorkDetailVO::getClassCount)
                .sum();
        int totalStudentCount = taskDetails.stream()
                .mapToInt(TaskWorkDetailVO::getStudentCount)
                .sum();

        // 计算该学期的教师总数（去重）
        int totalTeacherCount = calculateTotalTeacherCount(tasks);

        statistics.setTotalClassCount(totalClassCount);
        statistics.setTotalStudentCount(totalStudentCount);
        statistics.setTotalTeacherCount(totalTeacherCount);

        return statistics;
    }

    /**
     * 计算教学任务列表中的教师总数（去重统计）
     */
    private int calculateTotalTeacherCount(List<TaskWork> tasks) {
        if (CollectionUtils.isEmpty(tasks)) {
            return 0;
        }

        // 收集所有教学任务的教师ID，使用Set去重
        Set<Long> teacherIds = tasks.stream()
                .flatMap(task -> {
                    List<TaskTeacherVO> teachers = taskWorklistTeachersService.getTeachersByTaskId(task.getId());
                    return teachers.stream().map(TaskTeacherVO::teacherId);
                })
                .collect(Collectors.toSet());

        return teacherIds.size();
    }



    @Override
    public List<TaskWorkVO> getTasksByTeacherId(Long teacherId) {
        List<Long> taskIds = taskWorklistTeachersService.getTaskIdsByTeacherId(teacherId);
        if (CollectionUtils.isEmpty(taskIds)) {
            return List.of();
        }

        LambdaQueryWrapper<TaskWork> wrapper = Wrappers.lambdaQuery(TaskWork.class);
        wrapper.in(TaskWork::getId, taskIds);
        wrapper.eq(TaskWork::getStatus, 0);
        wrapper.orderByDesc(TaskWork::getCreateTime);

        return this.list(wrapper).stream()
                .map(this::convertToVOWithRelations)
                .collect(Collectors.toList());
    }

    @Override
    public List<TaskWorkVO> getTasksByClassId(Long classId) {
        List<Long> taskIds = taskWorklistClassesService.getTaskIdsByClassId(classId);
        if (CollectionUtils.isEmpty(taskIds)) {
            return List.of();
        }

        LambdaQueryWrapper<TaskWork> wrapper = Wrappers.lambdaQuery(TaskWork.class);
        wrapper.in(TaskWork::getId, taskIds);
        wrapper.eq(TaskWork::getStatus, 0);
        wrapper.orderByDesc(TaskWork::getCreateTime);

        return this.list(wrapper).stream()
                .map(this::convertToVOWithRelations)
                .collect(Collectors.toList());
    }

    @Override
    public List<TaskWorkVO> getTasksByCourseId(Long courseId) {
        LambdaQueryWrapper<TaskWork> wrapper = Wrappers.lambdaQuery(TaskWork.class);
        wrapper.eq(TaskWork::getCourseId, courseId);
        wrapper.eq(TaskWork::getStatus, 0);
        wrapper.orderByDesc(TaskWork::getCreateTime);

        return this.list(wrapper).stream()
                .map(this::convertToVOWithRelations)
                .collect(Collectors.toList());
    }

    @Override
    public Integer calculateStudentCount(Long taskId) {
        // 获取关联的班级ID列表
        List<Long> classIds = taskWorklistClassesService.getClassIdsByTaskId(taskId);
        if (CollectionUtils.isEmpty(classIds)) {
            return 0;
        }

        // 批量查询班级信息并汇总学生数量
        Map<Long, ClassesVO> classesMap = classesService.getClassesInfoByIds(classIds);
        return classesMap.values().stream()
                .mapToInt(classesVO -> classesVO.getStudentNumber() != null ? classesVO.getStudentNumber() : 0)
                .sum();
    }

    /**
     * 构建查询条件
     */
    private LambdaQueryWrapper<TaskWork> buildQueryWrapper(TaskWorkQueryDTO queryDTO) {
        LambdaQueryWrapper<TaskWork> wrapper = Wrappers.lambdaQuery(TaskWork.class);

        wrapper.eq(TaskWork::getStatus, 0); // 只查询正常状态的记录
        wrapper.eq(queryDTO.getCourseId() != null, TaskWork::getCourseId, queryDTO.getCourseId());
        wrapper.eq(queryDTO.getTaskNumber() != null, TaskWork::getTaskNumber, queryDTO.getTaskNumber());
        wrapper.like(StringUtils.hasText(queryDTO.getTaskName()), TaskWork::getTaskName, queryDTO.getTaskName());
        wrapper.eq(queryDTO.getTaskYear() != null, TaskWork::getTaskYear, queryDTO.getTaskYear());
        wrapper.eq(queryDTO.getTaskTerm() != null, TaskWork::getTaskTerm, queryDTO.getTaskTerm());
        wrapper.eq(queryDTO.getCourseLeaderId() != null, TaskWork::getCourseLeaderId, queryDTO.getCourseLeaderId());

        // 处理班级和教师的关联查询
        if (queryDTO.getClassId() != null) {
            List<Long> taskIds = taskWorklistClassesService.getTaskIdsByClassId(queryDTO.getClassId());
            if (!CollectionUtils.isEmpty(taskIds)) {
                wrapper.in(TaskWork::getId, taskIds);
            } else {
                wrapper.eq(TaskWork::getId, -1); // 如果没有关联记录，返回空结果
            }
        }

        if (queryDTO.getTeacherId() != null) {
            List<Long> taskIds = taskWorklistTeachersService.getTaskIdsByTeacherId(queryDTO.getTeacherId());
            if (!CollectionUtils.isEmpty(taskIds)) {
                wrapper.in(TaskWork::getId, taskIds);
            } else {
                wrapper.eq(TaskWork::getId, -1); // 如果没有关联记录，返回空结果
            }
        }

        wrapper.orderByDesc(TaskWork::getCreateTime);
        return wrapper;
    }

    /**
     * 转换为VO并填充关联信息
     */
    private TaskWorkVO convertToVOWithRelations(TaskWork taskWork) {
        TaskWorkVO vo = TaskWorkConvert.INSTANCE.toVO(taskWork);

        // 获取关联的班级ID列表
//        List<Long> classIds = taskWorklistClassesService.getClassIdsByTaskId(taskWork.getId());
//        vo.setClassIds(classIds);

        // 获取关联的教师信息列表
        List<TaskTeacherVO> teachers = taskWorklistTeachersService.getTeachersByTaskId(taskWork.getId());
        vo.setTeachers(teachers);

        // 计算学生总数
        Integer studentCount = calculateStudentCount(taskWork.getId());
        vo.setTotalStudentCount(studentCount);

        return vo;
    }

    //=======================培养方案：教学计划生成相关接口=======================



    @Override
    public PageResponse<TaskVO> generateTaskWorkList(Long planId,  Integer termType) {
        log.info("开始生成教学任务列表，planId: {}, termType: {}", planId, termType);
        Plan plan = planService.getById(planId);
        if (plan == null) {
            throw new RuntimeException("培养方案不存在");
        }
        Long majorId = plan.getMajorId();
        Long year = plan.getPlanVersion();
        try {
            // 1. 获取培养方案信息和课程列表
            List<Course> courses = getCoursesByPlanAndSemester(planId, termType);
            if (CollectionUtils.isEmpty(courses)) {
                log.warn("未找到培养方案下的课程，planId: {}, termType: {}", planId, termType);
                return new PageResponse<>(List.of(), 0L);
            }

            // 2. 获取专业下指定入学年份的班级列表
            List<Classes> classEntities = classesService.getClassByMajorAndEntranceYear(majorId, year.intValue());
            if (CollectionUtils.isEmpty(classEntities)) {
                log.warn("未找到专业下指定入学年份的班级，majorId: {}, entranceYear: {}", majorId, year);
                return new PageResponse<>(List.of(), 0L);
            }

            // 转换为ClassesVO
            List<ClassesVO> classes = classEntities.stream()
                .map(this::convertToClassesVO)
                .collect(Collectors.toList());

            log.info("找到 {} 个班级，专业ID: {}, 入学年份: {}", classes.size(), majorId, year);

            // 3. 批量查询所有课程的现有任务（进一步优化）
            List<TaskVO> result = generateTaskWorkListOptimized(courses, classes);

            log.info("成功生成教学任务列表，共 {} 个任务", result.size());
            return new PageResponse<>(result, (long) result.size());

        } catch (Exception e) {
            log.error("生成教学任务列表失败，planId: {}, majorId: {}, year: {}, termType: {}",
                     planId, majorId, year, termType, e);
            throw new RuntimeException("生成教学任务列表失败：" + e.getMessage(), e);
        }
    }

    /**
     * 根据培养方案ID和学期获取课程列表
     */
    private List<Course> getCoursesByPlanAndSemester(Long planId , Integer termType) {
        // 2. 根据培养方案版本和学期查询课程
        return courseService.getCourseListByMajorAndPlanAndSemester(
                planId,
            Long.valueOf(termType)
        );
    }

    /**
     * 最优化的任务列表生成方法（正确的课程-班级组合业务逻辑）
     */
    private List<TaskVO> generateTaskWorkListOptimized(List<Course> courses, List<ClassesVO> classes) {
        try {
            // 1. 批量查询所有课程的现有任务
            List<Long> courseIds = courses.stream().map(Course::getCourseId).collect(Collectors.toList());
            Map<Long, List<TaskWork>> courseTaskMap = batchGetExistingTasksForCourses(courseIds);

            // 2. 收集所有任务ID
            List<Long> allTaskIds = courseTaskMap.values().stream()
                .flatMap(List::stream)
                .map(TaskWork::getId)
                .collect(Collectors.toList());

            // 3. 批量查询所有任务的关联关系
            Map<Long, List<Long>> allTaskClassMap = batchGetTaskClassRelations(allTaskIds);
            Map<Long, List<Long>> allTaskTeacherMap = batchGetTaskTeacherRelations(allTaskIds);

            // 4. 批量查询所有教师信息
            Set<Long> allTeacherIds = allTaskTeacherMap.values().stream()
                .flatMap(List::stream)
                .collect(Collectors.toSet());
            Map<Long, TeacherVO> teacherMap = CollectionUtils.isEmpty(allTeacherIds) ?
                Map.of() : teacherService.getTeacherMapByIds(new ArrayList<>(allTeacherIds));

            // 5. 创建班级映射
            Map<Long, ClassesVO> classMap = classes.stream()
                .collect(Collectors.toMap(ClassesVO::getClassId, cls -> cls));

            // 6. 生成所有课程-班级组合的TaskVO（N×M个TaskVO）
            List<TaskVO> allTaskVOs = new ArrayList<>();

            for (Course course : courses) {
                List<TaskWork> courseTasks = courseTaskMap.getOrDefault(course.getCourseId(), List.of());

                if (CollectionUtils.isEmpty(courseTasks)) {
                    // 没有现有任务，为每个课程-班级组合创建虚拟TaskVO
                    List<TaskVO> virtualTasks = createVirtualTaskVOsForCourseClassCombinations(course, classes);
                    allTaskVOs.addAll(virtualTasks);
                } else {
                    // 有现有任务，需要同时处理现有任务和未覆盖的班级

                    // 6.1 为现有任务创建TaskVO
                    List<TaskVO> existingTaskVOs = createTaskVOsFromExistingForCombinations(course,
                        courseTasks, allTaskClassMap, allTaskTeacherMap, teacherMap, classMap);
                    allTaskVOs.addAll(existingTaskVOs);

                    // 6.2 找出现有任务已覆盖的班级ID
                    Set<Long> coveredClassIds = getCoveredClassIds(courseTasks, allTaskClassMap);

                    // 6.3 为未覆盖的班级生成虚拟任务
                    List<ClassesVO> uncoveredClasses = classes.stream()
                        .filter(cls -> !coveredClassIds.contains(cls.getClassId()))
                        .collect(Collectors.toList());

                    if (!CollectionUtils.isEmpty(uncoveredClasses)) {
                        List<TaskVO> virtualTasks = createVirtualTaskVOsForCourseClassCombinations(course, uncoveredClasses);
                        allTaskVOs.addAll(virtualTasks);
                        log.info("课程ID={} 有 {} 个现有任务，为 {} 个未覆盖班级生成虚拟任务",
                                course.getCourseId(), courseTasks.size(), uncoveredClasses.size());
                    }
                }
            }

            // 7. 按任务ID分组合并TaskVO（相同id的TaskVO合并为一个）
            List<TaskVO> mergedResult = mergeTaskVOsById(allTaskVOs);

            return mergedResult;

        } catch (Exception e) {
            log.error("优化版任务列表生成失败", e);
            return List.of();
        }
    }

    /**
     * 获取现有任务已覆盖的班级ID集合
     * @param courseTasks 课程的现有任务列表
     * @param allTaskClassMap 任务-班级关联映射
     * @return 已覆盖的班级ID集合
     */
    private Set<Long> getCoveredClassIds(List<TaskWork> courseTasks, Map<Long, List<Long>> allTaskClassMap) {
        return courseTasks.stream()
            .map(TaskWork::getId)
            .flatMap(taskId -> allTaskClassMap.getOrDefault(taskId, List.of()).stream())
            .collect(Collectors.toSet());
    }

    /**
     * 将Classes实体转换为ClassesVO
     * @param classes Classes实体
     * @return ClassesVO
     */
    private ClassesVO convertToClassesVO(Classes classes) {
        ClassesVO vo = new ClassesVO();
        vo.setClassId(classes.getClassId());
        vo.setClassName(classes.getClassName());
        vo.setMajorId(classes.getMajorId());
        vo.setEntranceYear(classes.getEntranceYear());
        vo.setStudentNumber(classes.getStudentNumber());
        vo.setHeadteacherId(classes.getHeadteacherId()); // 修正字段名
        vo.setClassStatus(classes.getClassStatus()); // 添加班级状态
        vo.setStatus(classes.getStatus());
        vo.setCreator(classes.getCreator());
        vo.setCreateTime(classes.getCreateTime());
        vo.setModifier(classes.getModifier());
        vo.setModifyTime(classes.getModifyTime());
        return vo;
    }

    /**
     * 批量查询多个课程的现有任务
     */
    private Map<Long, List<TaskWork>> batchGetExistingTasksForCourses(List<Long> courseIds) {
        if (CollectionUtils.isEmpty(courseIds)) {
            return Map.of();
        }

        try {
            LambdaQueryWrapper<TaskWork> wrapper = Wrappers.lambdaQuery(TaskWork.class);
            wrapper.in(TaskWork::getCourseId, courseIds)
                   //.eq(TaskWork::getTaskYear, year)，课程id确定后，学年学期也确定
                  // .eq(TaskWork::getTaskTerm, termType)
                   .eq(TaskWork::getStatus, 0)
                   .orderByDesc(TaskWork::getCreateTime);

            List<TaskWork> allTasks = this.list(wrapper);

            // 按课程ID分组
            return allTasks.stream()
                .collect(Collectors.groupingBy(TaskWork::getCourseId));

        } catch (Exception e) {
            log.error("批量查询课程任务失败，courseIds: {}", courseIds, e);
            return Map.of();
        }
    }



    /**
     * 为课程-班级组合创建虚拟TaskVO列表（N×M个TaskVO）
     */
    private List<TaskVO> createVirtualTaskVOsForCourseClassCombinations(Course course, List<ClassesVO> classes) {
        List<TaskVO> result = new ArrayList<>();

        // 为每个班级创建一个独立的TaskVO
        for (ClassesVO classVO : classes) {
            // 创建单个班级的TaskClassVO
            List<TaskClassVO> singleClassList = List.of(
                new TaskClassVO(classVO.getClassId(), classVO.getClassName())
            );

            // 计算单个班级的学生数
            int studentCount = classVO.getStudentNumber() != null ? classVO.getStudentNumber() : 0;

            // 创建任务名称：课程名称 + 班级名称
            String taskName = course.getCourseName();

            TaskVO taskVO = new TaskVO(
                null, // 虚拟任务没有ID
                taskName,
                course.getCourseId(),
                course.getCourseName(),
                course.getCourseCredit(),
                course.getCourseHoursTotal(),
                List.of(), // 没有教师
                singleClassList,
                studentCount
            );

            result.add(taskVO);
        }

        return result;
    }

    /**
     * 从现有任务为每个任务-班级组合创建TaskVO
     */
    private List<TaskVO> createTaskVOsFromExistingForCombinations(Course course,List<TaskWork> taskWorks,
                                                                 Map<Long, List<Long>> allTaskClassMap,
                                                                 Map<Long, List<Long>> allTaskTeacherMap,
                                                                 Map<Long, TeacherVO> teacherMap,
                                                                 Map<Long, ClassesVO> classMap) {
        List<TaskVO> result = new ArrayList<>();

        for (TaskWork taskWork : taskWorks) {
            // 获取该任务关联的班级ID列表
            List<Long> taskClassIds = allTaskClassMap.getOrDefault(taskWork.getId(), List.of());

            // 获取该任务关联的教师信息
            List<Long> teacherIds = allTaskTeacherMap.getOrDefault(taskWork.getId(), List.of());
            List<TaskTeacherVO> teachers = teacherIds.stream()
                .map(teacherMap::get)
                .filter(Objects::nonNull)
                .map(TaskTeacherVO::from)
                .collect(Collectors.toList());

            // 为每个班级创建一个独立的TaskVO
            for (Long classId : taskClassIds) {
                ClassesVO classVO = classMap.get(classId);
                if (classVO != null) {
                    // 创建单个班级的TaskClassVO
                    List<TaskClassVO> singleClassList = List.of(
                        new TaskClassVO(classVO.getClassId(), classVO.getClassName())
                    );

                    // 计算单个班级的学生数
                    int studentCount = classVO.getStudentNumber() != null ? classVO.getStudentNumber() : 0;

                    TaskVO taskVO = new TaskVO(
                        taskWork.getId(), // 使用真实的任务ID
                        taskWork.getTaskName(),
                        taskWork.getCourseId(),
                        course.getCourseName(),
                        course.getCourseCredit(),
                        course.getCourseHoursTotal(),
                        teachers, // 所有教师信息（同一任务的所有班级共享教师）
                        singleClassList,
                        studentCount
                    );

                    result.add(taskVO);
                }
            }
        }

        return result;
    }

    /**
     * 按任务ID分组合并TaskVO
     * 相同ID的TaskVO记录合并为一个任务项，聚合班级信息、教师信息和学生总数
     */
    private List<TaskVO> mergeTaskVOsById(List<TaskVO> taskVOs) {
        if (CollectionUtils.isEmpty(taskVOs)) {
            return List.of();
        }

        // 按任务ID分组（null ID的虚拟任务保持独立）
        Map<Long, List<TaskVO>> groupedTasks = new HashMap<>();
        List<TaskVO> virtualTasks = new ArrayList<>(); // ID为null的虚拟任务

        for (TaskVO taskVO : taskVOs) {
            if (taskVO.id() == null) {
                // 虚拟任务保持独立，不合并
                virtualTasks.add(taskVO);
            } else {
                // 真实任务按ID分组
                groupedTasks.computeIfAbsent(taskVO.id(), k -> new ArrayList<>()).add(taskVO);
            }
        }

        List<TaskVO> result = new ArrayList<>();

        // 添加虚拟任务（不需要合并）
        result.addAll(virtualTasks);

        // 合并相同ID的真实任务
        for (Map.Entry<Long, List<TaskVO>> entry : groupedTasks.entrySet()) {
            List<TaskVO> sameTasks = entry.getValue();

            if (sameTasks.size() == 1) {
                // 只有一个任务，直接添加
                result.add(sameTasks.get(0));
            } else {
                // 多个任务需要合并
                TaskVO mergedTask = mergeSameIdTasks(sameTasks);
                if (mergedTask != null) {
                    result.add(mergedTask);
                }
            }
        }

        return result;
    }

    /**
     * 合并相同ID的TaskVO列表
     */
    private TaskVO mergeSameIdTasks(List<TaskVO> tasks) {
        if (CollectionUtils.isEmpty(tasks)) {
            return null;
        }

        TaskVO firstTask = tasks.get(0);

        // 合并所有班级信息（去重）
        Set<TaskClassVO> allClasses = new HashSet<>();
        for (TaskVO task : tasks) {
            allClasses.addAll(task.classes());
        }

        // 合并所有教师信息（去重）
        Set<TaskTeacherVO> allTeachers = new HashSet<>();
        for (TaskVO task : tasks) {
            allTeachers.addAll(task.teachers());
        }

        // 计算总学生数
        int totalStudents = tasks.stream()
            .mapToInt(TaskVO::totalStudents)
            .sum();

        return new TaskVO(
            firstTask.id(),
            firstTask.name(),
            firstTask.courseId(),
            firstTask.courseName(),
            firstTask.courseCredit(),
            firstTask.courseHoursTotal(),
            new ArrayList<>(allTeachers),
            new ArrayList<>(allClasses),
            totalStudents
        );
    }



    /**
     * 批量获取任务-班级关联关系
     * @param taskIds 任务ID列表
     * @return Map<任务ID, 班级ID列表>
     */
    private Map<Long, List<Long>> batchGetTaskClassRelations(List<Long> taskIds) {
        if (CollectionUtils.isEmpty(taskIds)) {
            return Map.of();
        }

        try {
            // 使用现有的批量查询方法
            return taskWorklistClassesService.getClassesMap(taskIds)
                .entrySet().stream()
                .collect(Collectors.toMap(
                    Map.Entry::getKey,
                    entry -> entry.getValue().stream()
                        .map(tc -> tc.getClassId())
                        .collect(Collectors.toList())
                ));
        } catch (Exception e) {
            log.error("批量获取任务-班级关联关系失败，taskIds: {}", taskIds, e);
            return Map.of();
        }
    }

    /**
     * 批量获取任务-教师关联关系
     * @param taskIds 任务ID列表
     * @return Map<任务ID, 教师ID列表>
     */
    private Map<Long, List<Long>> batchGetTaskTeacherRelations(List<Long> taskIds) {
        if (CollectionUtils.isEmpty(taskIds)) {
            return Map.of();
        }

        try {
            // 使用现有的批量查询方法
            return taskWorklistTeachersService.getTeachersMap(taskIds)
                .entrySet().stream()
                .collect(Collectors.toMap(
                    Map.Entry::getKey,
                    entry -> entry.getValue().stream()
                        .map(tt -> tt.getTeacherId())
                        .collect(Collectors.toList())
                ));
        } catch (Exception e) {
            log.error("批量获取任务-教师关联关系失败，taskIds: {}", taskIds, e);
            return Map.of();
        }
    }

//    /**
//     * 获取课程的现有教学任务
//     */
//    private List<TaskWork> getExistingTasksForCourse(Long courseId, Integer year, Integer termType) {
//        LambdaQueryWrapper<TaskWork> wrapper = Wrappers.lambdaQuery(TaskWork.class);
//        wrapper.eq(TaskWork::getCourseId, courseId)
//               .eq(TaskWork::getTaskYear, year)
//               .eq(TaskWork::getTaskTerm, termType)
//               .eq(TaskWork::getStatus, 0)
//               .orderByDesc(TaskWork::getCreateTime);
//
//        return this.list(wrapper);
//    }


}
