package com.hnumi.obe.task.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hnumi.obe.base.service.ITeacherService;
import com.hnumi.obe.base.vo.TeacherVO;
import com.hnumi.obe.system.mapper.BaseUserMapper;
import com.hnumi.obe.task.dto.TaskWorkDTO;
import com.hnumi.obe.task.entity.TaskWorklistTeachers;
import com.hnumi.obe.task.mapper.TaskWorklistTeachersMapper;
import com.hnumi.obe.task.service.ITaskWorklistTeachersService;
import com.hnumi.obe.tp.vo.TaskTeacherVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 教学任务-教师关联表 服务实现类
 */
@Slf4j
@Service
public class TaskWorklistTeachersServiceImpl extends ServiceImpl<TaskWorklistTeachersMapper, TaskWorklistTeachers> implements ITaskWorklistTeachersService {

    @Autowired
    private ITeacherService teacherService;
    
    @Autowired
    private BaseUserMapper baseUserMapper;

    @Override
    public List<TaskTeacherVO> getTeachersByTaskId(Long taskId) {
        LambdaQueryWrapper<TaskWorklistTeachers> wrapper = Wrappers.lambdaQuery(TaskWorklistTeachers.class);
        wrapper.eq(TaskWorklistTeachers::getTaskId, taskId);

        // 获取任务关联的教师ID列表
        List<Long> teacherIds = this.list(wrapper).stream()
                .map(TaskWorklistTeachers::getTeacherId)
                .collect(Collectors.toList());

        if (teacherIds.isEmpty()) {
            return List.of();
        }

        // 批量查询教师详细信息
        Map<Long, TeacherVO> teacherMap = teacherService.getTeacherMapByIds(teacherIds);
        
        // 转换为TaskTeacherVO列表
        return teacherIds.stream()
                .map(teacherId -> {
                    TeacherVO teacherVO = teacherMap.get(teacherId);
                    if (teacherVO != null) {
                        return TaskTeacherVO.from(teacherVO);
                    } else {
                        // 如果找不到教师信息，创建一个基础的TaskTeacherVO
                        return TaskTeacherVO.of(teacherId, "未知教师", null, teacherId,null);
                    }
                })
                .collect(Collectors.toList());
    }

    @Override
    public List<Long> getTaskIdsByTeacherId(Long teacherId) {
        LambdaQueryWrapper<TaskWorklistTeachers> wrapper = Wrappers.lambdaQuery(TaskWorklistTeachers.class);
        wrapper.eq(TaskWorklistTeachers::getTeacherId, teacherId);

        return this.list(wrapper).stream()
                .map(TaskWorklistTeachers::getTaskId)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveTaskTeacherRelations(Long taskId, List<TaskWorkDTO.TaskTeacherDTO> teachers, Long creator) {
        if (taskId == null || CollectionUtils.isEmpty(teachers)) {
            log.warn("任务ID或教师列表为空，跳过保存教师关联关系");
            return true;
        }

        log.debug("开始批量保存任务ID={} 的教师关联关系，教师数量: {}", taskId, teachers.size());

        // 批量创建新的关联关系
        List<TaskWorklistTeachers> relations = teachers.stream()
                .map(teacher -> {
                    TaskWorklistTeachers relation = new TaskWorklistTeachers();
                    relation.setTaskId(taskId);
                    relation.setTeacherId(teacher.getTeacherId()); // Record类型使用方法名访问字段
                    // 注意：TaskTeacherVO中不包含role信息，这里暂时设置为默认值1（主讲教师）
                    relation.setRole(1);
                    relation.setCreator(creator);
                    relation.setCreateTime(LocalDateTime.now());
                    return relation;
                })
                .collect(Collectors.toList());

        boolean result = this.saveBatch(relations);
        if (result) {
            log.debug("成功批量保存任务ID={} 的教师关联关系", taskId);
        } else {
            log.error("批量保存任务ID={} 的教师关联关系失败", taskId);
        }

        return result;
    }

    @Override
    public boolean removeByTaskId(Long taskId) {
        LambdaQueryWrapper<TaskWorklistTeachers> wrapper = Wrappers.lambdaQuery(TaskWorklistTeachers.class);
        wrapper.eq(TaskWorklistTeachers::getTaskId, taskId);
        return this.remove(wrapper);
    }

    @Override
    public Map<Long, List<TaskWorklistTeachers>> getTeachersMap(List<Long> taskIds) {
        if (CollectionUtils.isEmpty(taskIds)) {
            return Map.of();
        }
        List<TaskWorklistTeachers> list = list(Wrappers.<TaskWorklistTeachers>lambdaQuery()
                .in(TaskWorklistTeachers::getTaskId, taskIds)
        );
        return list.stream().collect(Collectors.groupingBy(TaskWorklistTeachers::getTaskId));
    }
}