package com.hnumi.obe.task.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hnumi.obe.task.entity.TaskWork;
import com.hnumi.obe.task.entity.TaskWorklistClasses;
import com.hnumi.obe.task.mapper.TaskWorkMapper;
import com.hnumi.obe.task.mapper.TaskWorklistClassesMapper;
import com.hnumi.obe.task.service.ITaskWorklistClassesService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 教学任务-班级关联表 服务实现类
 */
@Slf4j
@Service
public class TaskWorklistClassesServiceImpl extends ServiceImpl<TaskWorklistClassesMapper, TaskWorklistClasses> implements ITaskWorklistClassesService {

    @Autowired
    private TaskWorkMapper taskWorkMapper;

    @Override
    public List<Long> getClassIdsByTaskId(Long taskId) {
        LambdaQueryWrapper<TaskWorklistClasses> wrapper = Wrappers.lambdaQuery(TaskWorklistClasses.class);
        wrapper.eq(TaskWorklistClasses::getTaskId, taskId);

        return this.list(wrapper).stream()
                .map(TaskWorklistClasses::getClassId)
                .collect(Collectors.toList());
    }

    @Override
    public List<Long> getTaskIdsByClassId(Long classId) {
        LambdaQueryWrapper<TaskWorklistClasses> wrapper = Wrappers.lambdaQuery(TaskWorklistClasses.class);
        wrapper.eq(TaskWorklistClasses::getClassId, classId);

        return this.list(wrapper).stream()
                .map(TaskWorklistClasses::getTaskId)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveTaskClassRelations(Long taskId, List<Long> classIds, Long creator) {
        if (taskId == null || CollectionUtils.isEmpty(classIds)) {
            log.warn("任务ID或班级ID列表为空，跳过保存班级关联关系");
            return true;
        }

        log.debug("开始批量保存任务ID={} 的班级关联关系，班级数量: {}", taskId, classIds.size());

        // 批量创建新的关联关系
        List<TaskWorklistClasses> relations = classIds.stream()
                .map(classId -> {
                    TaskWorklistClasses relation = new TaskWorklistClasses();
                    relation.setTaskId(taskId);
                    relation.setClassId(classId);
                    relation.setCreator(creator);
                    relation.setCreateTime(LocalDateTime.now());
                    return relation;
                })
                .collect(Collectors.toList());

        boolean result = this.saveBatch(relations);
        if (result) {
            log.debug("成功批量保存任务ID={} 的班级关联关系", taskId);
        } else {
            log.error("批量保存任务ID={} 的班级关联关系失败", taskId);
        }

        return result;
    }

    @Override
    public boolean removeByTaskId(Long taskId) {
        LambdaQueryWrapper<TaskWorklistClasses> wrapper = Wrappers.lambdaQuery(TaskWorklistClasses.class);
        wrapper.eq(TaskWorklistClasses::getTaskId, taskId);
        return this.remove(wrapper);
    }

    @Override
    public long countByTaskId(Long taskId) {
        LambdaQueryWrapper<TaskWorklistClasses> wrapper = Wrappers.lambdaQuery(TaskWorklistClasses.class);
        wrapper.eq(TaskWorklistClasses::getTaskId, taskId);
        return this.count(wrapper);
    }

    @Override
    public Map<Long, List<TaskWorklistClasses>> getClassesMap(List<Long> taskIds) {
        if (CollectionUtils.isEmpty(taskIds)) {
            return Map.of();
        }
        List<TaskWorklistClasses> list = list(Wrappers.<TaskWorklistClasses>lambdaQuery().in(TaskWorklistClasses::getTaskId, taskIds));
        return list.stream().collect(Collectors.groupingBy(TaskWorklistClasses::getTaskId));
    }

    @Override
    public List<TaskWorklistClasses> getByTaskId(Long taskId) {
        if (taskId == null) {
            return List.of();
        }
        LambdaQueryWrapper<TaskWorklistClasses> wrapper = Wrappers.lambdaQuery(TaskWorklistClasses.class);
        wrapper.eq(TaskWorklistClasses::getTaskId, taskId);
        return this.list(wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int cleanupDuplicateClassRelations(Long courseId, List<Long> classIds) {
        if (courseId == null || CollectionUtils.isEmpty(classIds)) {
            log.warn("课程ID或班级ID列表为空，跳过清理重复关联关系");
            return 0;
        }

        log.info("开始清理课程 {} 与班级 {} 的重复关联关系", courseId, classIds);

        // 1. 查询该课程下所有的教学任务ID
        LambdaQueryWrapper<TaskWork> taskWrapper = Wrappers.lambdaQuery(TaskWork.class);
        taskWrapper.eq(TaskWork::getCourseId, courseId)
                  .eq(TaskWork::getStatus, 0); // 只查询有效的教学任务

        List<TaskWork> taskWorks = taskWorkMapper.selectList(taskWrapper);
        if (taskWorks.isEmpty()) {
            log.info("课程 {} 下没有找到有效的教学任务，无需清理", courseId);
            return 0;
        }

        List<Long> taskIds = taskWorks.stream()
                .map(TaskWork::getId)
                .collect(Collectors.toList());

        // 2. 查询这些教学任务与指定班级的现有关联关系
        LambdaQueryWrapper<TaskWorklistClasses> relationWrapper = Wrappers.lambdaQuery(TaskWorklistClasses.class);
        relationWrapper.in(TaskWorklistClasses::getTaskId, taskIds)
                      .in(TaskWorklistClasses::getClassId, classIds);

        List<TaskWorklistClasses> existingRelations = this.list(relationWrapper);
        if (existingRelations.isEmpty()) {
            log.info("课程 {} 与班级 {} 之间没有找到现有关联关系", courseId, classIds);
            return 0;
        }

        // 3. 批量删除现有关联关系
        List<Long> relationIds = existingRelations.stream()
                .map(TaskWorklistClasses::getId)
                .collect(Collectors.toList());

        boolean removed = this.removeByIds(relationIds);
        int deletedCount = removed ? relationIds.size() : 0;

        log.info("成功清理课程 {} 与班级 {} 的 {} 条重复关联关系，删除的关联ID: {}",
                courseId, classIds, deletedCount, relationIds);

        return deletedCount;
    }
}
