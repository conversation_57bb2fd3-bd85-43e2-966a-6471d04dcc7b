package com.hnumi.obe.task.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hnumi.obe.task.dto.TaskWorkDTO;
import com.hnumi.obe.task.entity.TaskWorklistTeachers;
import com.hnumi.obe.task.vo.TaskWorkVO;
import com.hnumi.obe.tp.vo.TaskTeacherVO;

import java.util.List;
import java.util.Map;

/**
 * 教学任务-教师关联表 服务类
 */
public interface ITaskWorklistTeachersService extends IService<TaskWorklistTeachers> {

    /**
     * 根据教学任务ID获取关联的教师信息列表
     *
     * @param taskId 教学任务ID
     * @return 教师信息列表
     */
    List<TaskTeacherVO> getTeachersByTaskId(Long taskId);

    /**
     * 根据教师ID获取关联的教学任务ID列表
     *
     * @param teacherId 教师ID
     * @return 教学任务ID列表
     */
    List<Long> getTaskIdsByTeacherId(Long teacherId);

    /**
     * 批量保存教学任务-教师关联关系
     *
     * @param taskId   教学任务ID
     * @param teachers 教师信息列表
     * @param creator  创建者ID
     * @return 保存结果
     */
    boolean saveTaskTeacherRelations(Long taskId, List<TaskWorkDTO.TaskTeacherDTO> teachers, Long creator);

    /**
     * 删除教学任务的所有教师关联关系
     *
     * @param taskId 教学任务ID
     * @return 删除的记录数量
     */
    boolean removeByTaskId(Long taskId);

    Map<Long, List<TaskWorklistTeachers>> getTeachersMap(List<Long> taskIds);

}
