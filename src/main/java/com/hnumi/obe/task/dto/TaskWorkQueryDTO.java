package com.hnumi.obe.task.dto;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hnumi.obe.task.entity.TaskWork;
import lombok.Data;

/**
 * 教学任务查询条件 DTO
 */
@Data
public class TaskWorkQueryDTO {

    /**
     * 分页参数
     */
    private Page<TaskWork> page = new Page<>(1, 10);

    /**
     * 课程id
     */
    private Long courseId;

    /**
     * 课程序号
     */
    private Integer taskNumber;

    /**
     * 教学任务名称
     */
    private String taskName;

    /**
     * 授课年份
     */
    private Integer taskYear;

    /**
     * 授课学期
     */
    private Integer taskTerm;

    /**
     * 课程负责人id
     */
    private Long courseLeaderId;

    /**
     * 班级ID（用于查询包含指定班级的教学任务）
     */
    private Long classId;

    /**
     * 教师ID（用于查询包含指定教师的教学任务）
     */
    private Long teacherId;

    /**
     * 记录状态
     */
    private Integer status;
}
