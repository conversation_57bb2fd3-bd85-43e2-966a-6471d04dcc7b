package com.hnumi.obe.task.dto;

import com.hnumi.obe.common.valid.ValidGroup;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import java.util.List;

/**
 * 教学任务信息表 DTO
 */
@Data
public class TaskWorkDTO {

    /**
     * 教学任务id（taskId）
     */
    @NotNull(groups = ValidGroup.Update.class, message = "教学任务ID不能为空")
    private Long id;

    /**
     * 课程id，查tp_course -> id
     */
    @NotNull(groups = {ValidGroup.Add.class, ValidGroup.Update.class}, message = "课程ID不能为空")
    private Long courseId;

    /**
     * 课程序号，同一课程的不同授课班级
     */
    @NotNull(groups = {ValidGroup.Add.class, ValidGroup.Update.class}, message = "课程序号不能为空")
    @Positive(message = "课程序号必须为正数")
    private Integer taskNumber;

    /**
     * 教学任务名称, 如: 数据库原理(计2301,02班)
     */
    @NotNull(groups = {ValidGroup.Add.class, ValidGroup.Update.class}, message = "教学任务名称不能为空")
    private String taskName;

    /**
     * 授课年份(2023)，冗余
     */
    @NotNull(groups = {ValidGroup.Add.class, ValidGroup.Update.class}, message = "授课年份不能为空")
    @Positive(message = "授课年份必须为正数")
    private Integer taskYear;

    /**
     * 授课学期(1~8)，班级可以查到入学年份，对应学期
     */
    @NotNull(groups = {ValidGroup.Add.class, ValidGroup.Update.class}, message = "授课学期不能为空")
    @Positive(message = "授课学期必须为正数")
    private Integer taskTerm;

    /**
     * 授课周数
     */
    @NotNull(groups = {ValidGroup.Add.class, ValidGroup.Update.class}, message = "授课周数不能为空")
    @Positive(message = "授课周数必须为正数")
    private Integer teachWeek;

    /**
     * 周学时
     */
    @NotNull(groups = {ValidGroup.Add.class, ValidGroup.Update.class}, message = "周学时不能为空")
    @Positive(message = "周学时必须为正数")
    private Integer weekHours;

    /**
     * 总学时
     */
    @NotNull(groups = {ValidGroup.Add.class, ValidGroup.Update.class}, message = "总学时不能为空")
    @Positive(message = "总学时必须为正数")
    private Integer totalHours;

    /**
     * 课程负责人id（临时负责人）
     */
    @NotNull(groups = {ValidGroup.Add.class, ValidGroup.Update.class}, message = "课程负责人ID不能为空")
    private Long courseLeaderId;

    /**
     * 计划id
     */
    @NotNull(groups = {ValidGroup.Add.class}, message = "计划ID不能为空")
    private Long planId;

    /**
     * 专业id
     */
    @NotNull(groups = {ValidGroup.Add.class}, message = "专业ID不能为空")
    private Long majorId;

    /**
     * 关联的班级ID列表
     */
    @NotEmpty(groups = {ValidGroup.Add.class, ValidGroup.Update.class}, message = "关联班级不能为空")
    private List<Long> classIds;

    /**
     * 关联的教师信息列表
     */
//    @NotEmpty(groups = {ValidGroup.Add.class, ValidGroup.Update.class}, message = "关联教师不能为空")
    private List<TaskTeacherDTO> teachers;

    /**
     * 记录状态{0:正常状态；-1:删除状态；}
     */
    private Integer status;

    /**
     * 教师信息DTO
     */
    @Data
    public static class TaskTeacherDTO {
        @NotNull(message = "教师ID不能为空")
        private Long teacherId;

        @NotBlank(message = "教师角色不能为空")
        private Integer role;
    }
}
