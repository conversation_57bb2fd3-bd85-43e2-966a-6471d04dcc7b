package com.hnumi.obe.task.controller;

import com.hnumi.obe.common.entity.R;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;

/**
 * 教学任务模块异常处理器
 */
@RestControllerAdvice(basePackages = "com.hnumi.obe.task.controller")
@Slf4j
public class TaskExceptionHandler {

    /**
     * 处理路径参数类型转换异常
     */
    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    public R<Object> handleMethodArgumentTypeMismatch(MethodArgumentTypeMismatchException e) {
        log.error("路径参数类型转换异常: {}", e.getMessage());

        String paramName = e.getName();
        Object invalidValue = e.getValue();

        return R.fail(400, String.format("参数 %s 的值 '%s' 无效，请检查参数格式", paramName, invalidValue));
    }

    /**
     * 处理数字格式异常
     */
    @ExceptionHandler(NumberFormatException.class)
    public R<Object> handleNumberFormatException(NumberFormatException e) {
        log.error("数字格式异常: {}", e.getMessage());
        return R.fail(400, "参数格式错误，请提供有效的数字");
    }
}
