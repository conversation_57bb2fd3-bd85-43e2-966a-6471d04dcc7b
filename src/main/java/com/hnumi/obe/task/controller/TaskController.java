package com.hnumi.obe.task.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hnumi.obe.common.entity.PageResponse;
import com.hnumi.obe.common.entity.R;
import com.hnumi.obe.common.util.RequestUtil;
import com.hnumi.obe.common.valid.ValidGroup;
import com.hnumi.obe.task.dto.TaskWorkDTO;
import com.hnumi.obe.task.dto.TaskWorkQueryDTO;
import com.hnumi.obe.task.service.ITaskWorkService;
import com.hnumi.obe.task.vo.CourseStaticsByTaskVO;
import com.hnumi.obe.task.vo.TaskWorkDetailVO;
import com.hnumi.obe.task.vo.TaskWorkVO;
import com.hnumi.obe.task.vo.TaskWorkStatisticsVO;
import com.hnumi.obe.tp.vo.CourseCurrentSemesterVO;
import com.hnumi.obe.tp.vo.TaskVO;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 教学任务控制器
 */
@RestController
@RequestMapping("/teaching/task")
@RequiredArgsConstructor
public class TaskController {

    private final ITaskWorkService taskWorkService;

    /**
     * 新增教学任务（包含关联的班级和教师）
     * @param dto 教学任务信息
     * @return 操作结果
     */
    @PostMapping("/add")
    public R<Boolean> addTaskWork(@Validated(ValidGroup.Add.class) @RequestBody TaskWorkDTO dto) {
        boolean result = taskWorkService.createTaskWork(dto);
        return result ? R.ok("创建教学任务成功") : R.fail(500,"创建教学任务失败");
    }

    /**
     * 更新教学任务（包含关联的班级和教师）
     * @param dto 教学任务信息
     * @return 操作结果
     */
    @PutMapping("/update")
    public R<Boolean> updateTaskWork(@Validated(ValidGroup.Update.class) @RequestBody TaskWorkDTO dto) {
        boolean result = taskWorkService.updateTaskWork(dto);
        return result ? R.ok( "更新教学任务成功") : R.fail(500,"更新教学任务失败");
    }

    /**
     * 删除教学任务（软删除，同时删除关联关系）
     * @param id 教学任务ID
     * @return 操作结果
     */
    @DeleteMapping("/delete/{id}")
    public R<Boolean> deleteTaskWork_old(@PathVariable Long id) {
        boolean result = taskWorkService.deleteById(id);
        return result ? R.ok("删除教学任务成功") : R.fail(500,"删除教学任务失败");
    }
    /**
     * 物理删除教学任务
     * 删除教学任务及其所有关联关系（班级、教师）
     *
     * @param taskId 教学任务ID
     * @return 删除结果
     */
    @DeleteMapping("/delete/{taskId}")
    public R<Boolean> deleteTaskWork(@PathVariable Long taskId) {
        boolean result = taskWorkService.deleteTaskWork(taskId);
        return R.ok(result);
    }

    /**
     * 根据ID获取教学任务详情（包含关联的班级和教师）
     * @param id 教学任务ID
     * @return 教学任务详情
     */
    @GetMapping("/detail/{id}")
    public R<TaskWorkVO> getTaskWorkDetail(@PathVariable Long id) {
        TaskWorkVO taskWork = taskWorkService.getTaskWorkDetailById(id);
        return taskWork != null ? R.ok(taskWork) : R.fail(500,"教学任务不存在");
    }

    /**
     * 分页查询教学任务列表（包含关联信息）
     * @param courseId 课程ID
     * @param queryDTO 查询条件（可选）
     * @return 分页结果
     */
    @PostMapping("/page/{courseId}")
    public R<Page<TaskWorkStatisticsVO>> getCourseTaskWorkPage(@PathVariable Long courseId, @RequestBody(required = false) TaskWorkQueryDTO queryDTO) {
        // 参数验证
        if (courseId == null || courseId <= 0) {
            return R.fail(400, "课程ID参数无效");
        }

        // 如果没有提供查询条件，使用默认值
        if (queryDTO == null) {
            queryDTO = new TaskWorkQueryDTO();
        }

        try {
            Page<TaskWorkStatisticsVO> statistics = taskWorkService.getCourseTaskWorkPage(courseId, queryDTO);
            return R.ok(statistics);
        } catch (Exception e) {
            return R.fail(500, "获取教学任务分页数据失败: " + e.getMessage());
        }
    }

    /**
     * 根据教师ID获取教学任务列表
     * @param teacherId 教师ID
     * @return 教学任务列表
     */
    @GetMapping("/teacher/{teacherId}")
    public R<List<TaskWorkVO>> getTasksByTeacherId(@PathVariable Long teacherId) {
        List<TaskWorkVO> tasks = taskWorkService.getTasksByTeacherId(teacherId);
        return R.ok(tasks);
    }

    /**
     * 根据班级ID获取教学任务列表
     * @param classId 班级ID
     * @return 教学任务列表
     */
    @GetMapping("/class/{classId}")
    public R<List<TaskWorkVO>> getTasksByClassId(@PathVariable Long classId) {
        List<TaskWorkVO> tasks = taskWorkService.getTasksByClassId(classId);
        return R.ok(tasks);
    }

    /**
     * 根据课程ID获取教学任务列表
     * @param courseId 课程ID
     * @return 教学任务列表
     */
    @PostMapping("/course/{courseId}")
    public R<List<TaskWorkDetailVO>> getTasksByCourseId(@PathVariable Long courseId) {
        // 参数验证
        if (courseId == null || courseId <= 0) {
            return R.fail(400, "课程ID参数无效");
        }

        // 如果没有提供查询条件，返回错误信息
//        if (queryDTO == null) {
//            return R.fail(400, "查询条件不能为空，请提供学年和学期信息");
//        }

        // 验证必需的查询参数
        List<TaskWorkDetailVO> taskDetails = List.of();

        try {

            taskDetails = taskWorkService.getCourseTaskWorkBySemester(courseId);
            return R.ok(taskDetails);

        } catch (Exception e) {
            return R.fail(500, "获取教学任务数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取某学年学期的制定课程的教学任务列表
     * @param courseId 课程ID
     * @param queryDTO 查询条件（可选）
     * @return 教学任务列表
     */
    @PostMapping("/academicYear/{courseId}")
    public R<List<TaskWorkDetailVO>> getTasksByacademicYear(@PathVariable Long courseId, @RequestBody(required = false) TaskWorkQueryDTO queryDTO) {
        // 参数验证
        if (courseId == null || courseId <= 0) {
            return R.fail(400, "课程ID参数无效");
        }
        if (queryDTO == null) {
            return R.fail(400, "查询条件不能为空，请提供学年和学期信息");
        }

        // 验证必需的查询参数
        if (queryDTO.getTaskYear() == null || queryDTO.getTaskTerm() == null) {
            return R.fail(400, "学年和学期参数不能为空");
        }

        try {
            List<TaskWorkDetailVO> taskDetails = taskWorkService.getCourseTaskWorkBySemester(courseId, queryDTO);
            return R.ok(taskDetails);
        } catch (Exception e) {
            return R.fail(500, "获取教学任务数据失败: " + e.getMessage());
        }
    }

    /**
     * 计算教学任务的学生总数
     * @param taskId 教学任务ID
     * @return 学生总数
     */
    @GetMapping("/student-count/{taskId}")
    public R<Integer> getStudentCount(@PathVariable Long taskId) {
        Integer count = taskWorkService.calculateStudentCount(taskId);
        return R.ok(count);
    }

    /**
     * 根据课程ID获取按学期分组的教学任务统计信息
     * @param courseId 课程ID
     * @return 按学期分组的统计信息列表
     */
    @GetMapping("/statistics/course/{courseId}")
    public R<List<TaskWorkStatisticsVO>> getTaskWorkStatistics(@PathVariable Long courseId) {
        // 参数验证
        if (courseId == null || courseId <= 0) {
            return R.fail(400, "课程ID参数无效");
        }

        try {
            List<TaskWorkStatisticsVO> statistics = taskWorkService.getTaskWorkStatisticsByCourseId(courseId);
            return R.ok(statistics);
        } catch (Exception e) {
            return R.fail(500, "获取教学任务统计数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取教师学期课程统计（教师任务列表统计大屏，这里显示的主要是任务，是带有课程信息的任务）
     * @param termType 学期类型（0: 春季, 1: 秋季），可为空，默认根据当前月份推断
     * @return 当前学期课程统计列表
     */
    @GetMapping("/teacher/semester/static/{majorId}/{termType}")
    public R<PageResponse<TaskVO>> getTeacherSemesterCourses(
            @PathVariable(required = false) Long majorId,
            @PathVariable(required = false) Integer termType) {

        Long teacherId = RequestUtil.getExtendId();
        // 如果termType为空，根据当前月份推断
        if (termType == null) {
            // 2-8月为春季(0)，9-1月为秋季(1)
            int currentMonth = java.time.LocalDate.now().getMonthValue();
            termType = (currentMonth >= 2 && currentMonth <= 8) ? 0 : 1;
        }

        PageResponse<TaskVO> courses = null;//taskWorkService.generateTaskWorkList(planId, termType);
        return R.ok(courses);
    }

    /**
     * 生成和管理教学任务列表
     * 根据培养方案信息生成指定学期的教学任务数据
     *
     * @param planId 培养方案ID
     * @param termType 学期类型
     * @return 完整的教学任务列表数据
     */
    @GetMapping("/generate-task-list/{planId}/{termType}")
    public R<PageResponse<TaskVO>> generateTaskWorkList(
            @PathVariable(required = false) Long planId,
            @PathVariable(required = false) Integer termType) {

        PageResponse<TaskVO> taskList = taskWorkService.generateTaskWorkList(planId, termType);
        return R.ok(taskList);
    }


}
