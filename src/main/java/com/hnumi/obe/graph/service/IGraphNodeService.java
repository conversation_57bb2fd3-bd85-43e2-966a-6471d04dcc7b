package com.hnumi.obe.graph.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hnumi.obe.graph.dto.GraphNodeDTO;
import com.hnumi.obe.graph.entity.GraphNode;
import com.hnumi.obe.graph.vo.GraphNodeVO;

import java.util.List;

/**
 * 知识图谱节点服务接口
 *
 */
public interface IGraphNodeService extends IService<GraphNode> {

    /**
     * 添加节点
     *
     * @param nodeDTO 节点数据传输对象
     * @return 是否添加成功
     */
    boolean addNode(GraphNodeDTO nodeDTO);

    /**
     * 更新节点
     *
     * @param nodeId 节点ID
     * @param nodeDTO 节点数据传输对象
     * @return 是否更新成功
     */
    boolean updateNode(String nodeId, GraphNodeDTO nodeDTO);

    /**
     * 删除节点
     *
     * @param nodeId 节点ID
     * @return 是否删除成功
     */
    boolean deleteNode(String nodeId);

    /**
     * 根据ID获取节点
     *
     * @param nodeId 节点ID
     * @return 节点视图对象
     */
    GraphNodeVO getNodeById(String nodeId);

    /**
     * 获取所有有效节点
     *
     * @return 节点列表
     */
    List<GraphNodeVO> getAllActiveNodes();

    /**
     * 根据节点类型获取节点
     *
     * @param nodeType 节点类型
     * @return 节点列表
     */
    List<GraphNodeVO> getNodesByType(String nodeType);

    /**
     * 根据父节点ID获取子节点
     *
     * @param parentId 父节点ID
     * @return 子节点列表
     */
    List<GraphNodeVO> getNodesByParentId(String parentId);
} 