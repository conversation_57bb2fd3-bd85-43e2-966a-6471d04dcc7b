package com.hnumi.obe.graph.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hnumi.obe.graph.dto.GraphLinkDTO;
import com.hnumi.obe.graph.entity.GraphLink;
import com.hnumi.obe.graph.vo.GraphLinkVO;

import java.util.List;

/**
 * 知识图谱连接线服务接口
 *
 */
public interface IGraphLinkService extends IService<GraphLink> {

    /**
     * 添加连接线
     *
     * @param linkDTO 连接线数据传输对象
     * @return 是否添加成功
     */
    boolean addLink(GraphLinkDTO linkDTO);

    /**
     * 更新连接线
     *
     * @param linkId 连接线ID
     * @param linkDTO 连接线数据传输对象
     * @return 是否更新成功
     */
    boolean updateLink(String linkId, GraphLinkDTO linkDTO);

    /**
     * 删除连接线
     *
     * @param linkId 连接线ID
     * @return 是否删除成功
     */
    boolean deleteLink(String linkId);

    /**
     * 根据ID获取连接线
     *
     * @param linkId 连接线ID
     * @return 连接线视图对象
     */
    GraphLinkVO getLinkById(String linkId);

    /**
     * 获取所有有效连接线
     *
     * @return 连接线列表
     */
    List<GraphLinkVO> getAllActiveLinks();

    /**
     * 根据源节点ID获取连接线
     *
     * @param sourceId 源节点ID
     * @return 连接线列表
     */
    List<GraphLinkVO> getLinksBySourceId(String sourceId);

    /**
     * 根据目标节点ID获取连接线
     *
     * @param targetId 目标节点ID
     * @return 连接线列表
     */
    List<GraphLinkVO> getLinksByTargetId(String targetId);
} 