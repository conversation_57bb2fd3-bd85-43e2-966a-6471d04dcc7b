package com.hnumi.obe.graph.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hnumi.obe.common.util.RequestUtil;
import com.hnumi.obe.graph.dto.GraphNodeDTO;
import com.hnumi.obe.graph.entity.GraphNode;
import com.hnumi.obe.graph.entity.GraphLink;
import com.hnumi.obe.graph.mapper.GraphNodeMapper;
import com.hnumi.obe.graph.service.IGraphNodeService;
import com.hnumi.obe.graph.service.IGraphLinkService;
import com.hnumi.obe.graph.vo.GraphNodeVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 知识图谱节点服务实现类
 *
 */
@Service
public class GraphNodeServiceImpl extends ServiceImpl<GraphNodeMapper, GraphNode> implements IGraphNodeService {

    @Autowired
    private GraphNodeMapper graphNodeMapper;

    @Autowired
    @Lazy
    private IGraphLinkService graphLinkService;

    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    // 缓存字符串ID到数据库ID的映射
    private Map<String, Integer> stringIdToDbIdMap = new HashMap<>();
    private Map<Integer, String> dbIdToStringIdMap = new HashMap<>();

    @Override
    public boolean addNode(GraphNodeDTO nodeDTO) {
        GraphNode node = new GraphNode();
        node.setNodeName(nodeDTO.getName());
        node.setNodeDescription(nodeDTO.getDescription());
        node.setNodeType(nodeDTO.getNodeType());
        
        Integer parentDbId = null;
        // 处理父节点ID
        if (nodeDTO.getParentId() != null && !nodeDTO.getParentId().isEmpty()) {
            parentDbId = getDbIdByStringIdInternal(nodeDTO.getParentId());
            if (parentDbId != null) {
                node.setParentId(parentDbId);
            }
        }
        
        node.setStatus(0);
        node.setCreator(RequestUtil.getUserId());
        node.setCreateTime(LocalDateTime.now());
        node.setModifier(RequestUtil.getUserId());
        node.setModifyTime(LocalDateTime.now());
        
        boolean result = save(node);
        
        if (result) {
            // 重新初始化ID映射缓存，包含新创建的节点
            initializeIdMapping();
            
            // 获取新节点的字符串ID
            String newNodeStringId = dbIdToStringIdMap.get(node.getNodeId());
            
            System.out.println("节点创建成功 - 数据库ID: " + node.getNodeId() + ", 字符串ID: " + newNodeStringId + ", 父节点数据库ID: " + parentDbId);
            
            // 如果节点创建成功且有父节点，自动创建连接线
            if (parentDbId != null) {
                try {
                    GraphLink link = new GraphLink();
                    link.setLinkSource(parentDbId);
                    link.setLinkTarget(node.getNodeId());
                    link.setLinkDescription(getConnectionDescription(nodeDTO.getNodeType()));
                    link.setStatus(0);
                    link.setCreator(RequestUtil.getUserId());
                    link.setCreateTime(LocalDateTime.now());
                    link.setModifier(RequestUtil.getUserId());
                    link.setModifyTime(LocalDateTime.now());
                    
                    // 直接使用GraphLinkService的save方法
                    boolean linkResult = graphLinkService.save(link);
                    if (linkResult) {
                        System.out.println("连接线创建成功 - 连接线ID: " + link.getLinkId() + ", source: " + parentDbId + ", target: " + node.getNodeId());
                    } else {
                        System.err.println("警告：节点创建成功，但连接线创建失败。节点ID: " + node.getNodeId() + ", 父节点ID: " + parentDbId);
                    }
                } catch (Exception e) {
                    System.err.println("警告：自动创建连接线时发生异常: " + e.getMessage());
                    e.printStackTrace();
                }
            }
        }
        
        return result;
    }

    /**
     * 根据节点类型获取连接线描述
     */
    private String getConnectionDescription(String nodeType) {
        switch (nodeType) {
            case "indicator":
                return "一级指标连接";
            case "secondaryIndicator":
                return "二级指标连接";
            case "thirdIndicator":
                return "三级指标连接";
            case "fourthIndicator":
                return "四级指标连接";
            default:
                return "节点连接";
        }
    }

    @Override
    public boolean updateNode(String nodeId, GraphNodeDTO nodeDTO) {
        Integer dbId = getDbIdByStringIdInternal(nodeId);
        if (dbId == null) {
            return false;
        }
        
        LambdaUpdateWrapper<GraphNode> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(GraphNode::getNodeId, dbId)
                     .set(GraphNode::getNodeName, nodeDTO.getName())
                     .set(GraphNode::getNodeDescription, nodeDTO.getDescription())
                     .set(GraphNode::getNodeType, nodeDTO.getNodeType())
                     .set(GraphNode::getModifier, RequestUtil.getUserId())
                     .set(GraphNode::getModifyTime, LocalDateTime.now());
        
        // 处理父节点ID
        if (nodeDTO.getParentId() != null && !nodeDTO.getParentId().isEmpty()) {
            Integer parentDbId = getDbIdByStringIdInternal(nodeDTO.getParentId());
            if (parentDbId != null) {
                updateWrapper.set(GraphNode::getParentId, parentDbId);
            }
        } else {
            updateWrapper.set(GraphNode::getParentId, null);
        }
        
        return update(updateWrapper);
    }

    @Override
    public boolean deleteNode(String nodeId) {
        Integer dbId = getDbIdByStringIdInternal(nodeId);
        if (dbId == null) {
            return false;
        }
        
        LambdaUpdateWrapper<GraphNode> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(GraphNode::getNodeId, dbId)
                     .set(GraphNode::getStatus, -1)
                     .set(GraphNode::getModifier, RequestUtil.getUserId())
                     .set(GraphNode::getModifyTime, LocalDateTime.now());
        
        return update(updateWrapper);
    }

    @Override
    public GraphNodeVO getNodeById(String nodeId) {
        Integer dbId = getDbIdByStringIdInternal(nodeId);
        if (dbId == null) {
            return null;
        }
        
        LambdaQueryWrapper<GraphNode> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(GraphNode::getStatus, 0)
                   .eq(GraphNode::getNodeId, dbId);
        GraphNode node = getOne(queryWrapper);
        
        if (node == null) {
            return null;
        }
        return convertToVO(node, nodeId);
    }

    @Override
    public List<GraphNodeVO> getAllActiveNodes() {
        LambdaQueryWrapper<GraphNode> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(GraphNode::getStatus, 0);
        List<GraphNode> nodes = list(queryWrapper);
        return nodes.stream().map(node -> convertToVO(node, null)).collect(Collectors.toList());
    }

    @Override
    public List<GraphNodeVO> getNodesByType(String nodeType) {
        LambdaQueryWrapper<GraphNode> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(GraphNode::getStatus, 0)
                   .eq(GraphNode::getNodeType, nodeType);
        List<GraphNode> nodes = list(queryWrapper);
        return nodes.stream().map(node -> convertToVO(node, null)).collect(Collectors.toList());
    }

    @Override
    public List<GraphNodeVO> getNodesByParentId(String parentId) {
        Integer parentDbId = getDbIdByStringIdInternal(parentId);
        if (parentDbId == null) {
            return List.of();
        }
        
        LambdaQueryWrapper<GraphNode> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(GraphNode::getStatus, 0)
                   .eq(GraphNode::getParentId, parentDbId);
        List<GraphNode> nodes = list(queryWrapper);
        return nodes.stream().map(node -> convertToVO(node, null)).collect(Collectors.toList());
    }

    /**
     * 根据字符串ID获取数据库ID（供其他服务调用）
     */
    public Integer getDbIdByStringId(String stringId) {
        return getDbIdByStringIdInternal(stringId);
    }

    /**
     * 根据数据库ID获取字符串ID（供其他服务调用）
     */
    public String getStringIdByDbId(Integer dbId) {
        if (dbId == null) {
            return null;
        }
        
        // 确保ID映射已初始化
        if (dbIdToStringIdMap.isEmpty()) {
            initializeIdMapping();
        }
        
        return dbIdToStringIdMap.get(dbId);
    }

    /**
     * 根据字符串ID获取数据库ID
     */
    private Integer getDbIdByStringIdInternal(String stringId) {
        if (stringId == null || stringId.isEmpty()) {
            return null;
        }
        
        // 先从缓存中查找
        Integer cachedDbId = stringIdToDbIdMap.get(stringId);
        if (cachedDbId != null) {
            return cachedDbId;
        }
        
        // 初始化ID映射缓存
        if (stringIdToDbIdMap.isEmpty()) {
            initializeIdMapping();
        }
        
        return stringIdToDbIdMap.get(stringId);
    }

    /**
     * 初始化ID映射缓存
     */
    private void initializeIdMapping() {
        LambdaQueryWrapper<GraphNode> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(GraphNode::getStatus, 0);
        List<GraphNode> allNodes = list(queryWrapper);
        
        // 清空缓存
        stringIdToDbIdMap.clear();
        dbIdToStringIdMap.clear();
        
        for (GraphNode node : allNodes) {
            String stringId = generateStringId(node, allNodes);
            stringIdToDbIdMap.put(stringId, node.getNodeId());
            dbIdToStringIdMap.put(node.getNodeId(), stringId);
        }
    }

    /**
     * 根据节点属性生成字符串ID
     */
    private String generateStringId(GraphNode node, List<GraphNode> allNodes) {
        // 根据节点类型和层级关系生成字符串ID
        if ("center".equals(node.getNodeType())) {
            return "center";
        } else if ("indicator".equals(node.getNodeType())) {
            // 一级指标，使用数字ID (2, 3, 4...)，对应数据库中的实际ID
            return String.valueOf(node.getNodeId());
        } else if ("secondaryIndicator".equals(node.getNodeType())) {
            // 二级指标，使用父级.序号的格式 (2.1, 2.2, 3.1...)
            if (node.getParentId() != null) {
                // 查找同一父节点下的所有二级指标
                long childIndex = allNodes.stream()
                    .filter(n -> "secondaryIndicator".equals(n.getNodeType()))
                    .filter(n -> node.getParentId().equals(n.getParentId()))
                    .filter(n -> n.getNodeId() <= node.getNodeId())
                    .count();
                return node.getParentId() + "." + childIndex;
            }
            return String.valueOf(node.getNodeId());
        } else if ("thirdIndicator".equals(node.getNodeType())) {
            // 三级指标，使用父级.序号的格式 (2.1.1, 2.1.2, 3.1.1...)
            if (node.getParentId() != null) {
                // 找到父节点的字符串ID
                String parentStringId = allNodes.stream()
                    .filter(n -> n.getNodeId().equals(node.getParentId()))
                    .map(n -> generateStringId(n, allNodes))
                    .findFirst()
                    .orElse(String.valueOf(node.getParentId()));
                
                // 查找同一父节点下的所有三级指标
                long childIndex = allNodes.stream()
                    .filter(n -> "thirdIndicator".equals(n.getNodeType()))
                    .filter(n -> node.getParentId().equals(n.getParentId()))
                    .filter(n -> n.getNodeId() <= node.getNodeId())
                    .count();
                return parentStringId + "." + childIndex;
            }
            return String.valueOf(node.getNodeId());
        } else if ("fourthIndicator".equals(node.getNodeType())) {
            // 四级指标，使用父级.序号的格式 (2.1.1.1, 2.1.1.2...)
            if (node.getParentId() != null) {
                // 找到父节点的字符串ID
                String parentStringId = allNodes.stream()
                    .filter(n -> n.getNodeId().equals(node.getParentId()))
                    .map(n -> generateStringId(n, allNodes))
                    .findFirst()
                    .orElse(String.valueOf(node.getParentId()));
                
                // 查找同一父节点下的所有四级指标
                long childIndex = allNodes.stream()
                    .filter(n -> "fourthIndicator".equals(n.getNodeType()))
                    .filter(n -> node.getParentId().equals(n.getParentId()))
                    .filter(n -> n.getNodeId() <= node.getNodeId())
                    .count();
                return parentStringId + "." + childIndex;
            }
            return String.valueOf(node.getNodeId());
        }
        return String.valueOf(node.getNodeId());
    }

    /**
     * 将实体转换为VO
     */
    private GraphNodeVO convertToVO(GraphNode node, String providedStringId) {
        GraphNodeVO vo = new GraphNodeVO();
        
        // 确保ID映射已初始化
        if (stringIdToDbIdMap.isEmpty()) {
            initializeIdMapping();
        }
        
        // 使用提供的字符串ID或从缓存中获取
        String stringId = providedStringId != null ? providedStringId : 
                         dbIdToStringIdMap.get(node.getNodeId());
        
        vo.setId(stringId);
        vo.setName(node.getNodeName());
        vo.setDescription(node.getNodeDescription());
        vo.setNodeType(node.getNodeType());
        
        if (node.getParentId() != null) {
            String parentStringId = dbIdToStringIdMap.get(node.getParentId());
            vo.setParentId(parentStringId);
        }
        
        if (node.getCreateTime() != null) {
            vo.setCreateTime(node.getCreateTime().format(FORMATTER));
        }
        if (node.getModifyTime() != null) {
            vo.setUpdateTime(node.getModifyTime().format(FORMATTER));
        }
        
        vo.setCreator(node.getCreator() != null ? String.valueOf(node.getCreator()) : "admin");
        vo.setUpdater(node.getModifier() != null ? String.valueOf(node.getModifier()) : "admin");
        
        return vo;
    }
} 