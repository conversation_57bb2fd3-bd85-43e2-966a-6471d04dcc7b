package com.hnumi.obe.graph.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hnumi.obe.common.util.RequestUtil;
import com.hnumi.obe.graph.dto.GraphLinkDTO;
import com.hnumi.obe.graph.entity.GraphLink;
import com.hnumi.obe.graph.entity.GraphNode;
import com.hnumi.obe.graph.mapper.GraphLinkMapper;
import com.hnumi.obe.graph.service.IGraphLinkService;
import com.hnumi.obe.graph.service.IGraphNodeService;
import com.hnumi.obe.graph.vo.GraphLinkVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 知识图谱连接线服务实现类
 *
 */
@Service
public class GraphLinkServiceImpl extends ServiceImpl<GraphLinkMapper, GraphLink> implements IGraphLinkService {

    @Autowired
    private GraphLinkMapper graphLinkMapper;

    @Autowired
    private IGraphNodeService graphNodeService;

    @Override
    public boolean addLink(GraphLinkDTO linkDTO) {
        // 获取源节点和目标节点的数据库ID
        Integer sourceNodeId = getNodeIdByStringId(linkDTO.getSource());
        Integer targetNodeId = getNodeIdByStringId(linkDTO.getTarget());
        
        if (sourceNodeId == null || targetNodeId == null) {
            System.err.println("创建连接线失败：节点ID转换失败。source: "
                    + linkDTO.getSource()
                    + " -> "
                    + sourceNodeId + ", target: "
                    + linkDTO.getTarget()
                    + " -> " + targetNodeId);
            return false;
        }
        
        // 验证节点是否真实存在且有效
        if (!isValidNode(sourceNodeId) || !isValidNode(targetNodeId)) {
            System.err.println("创建连接线失败：节点不存在或已被删除。source: "
                    + sourceNodeId
                    + ", target: "
                    + targetNodeId);
            return false;
        }
        
        GraphLink link = new GraphLink();
        link.setLinkSource(sourceNodeId);
        link.setLinkTarget(targetNodeId);
        link.setLinkDescription(linkDTO.getDescription());
        link.setStatus(0);
        link.setCreator(RequestUtil.getUserId());
        link.setCreateTime(LocalDateTime.now());
        link.setModifier(RequestUtil.getUserId());
        link.setModifyTime(LocalDateTime.now());
        
        return save(link);
    }

    @Override
    public boolean updateLink(String linkId, GraphLinkDTO linkDTO) {
        GraphLink existingLink = getLinkByStringId(linkId);
        if (existingLink == null) {
            return false;
        }
        
        Integer sourceNodeId = getNodeIdByStringId(linkDTO.getSource());
        Integer targetNodeId = getNodeIdByStringId(linkDTO.getTarget());
        
        if (sourceNodeId == null || targetNodeId == null) {
            return false;
        }
        
        LambdaUpdateWrapper<GraphLink> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(GraphLink::getLinkId, existingLink.getLinkId())
                     .set(GraphLink::getLinkSource, sourceNodeId)
                     .set(GraphLink::getLinkTarget, targetNodeId)
                     .set(GraphLink::getLinkDescription, linkDTO.getDescription())
                     .set(GraphLink::getModifier, RequestUtil.getUserId())
                     .set(GraphLink::getModifyTime, LocalDateTime.now());
        
        return update(updateWrapper);
    }

    @Override
    public boolean deleteLink(String linkId) {
        GraphLink existingLink = getLinkByStringId(linkId);
        if (existingLink == null) {
            return false;
        }
        
        LambdaUpdateWrapper<GraphLink> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(GraphLink::getLinkId, existingLink.getLinkId())
                     .set(GraphLink::getStatus, -1)
                     .set(GraphLink::getModifier, RequestUtil.getUserId())
                     .set(GraphLink::getModifyTime, LocalDateTime.now());
        
        return update(updateWrapper);
    }

    @Override
    public GraphLinkVO getLinkById(String linkId) {
        GraphLink link = getLinkByStringId(linkId);
        if (link == null) {
            return null;
        }
        return convertToVO(link);
    }

    @Override
    public List<GraphLinkVO> getAllActiveLinks() {
        LambdaQueryWrapper<GraphLink> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(GraphLink::getStatus, 0);
        List<GraphLink> links = list(queryWrapper);
        
        // 过滤并清理无效连接线
        List<GraphLink> validLinks = new ArrayList<>();
        List<Integer> invalidLinkIds = new ArrayList<>();
        
        for (GraphLink link : links) {
            String sourceStringId = getStringIdByDbId(link.getLinkSource());
            String targetStringId = getStringIdByDbId(link.getLinkTarget());
            
            if (sourceStringId == null || targetStringId == null) {
                // 记录无效连接线
                invalidLinkIds.add(link.getLinkId());
                System.err.println("发现无效连接线 ID: "
                        + link.getLinkId()
                        + ", source: "
                        + link.getLinkSource()
                        + " (映射: " + sourceStringId
                        + ")"
                        + ", target: "
                        + link.getLinkTarget()
                        + " (映射: "
                        + targetStringId
                        + ")");
            } else {
                validLinks.add(link);
            }
        }
        
        // 自动清理无效连接线
        if (!invalidLinkIds.isEmpty()) {
            cleanupInvalidLinks(invalidLinkIds);
        }
        
        return validLinks.stream()
                        .map(this::convertToVO)
                        .filter(vo -> vo != null) // 过滤掉null值
                        .collect(Collectors.toList());
    }

    /**
     * 清理无效连接线
     */
    private void cleanupInvalidLinks(List<Integer> invalidLinkIds) {
        try {
            System.out.println("开始清理 " + invalidLinkIds.size() + " 个无效连接线...");
            
            LambdaUpdateWrapper<GraphLink> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.in(GraphLink::getLinkId, invalidLinkIds)
                         .set(GraphLink::getStatus, -1)
                         .set(GraphLink::getModifier, RequestUtil.getUserId())
                         .set(GraphLink::getModifyTime, LocalDateTime.now());
            
            boolean result = update(updateWrapper);
            if (result) {
                System.out.println("成功清理无效连接线，数量: " + invalidLinkIds.size());
            } else {
                System.err.println("清理无效连接线失败");
            }
        } catch (Exception e) {
            System.err.println("清理无效连接线时发生异常: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 检查节点是否存在且有效
     */
    private boolean isValidNode(Integer nodeId) {
        if (nodeId == null) {
            return false;
        }
        
        // 通过字符串ID映射检查节点是否有效
        String stringId = getStringIdByDbId(nodeId);
        return stringId != null;
    }

    @Override
    public List<GraphLinkVO> getLinksBySourceId(String sourceId) {
        Integer sourceNodeId = getNodeIdByStringId(sourceId);
        if (sourceNodeId == null) {
            return List.of();
        }
        
        LambdaQueryWrapper<GraphLink> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(GraphLink::getStatus, 0)
                   .eq(GraphLink::getLinkSource, sourceNodeId);
        List<GraphLink> links = list(queryWrapper);
        return links.stream()
                   .map(this::convertToVO)
                   .filter(vo -> vo != null)
                   .collect(Collectors.toList());
    }

    @Override
    public List<GraphLinkVO> getLinksByTargetId(String targetId) {
        Integer targetNodeId = getNodeIdByStringId(targetId);
        if (targetNodeId == null) {
            return List.of();
        }
        
        LambdaQueryWrapper<GraphLink> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(GraphLink::getStatus, 0)
                   .eq(GraphLink::getLinkTarget, targetNodeId);
        List<GraphLink> links = list(queryWrapper);
        return links.stream()
                   .map(this::convertToVO)
                   .filter(vo -> vo != null)
                   .collect(Collectors.toList());
    }

    /**
     * 根据字符串ID获取连接线
     */
    private GraphLink getLinkByStringId(String linkId) {
        if (linkId == null || linkId.isEmpty()) {
            return null;
        }
        
        try {
            Integer id = Integer.parseInt(linkId);
            LambdaQueryWrapper<GraphLink> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(GraphLink::getStatus, 0)
                       .eq(GraphLink::getLinkId, id);
            return getOne(queryWrapper);
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * 根据字符串ID获取节点的数据库ID
     */
    private Integer getNodeIdByStringId(String nodeId) {
        if (nodeId == null || nodeId.isEmpty()) {
            return null;
        }
        
        // 通过节点服务获取数据库ID
        return ((GraphNodeServiceImpl) graphNodeService).getDbIdByStringId(nodeId);
    }

    /**
     * 根据数据库ID获取字符串ID
     */
    private String getStringIdByDbId(Integer dbId) {
        if (dbId == null) {
            return null;
        }
        
        // 通过节点服务获取字符串ID
        return ((GraphNodeServiceImpl) graphNodeService).getStringIdByDbId(dbId);
    }

    /**
     * 将实体转换为VO
     */
    private GraphLinkVO convertToVO(GraphLink link) {
        GraphLinkVO vo = new GraphLinkVO();
        vo.setId(String.valueOf(link.getLinkId()));
        
        // 将数据库ID转换为字符串ID
        String sourceStringId = getStringIdByDbId(link.getLinkSource());
        String targetStringId = getStringIdByDbId(link.getLinkTarget());
        
        // 如果转换失败，返回null，让调用方处理
        if (sourceStringId == null || targetStringId == null) {
            // 记录详细的调试信息
            System.err.println("连接线 "
                    + link.getLinkId()
                    + " ID转换失败: "
                    + "source "
                    + link.getLinkSource()
                    + " -> " + sourceStringId
                    + ", "
                    + "target "
                    + link.getLinkTarget()
                    + " -> "
                    + targetStringId);
            return null;
        }
        
        vo.setSource(sourceStringId);
        vo.setTarget(targetStringId);
        vo.setDescription(link.getLinkDescription());
        
        return vo;
    }
} 