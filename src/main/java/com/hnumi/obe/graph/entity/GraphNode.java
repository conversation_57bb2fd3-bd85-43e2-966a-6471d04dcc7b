package com.hnumi.obe.graph.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hnumi.obe.base.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 知识图谱节点实体类
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("graph_nodes")
public class GraphNode extends BaseEntity {

    /**
     * 节点ID
     */
    @TableId(value = "node_id", type = IdType.AUTO)
    private Integer nodeId;

    /**
     * 节点名称
     */
    @TableField("node_name")
    private String nodeName;

    /**
     * 节点描述
     */
    @TableField("node_description")
    private String nodeDescription;

    /**
     * 节点类型
     * center: 中心节点
     * indicator: 一级指标
     * secondaryIndicator: 二级指标
     */
    @TableField("node_type")
    private String nodeType;

    /**
     * 父节点ID
     */
    @TableField("parent_id")
    private Integer parentId;

    /**
     * 记录状态{0:正常状态；-1:删除状态；}
     */
    @TableField("status")
    private Integer status;
} 