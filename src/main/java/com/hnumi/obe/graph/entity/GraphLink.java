package com.hnumi.obe.graph.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hnumi.obe.base.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 知识图谱连接线实体类
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("graph_links")
public class GraphLink extends BaseEntity {

    /**
     * 连接线ID
     */
    @TableId(value = "link_id", type = IdType.AUTO)
    private Integer linkId;

    /**
     * 源节点ID
     */
    @TableField("link_source")
    private Integer linkSource;

    /**
     * 目标节点ID
     */
    @TableField("link_target")
    private Integer linkTarget;

    /**
     * 连接线描述
     */
    @TableField("link_description")
    private String linkDescription;

    /**
     * 记录状态{0:正常状态；-1:删除状态；}
     */
    @TableField("status")
    private Integer status;
} 