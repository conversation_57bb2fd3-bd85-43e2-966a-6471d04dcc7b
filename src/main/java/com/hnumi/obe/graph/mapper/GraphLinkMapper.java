package com.hnumi.obe.graph.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hnumi.obe.graph.entity.GraphLink;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 知识图谱连接线数据访问接口
 *
 */
@Mapper
public interface GraphLinkMapper extends BaseMapper<GraphLink> {

    /**
     * 根据源节点ID查询连接线列表
     *
     * @param sourceId 源节点ID
     * @return 连接线列表
     */
    List<GraphLink> selectBySourceId(@Param("sourceId") Integer sourceId);

    /**
     * 根据目标节点ID查询连接线列表
     *
     * @param targetId 目标节点ID
     * @return 连接线列表
     */
    List<GraphLink> selectByTargetId(@Param("targetId") Integer targetId);

    /**
     * 查询所有有效连接线（状态为0）
     *
     * @return 有效连接线列表
     */
    List<GraphLink> selectAllActive();
} 