package com.hnumi.obe.graph.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hnumi.obe.graph.entity.GraphNode;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 知识图谱节点数据访问接口
 *
 */
@Mapper
public interface GraphNodeMapper extends BaseMapper<GraphNode> {

    /**
     * 根据节点类型查询节点列表
     *
     * @param nodeType 节点类型
     * @return 节点列表
     */
    List<GraphNode> selectByNodeType(@Param("nodeType") String nodeType);

    /**
     * 根据父节点ID查询子节点列表
     *
     * @param parentId 父节点ID
     * @return 子节点列表
     */
    List<GraphNode> selectByParentId(@Param("parentId") Integer parentId);

    /**
     * 查询所有有效节点（状态为0）
     *
     * @return 有效节点列表
     */
    List<GraphNode> selectAllActive();
} 