package com.hnumi.obe.graph.controller;

import com.hnumi.obe.common.entity.R;
import com.hnumi.obe.common.entity.ResultCode;
import com.hnumi.obe.graph.dto.GraphLinkDTO;
import com.hnumi.obe.graph.dto.GraphNodeDTO;
import com.hnumi.obe.graph.service.IGraphLinkService;
import com.hnumi.obe.graph.service.IGraphNodeService;
import com.hnumi.obe.graph.vo.GraphLinkVO;
import com.hnumi.obe.graph.vo.GraphNodeVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 知识图谱控制器
 *
 */
@RestController
@RequestMapping("/academicStyle-graph")
public class GraphController {

    @Autowired
    private IGraphNodeService graphNodeService;

    @Autowired
    private IGraphLinkService graphLinkService;

    /**
     * 获取知识图谱完整数据
     *
     */
    @GetMapping("/data")
    public R<Map<String, Object>> getKnowledgeGraphData() {
        List<GraphNodeVO> nodes = graphNodeService.getAllActiveNodes();
        List<GraphLinkVO> links = graphLinkService.getAllActiveLinks();
        
        Map<String, Object> data = new HashMap<>();
        data.put("nodes", nodes);
        data.put("links", links);
        
        return R.ok("获取知识图谱数据成功", data);
    }

    /**
     * 添加节点
     *
     * @param nodeDTO 节点数据
     * @return 添加结果
     */
    @PostMapping("/addNode")
    public R<GraphNodeVO> addNode(@RequestBody GraphNodeDTO nodeDTO) {
        boolean success = graphNodeService.addNode(nodeDTO);
        if (success) {
            return R.ok("添加节点成功");
        } else {
            return R.fail(500, "添加节点失败");
        }
    }

    /**
     * 更新节点
     *
     * @param id 节点ID
     * @param nodeDTO 节点数据
     * @return 更新结果
     */
    @PutMapping("/updateNode/{id}")
    public R<GraphNodeVO> updateNode(@PathVariable("id") String id, @RequestBody GraphNodeDTO nodeDTO) {
        boolean success = graphNodeService.updateNode(id, nodeDTO);
        if (success) {
            return R.ok("更新节点成功");
        } else {
            return R.fail(500, "更新节点失败");
        }
    }

    /**
     * 删除节点
     *
     * @param id 节点ID
     * @return 删除结果
     */
    @DeleteMapping("/delNode/{id}")
    public R<Void> deleteNode(@PathVariable("id") String id) {
        boolean success = graphNodeService.deleteNode(id);
        if (success) {
            return R.ok("删除节点成功");
        } else {
            return R.fail(500, "删除节点失败");
        }
    }

    /**
     * 根据ID获取节点
     *
     * @param id 节点ID
     * @return 节点信息
     */
    @GetMapping("/node/{id}")
    public R<GraphNodeVO> getNodeById(@PathVariable("id") String id) {
        GraphNodeVO node = graphNodeService.getNodeById(id);
        if (node != null) {
            return R.ok("获取节点成功", node);
        } else {
            return R.fail(404, "节点不存在");
        }
    }

    /**
     * 添加连接线
     *
     * @param linkDTO 连接线数据
     * @return 添加结果
     */
    @PostMapping("/addLink")
    public R<GraphLinkVO> addLink(@RequestBody GraphLinkDTO linkDTO) {
        boolean success = graphLinkService.addLink(linkDTO);
        if (success) {
            return R.ok("添加连接线成功");
        } else {
            return R.fail(500, "添加连接线失败");
        }
    }

    /**
     * 更新连接线
     *
     * @param id 连接线ID
     * @param linkDTO 连接线数据
     * @return 更新结果
     */
    @PutMapping("/updateLink/{id}")
    public R<GraphLinkVO> updateLink(@PathVariable("id") String id, @RequestBody GraphLinkDTO linkDTO) {
        boolean success = graphLinkService.updateLink(id, linkDTO);
        if (success) {
            return R.ok("更新连接线成功");
        } else {
            return R.fail(500, "更新连接线失败");
        }
    }

    /**
     * 删除连接线
     *
     * @param id 连接线ID
     * @return 删除结果
     */
    @DeleteMapping("/delLink/{id}")
    public R<Void> deleteLink(@PathVariable("id") String id) {
        boolean success = graphLinkService.deleteLink(id);
        if (success) {
            return R.ok("删除连接线成功");
        } else {
            return R.fail(500, "删除连接线失败");
        }
    }

    /**
     * 根据ID获取连接线
     *
     * @param id 连接线ID
     * @return 连接线信息
     */
    @GetMapping("/link/{id}")
    public R<GraphLinkVO> getLinkById(@PathVariable("id") String id) {
        GraphLinkVO link = graphLinkService.getLinkById(id);
        if (link != null) {
            return R.ok("获取连接线成功", link);
        } else {
            return R.fail(404, "连接线不存在");
        }
    }

    /**
     * 根据节点类型获取节点
     *
     * @param nodeType 节点类型
     * @return 节点列表
     */
    @GetMapping("/nodes/type/{nodeType}")
    public R<List<GraphNodeVO>> getNodesByType(@PathVariable("nodeType") String nodeType) {
        List<GraphNodeVO> nodes = graphNodeService.getNodesByType(nodeType);
        return R.ok("获取节点列表成功", nodes);
    }

    /**
     * 根据父节点ID获取子节点
     *
     * @param parentId 父节点ID
     * @return 子节点列表
     */
    @GetMapping("/nodes/parent/{parentId}")
    public R<List<GraphNodeVO>> getNodesByParentId(@PathVariable("parentId") String parentId) {
        List<GraphNodeVO> nodes = graphNodeService.getNodesByParentId(parentId);
        return R.ok("获取子节点列表成功", nodes);
    }

    /**
     * 清理无效连接线
     *
     * @return 清理结果
     */
    @PostMapping("/cleanupInvalidLinks")
    public R<Map<String, Object>> cleanupInvalidLinks() {
        try {
            // 获取所有连接线，这会触发自动清理
            List<GraphLinkVO> links = graphLinkService.getAllActiveLinks();
            
            Map<String, Object> result = new HashMap<>();
            result.put("validLinksCount", links.size());
            result.put("message", "无效连接线清理完成");
            
            return R.ok("清理无效连接线成功", result);
        } catch (Exception e) {
            return R.fail(500, "清理无效连接线失败: " + e.getMessage());
        }
    }
} 