package com.hnumi.obe.graph.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 知识图谱连接线数据传输对象（DTO）
 *
 */
@Data
public class GraphLinkDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 连接线ID
     */
    private String id;

    /**
     * 源节点ID
     */
    private String source;

    /**
     * 目标节点ID
     */
    private String target;

    /**
     * 连接线描述
     */
    private String description;
} 