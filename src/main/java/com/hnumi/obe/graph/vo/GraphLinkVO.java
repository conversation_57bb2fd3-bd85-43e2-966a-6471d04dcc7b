package com.hnumi.obe.graph.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * 知识图谱连接线视图对象（VO）
 * 用于前端展示
 */
@Data
public class GraphLinkVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 连接线ID
     */
    private String id;

    /**
     * 源节点ID
     */
    private String source;

    /**
     * 目标节点ID
     */
    private String target;

    /**
     * 连接线描述
     */
    private String description;
} 