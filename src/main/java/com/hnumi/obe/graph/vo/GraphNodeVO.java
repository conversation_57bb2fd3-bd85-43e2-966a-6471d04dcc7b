package com.hnumi.obe.graph.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * 知识图谱节点视图对象（VO）
 *
 */
@Data
public class GraphNodeVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 节点ID
     */
    private String id;

    /**
     * 节点名称
     */
    private String name;

    /**
     * 节点描述
     */
    private String description;

    /**
     * 节点类型
     * center: 中心节点
     * indicator: 一级指标
     * secondaryIndicator: 二级指标
     */
    private String nodeType;

    /**
     * 父节点ID
     */
    private String parentId;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 更新时间
     */
    private String updateTime;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 更新者
     */
    private String updater;
} 