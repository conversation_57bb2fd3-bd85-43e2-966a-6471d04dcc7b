package com.hnumi.obe.common.mapstruct;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

/**
 * 基础对象转换接口
 * 用于定义实体对象和VO对象之间的转换方法
 *
 *
 * @param <V> VO对象类型，必须实现Serializable接口
 * @param <E> 实体对象类型，必须实现Serializable接口
 */
public interface BaseConvert<V, E> {
    /**
     * 将实体对象转换为VO对象
     *
     * @param entity 实体对象
     * @return VO对象
     */
    V toVO(E entity);

    /**
     * 将实体对象列表转换为VO对象列表
     *
     * @param entities 实体对象列表
     * @return VO对象列表
     */
    List<V> toVO(List<E> entities);

    /**
     * 将VO对象转换为实体对象
     *
     * @param vo VO对象
     * @return 实体对象
     */
    E toEntity(V vo);

    /**
     * 将VO对象列表转换为实体对象列表
     *
     * @param vos VO对象列表
     * @return 实体对象列表
     */
    List<E> toEntity(List<V> vos);


    /**
     * 将Entity的分页查询结构转换为VO的分页查询结果
     *
     * @param entity Entity对象分页查询结果
     * @return VO的分页查询结果
     */
    Page<V> toPageVO(Page<E> entity);
}
