package com.hnumi.obe.common.log.annotation;

import com.hnumi.obe.common.log.enums.LogType;
import java.lang.annotation.*;

/**
 * 日志注解
 * 用于标记需要记录日志的方法
 * 支持记录登录日志、操作日志和错误日志
 * 支持SpEL表达式解析和参数记录
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface Log {
    /**
     * 日志类型
     * 默认为操作日志
     *
     * @return 日志类型
     */
    LogType type() default LogType.OPERATE;

    /**
     * 业务名称
     * 用于标识当前操作所属的业务模块
     *
     * @return 业务名称
     */
    String businessName() default "";

    /**
     * 操作内容
     * 支持SpEL表达式，可以引用方法参数
     * 例如：#{#user.name}表示引用user参数的name属性
     *
     * @return 操作内容
     */
    String content() default "";

    /**
     * 是否保存请求参数
     * 设置为true时会记录方法的入参
     *
     * @return 是否保存请求参数
     */
    boolean saveRequestParam() default true;

    /**
     * 是否保存响应结果
     * 设置为true时会记录方法的返回值
     *
     * @return 是否保存响应结果
     */
    boolean saveResponseResult() default true;
} 