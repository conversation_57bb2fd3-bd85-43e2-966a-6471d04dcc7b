package com.hnumi.obe.common.log.aspect;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.hnumi.obe.common.log.annotation.Log;
import com.hnumi.obe.common.service.ILogService;
import com.hnumi.obe.common.util.IpUtil;
import com.hnumi.obe.common.util.RequestUtil;
import com.hnumi.obe.system.entity.ErrorLog;
import com.hnumi.obe.system.entity.LoginLog;
import com.hnumi.obe.system.entity.OperateLog;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 日志切面
 * 用于处理系统日志记录，包括：
 * 1. 登录日志：记录用户登录信息
 * 2. 操作日志：记录用户操作信息
 * 3. 错误日志：记录系统异常信息
 * 支持SpEL表达式解析和参数缓存机制
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Aspect
@Component
@Slf4j
@RequiredArgsConstructor
public class LogAspect {

    private final ILogService logService;
    private final ObjectMapper objectMapper;
    private final ExpressionParser parser = new SpelExpressionParser();
    
    /**
     * SpEL表达式解析结果缓存
     * 使用ConcurrentHashMap保证线程安全
     */
    private static final Map<String, String> spelCache = new ConcurrentHashMap<>();
    
    /**
     * 请求参数缓存
     * 使用ConcurrentHashMap保证线程安全
     */
    private static final Map<String, String> paramCache = new ConcurrentHashMap<>();
    
    /**
     * 参数大小限制：1MB
     */
    private static final int MAX_PARAM_SIZE = 1024 * 1024;

    /**
     * 环绕通知，处理日志记录
     *
     * @param point 连接点
     * @return 方法执行结果
     * @throws Throwable 执行异常
     */
    @Around("@annotation(com.hnumi.obe.common.log.annotation.Log)")
    public Object around(ProceedingJoinPoint point) throws Throwable {
        long startTime = System.currentTimeMillis();
        Object result = null;
        Throwable e = null;
        try {
            result = point.proceed();
            return result;
        } catch (Throwable ex) {
            e = ex;
            throw ex;
        } finally {
            long duration = System.currentTimeMillis() - startTime;
            // 异步记录日志
            recordLogAsync(point, duration, result, e);
        }
    }

    /**
     * 异步记录日志
     *
     * @param point 连接点
     * @param duration 执行时长
     * @param result 执行结果
     * @param e 异常信息
     */
    @Async("logExecutor")
    protected void recordLogAsync(ProceedingJoinPoint point, long duration, Object result, Throwable e) {
        try {
            MethodSignature signature = (MethodSignature) point.getSignature();
            Method method = signature.getMethod();
            Log logAnnotation = method.getAnnotation(Log.class);
            if (logAnnotation == null) {
                return;
            }
            
            // 获取请求信息
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes == null) {
                log.warn("无法获取请求信息，跳过日志记录");
                return;
            }

            // 解析SpEL表达式
            String content = parseSpEL(logAnnotation.content(), point);

            // 根据日志类型记录不同的日志
            switch (logAnnotation.type()) {
                case LOGIN:
                    recordLoginLog(logAnnotation, attributes, e);
                    break;
                default:
                    if (e != null) {
                        recordErrorLog(logAnnotation, method, attributes, point, e);
                    } else {
                        recordOperateLog(logAnnotation, method, attributes, point, content, result, duration);
                    }
                    break;
            }
        } catch (Exception ex) {
            log.error("记录日志失败", ex);
        }
    }

    /**
     * 记录登录日志
     *
     * @param logAnnotation 日志注解
     * @param attributes 请求属性
     * @param e 异常信息
     */
    private void recordLoginLog(Log logAnnotation, ServletRequestAttributes attributes, Throwable e) {
        try {
            LoginLog loginLog = new LoginLog();
            loginLog.setUsername(RequestUtil.getCurrentUsername());
            String ip = RequestUtil.getIpAddress();
            loginLog.setRequestIp(ip);
            loginLog.setUserAgent(attributes.getRequest().getHeader("User-Agent"));
            loginLog.setResult(e != null ? "登录失败：" + e.getMessage() : "登录成功");
            loginLog.setResultType(e != null ? 1 : 0);
            loginLog.setCreateTime(LocalDateTime.now());
            
            // 获取IP地址位置信息
            Map<String, String> location = IpUtil.getLocation(ip);
            if (location != null) {
                loginLog.setIpLocationCountry(location.get("country"));
                loginLog.setIpLocationProvince(location.get("province"));
                loginLog.setIpLocationCity(location.get("city"));
            }

            logService.recordLoginLog(loginLog);
        } catch (Exception ex) {
            log.error("记录登录日志失败", ex);
        }
    }

    /**
     * 记录错误日志
     *
     * @param logAnnotation 日志注解
     * @param method 方法信息
     * @param attributes 请求属性
     * @param point 连接点
     * @param e 异常信息
     */
    private void recordErrorLog(Log logAnnotation, Method method, ServletRequestAttributes attributes, 
                              ProceedingJoinPoint point, Throwable e) {
        try {
            ErrorLog errorLog = new ErrorLog();
            errorLog.setUsername(RequestUtil.getCurrentUsername());
            errorLog.setBusinessName(logAnnotation.businessName());
            errorLog.setRequestParam(getRequestParam(point));
            errorLog.setRequestMethod(method.getName());
            errorLog.setRequestUri(attributes.getRequest().getRequestURI());
            String ip = RequestUtil.getIpAddress();
            errorLog.setRequestIp(ip);
            errorLog.setExceptionDetail(e.getMessage());
            errorLog.setCreateTime(LocalDateTime.now());
            
            // 获取IP地址位置信息
            Map<String, String> location = IpUtil.getLocation(ip);
            if (location != null) {
                errorLog.setIpLocationCountry(location.get("country"));
                errorLog.setIpLocationProvince(location.get("province"));
                errorLog.setIpLocationCity(location.get("city"));
            }

            logService.recordErrorLog(errorLog);
        } catch (Exception ex) {
            log.error("记录错误日志失败", ex);
        }
    }

    /**
     * 记录操作日志
     *
     * @param logAnnotation 日志注解
     * @param method 方法信息
     * @param attributes 请求属性
     * @param point 连接点
     * @param content 操作内容
     * @param result 执行结果
     * @param duration 执行时长
     */
    private void recordOperateLog(Log logAnnotation, Method method, ServletRequestAttributes attributes,
                                ProceedingJoinPoint point, String content, Object result, long duration) {
        try {
            OperateLog operateLog = new OperateLog();
            operateLog.setUsername(RequestUtil.getCurrentUsername());
            operateLog.setBusinessName(logAnnotation.businessName());
            operateLog.setActionContent(content);
            operateLog.setActionResult(0);
            if (logAnnotation.saveRequestParam()) {
                operateLog.setRequestParam(getRequestParam(point));
            }
            if (logAnnotation.saveResponseResult()) {
                operateLog.setResult(result != null ? result.toString() : null);
            }
            operateLog.setRequestMethod(method.getName());
            operateLog.setRequestUri(attributes.getRequest().getRequestURI());
            String ip = RequestUtil.getIpAddress();
            operateLog.setRequestIp(ip);
            operateLog.setUserAgent(attributes.getRequest().getHeader("User-Agent"));
            operateLog.setDuration(duration);
            operateLog.setCreateTime(LocalDateTime.now());
            
            // 获取IP地址位置信息
            Map<String, String> location = IpUtil.getLocation(ip);
            if (location != null) {
                operateLog.setIpLocationCountry(location.get("country"));
                operateLog.setIpLocationProvince(location.get("province"));
                operateLog.setIpLocationCity(location.get("city"));
            }

            logService.recordOperateLog(operateLog);
        } catch (Exception ex) {
            log.error("记录操作日志失败", ex);
        }
    }

    /**
     * 获取请求参数
     *
     * @param point 连接点
     * @return 请求参数字符串
     */
    private String getRequestParam(ProceedingJoinPoint point) {
        try {
            MethodSignature signature = (MethodSignature) point.getSignature();
            String methodKey = signature.getDeclaringTypeName() + "." + signature.getName();
            
            // 先从缓存中获取
            String cachedParams = paramCache.get(methodKey);
            if (cachedParams != null) {
                return cachedParams;
            }

            String[] paramNames = signature.getParameterNames();
            Object[] args = point.getArgs();
            
            if (args == null || args.length == 0) {
                return null;
            }

            // 构建参数名和值的映射
            String params = Arrays.stream(paramNames)
                .map(name -> {
                    Object value = args[Arrays.asList(paramNames).indexOf(name)];
                    try {
                        return String.format("\"%s\":%s", name, objectMapper.writeValueAsString(value));
                    } catch (JsonProcessingException e) {
                        return String.format("\"%s\":\"%s\"", name, value);
                    }
                })
                .collect(Collectors.joining(",", "{", "}"));

            // 如果参数大小在限制范围内，则缓存
            if (params.getBytes().length <= MAX_PARAM_SIZE) {
                paramCache.put(methodKey, params);
            }

            return params;
        } catch (Exception e) {
            log.error("获取请求参数失败", e);
            return null;
        }
    }

    /**
     * 解析SpEL表达式
     *
     * @param expression SpEL表达式
     * @param point 连接点
     * @return 解析结果
     */
    private String parseSpEL(String expression, ProceedingJoinPoint point) {
        if (expression == null || expression.isEmpty()) {
            return "";
        }

        // 先从缓存中获取
        String cachedResult = spelCache.get(expression);
        if (cachedResult != null) {
            return cachedResult;
        }

        try {
            MethodSignature signature = (MethodSignature) point.getSignature();
            String[] paramNames = signature.getParameterNames();
            Object[] args = point.getArgs();
            
            EvaluationContext context = new StandardEvaluationContext();
            for (int i = 0; i < paramNames.length; i++) {
                context.setVariable(paramNames[i], args[i]);
            }
            
            String result = parser.parseExpression(expression).getValue(context, String.class);
            // 缓存解析结果
            spelCache.put(expression, result);
            return result;
        } catch (Exception e) {
            log.error("解析SpEL表达式失败: {}", expression, e);
            return expression;
        }
    }
} 