package com.hnumi.obe.common.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 唯一性校验ID字段注解
 * 用于标识实体类中的ID字段，在唯一性校验时使用
 * 可以指定数据库中的列名，默认为字段名
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface UniqueId {
    /**
     * 数据库列名
     * 默认为空，使用字段名作为列名
     *
     * @return 数据库列名
     */
    String value() default "";
}
