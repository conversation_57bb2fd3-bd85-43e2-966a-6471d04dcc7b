package com.hnumi.obe.common.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Excel Sheet注解
 * 用于标识实体类对应的Excel Sheet及其读取范围
 * 支持通过Sheet名称或索引匹配，支持起始行、结束行、最大读取条数等参数
 *
 * <AUTHOR>
 * @since 2024-06-10
 */
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface ExcelSheet {
    /**
     * Sheet名称，支持正则，默认为空不启用
     */
    String name() default "";

    /**
     * Sheet索引（从0开始），-1表示不使用索引匹配
     */
    int index() default -1;

    /**
     * 支持多Sheet名称批量导入，优先于name
     */
    String[] names() default {};

    /**
     * 支持多Sheet索引批量导入，优先于index
     */
    int[] indices() default {};

    /**
     * 数据起始行（从1开始，包含表头时通常为2），默认为1
     */
    int startRow() default 1;

    /**
     * 数据结束行，-1为不限制
     */
    int endRow() default -1;

    /**
     * 最大读取条数，-1为不限制
     */
    int limit() default -1;

    /**
     * Sheet是否为必需，导入时校验
     */
    boolean required() default false;
} 