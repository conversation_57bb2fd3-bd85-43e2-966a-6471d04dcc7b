package com.hnumi.obe.common.annotation;

/**
 * Excel列自定义转换器接口
 * 支持导入和导出双向转换
 *
 * @param <S> Excel单元格原始类型
 * @param <T> Java字段类型
 */
public interface ExcelColumnConverter<S, T> {
    /**
     * Excel导入时的类型转换（Excel -> Java）
     * @param value Excel单元格原始值
     * @return 转换后的Java对象
     */
    T convertToJava(S value);

    /**
     * Excel导出时的类型转换（Java -> Excel）
     * @param value Java字段值
     * @return 转换后的Excel单元格值
     */
    S convertToExcel(T value);
} 