package com.hnumi.obe.common.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Excel列注解
 * 用于标识实体类中需要导出到Excel的字段
 * 可以指定Excel中的列名，默认为字段名
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface ExcelColumn {
    /**
     * Excel列名，默认为空，使用字段名作为列名
     * 支持精确匹配
     */
    String value() default "";

    /**
     * Excel列索引（从0开始），-1表示不使用索引匹配
     */
    int index() default -1;

    /**
     * 列名正则匹配表达式，默认为空不启用
     */
    String pattern() default "";

    /**
     * 列名匹配时是否忽略大小写
     */
    boolean ignoreCase() default true;

    /**
     * 匹配列名时是否去除首尾空格
     */
    boolean trim() default true;

    /**
     * 是否为必填列，导入时校验
     */
    boolean required() default false;

    /**
     * 缺失时的默认值
     */
    String defaultValue() default "";

    /**
     * 自定义转换器类，需实现ExcelColumnConverter接口，默认无
     * 支持导入和导出双向转换
     */
    Class<?> converter() default void.class;

    /**
     * 导出格式（如日期格式、数值格式等），仅导出时生效
     */
    String exportFormat() default "";

    /**
     * 导出列宽，单位为字符数，-1为自动
     */
    int width() default -1;

    /**
     * 导出对齐方式（left/center/right），默认left
     */
    String align() default "left";

    /**
     * 字段作用域控制，指定该字段在哪些场景下生效，如{"import"}只参与导入，{"export"}只参与导出，{"import","export"}均参与
     * 可选值："import", "export"，留空或不包含时均参与
     */
    String[] scope() default {};

    /**
     * 多级表头支持，表头层级（1为最外层）
     */
    int headerLevel() default 1;
}
