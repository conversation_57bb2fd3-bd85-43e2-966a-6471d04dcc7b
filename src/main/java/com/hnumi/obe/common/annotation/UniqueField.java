package com.hnumi.obe.common.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 唯一性字段注解
 * 用于标记需要进行唯一性校验的字段
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface UniqueField {
    /**
     * 字段名称，默认为属性名
     */
    String value() default "";

    /**
     * 排除值列表，当字段值等于列表中的任一值时，不进行唯一性校验
     */
    String[] excludeValues() default {};
}
