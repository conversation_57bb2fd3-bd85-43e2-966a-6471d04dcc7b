package com.hnumi.obe.common.enums;

import lombok.Getter;

/**
 * 用户类型枚举
 * 定义系统中不同用户类型的常量
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Getter
public enum UserType {
    
    /**
     * 系统用户
     */
    SYSTEM(0, "系统用户"),
    
    /**
     * 教师
     */
    TEACHER(1, "教师"),
    
    /**
     * 学生
     */
    STUDENT(2, "学生");
    
    /**
     * 类型值
     */
    private final Integer value;
    
    /**
     * 类型描述
     */
    private final String description;
    
    UserType(Integer value, String description) {
        this.value = value;
        this.description = description;
    }
    
    /**
     * 根据值获取用户类型
     *
     * @param value 类型值
     * @return 用户类型，如果未找到返回null
     */
    public static UserType fromValue(Integer value) {
        if (value == null) {
            return null;
        }
        for (UserType type : values()) {
            if (type.getValue().equals(value)) {
                return type;
            }
        }
        return null;
    }
    
    /**
     * 判断是否为教师
     *
     * @return true表示是教师
     */
    public boolean isTeacher() {
        return this == TEACHER;
    }
    
    /**
     * 判断是否为学生
     *
     * @return true表示是学生
     */
    public boolean isStudent() {
        return this == STUDENT;
    }
    
    /**
     * 判断是否为系统用户
     *
     * @return true表示是系统用户
     */
    public boolean isSystem() {
        return this == SYSTEM;
    }
} 