package com.hnumi.obe.common.valid;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.hnumi.obe.common.valid.annotation.Phone;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 手机号校验器
 * 用于验证手机号格式和唯一性
 * 支持格式校验和数据库唯一性校验
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Slf4j
public class PhoneValidator implements ConstraintValidator<Phone, Object> {
    private String field;
    private Class<? extends Model> clazz;

    /**
     * 手机号格式正则表达式
     * 支持中国大陆手机号格式
     */
    private static final String PHONE_REGEX = "^1[356789][0-9]{9}$";

    @Override
    public void initialize(Phone phone) {
        this.field = phone.value();
        this.clazz = phone.clazz();
    }

    @Override
    public boolean isValid(Object obj, ConstraintValidatorContext context) {
        if (obj == null) {
            return true;
        }

        try {
            String phone = String.valueOf(obj);
            
            // 1. 格式校验
            if (!isValidFormat(phone, context)) {
                return false;
            }

            // 2. 唯一性校验
            return isValidUnique(phone, context);
        } catch (Exception e) {
            log.error("手机号校验失败", e);
            return false;
        }
    }

    /**
     * 验证手机号格式
     *
     * @param phone 手机号
     * @param context 校验上下文
     * @return 是否通过格式校验
     */
    private boolean isValidFormat(String phone, ConstraintValidatorContext context) {
        if (!phone.matches(PHONE_REGEX)) {
            context.disableDefaultConstraintViolation();
            context.buildConstraintViolationWithTemplate("手机号格式有误")
                    .addConstraintViolation();
            return false;
        }
        return true;
    }

    /**
     * 验证手机号唯一性
     *
     * @param phone 手机号
     * @param context 校验上下文
     * @return 是否通过唯一性校验
     */
    private boolean isValidUnique(String phone, ConstraintValidatorContext context) {
        try {
            QueryWrapper<Object> wrapper = Wrappers.query();
            wrapper.eq(field, phone);
            wrapper.last("limit 1");
            wrapper.select(field);

            Model model = clazz.getDeclaredConstructor().newInstance();
            List<?> list = model.selectList(wrapper);

            if (!list.isEmpty()) {
                context.disableDefaultConstraintViolation();
                context.buildConstraintViolationWithTemplate("手机号已被注册")
                        .addConstraintViolation();
                return false;
            }
            return true;
        } catch (Exception e) {
            log.error("手机号唯一性校验失败", e);
            return false;
        }
    }
}
