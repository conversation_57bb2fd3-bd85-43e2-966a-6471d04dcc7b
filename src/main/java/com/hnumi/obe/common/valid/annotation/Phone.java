package com.hnumi.obe.common.valid.annotation;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.hnumi.obe.common.valid.PhoneValidator;
import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 手机号校验注解
 * 用于验证手机号格式和唯一性
 * 支持中国大陆手机号格式校验和数据库唯一性校验
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = PhoneValidator.class)
public @interface Phone {
    /**
     * 数据库字段名
     * 默认为空，使用注解所在字段名
     *
     * @return 数据库字段名
     */
    String value() default "";

    /**
     * 校验失败时的提示消息
     * 默认为空，使用默认提示消息
     *
     * @return 提示消息
     */
    String message() default "";

    /**
     * 实体类
     * 指定要校验唯一性的实体类
     *
     * @return 实体类
     */
    Class<? extends Model> clazz();

    /**
     * 校验分组
     * 用于指定在哪些分组下进行校验
     *
     * @return 校验分组
     */
    Class<?>[] groups() default {};

    /**
     * 校验负载
     * 用于传递额外的校验信息
     *
     * @return 校验负载
     */
    Class<? extends Payload>[] payload() default {};
}