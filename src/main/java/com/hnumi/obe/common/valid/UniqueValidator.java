package com.hnumi.obe.common.valid;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.hnumi.obe.common.valid.enums.OperateEnum;
import com.hnumi.obe.common.util.StringUtil;
import com.hnumi.obe.common.valid.annotation.Unique;
import com.hnumi.obe.common.annotation.UniqueField;
import com.hnumi.obe.common.annotation.UniqueId;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 唯一性校验器
 * 用于验证实体对象的字段值是否唯一
 * 支持新增和更新两种操作类型的校验
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Slf4j
public class UniqueValidator implements ConstraintValidator<Unique, Object> {
    private String field;
    private Class<? extends Model> clazz;
    private OperateEnum operate;

    @Override
    public void initialize(Unique unique) {
        this.field = unique.value();
        this.clazz = unique.clazz();
        this.operate = unique.operate();
    }

    @Override
    public boolean isValid(Object obj, ConstraintValidatorContext context) {
        if (obj == null) {
            return true;
        }

        try {
            return switch (operate) {
                case INSERT -> validateInsert(obj);
                case UPDATE -> validateUpdate(obj);
                default -> {
                    log.warn("不支持的操作类型: {}", operate);
                    yield false;
                }
            };
        } catch (Exception e) {
            log.error("唯一性校验失败", e);
            return false;
        }
    }

    /**
     * 验证新增操作
     *
     * @param obj 待验证对象
     * @return 是否通过验证
     */
    private boolean validateInsert(Object obj) throws Exception {
        Map<String, Object> checkRepeatField = new HashMap<>();
        
        // 解析字段注解
        for (Field field : obj.getClass().getDeclaredFields()) {
            field.setAccessible(true);
            
            // 处理唯一性字段
            UniqueField uniqueField = field.getAnnotation(UniqueField.class);
            if (uniqueField != null) {
                String fieldName = StringUtil.setNotBlank(uniqueField.value(), field.getName());
                Object value = field.get(obj);
                if (value != null) {
                    // 检查是否需要排除该值
                    String[] excludeValues = uniqueField.excludeValues();
                    if (excludeValues.length == 0 || !Arrays.asList(excludeValues).contains(value.toString())) {
                        checkRepeatField.put(fieldName, value);
                    }
                }
            }
        }

        if (checkRepeatField.isEmpty()) {
            return true;
        }

        // 构建查询条件
        QueryWrapper<Object> wrapper = Wrappers.query();
        checkRepeatField.forEach((key, value) -> 
            wrapper.or(w -> w.eq(key, value))
        );
        wrapper.last("limit 1");

        // 执行查询
        Model model = clazz.getDeclaredConstructor().newInstance();
        List<?> list = model.selectList(wrapper);
        return list.isEmpty();
    }

    /**
     * 验证更新操作
     *
     * @param obj 待验证对象
     * @return 是否通过验证
     */
    private boolean validateUpdate(Object obj) throws Exception {
        // 1. 获取ID字段及值
        String idColumn = "";
        String idValue = "";
        Map<String, Object> checkRepeatField = new HashMap<>();
        
        // 2. 解析字段注解
        for (Field field : obj.getClass().getDeclaredFields()) {
            field.setAccessible(true);
            
            // 处理ID字段
            UniqueId uniqueId = field.getAnnotation(UniqueId.class);
            if (uniqueId != null) {
                Object value = field.get(obj);
                if (value != null) {
                    idValue = value.toString();
                    idColumn = StringUtil.setNotBlank(uniqueId.value(), field.getName());
                }
                continue;
            }
            
            // 处理唯一性字段
            UniqueField uniqueField = field.getAnnotation(UniqueField.class);
            if (uniqueField != null) {
                String fieldName = StringUtil.setNotBlank(uniqueField.value(), field.getName());
                Object value = field.get(obj);
                if (value != null) {
                    // 检查是否需要排除该值
                    String[] excludeValues = uniqueField.excludeValues();
                    if (excludeValues.length == 0 || !Arrays.asList(excludeValues).contains(value.toString())) {
                        checkRepeatField.put(fieldName, value);
                    }
                }
            }
        }

        // 3. 验证必要参数
        if (StringUtil.isBlank(idValue) || StringUtil.isBlank(idColumn) || checkRepeatField.isEmpty()) {
            log.warn("唯一性校验参数不完整: idColumn={}, idValue={}, checkRepeatField={}", 
                    idColumn, idValue, checkRepeatField);
            return false;
        }

        // 4. 构建查询条件
        QueryWrapper<Object> wrapper = Wrappers.query();
        checkRepeatField.forEach((key, value) -> 
            wrapper.or(w -> w.eq(key, value))
        );
        String finalIdColumn = idColumn;
        String finalIdValue = idValue;
        wrapper.and(w -> w.ne(finalIdColumn, finalIdValue));
        wrapper.last("limit 1");

        // 5. 执行查询
        Model model = clazz.getDeclaredConstructor().newInstance();
        List<?> list = model.selectList(wrapper);
        return list.isEmpty();
    }
}
