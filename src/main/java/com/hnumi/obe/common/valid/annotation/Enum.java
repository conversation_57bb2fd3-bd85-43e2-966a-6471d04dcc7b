package com.hnumi.obe.common.valid.annotation;

import com.hnumi.obe.common.valid.EnumValidator;
import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 枚举值校验注解
 * 用于验证字段值是否在指定的枚举值列表中
 * 支持字符串类型的枚举值校验
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = EnumValidator.class)
public @interface Enum {
    /**
     * 允许的值列表
     * 字段值必须在此列表中
     *
     * @return 允许的值列表
     */
    String[] value() default {};

    /**
     * 校验失败时的提示消息
     * 默认为空，使用默认提示消息
     *
     * @return 提示消息
     */
    String message() default "";

    /**
     * 校验分组
     * 用于指定在哪些分组下进行校验
     *
     * @return 校验分组
     */
    Class<?>[] groups() default {};

    /**
     * 校验负载
     * 用于传递额外的校验信息
     *
     * @return 校验负载
     */
    Class<? extends Payload>[] payload() default {};
}