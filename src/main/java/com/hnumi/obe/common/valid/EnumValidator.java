package com.hnumi.obe.common.valid;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.hnumi.obe.common.util.CollectionUtil;
import com.hnumi.obe.common.valid.annotation.Enum;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 枚举值校验器
 * 用于验证字段值是否在指定的枚举值列表中
 * 支持字符串类型的枚举值校验
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Slf4j
public class EnumValidator implements ConstraintValidator<Enum, Object> {
    private List<String> values;

    @Override
    public void initialize(Enum e) {
        this.values = CollectionUtil.toList(e.value());
    }

    @Override
    public boolean isValid(Object obj, ConstraintValidatorContext context) {
        if (obj == null) {
            return true;
        }

        try {
            String value = obj.toString();
            boolean isValid = values.contains(value);
            
            if (!isValid) {
                context.disableDefaultConstraintViolation();
                context.buildConstraintViolationWithTemplate(
                    String.format("值[%s]不在允许的范围内，允许的值：%s", value, values))
                    .addConstraintViolation();
            }
            
            return isValid;
        } catch (Exception e) {
            log.error("枚举值校验失败", e);
            return false;
        }
    }
}
