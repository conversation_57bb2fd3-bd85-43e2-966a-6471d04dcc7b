package com.hnumi.obe.common.valid;

import jakarta.validation.groups.Default;

/**
 * 参数校验分组接口
 * 用于定义不同场景下的参数校验规则
 * 继承自Default分组，支持与默认校验规则组合使用
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
public interface ValidGroup extends Default {
    /**
     * 创建操作校验分组
     * 用于新增数据时的参数校验
     */
    interface Add extends ValidGroup {
    }

    /**
     * 更新操作校验分组
     * 用于修改数据时的参数校验
     */
    interface Update extends ValidGroup {
    }

    /**
     * 查询操作校验分组
     * 用于查询数据时的参数校验
     */
    interface Query extends ValidGroup {
    }

    /**
     * 删除操作校验分组
     * 用于删除数据时的参数校验
     */
    interface Delete extends ValidGroup {
    }

    /**
     * 状态修改校验分组
     * 用于修改数据状态时的参数校验
     */
    interface Status extends ValidGroup {
    }

    /**
     * 验证码校验分组
     * 用于验证码相关的参数校验
     */
    interface ValidCode extends ValidGroup {
    }
}
