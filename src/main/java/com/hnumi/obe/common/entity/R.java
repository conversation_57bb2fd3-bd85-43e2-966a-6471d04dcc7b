package com.hnumi.obe.common.entity;

import lombok.Data;

import java.io.Serializable;

/**
 * 统一响应结果类
 * 用于封装所有接口的响应数据
 * 支持泛型，可以返回任意类型的数据
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Data
public class R<T> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 响应码
     */
    private Integer code;

    /**
     * 响应消息
     */
    private String msg;

    /**
     * 响应数据
     */
    private T data;

    /**
     * 私有构造函数
     * 用于创建带有完整信息的响应对象
     *
     * @param code 响应码
     * @param msg 响应消息
     * @param data 响应数据
     */
    private R(Integer code, String msg, T data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
    }

    /**
     * 私有构造函数
     * 用于创建只包含响应码和消息的响应对象
     *
     * @param code 响应码
     * @param msg 响应消息
     */
    private R(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    /**
     * 私有构造函数
     * 用于创建只包含响应码和数据的响应对象
     *
     * @param code 响应码
     * @param data 响应数据
     */
    private R(Integer code, T data) {
        this.code = code;
        this.data = data;
    }

    /**
     * 私有构造函数
     * 用于根据ResultCode创建响应对象
     *
     * @param resultCode 响应码枚举
     */
    private R(ResultCode resultCode) {
        this.code = resultCode.getCode();
        this.msg = resultCode.getMsg();
    }

    /**
     * 私有构造函数
     * 用于根据ResultCode和数据创建响应对象
     *
     * @param resultCode 响应码枚举
     * @param data 响应数据
     */
    private R(ResultCode resultCode, T data) {
        this.code = resultCode.getCode();
        this.msg = resultCode.getMsg();
        this.data = data;
    }

    /**
     * 创建成功响应
     *
     * @param <T> 响应数据类型
     * @return 成功响应对象
     */
    public static <T> R<T> ok() {
        return new R<>(ResultCode.SUCCESS);
    }

    /**
     * 创建带消息的成功响应
     *
     * @param msg 响应消息
     * @param <T> 响应数据类型
     * @return 成功响应对象
     */
    public static <T> R<T> ok(String msg) {
        return new R<>(ResultCode.SUCCESS.getCode(), msg);
    }

    /**
     * 创建带消息和数据的成功响应
     *
     * @param msg 响应消息
     * @param data 响应数据
     * @param <T> 响应数据类型
     * @return 成功响应对象
     */
    public static <T> R<T> ok(String msg, T data) {
        return new R<>(ResultCode.SUCCESS.getCode(), msg, data);
    }

    /**
     * 根据响应码创建成功响应
     *
     * @param resultCode 响应码枚举
     * @param <T> 响应数据类型
     * @return 成功响应对象
     */
    public static <T> R<T> ok(ResultCode resultCode) {
        return new R<>(resultCode);
    }

    /**
     * 创建带数据的成功响应
     *
     * @param data 响应数据
     * @param <T> 响应数据类型
     * @return 成功响应对象
     */
    public static <T> R<T> ok(T data) {
        return new R<>(ResultCode.SUCCESS, data);
    }

    /**
     * 创建失败响应
     *
     * @param resultCode 响应码枚举
     * @param <T> 响应数据类型
     * @return 失败响应对象
     */
    public static <T> R<T> fail(ResultCode resultCode) {
        return new R<>(resultCode);
    }

    /**
     * 创建带数据的失败响应
     *
     * @param resultCode 响应码枚举
     * @param data 响应数据
     * @param <T> 响应数据类型
     * @return 失败响应对象
     */
    public static <T> R<T> fail(ResultCode resultCode, T data) {
        return new R<>(resultCode, data);
    }

    /**
     * 创建自定义失败响应
     *
     * @param code 响应码
     * @param msg 响应消息
     * @param <T> 响应数据类型
     * @return 失败响应对象
     */
    public static <T> R<T> fail(Integer code, String msg) {
        return new R<>(code, msg);
    }
}
