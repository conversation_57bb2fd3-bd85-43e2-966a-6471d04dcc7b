package com.hnumi.obe.common.entity;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.Data;

/**
 * 分页查询基类
 * 用于封装分页查询的通用参数
 * 支持自定义当前页和每页大小
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Data
public class BasePage<T> {
    /**
     * 当前页码，默认第1页
     */
    private int current = 1;

    /**
     * 每页大小，默认10条
     */
    private int size = 10;

    /**
     * 获取MyBatis-Plus分页对象
     * 将当前页和每页大小转换为Page对象
     *
     * @return MyBatis-Plus分页对象
     */
    public Page<T> getPage() {
        // 确保页码和每页大小合法
        if (current < 1) {
            current = 1;
        }
        if (size < 1) {
            size = 10;
        }
        return new Page<>(current, size);
    }
}
