package com.hnumi.obe.common.entity;

import lombok.Getter;

/**
 * 系统响应码枚举类
 * 用于定义系统中所有的响应码和对应的消息
 * 按照模块进行分类，便于管理和维护
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Getter
public enum ResultCode {
    // 通用响应码
    SUCCESS(200, "操作成功"),
    ERROR(-1, "系统错误"),
    NOT_LOGIN(401, "登录失效"),
    METHOD_ARGUMENT_ERROR(1, "参数校验异常"),

    // 用户认证相关响应码：10开头
    DISABLE_LOGIN(10001, "用户名或密码错误"),
    LOGIN_PARAM_ERROR(10002, "登录参数错误"),
    LOGIN_USER_NOT_FOUND(10003, "用户不存在"),
    LOGIN_PASSWORD_ERROR(10004, "登录密码错误"),
    //用户管理相关操作：11开头
    USER_IMPORT_ERROR(11001, "导入用户失败"),
    USER_ROLE_ASSIGN_ERROR(11002, "用户角色分配失败"),
    USER_PASSWORD_RESET_ERROR(11003, "用户重置密码失败"),
    USER_BATCH_DELETE_ERROR(11004, "删除失败"),
    USER_NAME_EXIST(2, "用户名已存在"),
    PHONE_EXIST(3, "手机号已存在"),
    USER_REGISTER_ERROR(4, "注册异常"),
    USER_NOT_FOUND(5, "该用户不存在"),
    USER_ADD_ERROR(6, "用户添加失败"),
    USER_DELETE_ERROR(7, "用户删除失败"),
    USER_BLOCK_ERROR(8, "封禁用户失败"),

    USER_STATUS_ERROR(9, "导入用户失败"),
    USER_PARAM_ERROR(10, "更新用户失败"),
    USER_UPDATE_ERROR(10, "更新用户失败"),

    // 手机验证码相关响应码
    PHONE_CODE_ERROR(11, "手机验证码错误"),
    PHONE_CODE_REGISTER_SEND_ERROR(12, "获取验证码频繁，请稍后重试"),
    PHONE_CODE_REGISTER_ERROR(13, "手机注册验证码失效"),
    PHONE_CODE_SEND_ERROR(14, "手机验证码发送失败"),
    PHONE_CODE_API_ERROR(15, "获取手机短信验证码接口错误"),
    PHONE_CODE_EXPIRE(16, "验证码过期"),
    PHONE_PARAM_ERROR(17, "参数错误"),

    // 字典管理相关响应码
    DICT_TYPE_ADD_ERROR(17, "字典类型添加失败"),
    DICT_TYPE_DELETE_ERROR(18, "字典类型删除失败"),
    DICT_TYPE_IS_NOT_EMPTY(19, "该字典类型存在字典数据，不支持删除"),

    // 菜单管理相关响应码
    MENU_ADD_ERROR(20, "添加菜单失败"),
    MENU_UPDATE_ERROR(21, "更新菜单失败"),
    MENU_DELETE_ERROR(22, "删除菜单失败"),
    MENU_NOT_FOUND(23, "菜单不存在"),
    MENU_IS_NOT_EMPTY(24, "该菜单下存在子菜单，不支持直接删除"),

    // 角色管理相关响应码
    ROLE_ADD_ERROR(25, "添加角色失败"),
    ROLE_UPDATE_ERROR(26, "更新角色失败"),
    ROLE_DELETE_ERROR(27, "删除角色失败"),
    ROLE_NOT_FOUND(28, "角色不存在"),
    ROLE_STATUS_UPDATE_ERROR(29, "更新角色状态失败"),
    ROLE_MENU_UPDATE_ERROR(30, "更新角色菜单失败"),

    // 租户管理相关响应码
    TENANT_ADD_ERROR(31, "添加租户失败"),
    TENANT_PARAM_ERROR(31, "参数异常"),
    TENANT_UPDATE_ERROR(32, "更新租户失败"),
    TENANT_DELETE_ERROR(33, "删除租户失败"),
    TENANT_NOT_FOUND(34, "租户不存在"),
    TENANT_UPDATE_MENU_ERROR(35, "更新租户菜单失败"),
    TENANT_IS_NOT_EMPTY(36, "该租户不为空，不支持直接删除"),
    TENANT_USER_NOT_FOUND(36, "租户用户不存在"),

    // 标签管理相关响应码
    TAG_ADD_ERROR(37, "添加标签失败"),
    TAG_UPDATE_ERROR(38, "更新标签失败"),
    TAG_DELETE_ERROR(39, "删除标签失败"),
    TAG_NAME_EXISTS(39, "删除标签失败"),
    TAG_CREATE_ERROR(39, "删除标签失败"),
    TAG_NOT_FOUND(40, "标签不存在"),

    // 专业管理相关响应码：50开头
    MAJOR_EXISTS(50001, "专业已存在"),
    MAJOR_NOT_EXISTS(50002, "专业不存在"),
    MAJOR_CODE_EXISTS(50003, "专业代码已存在"),
    MAJOR_IN_USE(50004, "专业正在使用中，无法删除"),
    MAJOR_LEADER_ASSIGNED(50005, "该教师已被指定为其他专业的负责人"),
    MAJOR_IMPORT_DATA_FORMAT_ERROR(50006, "导入数据格式错误");

    /**
     * 响应码
     */
    private final Integer code;

    /**
     * 响应消息
     */
    private final String msg;

    /**
     * 构造函数
     *
     * @param code 响应码
     * @param msg 响应消息
     */
    ResultCode(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }
}
