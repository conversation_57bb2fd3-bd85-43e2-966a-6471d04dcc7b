package com.hnumi.obe.common.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 双值元组类
 * 用于封装两个任意类型的值
 * 支持泛型，可以分别指定两个值的类型
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class Tuple2<T1, T2> extends Tuple {
    /**
     * 元组中的第一个值
     */
    private T1 _1;

    /**
     * 元组中的第二个值
     */
    private T2 _2;
}
