package com.hnumi.obe.common.entity;

import com.hnumi.obe.base.vo.StudentVO;
import com.hnumi.obe.base.vo.TeacherVO;
import com.hnumi.obe.system.entity.BaseUser;
import com.hnumi.obe.common.enums.UserType;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 登录会话信息视图对象
 * 用于存储用户登录后的会话信息
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Data
@Accessors(chain = true)
public class LoginSessionVO {
    /**
     * 用户业务ID：如果是教师则为教师ID，如果是学生则为学生ID
     */
    private Long id;
    /**
     * 用户类型：1-教师，2-学生, 0-系统用户
     */
    private Integer type;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 用户手机号
     */
    private String phone;
    /**
     * 用户邮箱
     */
    private String email;
    /**
     * 用户号：如果是教师则为工号，如果是学生则为用户名
     */
    private String number;
    /**
     * 用户名：如果是教师则为教师姓名，如果是学生则为学生姓名
     */
    private String realName;
    /**
     * 用户性别：1-男，2-女，0-保密
     */
    private Integer gender;
    
    /**
     * 创建教师会话信息
     *
     * @param teacherVO 教师信息
     * @return 登录会话信息
     */
    public static LoginSessionVO fromTeacher(TeacherVO teacherVO) {
        if (teacherVO == null) {
            return null;
        }
        
        return new LoginSessionVO()
                .setId(teacherVO.getId())
                .setType(UserType.TEACHER.getValue())
                .setUserId(teacherVO.getUser() != null ? teacherVO.getUser().getId() : null)
                .setPhone(teacherVO.getPhone())
                .setEmail(teacherVO.getEmail())
                .setNumber(teacherVO.getNumber())
                .setRealName(teacherVO.getName())
                .setGender(teacherVO.getGender());
    }
    
    /**
     * 创建学生会话信息
     *
     * @param studentVO 学生信息
     * @return 登录会话信息
     */
    public static LoginSessionVO fromStudent(StudentVO studentVO) {
        if (studentVO == null) {
            return null;
        }
        
        return new LoginSessionVO()
                .setId(studentVO.getId())
                .setType(UserType.STUDENT.getValue())
                .setUserId(studentVO.getUser() != null ? studentVO.getUser().getId() : null)
                .setPhone(studentVO.getPhone())
                .setEmail(studentVO.getEmail())
                .setNumber(studentVO.getStudentNumber())
                .setRealName(studentVO.getName())
                .setGender(studentVO.getGender());
    }
    
    /**
     * 创建系统用户会话信息
     *
     * @param baseUser 基础用户信息
     * @return 登录会话信息
     */
    public static LoginSessionVO fromSystemUser(BaseUser baseUser) {
        if (baseUser == null) {
            return null;
        }
        
        return new LoginSessionVO()
                .setId(baseUser.getId())
                .setType(UserType.SYSTEM.getValue())
                .setUserId(baseUser.getId())
                .setPhone(baseUser.getPhone())
                .setEmail(baseUser.getEmail())
                .setNumber(baseUser.getUsername())
                .setRealName(baseUser.getRealName())
                .setGender(baseUser.getGender());
    }
    
    /**
     * 验证会话信息是否完整
     *
     * @return true表示信息完整
     */
    public boolean isValid() {
        return id != null && type != null && userId != null && realName != null;
    }
    
    /**
     * 获取用户类型枚举
     *
     * @return 用户类型枚举
     */
    public UserType getUserType() {
        return UserType.fromValue(type);
    }
}
