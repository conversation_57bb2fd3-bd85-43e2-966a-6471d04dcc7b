package com.hnumi.obe.common.service;

import com.hnumi.obe.system.entity.ErrorLog;
import com.hnumi.obe.system.entity.LoginLog;
import com.hnumi.obe.system.entity.OperateLog;

/**
 * 日志记录服务接口
 * 用于处理系统各类日志的记录操作，包括：
 * 1. 操作日志：记录用户的操作行为
 * 2. 错误日志：记录系统异常信息
 * 3. 登录日志：记录用户登录相关信息
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
public interface ILogService {
    /**
     * 记录操作日志
     * 异步记录用户的操作行为，包括操作内容、请求参数、响应结果等
     *
     * @param operateLog 操作日志对象
     * @throws RuntimeException 当日志记录失败时抛出异常
     */
    void recordOperateLog(OperateLog operateLog);

    /**
     * 记录错误日志
     * 记录系统运行过程中发生的异常信息，包括异常堆栈、请求参数等
     *
     * @param errorLog 错误日志对象
     * @throws RuntimeException 当日志记录失败时抛出异常
     */
    void recordErrorLog(ErrorLog errorLog);

    /**
     * 记录登录日志
     * 记录用户的登录行为，包括登录时间、IP地址、登录结果等
     *
     * @param loginLog 登录日志对象
     * @throws RuntimeException 当日志记录失败时抛出异常
     */
    void recordLoginLog(LoginLog loginLog);
} 