package com.hnumi.obe.common.service.impl;

import com.hnumi.obe.common.service.ILogService;
import com.hnumi.obe.system.entity.ErrorLog;
import com.hnumi.obe.system.entity.LoginLog;
import com.hnumi.obe.system.entity.OperateLog;
import com.hnumi.obe.system.mapper.ErrorLogMapper;
import com.hnumi.obe.system.mapper.LoginLogMapper;
import com.hnumi.obe.system.mapper.OperateLogMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * 日志记录服务实现类
 * 使用异步方式处理日志记录，避免影响主业务流程
 * 所有日志记录操作都在独立的线程池中执行
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LogServiceImpl implements ILogService {

    private final OperateLogMapper operateLogMapper;
    private final ErrorLogMapper errorLogMapper;
    private final LoginLogMapper loginLogMapper;

    /**
     * 异步记录操作日志
     * 使用logExecutor线程池执行，避免阻塞主线程
     *
     * @param operateLog 操作日志对象
     */
    @Async("logExecutor")
    @Override
    public void recordOperateLog(OperateLog operateLog) {
        try {
            if (operateLog == null) {
                log.warn("操作日志对象为空，跳过记录");
                return;
            }
            operateLogMapper.insert(operateLog);
            log.debug("操作日志记录成功: {}", operateLog);
        } catch (Exception e) {
            log.error("记录操作日志失败: {}", operateLog, e);
            throw new RuntimeException("记录操作日志失败", e);
        }
    }

    /**
     * 异步记录错误日志
     * 使用logExecutor线程池执行，避免阻塞主线程
     *
     * @param errorLog 错误日志对象
     */
    @Async("logExecutor")
    @Override
    public void recordErrorLog(ErrorLog errorLog) {
        try {
            if (errorLog == null) {
                log.warn("错误日志对象为空，跳过记录");
                return;
            }
            errorLogMapper.insert(errorLog);
            log.debug("错误日志记录成功: {}", errorLog);
        } catch (Exception e) {
            log.error("记录错误日志失败: {}", errorLog, e);
            throw new RuntimeException("记录错误日志失败", e);
        }
    }

    /**
     * 异步记录登录日志
     * 使用logExecutor线程池执行，避免阻塞主线程
     *
     * @param loginLog 登录日志对象
     */
    @Async("logExecutor")
    @Override
    public void recordLoginLog(LoginLog loginLog) {
        try {
            if (loginLog == null) {
                log.warn("登录日志对象为空，跳过记录");
                return;
            }
            loginLogMapper.insert(loginLog);
            log.debug("登录日志记录成功: {}", loginLog);
        } catch (Exception e) {
            log.error("记录登录日志失败: {}", loginLog, e);
            throw new RuntimeException("记录登录日志失败", e);
        }
    }
} 