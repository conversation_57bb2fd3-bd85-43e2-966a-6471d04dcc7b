package com.hnumi.obe.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;

/**
 * OBE系统配置属性类
 * 用于管理系统的全局配置属性
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Validated
@ConfigurationProperties(prefix = "obe")
@Data
public class OBEProperties {
    /**
     * Redis中手机验证码的前缀
     */
    @NotBlank(message = "手机验证码Redis前缀不能为空")
    private String redisKeyPrefixPhoneCode;

    /**
     * Redis中租户信息的前缀
     */
    @NotBlank(message = "租户信息Redis前缀不能为空")
    private String redisKeyPrefixTenant;

    /**
     * 短信服务配置信息
     */
    @Valid
    private SmsInfo sms;

    /**
     * 短信服务配置信息类
     */
    @Data
    public static class SmsInfo {
        /**
         * 短信签名
         */
        @NotBlank(message = "短信签名不能为空")
        private String signName;

        /**
         * 阿里云区域ID
         */
        @NotBlank(message = "区域ID不能为空")
        private String regionId;

        /**
         * 阿里云访问密钥ID
         */
        @NotBlank(message = "访问密钥ID不能为空")
        private String accessKeyId;

        /**
         * 阿里云访问密钥密码
         */
        @NotBlank(message = "访问密钥密码不能为空")
        private String accessSecret;

        /**
         * 注册短信模板代码
         */
        @NotBlank(message = "注册短信模板代码不能为空")
        private String registerTemplateCode;

        /**
         * 登录短信模板代码
         */
        @NotBlank(message = "登录短信模板代码不能为空")
        private String loginTemplateCode;
    }
}
