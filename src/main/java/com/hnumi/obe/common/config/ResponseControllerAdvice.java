package com.hnumi.obe.common.config;

import com.alibaba.fastjson2.JSONObject;
import com.hnumi.obe.common.entity.R;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

/**
 * 响应控制器增强类
 * 用于统一处理控制器返回的响应数据
 * 自动将返回数据包装成统一的响应格式
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@RestControllerAdvice(basePackages = {
        "com.hnumi.obe.system.controller",
        "com.hnumi.obe.cs.controller",
        "com.hnumi.obe.tp.controller",
        "com.hnumi.obe.base.controller"
})
public class ResponseControllerAdvice implements ResponseBodyAdvice<Object> {

    /**
     * 判断是否需要处理响应
     * 默认处理所有响应
     *
     * @param returnType 返回类型
     * @param converterType 转换器类型
     * @return 是否处理响应
     */
    @Override
    public boolean supports(MethodParameter returnType, Class<? extends HttpMessageConverter<?>> converterType) {
        return true;
    }

    /**
     * 处理响应数据
     * 将返回数据包装成统一的响应格式
     *
     * @param body 响应体
     * @param returnType 返回类型
     * @param selectedContentType 选中的内容类型
     * @param selectedConverterType 选中的转换器类型
     * @param request 请求
     * @param response 响应
     * @return 处理后的响应数据
     */
    @Override
    public Object beforeBodyWrite(Object body, MethodParameter returnType, MediaType selectedContentType,
                                Class<? extends HttpMessageConverter<?>> selectedConverterType,
                                ServerHttpRequest request, ServerHttpResponse response) {
        // 如果已经是R类型，直接返回
        if (returnType.getParameterType().equals(R.class)) {
            return body;
        }

        // 如果是String类型，需要特殊处理
        if (body instanceof String) {
            response.getHeaders().setContentType(MediaType.APPLICATION_JSON);
            return JSONObject.toJSONString(R.ok(body));
        }

        // 其他类型直接包装
        return R.ok(body);
    }
}
