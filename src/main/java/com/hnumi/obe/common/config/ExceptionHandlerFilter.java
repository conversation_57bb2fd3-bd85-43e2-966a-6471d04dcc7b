package com.hnumi.obe.common.config;

import jakarta.servlet.FilterChain;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.web.servlet.HandlerExceptionResolver;

/**
 * 全局异常处理过滤器
 * 用于捕获在过滤器链中发生的异常，并将其转换为合适的响应
 * 优先级设置为最高，确保能够捕获所有异常
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Component
@Order(Ordered.HIGHEST_PRECEDENCE)
@Slf4j
public class ExceptionHandlerFilter extends OncePerRequestFilter {

    private HandlerExceptionResolver resolver;

    @Autowired
    public void setResolver(@Qualifier("handlerExceptionResolver") HandlerExceptionResolver resolver) {
        this.resolver = resolver;
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) {
        String requestPath = request.getRequestURI();
        try {
            filterChain.doFilter(request, response);
        } catch (Exception e) {
            log.error("请求处理异常 - 路径: {}, 异常: {}", requestPath, e.getMessage(), e);
            try {
                resolver.resolveException(request, response, null, e);
            } catch (Exception ex) {
                log.error("异常处理失败 - 路径: {}, 异常: {}", requestPath, ex.getMessage(), ex);
                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            }
        }
    }
}