package com.hnumi.obe.common.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import java.util.concurrent.Executor;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 异步任务配置类
 * 用于配置系统中异步任务的线程池
 * 目前主要配置了日志异步处理的线程池
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Configuration
@EnableAsync
public class AsyncConfig {

    /**
     * 日志异步处理线程池配置
     * 核心线程数：5
     * 最大线程数：10
     * 队列容量：100
     * 拒绝策略：调用者运行
     */
    private static final int LOG_CORE_POOL_SIZE = 5;
    private static final int LOG_MAX_POOL_SIZE = 10;
    private static final int LOG_QUEUE_CAPACITY = 100;
    private static final int LOG_SHUTDOWN_TIMEOUT = 60;

    @Bean("logExecutor")
    public Executor logExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 核心线程数
        executor.setCorePoolSize(LOG_CORE_POOL_SIZE);
        // 最大线程数
        executor.setMaxPoolSize(LOG_MAX_POOL_SIZE);
        // 队列大小
        executor.setQueueCapacity(LOG_QUEUE_CAPACITY);
        // 线程前缀名
        executor.setThreadNamePrefix("log-async-");
        // 拒绝策略
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        // 等待所有任务结束后再关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);
        // 设置线程池关闭超时时间（秒）
        executor.setAwaitTerminationSeconds(LOG_SHUTDOWN_TIMEOUT);
        // 初始化
        executor.initialize();
        return executor;
    }
} 