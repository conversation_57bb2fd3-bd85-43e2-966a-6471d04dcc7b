package com.hnumi.obe.common.config;

import jakarta.validation.Validation;
import jakarta.validation.Validator;
import jakarta.validation.ValidatorFactory;
import org.hibernate.validator.HibernateValidator;
import org.springframework.beans.factory.config.AutowireCapableBeanFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.validation.beanvalidation.SpringConstraintValidatorFactory;

/**
 * 验证器配置类
 * 用于配置Hibernate Validator的行为
 * 支持快速失败模式和自定义验证器注入
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Configuration
public class ValidatorConfig {

    /**
     * 配置Hibernate Validator
     * 启用快速失败模式，一旦发现验证失败立即返回
     * 支持在验证器中注入Spring Bean
     *
     * @param springFactory Spring Bean工厂
     * @return Validator实例
     */
    @Bean
    public Validator validator(AutowireCapableBeanFactory springFactory) {
        try (ValidatorFactory validatorFactory = Validation.byProvider(HibernateValidator.class)
                .configure()
                // 启用快速失败模式
                .failFast(true)
                // 允许在验证器中注入Spring Bean
                .constraintValidatorFactory(new SpringConstraintValidatorFactory(springFactory))
                .buildValidatorFactory()) {
            return validatorFactory.getValidator();
        }
    }
}
