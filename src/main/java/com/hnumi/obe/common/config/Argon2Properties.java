package com.hnumi.obe.common.config;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Component
@ConfigurationProperties(prefix = "password4j.argon2")
public class Argon2Properties {
    private int memory;
    private int iterations;
    private int length;
    private int parallelism;
    private String type;
    private int version;
    private int salt;
    private String password;
}

