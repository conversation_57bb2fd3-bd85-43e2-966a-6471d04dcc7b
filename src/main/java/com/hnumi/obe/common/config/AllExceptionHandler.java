package com.hnumi.obe.common.config;

import cn.dev33.satoken.exception.NotLoginException;
import com.hnumi.obe.common.entity.R;
import com.hnumi.obe.common.entity.ResultCode;
import com.hnumi.obe.common.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.BindException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

@Slf4j
@RestControllerAdvice
public class AllExceptionHandler {

    @ExceptionHandler(ServiceException.class)
    public R<?> handleServiceException(ServiceException e) {
        log.error(e.getMessage(), e);
        return R.fail(e.getCode(), e.getMessage());
    }

    @ExceptionHandler(RuntimeException.class)
    public R<?> handleRuntimeException(RuntimeException e) {
        log.error(e.getMessage(), e);
        return R.fail(ResultCode.ERROR.getCode(), e.getMessage());
    }

    @ExceptionHandler(Exception.class)
    public R<?> handleException(Exception e) {
        log.error(e.getMessage(), e);
        return R.fail(ResultCode.ERROR.getCode(), e.getMessage());
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public R<?> handleMethodArgumentNotValidException(MethodArgumentNotValidException e) {
        log.error(e.getMessage(), e);
        return R.fail(ResultCode.METHOD_ARGUMENT_ERROR.getCode(), getFirstErrorMessage(e.getBindingResult().getAllErrors()));
    }

    @ExceptionHandler(BindException.class)
    public R<?> handleBindException(BindException e) {
        log.error(e.getMessage(), e);
        return R.fail(ResultCode.METHOD_ARGUMENT_ERROR.getCode(), getFirstErrorMessage(e.getBindingResult().getAllErrors()));
    }

    @ExceptionHandler(NotLoginException.class)
    public R<?> handleNotLoginException(NotLoginException e) {
        String msg = getNotLoginMessage(e.getType());
        log.error(e.getMessage(), e);
        return R.fail(ResultCode.NOT_LOGIN.getCode(), msg);
    }

    private String getFirstErrorMessage(java.util.List<? extends org.springframework.validation.ObjectError> errors) {
        return errors.stream()
                .findFirst()
                .map(org.springframework.validation.ObjectError::getDefaultMessage)
                .orElse("参数验证失败");
    }

    private String getNotLoginMessage(String type) {
        return switch (type) {
            case NotLoginException.NOT_TOKEN -> "登录后方可进入系统";
            case NotLoginException.INVALID_TOKEN -> "未提供有效的Token";
            case NotLoginException.TOKEN_TIMEOUT -> "登录信息已过期，请重新登录";
            case NotLoginException.BE_REPLACED -> "您的账户在另一台设备上已登录，如非本人操作，请立即修改密码";
            case NotLoginException.KICK_OUT -> "您的账号已被系统强制下线";
            case NotLoginException.TOKEN_FREEZE -> "您的账号已被系统冻结";
            default -> "当前会话未登录";
        };
    }
}
