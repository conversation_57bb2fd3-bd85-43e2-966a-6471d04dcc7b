package com.hnumi.obe.common.config;

import com.baomidou.mybatisplus.extension.plugins.handler.TenantLineHandler;
import com.hnumi.obe.common.util.RequestUtil;
import com.hnumi.obe.common.util.StringUtil;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.LongValue;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * MyBatis-Plus多租户处理器
 * 用于实现多租户数据隔离
 * 通过拦截SQL语句，自动添加租户ID条件
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Component
public class MyBatisPlusTenantHandler implements TenantLineHandler {

    /**
     * 不需要进行租户隔离的表名列表
     * 系统表、公共表等不需要租户隔离的表
     */
    private static final List<String> EXCLUDE_TABLES = Arrays.asList(
            "sys_base_user", "sys_role", "sys_role_menu", "sys_tenant",
            "sys_dict_type", "sys_dict_data", "sys_menu", "sys_tenant_user",
            "bus_content_library", "bus_content", "bus_paper", "bus_topic",
            "bus_question_bank", "bus_learning_progress", "bus_like"
    );

    /**
     * 租户ID列名
     */
    private static final String TENANT_ID_COLUMN = "tenant_id";

    @Override
    public Expression getTenantId() {
        long tenantId = RequestUtil.getTenantId();
        if (tenantId <= 0) {
            throw new IllegalArgumentException("无效的租户ID");
        }
        return new LongValue(tenantId);
    }

    @Override
    public String getTenantIdColumn() {
        return TENANT_ID_COLUMN;
    }

    @Override
    public boolean ignoreTable(String tableName) {
        return StringUtil.containsIgnoreCase(EXCLUDE_TABLES, tableName);
    }
}
