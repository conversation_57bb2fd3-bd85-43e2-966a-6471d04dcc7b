package com.hnumi.obe.common.config;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.TenantLineInnerInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * MyBatis-Plus配置类
 * 用于配置MyBatis-Plus的插件和拦截器
 * 包括多租户插件和分页插件
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Configuration
public class MyBatisPlusConfig {

    @Autowired
    private MyBatisPlusTenantHandler tenantHandler;

    /**
     * 配置MyBatis-Plus拦截器
     * 添加多租户插件和分页插件
     * 注意：分页插件必须在最后添加
     *
     * @return MybatisPlusInterceptor
     */
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();

        // 配置多租户插件
        //TenantLineInnerInterceptor tenantInterceptor = new TenantLineInnerInterceptor();
        //tenantInterceptor.setTenantLineHandler(tenantHandler);
        //interceptor.addInnerInterceptor(tenantInterceptor);

        // 配置分页插件
        PaginationInnerInterceptor paginationInterceptor = new PaginationInnerInterceptor(DbType.MYSQL);
        // 设置最大单页限制数量，默认 500 条，-1 不受限制
        paginationInterceptor.setMaxLimit(-1L);
        interceptor.addInnerInterceptor(paginationInterceptor);

        return interceptor;
    }
}
