package com.hnumi.obe.common.config;

import cn.dev33.satoken.fun.strategy.SaCorsHandleFunction;
import cn.dev33.satoken.interceptor.SaInterceptor;
import cn.dev33.satoken.router.SaHttpMethod;
import cn.dev33.satoken.router.SaRouter;
import cn.dev33.satoken.stp.StpUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Sa-Token配置类
 * 用于配置Sa-Token的拦截器、跨域处理和安全头
 * 实现登录校验、跨域请求处理和安全防护
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Configuration
@Slf4j
public class SaTokenConfigure implements WebMvcConfigurer {

    /**
     * 不需要登录校验的路径
     */
    private static final String[] EXCLUDE_PATHS = {
            "/favicon.ico",
            "/user/import",
            "/user/login",
    };

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 注册Sa-Token拦截器，进行登录校验
        registry.addInterceptor(new SaInterceptor(handle -> {
            try {
                StpUtil.checkLogin();
            } catch (Exception e) {
                log.error("登录校验失败: {}", e.getMessage());
                throw e;
            }
        }))
                //.addPathPatterns("/**")
                .excludePathPatterns(EXCLUDE_PATHS);
    }

    /**
     * 配置CORS跨域处理
     * 设置跨域请求头和安全响应头
     *
     * @return SaCorsHandleFunction
     */
    @Bean
    public SaCorsHandleFunction corsHandle() {
        return (req, res, sto) -> {
            // 设置跨域请求头
            res.setHeader("Access-Control-Allow-Origin", "*")
                    .setHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
                    .setHeader("Access-Control-Max-Age", "3600")
                    .setHeader("Access-Control-Allow-Headers", "Content-Type, Authorization, X-Requested-With")
                    .setHeader("Access-Control-Allow-Credentials", "true");

            // 设置安全响应头
            res.setServer("obe")
                    .setHeader("X-Frame-Options", "SAMEORIGIN")
                    .setHeader("X-XSS-Protection", "1; mode=block")
                    .setHeader("X-Content-Type-Options", "nosniff")
                    .setHeader("Strict-Transport-Security", "max-age=31536000; includeSubDomains")
                    .setHeader("Content-Security-Policy", "default-src 'self'")
                    .setHeader("Referrer-Policy", "strict-origin-when-cross-origin");

            // 如果是预检请求，则立即返回到前端
            SaRouter.match(SaHttpMethod.OPTIONS) .free(r ->  log.debug("处理OPTIONS预检请求"))
                    .back();
        };
    }
}
