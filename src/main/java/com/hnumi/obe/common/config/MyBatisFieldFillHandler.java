package com.hnumi.obe.common.config;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.hnumi.obe.common.util.RequestUtil;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * MyBatis-Plus字段自动填充处理器
 * 用于自动填充实体类中的创建时间、更新时间、创建人、更新人等字段
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Component
public class MyBatisFieldFillHandler implements MetaObjectHandler {

    private static final String CREATE_TIME = "createTime";
    private static final String UPDATE_TIME = "modifyTime";
    private static final String CREATE_BY = "creator";
    private static final String UPDATE_BY = "modifier";

    @Override
    public void insertFill(MetaObject metaObject) {
        LocalDateTime now = LocalDateTime.now();
        Long userId = RequestUtil.getUserId();

        // 填充创建时间和更新时间
        if (metaObject.hasSetter(CREATE_TIME)) {
            metaObject.setValue(CREATE_TIME, now);
        }
        if (metaObject.hasSetter(UPDATE_TIME)) {
            metaObject.setValue(UPDATE_TIME, now);
        }

        // 填充创建人和更新人
        if (userId != null) {
            if (metaObject.hasSetter(CREATE_BY)) {
                metaObject.setValue(CREATE_BY, userId);
            }
            if (metaObject.hasSetter(UPDATE_BY)) {
                metaObject.setValue(UPDATE_BY, userId);
            }
        }

    }

    @Override
    public void updateFill(MetaObject metaObject) {
        // 填充更新时间
        if (metaObject.hasSetter(UPDATE_TIME)) {
            metaObject.setValue(UPDATE_TIME, LocalDateTime.now());
        }

        // 填充更新人
        Long userId = RequestUtil.getUserId();
        if (userId != null && metaObject.hasSetter(UPDATE_BY)) {
            metaObject.setValue(UPDATE_BY, userId);
        }

    }
}
