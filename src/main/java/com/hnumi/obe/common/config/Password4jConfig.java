package com.hnumi.obe.common.config;

import com.password4j.Argon2Function;
import com.password4j.types.Argon2;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class Password4jConfig {

    @Bean
    public Argon2Function argon2Function(Argon2Properties props) {


        return Argon2Function.getInstance(
                props.getMemory(),
                props.getIterations(),
                props.getParallelism(),
                props.getLength(),
                getType(props.getType()),
                props.getVersion()
        );
    }

    private Argon2 getType(String type) {
        return switch (type.toLowerCase()) {
            case "i" -> Argon2.I;
            case "d" -> Argon2.D;
            default -> Argon2.ID;
        };
    }
}