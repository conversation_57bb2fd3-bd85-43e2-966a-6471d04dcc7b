package com.hnumi.obe.common.config;

import cn.dev33.satoken.stp.StpInterface;
import com.hnumi.obe.base.service.ITeacherService;
import com.hnumi.obe.system.entity.Role;
import com.hnumi.obe.system.service.IMenuService;
import com.hnumi.obe.system.service.IRoleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Sa-Token权限接口实现类
 * 用于实现Sa-Token的权限和角色获取接口
 * 支持多端登录，根据登录类型获取不同的权限和角色
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SaTokenStpInterfaceImpl implements StpInterface {

    private final IMenuService menuService;
    private final IRoleService roleService;

    /**
     * 获取权限列表
     * 根据登录ID和登录类型获取用户权限
     *
     * @param loginId 登录ID
     * @param loginType 登录类型
     * @return 权限列表
     */
    @Override
    public List<String> getPermissionList(Object loginId, String loginType) {
        log.debug("获取用户权限 - 登录ID: {}, 登录类型: {}", loginId, loginType);
        return menuService.listPermission(loginId);
    }

    /**
     * 获取角色列表
     * 根据登录ID和登录类型获取用户角色
     *
     * @param loginId 登录ID
     * @param loginType 登录类型
     * @return 角色列表
     */
    @Override
    public List<String> getRoleList(Object loginId, String loginType) {
        log.debug("获取用户角色 - 登录ID: {}, 登录类型: {}", loginId, loginType);
        return roleService.listRole(loginId);
    }
}
