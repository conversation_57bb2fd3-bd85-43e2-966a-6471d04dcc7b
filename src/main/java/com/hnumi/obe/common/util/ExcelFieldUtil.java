package com.hnumi.obe.common.util;

import com.hnumi.obe.common.entity.Columns;
import com.hnumi.obe.common.annotation.ExcelColumn;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 字段工具类
 * 提供对实体类字段的通用操作方法
 * 支持字段映射、字符串处理等功能
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
public final class ExcelFieldUtil {
    
    private ExcelFieldUtil() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * 字段映射缓存
     * key: 类名
     * value: 字段映射Map
     */
    private static final Map<String, Map<String, String>> columnFieldMapCache = new ConcurrentHashMap<>();
    
    /**
     * 字段列表缓存
     * key: 类名
     * value: 字段列表
     */
    private static final Map<String, List<Columns>> columnFieldListCache = new ConcurrentHashMap<>();

    /**
     * 根据实体类Class对象生成字段映射Map
     * 优先从缓存中获取，缓存未命中时进行解析
     *
     * @param clazz 实体类的Class对象
     * @return 中文字段名与变量名的映射集合
     */
    public static Map<String, String> getColumnFieldMap(Class<?> clazz) {
        if (clazz == null) {
            return new LinkedHashMap<>();
        }

        return columnFieldMapCache.computeIfAbsent(clazz.getName(), k -> {
            Map<String, String> columnMap = new LinkedHashMap<>();
            Field[] fields = clazz.getDeclaredFields();

            for (Field field : fields) {
                if (field.isAnnotationPresent(ExcelColumn.class)) {
                    ExcelColumn columnAnnotation = field.getAnnotation(ExcelColumn.class);
                    if (columnAnnotation != null && StringUtil.isNotBlank(columnAnnotation.value())) {
                        columnMap.put(columnAnnotation.value(), field.getName());
                    }
                }
            }
            
            log.debug("生成字段映射: class={}, size={}", clazz.getName(), columnMap.size());
            return columnMap;
        });
    }

    /**
     * 根据实体类Class对象生成字段列表
     * 优先从缓存中获取，缓存未命中时进行解析
     *
     * @param clazz 实体类的Class对象
     * @return 字段列表
     */
    public static List<Columns> getColumnFieldList(Class<?> clazz) {
        if (clazz == null) {
            throw new IllegalArgumentException("类对象不能为空");
        }

        return columnFieldListCache.computeIfAbsent(clazz.getName(), k -> {
            List<Columns> columns = new ArrayList<>();
            Field[] fields = clazz.getDeclaredFields();

            for (Field field : fields) {
                if (field.isAnnotationPresent(ExcelColumn.class)) {
                    ExcelColumn columnAnnotation = field.getAnnotation(ExcelColumn.class);
                    if (columnAnnotation != null) {
                        columns.add(new Columns()
                            .setProp(field.getName())
                            .setLabel(columnAnnotation.value()));
                    }
                }
            }
            
            log.debug("生成字段列表: class={}, size={}", clazz.getName(), columns.size());
            return columns;
        });
    }

    /**
     * 替换实体类中所有字符串字段的换行符为指定分隔符
     * 该方法会递归处理实体类及其父类中的所有String类型字段
     * 对于非String类型字段和静态字段会跳过处理
     *
     * @param entity 目标实体对象
     * @param separator 替换后的分隔符
     * @throws IllegalArgumentException 参数错误时抛出
     * @throws IllegalStateException 如果无法访问或修改字段值
     */
    public static void replaceNewLineWithSeparator(Object entity, String separator) {
        if (entity == null) {
            throw new IllegalArgumentException("实体对象不能为空");
        }
        if (separator == null) {
            throw new IllegalArgumentException("分隔符不能为空");
        }

        Class<?> clazz = entity.getClass();
        while (clazz != null && clazz != Object.class) {
            processNewLineFields(clazz, entity, separator);
            clazz = clazz.getSuperclass();
        }
    }

    /**
     * 处理类中的所有字段，替换换行符
     *
     * @param clazz 类对象
     * @param entity 实体对象
     * @param separator 替换后的分隔符
     */
    private static void processNewLineFields(Class<?> clazz, Object entity, String separator) {
        for (Field field : clazz.getDeclaredFields()) {
            if (Modifier.isStatic(field.getModifiers()) || field.getType() != String.class) {
                continue;
            }
            processNewLineField(field, entity, separator);
        }
    }

    /**
     * 处理单个字符串字段的换行符替换
     *
     * @param field 字段对象
     * @param entity 实体对象
     * @param separator 替换后的分隔符
     */
    private static void processNewLineField(Field field, Object entity, String separator) {
        try {
            field.setAccessible(true);
            String value = (String) field.get(entity);
            
            if (value != null) {
                String newValue = value.replaceAll("\\r\\n|\\r|\\n", separator);
                if (!value.equals(newValue)) {
                    field.set(entity, newValue);
                    log.debug("字段 {} 的换行符已被替换为 '{}'", field.getName(), separator);
                }
            }
        } catch (IllegalAccessException e) {
            String errorMessage = String.format("无法访问或修改字段 %s: %s", field.getName(), e.getMessage());
            log.error(errorMessage, e);
            throw new IllegalStateException(errorMessage, e);
        }
    }

    /**
     * 去除实体类中所有字符串字段的首尾空格
     * 该方法会递归处理实体类及其父类中的所有String类型字段
     * 对于非String类型字段和静态字段会跳过处理
     *
     * @param entity 目标实体对象
     * @throws IllegalArgumentException 参数错误时抛出
     * @throws IllegalStateException 如果无法访问或修改字段值
     */
    public static void trimStringFields(Object entity) {
        if (entity == null) {
            throw new IllegalArgumentException("实体对象不能为空");
        }

        Class<?> clazz = entity.getClass();
        while (clazz != null && clazz != Object.class) {
            processClassFields(clazz, entity);
            clazz = clazz.getSuperclass();
        }
    }

    /**
     * 处理类中的所有字段
     *
     * @param clazz 类对象
     * @param entity 实体对象
     */
    private static void processClassFields(Class<?> clazz, Object entity) {
        for (Field field : clazz.getDeclaredFields()) {
            if (Modifier.isStatic(field.getModifiers()) || field.getType() != String.class) {
                continue;
            }
            processStringField(field, entity);
        }
    }

    /**
     * 处理单个字符串字段
     *
     * @param field 字段对象
     * @param entity 实体对象
     */
    private static void processStringField(Field field, Object entity) {
        try {
            field.setAccessible(true);
            String value = (String) field.get(entity);
            
            if (value != null) {
                String trimmed = value.trim();
                if (!value.equals(trimmed)) {
                    field.set(entity, trimmed);
                    log.debug("字段 {} 的值已被修剪: '{}' -> '{}'", field.getName(), value, trimmed);
                }
            }
        } catch (IllegalAccessException e) {
            String errorMessage = String.format("无法访问或修改字段 %s: %s", field.getName(), e.getMessage());
            log.error(errorMessage, e);
            throw new IllegalStateException(errorMessage, e);
        }
    }

    /**
     * 替换实体类中所有字符串字段的值为指定字符串
     * 该方法会递归处理实体类及其父类中的所有String类型字段
     * 对于非String类型字段和静态字段会跳过处理
     *
     * @param entity 目标实体对象
     * @param replacement 要替换成的字符串
     * @throws IllegalArgumentException 参数错误时抛出
     * @throws IllegalStateException 如果无法访问或修改字段值
     */
    public static void replaceStringFields(Object entity, String replacement) {
        if (entity == null) {
            throw new IllegalArgumentException("实体对象不能为空");
        }
        if (replacement == null) {
            throw new IllegalArgumentException("替换字符串不能为空");
        }

        Class<?> clazz = entity.getClass();
        while (clazz != null && clazz != Object.class) {
            processReplacementFields(clazz, entity, replacement);
            clazz = clazz.getSuperclass();
        }
    }

    /**
     * 处理类中的所有字段，替换字符串值
     *
     * @param clazz 类对象
     * @param entity 实体对象
     * @param replacement 要替换成的字符串
     */
    private static void processReplacementFields(Class<?> clazz, Object entity, String replacement) {
        for (Field field : clazz.getDeclaredFields()) {
            if (Modifier.isStatic(field.getModifiers()) || field.getType() != String.class) {
                continue;
            }
            processReplacementField(field, entity, replacement);
        }
    }

    /**
     * 处理单个字符串字段的值替换
     *
     * @param field 字段对象
     * @param entity 实体对象
     * @param replacement 要替换成的字符串
     */
    private static void processReplacementField(Field field, Object entity, String replacement) {
        try {
            field.setAccessible(true);
            field.set(entity, replacement);
            log.debug("字段 {} 的值已被替换为 '{}'", field.getName(), replacement);
        } catch (IllegalAccessException e) {
            String errorMessage = String.format("无法访问或修改字段 %s: %s", field.getName(), e.getMessage());
            log.error(errorMessage, e);
            throw new IllegalStateException(errorMessage, e);
        }
    }

    /**
     * 清理字段映射缓存
     */
    public static void clearCache() {
        columnFieldMapCache.clear();
        columnFieldListCache.clear();
        log.info("字段映射缓存已清理");
    }

    /**
     * Excel字段元信息
     */
    static class ExcelColumnMeta {
        private String fieldName;
        private String columnName;
        private int index;
        private String pattern;
        private boolean ignoreCase;
        private boolean trim;
        private boolean required;
        private String defaultValue;
        private Class<?> converter;
        private boolean ignore;
        private int headerLevel;
        private String exportFormat;
        private int width;
        private String align;
        private Field field;
        private String[] scope;
        // getter/setter略，可用lombok或手写
        // 构造方法
        public ExcelColumnMeta(Field field, com.hnumi.obe.common.annotation.ExcelColumn ann) {
            this.field = field;
            this.fieldName = field != null ? field.getName() : "";
            this.columnName = ann != null ? ann.value() : "";
            this.index = ann != null ? ann.index() : -1;
            this.pattern = ann != null ? ann.pattern() : "";
            this.ignoreCase = ann != null && ann.ignoreCase();
            this.trim = ann != null && ann.trim();
            this.required = ann != null && ann.required();
            this.defaultValue = ann != null ? ann.defaultValue() : "";
            this.converter = ann != null ? ann.converter() : void.class;
            this.headerLevel = ann != null ? ann.headerLevel() : 1;
            this.exportFormat = ann != null ? ann.exportFormat() : "";
            this.width = ann != null ? ann.width() : -1;
            this.align = ann != null ? ann.align() : "left";
            this.scope = ann != null && ann.scope() != null ? ann.scope() : new String[0];
        }
        public String getFieldName() { return fieldName; }
        public String getColumnName() { return columnName; }
        public int getIndex() { return index; }
        public String getPattern() { return pattern; }
        public boolean isIgnoreCase() { return ignoreCase; }
        public boolean isTrim() { return trim; }
        public boolean isRequired() { return required; }
        public String getDefaultValue() { return defaultValue; }
        public Class<?> getConverter() { return converter; }
        public boolean isIgnore() { return ignore; }
        public int getHeaderLevel() { return headerLevel; }
        public String getExportFormat() { return exportFormat; }
        public int getWidth() { return width; }
        public String getAlign() { return align; }
        public Field getField() { return field; }
        public String[] getScope() { return scope; }
        /**
         * 判断该字段在指定场景（import/export）下是否生效
         */
        public boolean isInScope(String scene) {
            if (scope == null || scope.length == 0 || scene == null) return true;
            for (String s : scope) {
                if (scene.equalsIgnoreCase(s != null ? s.trim() : "")) return true;
            }
            return false;
        }
    }

    /**
     * 获取所有带ExcelColumn注解的字段元信息
     * @param clazz 实体类
     * @return 字段元信息列表
     */
    public static List<ExcelColumnMeta> getExcelColumnMetaList(Class<?> clazz) {
        List<ExcelColumnMeta> metaList = new ArrayList<>();
        if (clazz == null) return metaList;
        Field[] fields = clazz.getDeclaredFields();
        if (fields == null || fields.length == 0) return metaList;
        for (Field field : fields) {
            if (field.isAnnotationPresent(com.hnumi.obe.common.annotation.ExcelColumn.class)) {
                com.hnumi.obe.common.annotation.ExcelColumn ann = field.getAnnotation(com.hnumi.obe.common.annotation.ExcelColumn.class);
                metaList.add(new ExcelColumnMeta(field, ann));
            }
        }
        return metaList;
    }
}
