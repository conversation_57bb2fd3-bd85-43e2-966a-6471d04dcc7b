package com.hnumi.obe.common.util;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 教务系统应用示例
 * 演示如何在实际教务系统中使用课程学年学期转换器
 *
 * <AUTHOR>
 * @since 2025-07-22
 */
public class AcademicSystemExample {

    /**
     * 学生信息类
     */
    public static class Student {
        private String studentId;
        private String name;
        private Integer enrollmentYear;

        public Student(String studentId, String name, Integer enrollmentYear) {
            this.studentId = studentId;
            this.name = name;
            this.enrollmentYear = enrollmentYear;
        }

        // Getters
        public String getStudentId() { return studentId; }
        public String getName() { return name; }
        public Integer getEnrollmentYear() { return enrollmentYear; }
    }

    /**
     * 课程信息类
     */
    public static class Course {
        private String courseId;
        private String courseName;
        private Integer targetSemester; // 目标学期（1-8）

        public Course(String courseId, String courseName, Integer targetSemester) {
            this.courseId = courseId;
            this.courseName = courseName;
            this.targetSemester = targetSemester;
        }

        // Getters
        public String getCourseId() { return courseId; }
        public String getCourseName() { return courseName; }
        public Integer getTargetSemester() { return targetSemester; }
    }

    /**
     * 示例1：学生选课资格验证
     */
    public static void demonstrateCourseEligibility() {
        System.out.println("=== 学生选课资格验证示例 ===");

        // 创建测试学生
        List<Student> students = List.of(
            new Student("2023001", "张三", 2023),
            new Student("2022001", "李四", 2022),
            new Student("2021001", "王五", 2021)
        );

        // 创建测试课程
        List<Course> courses = List.of(
            new Course("CS101", "计算机基础", 1),
            new Course("CS201", "数据结构", 3),
            new Course("CS301", "算法设计", 5),
            new Course("CS401", "毕业设计", 8)
        );

        AcademicSemester currentSemester = AcademicSemesterUtil.getCurrentAcademicSemester();
        System.out.println("当前学期: " + currentSemester);
        System.out.println();

        for (Student student : students) {
            System.out.println("学生: " + student.getName() + " (入学年份: " + student.getEnrollmentYear() + ")");
            
            Integer currentCourseSemester = CourseAcademicSemesterConverter
                .getCurrentCourseSemester(student.getEnrollmentYear());
            
            if (currentCourseSemester == null) {
                if (CourseAcademicSemesterConverter.isGraduated(student.getEnrollmentYear())) {
                    System.out.println("  状态: 已毕业");
                } else {
                    System.out.println("  状态: 尚未入学");
                }
                continue;
            }

            System.out.println("  当前学期: 第" + currentCourseSemester + "学期");
            System.out.println("  可选课程:");

            for (Course course : courses) {
                boolean canSelect = currentCourseSemester >= course.getTargetSemester();
                String status = canSelect ? "✓ 可选" : "✗ 不可选";
                System.out.printf("    %s - %s: %s%n", 
                                 course.getCourseId(), course.getCourseName(), status);
            }
            System.out.println();
        }
    }

    /**
     * 示例2：课程开课计划生成
     */
    public static void demonstrateCourseScheduling() {
        System.out.println("=== 课程开课计划生成示例 ===");

        // 模拟不同入学年份的学生数量
        Map<Integer, Integer> enrollmentCounts = Map.of(
            2021, 150,  // 2021年入学150人
            2022, 180,  // 2022年入学180人
            2023, 200,  // 2023年入学200人
            2024, 220   // 2024年入学220人
        );

        // 定义课程及其目标学期
        List<Course> courses = List.of(
            new Course("CS101", "计算机基础", 1),
            new Course("CS102", "程序设计", 2),
            new Course("CS201", "数据结构", 3),
            new Course("CS202", "计算机组成", 4),
            new Course("CS301", "操作系统", 5),
            new Course("CS302", "数据库系统", 6),
            new Course("CS401", "软件工程", 7),
            new Course("CS402", "毕业设计", 8)
        );

        // 计算下学期的开课需求
        AcademicSemester currentSemester = AcademicSemesterUtil.getCurrentAcademicSemester();
        AcademicSemester nextSemester = AcademicSemesterUtil.getNextSemester(currentSemester);
        
        System.out.println("当前学期: " + currentSemester);
        System.out.println("下学期: " + nextSemester);
        System.out.println();
        System.out.println("下学期开课计划:");

        for (Course course : courses) {
            int totalStudents = 0;
            List<String> targetGroups = new ArrayList<>();

            for (Map.Entry<Integer, Integer> entry : enrollmentCounts.entrySet()) {
                Integer enrollmentYear = entry.getKey();
                Integer studentCount = entry.getValue();

                // 计算该入学年份的学生在下学期是第几学期
                Integer courseSemester = CourseAcademicSemesterConverter
                    .calculateCourseSemester(enrollmentYear, nextSemester);

                // 检查是否需要上这门课
                if (courseSemester != null && courseSemester.equals(course.getTargetSemester())) {
                    totalStudents += studentCount;
                    targetGroups.add(enrollmentYear + "级(" + studentCount + "人)");
                }
            }

            if (totalStudents > 0) {
                System.out.printf("%s - %s:%n", course.getCourseId(), course.getCourseName());
                System.out.printf("  目标学生: %s%n", String.join(", ", targetGroups));
                System.out.printf("  预计人数: %d人%n", totalStudents);
                System.out.printf("  建议班级数: %d个班%n", (totalStudents + 49) / 50); // 每班50人
                System.out.println();
            }
        }
    }

    /**
     * 示例3：学生学业进度跟踪
     */
    public static void demonstrateProgressTracking() {
        System.out.println("=== 学生学业进度跟踪示例 ===");

        Student student = new Student("2022001", "李四", 2022);
        System.out.println("学生: " + student.getName() + " (入学年份: " + student.getEnrollmentYear() + ")");
        System.out.println();

        // 获取学生的完整学期规划
        AcademicSemester[] allSemesters = CourseAcademicSemesterConverter
            .getAllCourseSemesters(student.getEnrollmentYear());

        Integer currentCourseSemester = CourseAcademicSemesterConverter
            .getCurrentCourseSemester(student.getEnrollmentYear());

        System.out.println("完整学期规划:");
        for (int i = 0; i < allSemesters.length; i++) {
            int semesterNumber = i + 1;
            AcademicSemester semester = allSemesters[i];
            
            String status;
            if (currentCourseSemester == null) {
                status = " (已毕业)";
            } else if (semesterNumber < currentCourseSemester) {
                status = " (已完成)";
            } else if (semesterNumber == currentCourseSemester) {
                status = " (当前学期) ←";
            } else {
                status = " (未来学期)";
            }

            System.out.printf("  第%d学期: %s%s%n", semesterNumber, semester, status);
        }

        System.out.println();

        // 计算学业完成度
        if (currentCourseSemester != null) {
            double progress = (currentCourseSemester - 1) * 100.0 / 8;
            System.out.printf("学业完成度: %.1f%% (%d/8学期)%n", progress, currentCourseSemester - 1);
        } else if (CourseAcademicSemesterConverter.isGraduated(student.getEnrollmentYear())) {
            System.out.println("学业完成度: 100.0% (已毕业)");
        }

        System.out.println();
    }

    /**
     * 示例4：教学资源分配
     */
    public static void demonstrateResourceAllocation() {
        System.out.println("=== 教学资源分配示例 ===");

        // 模拟当前学期各年级的课程需求
        AcademicSemester currentSemester = AcademicSemesterUtil.getCurrentAcademicSemester();
        System.out.println("当前学期: " + currentSemester);
        System.out.println();

        Map<Integer, String> semesterCourses = Map.of(
            1, "计算机基础、高等数学",
            2, "程序设计、线性代数", 
            3, "数据结构、概率统计",
            4, "计算机组成、离散数学",
            5, "操作系统、编译原理",
            6, "数据库系统、计算机网络",
            7, "软件工程、人工智能",
            8, "毕业设计、专业实习"
        );

        System.out.println("当前学期各年级课程安排:");
        
        for (int enrollmentYear = 2021; enrollmentYear <= 2024; enrollmentYear++) {
            Integer courseSemester = CourseAcademicSemesterConverter
                .calculateCourseSemester(enrollmentYear, currentSemester);

            if (courseSemester != null && courseSemester >= 1 && courseSemester <= 8) {
                String courses = semesterCourses.get(courseSemester);
                System.out.printf("  %d级学生 (第%d学期): %s%n", 
                                 enrollmentYear, courseSemester, courses);
            } else if (courseSemester != null && courseSemester > 8) {
                System.out.printf("  %d级学生: 已毕业%n", enrollmentYear);
            } else {
                System.out.printf("  %d级学生: 尚未入学%n", enrollmentYear);
            }
        }

        System.out.println();
    }

    /**
     * 示例5：毕业审核系统
     */
    public static void demonstrateGraduationAudit() {
        System.out.println("=== 毕业审核系统示例 ===");

        // 模拟即将毕业的学生
        List<Student> graduatingStudents = List.of(
            new Student("2021001", "王五", 2021),
            new Student("2021002", "赵六", 2021)
        );

        for (Student student : graduatingStudents) {
            System.out.println("学生: " + student.getName() + " (入学年份: " + student.getEnrollmentYear() + ")");

            // 检查是否已完成第8学期
            AcademicSemester eighthSemester = CourseAcademicSemesterConverter
                .getExecutionSemester(student.getEnrollmentYear(), 8);
            
            AcademicSemester currentSemester = AcademicSemesterUtil.getCurrentAcademicSemester();
            
            boolean hasCompletedEighthSemester = AcademicSemesterUtil
                .compareSemesters(currentSemester, eighthSemester) > 0;

            System.out.println("  第8学期时间: " + eighthSemester);
            System.out.println("  是否完成第8学期: " + (hasCompletedEighthSemester ? "是" : "否"));

            if (hasCompletedEighthSemester) {
                System.out.println("  毕业审核状态: ✓ 符合毕业条件");
            } else {
                System.out.println("  毕业审核状态: ✗ 尚未完成学业");
            }

            System.out.println();
        }
    }

    /**
     * 主方法，运行所有示例
     */
    public static void main(String[] args) {
        demonstrateCourseEligibility();
        demonstrateCourseScheduling();
        demonstrateProgressTracking();
        demonstrateResourceAllocation();
        demonstrateGraduationAudit();
    }
}
