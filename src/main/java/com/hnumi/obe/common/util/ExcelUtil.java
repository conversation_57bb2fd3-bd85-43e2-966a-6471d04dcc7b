package com.hnumi.obe.common.util;

import cn.hutool.poi.excel.ExcelReader;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Map;
import com.hnumi.obe.common.annotation.ExcelSheet;
import com.hnumi.obe.common.util.ExcelFieldUtil.ExcelColumnMeta;
import java.lang.reflect.Field;
import java.util.ArrayList;
import com.hnumi.obe.common.annotation.ExcelColumnConverter;
import java.util.LinkedHashMap;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.CellReference;

/**
 * Excel工具类
 * 用于读取Excel文件并转换为Java对象
 * 支持通过字段别名映射进行数据转换
 *
 * <AUTHOR>
 */
@Slf4j
public final class ExcelUtil {
    
    private ExcelUtil() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * 读取Excel文件并转换为指定类型的对象列表
     *
     * @param is Excel文件输入流
     * @param clazz 目标对象类型
     * @param <T> 目标对象泛型
     * @return 对象列表
     * @throws IllegalArgumentException 参数错误时抛出
     * @throws RuntimeException Excel读取失败时抛出
     */
    public static <T> List<T> readAll(InputStream is, Class<T> clazz) {
        if (is == null || clazz == null) {
            log.warn("readAll参数为空: is={}, clazz={}", is, clazz);
            return new ArrayList<>();
        }
        try {
            // 创建Excel读取器
            ExcelReader reader = cn.hutool.poi.excel.ExcelUtil.getReader(is);
            
            // 获取字段别名映射
            Map<String, String> alias = ExcelFieldUtil.getColumnFieldMap(clazz);
            if (alias == null || alias.isEmpty()) {
                log.warn("未找到字段别名映射: class={}", clazz.getName());
            }
            
            // 设置字段别名
            reader.setHeaderAlias(alias);
            
            // 读取数据
            List<T> result = reader.readAll(clazz);
            if (result == null) return new ArrayList<>();
            log.debug("Excel读取成功: class={}, size={}", clazz.getName(), result.size());
            return result;
            
        } catch (Exception e) {
            log.error("Excel读取失败: class={}", clazz != null ? clazz.getName() : "null", e);
            return new ArrayList<>();
        } finally {
            try { if (is != null) is.close(); } catch (IOException e) { log.warn("关闭输入流失败", e); }
        }
    }

    /**
     * Excel导入结果封装
     */
    public static class ExcelImportResult<T> {
        private List<T> data;
        private List<String> errors;
        public ExcelImportResult(List<T> data, List<String> errors) {
            this.data = data;
            this.errors = errors;
        }
        public List<T> getData() { return data; }
        public List<String> getErrors() { return errors; }
        public boolean hasError() { return errors != null && !errors.isEmpty(); }
    }

    /**
     * 导出对象列表到Excel（支持注解驱动的导出顺序、格式、样式、多级表头）
     * @param dataList 数据列表
     * @param clazz 数据类型
     * @param <T> 泛型
     * @return hutool ExcelWriter对象（可进一步自定义写入到流等）
     */
    public static <T> cn.hutool.poi.excel.ExcelWriter export(List<T> dataList, Class<T> clazz) {
        if (dataList == null || clazz == null) {
            log.warn("export参数为空: dataList={}, clazz={}", dataList, clazz);
            return cn.hutool.poi.excel.ExcelUtil.getWriter(true);
        }
        List<ExcelFieldUtil.ExcelColumnMeta> metaList = ExcelFieldUtil.getExcelColumnMetaList(clazz);
        if (metaList == null || metaList.isEmpty()) {
            log.warn("未找到字段元信息: class={}", clazz.getName());
        }
        // 构建表头顺序和样式
        List<String> headers = new ArrayList<>();
        Map<String, String> alias = new LinkedHashMap<>();
        for (ExcelFieldUtil.ExcelColumnMeta meta : metaList) {
            if (!meta.isInScope("export")) continue;
            headers.add(meta.getColumnName());
            alias.put(meta.getFieldName(), meta.getColumnName());
        }
        cn.hutool.poi.excel.ExcelWriter writer = cn.hutool.poi.excel.ExcelUtil.getWriter(true);
        writer.setHeaderAlias(alias);
        // 设置列宽、对齐等样式
        int colIdx = 0;
        for (ExcelFieldUtil.ExcelColumnMeta meta : metaList) {
            if (!meta.isInScope("export")) continue;
            if (meta.getWidth() > 0) {
                writer.setColumnWidth(colIdx, meta.getWidth());
            }
            // TODO: 设置对齐、格式化等
            colIdx++;
        }
        // 数据转换与格式化
        List<Map<String, Object>> exportList = new ArrayList<>();
        for (T obj : dataList) {
            Map<String, Object> row = new LinkedHashMap<>();
            for (ExcelFieldUtil.ExcelColumnMeta meta : metaList) {
                if (!meta.isInScope("export")) continue;
                try {
                    Field field = meta.getField();
                    field.setAccessible(true);
                    Object value = null;
                    try {
                        value = field.get(obj);
                    } catch (Exception e) {
                        log.warn("导出反射获取字段异常: {}.{}", clazz.getSimpleName(), meta.getFieldName(), e);
                    }
                    // 调用自定义转换器
                    if (meta.getConverter() != void.class) {
                        try {
                            com.hnumi.obe.common.annotation.ExcelColumnConverter converter = (com.hnumi.obe.common.annotation.ExcelColumnConverter) meta.getConverter().getDeclaredConstructor().newInstance();
                            value = converter.convertToExcel(value);
                        } catch (Exception e) {
                            log.warn("导出自定义转换器异常: {}.{}", clazz.getSimpleName(), meta.getFieldName(), e);
                        }
                    }
                    // TODO: 格式化处理（如日期、数值等）
                    row.put(meta.getFieldName(), value);
                } catch (Exception e) {
                    log.warn("导出字段处理异常: {}.{}", clazz.getSimpleName(), meta.getFieldName(), e);
                    row.put(meta.getFieldName(), null);
                }
            }
            exportList.add(row);
        }
        writer.write(exportList, true);
        return writer;
    }

    /**
     * 使用配置对象导出Excel（支持自定义表头、表头注释、单元格锁定格式等）
     * @param config 导出配置对象
     * @return hutool ExcelWriter对象
     */
    public static cn.hutool.poi.excel.ExcelWriter export(ExcelExportConfig config) {
        if (config == null || !config.isValid()) {
            log.warn("导出配置无效: config={}", config);
            return cn.hutool.poi.excel.ExcelUtil.getWriter(true);
        }
        
        try {
            // 创建Excel写入器
            cn.hutool.poi.excel.ExcelWriter writer = cn.hutool.poi.excel.ExcelUtil.getWriter(true);
            
            // 设置工作表名称
            if (config.getSheetName() != null && !config.getSheetName().trim().isEmpty()) {
                writer.setSheet(config.getSheetName());
            }
            
            // 处理数据导出
            if (config.getDataClass() != null) {
                // 使用注解驱动的导出
                writer = exportWithAnnotation(writer, config);
            } else {
                // 使用自定义表头的导出
                writer = exportWithCustomHeaders(writer, config);
            }
            
            // 处理单元格锁定格式
            if (config.getUnlockRange() != null && !config.getUnlockRange().trim().isEmpty()) {
                unlockCellRange(writer, config.getUnlockRange(), config.getProtectionPassword());
            }
            
            // 处理表头注释
            if (config.getHeaderComments() != null && !config.getHeaderComments().isEmpty()) {
                addHeaderComments(writer, config.getHeaderComments());
            }
            
            // 自动调整列宽
            if (config.isAutoSize()) {
                writer.autoSizeColumnAll();
            }
            
            log.debug("Excel导出成功: 数据量={}, 配置={}", 
                config.getDataList() != null ? config.getDataList().size() : 0, config);
            return writer;
            
        } catch (Exception e) {
            log.error("Excel导出失败: config={}", config, e);
            return cn.hutool.poi.excel.ExcelUtil.getWriter(true);
        }
    }

    /**
     * 使用注解驱动的导出
     */
    private static cn.hutool.poi.excel.ExcelWriter exportWithAnnotation(cn.hutool.poi.excel.ExcelWriter writer, ExcelExportConfig config) {
        List<ExcelFieldUtil.ExcelColumnMeta> metaList = ExcelFieldUtil.getExcelColumnMetaList(config.getDataClass());
        if (metaList == null || metaList.isEmpty()) {
            log.warn("未找到字段元信息: class={}", config.getDataClass().getName());
            return writer;
        }
        
        // 构建表头顺序和样式
        List<String> headers = new ArrayList<>();
        Map<String, String> alias = new LinkedHashMap<>();
        for (ExcelFieldUtil.ExcelColumnMeta meta : metaList) {
            if (!meta.isInScope("export")) continue;
            headers.add(meta.getColumnName());
            alias.put(meta.getFieldName(), meta.getColumnName());
        }
        
        writer.setHeaderAlias(alias);
        
        // 设置列宽、对齐等样式
        int colIdx = 0;
        for (ExcelFieldUtil.ExcelColumnMeta meta : metaList) {
            if (!meta.isInScope("export")) continue;
            if (meta.getWidth() > 0) {
                writer.setColumnWidth(colIdx, meta.getWidth());
            }
            colIdx++;
        }
        
        // 数据转换与格式化
        List<Map<String, Object>> exportList = new ArrayList<>();
        for (Object obj : config.getDataList()) {
            Map<String, Object> row = new LinkedHashMap<>();
            for (ExcelFieldUtil.ExcelColumnMeta meta : metaList) {
                if (!meta.isInScope("export")) continue;
                try {
                    Field field = meta.getField();
                    field.setAccessible(true);
                    Object value = null;
                    try {
                        value = field.get(obj);
                    } catch (Exception e) {
                        log.warn("导出反射获取字段异常: {}.{}", config.getDataClass().getSimpleName(), meta.getFieldName(), e);
                    }
                    // 调用自定义转换器
                    if (meta.getConverter() != void.class) {
                        try {
                            com.hnumi.obe.common.annotation.ExcelColumnConverter converter = (com.hnumi.obe.common.annotation.ExcelColumnConverter) meta.getConverter().getDeclaredConstructor().newInstance();
                            value = converter.convertToExcel(value);
                        } catch (Exception e) {
                            log.warn("导出自定义转换器异常: {}.{}", config.getDataClass().getSimpleName(), meta.getFieldName(), e);
                        }
                    }
                    row.put(meta.getFieldName(), value);
                } catch (Exception e) {
                    log.warn("导出字段处理异常: {}.{}", config.getDataClass().getSimpleName(), meta.getFieldName(), e);
                    row.put(meta.getFieldName(), null);
                }
            }
            exportList.add(row);
        }
        
        writer.write(exportList, config.isIncludeHeader());
        return writer;
    }

    /**
     * 使用自定义表头的导出
     */
    private static cn.hutool.poi.excel.ExcelWriter exportWithCustomHeaders(cn.hutool.poi.excel.ExcelWriter writer, ExcelExportConfig config) {
        // 如果有自定义表头，使用自定义表头
        if (config.getHeaders() != null && !config.getHeaders().isEmpty()) {
            writer.addHeaderAlias("header", "表头");
            List<Map<String, Object>> exportList = new ArrayList<>();
            for (Object obj : config.getDataList()) {
                Map<String, Object> row = new LinkedHashMap<>();
                // 这里需要根据实际数据结构进行处理
                // 暂时使用toString()方法
                row.put("header", obj.toString());
                exportList.add(row);
            }
            writer.write(exportList, config.isIncludeHeader());
        } else {
            // 没有自定义表头，直接写入数据
            writer.write(config.getDataList(), config.isIncludeHeader());
        }
        return writer;
    }

    /**
     * 取消指定范围的单元格锁定
     * @param writer Excel写入器
     * @param range 单元格范围，如"A1:D2"
     * @param password 保护密码，为空则不设置密码
     */
    private static void unlockCellRange(cn.hutool.poi.excel.ExcelWriter writer, String range, String password) {
        try {
            Sheet sheet = writer.getSheet();
            if (sheet == null) {
                log.warn("无法获取工作表，跳过单元格锁定设置");
                return;
            }
            
            Workbook workbook = sheet.getWorkbook();
            
            // 创建锁定和解锁的样式
            CellStyle lockedStyle = workbook.createCellStyle();
            lockedStyle.setLocked(true);
            
            CellStyle unlockedStyle = workbook.createCellStyle();
            unlockedStyle.setLocked(false);
            
            // 解析单元格范围
            CellRangeAddress cellRange = CellRangeAddress.valueOf(range);
            int firstRow = cellRange.getFirstRow();
            int lastRow = cellRange.getLastRow();
            int firstCol = cellRange.getFirstColumn();
            int lastCol = cellRange.getLastColumn();
            
            // 获取工作表的实际数据范围
            int lastDataRow = sheet.getLastRowNum();
            int lastDataCol = 0;
            for (int i = 0; i <= lastDataRow; i++) {
                Row row = sheet.getRow(i);
                if (row != null) {
                    lastDataCol = Math.max(lastDataCol, row.getLastCellNum());
                }
            }
            
            // 为所有单元格设置锁定样式（默认状态）
            for (int rowNum = 0; rowNum <= lastDataRow; rowNum++) {
                Row row = sheet.getRow(rowNum);
                if (row != null) {
                    for (int colNum = 0; colNum < lastDataCol; colNum++) {
                        Cell cell = row.getCell(colNum);
                        if (cell != null) {
                            // 检查是否在解锁范围内
                            boolean inUnlockRange = (rowNum >= firstRow && rowNum <= lastRow && 
                                                   colNum >= firstCol && colNum <= lastCol);
                            
                            if (inUnlockRange) {
                                // 在解锁范围内，设置解锁样式
                                cell.setCellStyle(unlockedStyle);
                            } else {
                                // 不在解锁范围内，设置锁定样式
                                cell.setCellStyle(lockedStyle);
                            }
                        }
                    }
                }
            }
            
            // 启用工作表保护
            if (password != null && !password.trim().isEmpty()) {
                sheet.protectSheet(password);
            } else {
                sheet.protectSheet(""); // 无密码保护
            }
            
            log.debug("成功设置单元格锁定: 解锁范围={}, 保护已启用", range);
            
        } catch (Exception e) {
            log.warn("设置单元格锁定失败: 范围={}", range, e);
        }
    }

    /**
     * 添加表头注释
     * @param writer Excel写入器
     * @param headerComments 表头注释映射
     */
    private static void addHeaderComments(cn.hutool.poi.excel.ExcelWriter writer, Map<String, String> headerComments) {
        try {
            Sheet sheet = writer.getSheet();
            if (sheet == null) {
                log.warn("无法获取工作表，跳过表头注释设置");
                return;
            }
            
            for (Map.Entry<String, String> entry : headerComments.entrySet()) {
                String key = entry.getKey();
                String comment = entry.getValue();
                
                try {
                    int colIndex;
                    if (key.matches("\\d+")) {
                        // 数字索引
                        colIndex = Integer.parseInt(key);
                    } else {
                        // 列名，需要转换为索引
                        colIndex = CellReference.convertColStringToIndex(key);
                    }
                    
                    // 获取第一行（表头行）
                    Row headerRow = sheet.getRow(0);
                    if (headerRow != null) {
                        Cell cell = headerRow.getCell(colIndex);
                        if (cell != null) {
                            // 创建注释
                            org.apache.poi.ss.usermodel.Comment cellComment = sheet.createDrawingPatriarch().createCellComment(
                                sheet.getWorkbook().getCreationHelper().createClientAnchor()
                            );
                            cellComment.setString(sheet.getWorkbook().getCreationHelper().createRichTextString(comment));
                            cell.setCellComment(cellComment);
                        }
                    }
                } catch (Exception e) {
                    log.warn("添加表头注释失败: 列={}, 注释={}", key, comment, e);
                }
            }
            
            log.debug("成功添加表头注释: 数量={}", headerComments.size());
            
        } catch (Exception e) {
            log.warn("添加表头注释失败", e);
        }
    }

    /**
     * 便捷的导出方法，使用建造者模式
     * @param dataList 数据列表
     * @param clazz 数据类型
     * @param <T> 泛型
     * @return ExcelExportBuilder实例
     */
    public static <T> ExcelExportBuilder exportBuilder(List<T> dataList, Class<T> clazz) {
        return ExcelExportBuilder.create()
                .setData(dataList)
                .setDataClass(clazz);
    }

    /**
     * 便捷的导出方法，使用建造者模式（无类型信息）
     * @param dataList 数据列表
     * @return ExcelExportBuilder实例
     */
    public static ExcelExportBuilder exportBuilder(List<?> dataList) {
        return ExcelExportBuilder.create()
                .setData(dataList);
    }

    /**
     * 多Sheet批量导入，支持注解驱动和API映射
     * @param is Excel文件输入流
     * @param sheetDtoMap Sheet名/索引与DTO类型的映射（如Map<String,Class<?>>或Map<Integer,Class<?>>）
     * @return Map<SheetKey, ExcelImportResult<?>>
     */
    public static Map<Object, ExcelImportResult<?>> readMultiSheet(InputStream is, Map<Object, Class<?>> sheetDtoMap) {
        Map<Object, ExcelImportResult<?>> resultMap = new LinkedHashMap<>();
        try {
            for (Map.Entry<Object, Class<?>> entry : sheetDtoMap.entrySet()) {
                Object key = entry.getKey();
                Class<?> dtoClass = entry.getValue();
                cn.hutool.poi.excel.ExcelReader sheetReader;
                if (key instanceof String) {
                    sheetReader = cn.hutool.poi.excel.ExcelUtil.getReader(is, (String) key);
                } else if (key instanceof Integer) {
                    sheetReader = cn.hutool.poi.excel.ExcelUtil.getReader(is, (Integer) key);
                } else {
                    continue;
                }
                ExcelImportResult<?> result = readAllWithAnnotationAndError(sheetReader, dtoClass);
                resultMap.put(key, result);
            }
        } catch (Exception e) {
            // 全局异常
        }
        return resultMap;
    }

    /**
     * 重载：直接用ExcelReader和Class导入，便于多Sheet批量导入
     */
    public static <T> ExcelImportResult<T> readAllWithAnnotationAndError(cn.hutool.poi.excel.ExcelReader reader, Class<T> clazz) {
        if (reader == null || clazz == null) {
            throw new IllegalArgumentException("ExcelReader或目标类型不能为空");
        }
        ExcelSheet sheetAnn = clazz.getAnnotation(ExcelSheet.class);
        int startRow = sheetAnn != null ? sheetAnn.startRow() : 1;
        int endRow = sheetAnn != null ? sheetAnn.endRow() : -1;
        int limit = sheetAnn != null ? sheetAnn.limit() : -1;
        List<String> errorList = new ArrayList<>();
        try {
            // 字段别名映射
            List<ExcelColumnMeta> metaList = ExcelFieldUtil.getExcelColumnMetaList(clazz);
            Map<String, String> alias = new java.util.LinkedHashMap<>();
            for (ExcelColumnMeta meta : metaList) {
                if (!meta.isInScope("import")) continue;
                alias.put(meta.getColumnName(), meta.getFieldName());
            }
            reader.setHeaderAlias(alias);
            List<T> all = reader.readAll(clazz);
            List<T> result = new ArrayList<>();
            int count = 0;
            for (int i = 0; i < all.size(); i++) {
                int rowNum = startRow + i;
                if (i + 1 < startRow) continue;
                if (endRow > 0 && rowNum > endRow) break;
                if (limit > 0 && count >= limit) break;
                T obj = all.get(i);
                for (ExcelColumnMeta meta : metaList) {
                    if (!meta.isInScope("import")) continue;
                    try {
                        Field field = meta.getField();
                        field.setAccessible(true);
                        Object value = field.get(obj);
                        if (meta.getConverter() != void.class) {
                            ExcelColumnConverter converter = (ExcelColumnConverter) meta.getConverter().getDeclaredConstructor().newInstance();
                            value = converter.convertToJava(value);
                            field.set(obj, value);
                        }
                        if (value != null) {
                            Class<?> targetType = field.getType();
                            if (!targetType.isInstance(value)) {
                                Object converted = null;
                                try {
                                    if (targetType == String.class) {
                                        converted = value.toString();
                                    } else if (targetType == Integer.class || targetType == int.class) {
                                        converted = Integer.parseInt(value.toString());
                                    } else if (targetType == Long.class || targetType == long.class) {
                                        converted = Long.parseLong(value.toString());
                                    } else if (targetType == Double.class || targetType == double.class) {
                                        converted = Double.parseDouble(value.toString());
                                    } else if (targetType == Boolean.class || targetType == boolean.class) {
                                        converted = Boolean.parseBoolean(value.toString());
                                    } else {
                                        throw new Exception("不支持的类型转换");
                                    }
                                    field.set(obj, converted);
                                } catch (Exception convEx) {
                                    errorList.add(String.format("第%d行，字段[%s]类型不匹配，期望%s，实际值[%s]，已置空", rowNum, meta.getColumnName(), targetType.getSimpleName(), value));
                                    field.set(obj, null);
                                }
                            }
                        }
                        value = field.get(obj);
                        if (value == null || (value instanceof String && ((String) value).isEmpty())) {
                            if (meta.isRequired()) {
                                errorList.add(String.format("第%d行，必填字段[%s]缺失", rowNum, meta.getColumnName()));
                            }
                            if (!meta.getDefaultValue().isEmpty()) {
                                field.set(obj, meta.getDefaultValue());
                            }
                        }
                        if (value instanceof String && meta.isTrim()) {
                            String trimmed = ((String) value).trim();
                            if (!trimmed.equals(value)) {
                                field.set(obj, trimmed);
                            }
                        }
                    } catch (Exception e) {
                        errorList.add(String.format("第%d行，字段[%s]处理异常：%s", rowNum, meta.getColumnName(), e.getMessage()));
                    }
                }
                result.add(obj);
                count++;
            }
            log.debug("Excel读取成功: class={}, size={}", clazz.getName(), result.size());
            return new ExcelImportResult<>(result, errorList);
        } catch (Exception e) {
            log.error("Excel读取失败: class={}", clazz.getName(), e);
            errorList.add("Excel读取失败: " + e.getMessage());
            return new ExcelImportResult<>(new ArrayList<>(), errorList);
        }
    }
}
