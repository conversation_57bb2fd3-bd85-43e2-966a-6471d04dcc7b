package com.hnumi.obe.common.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 集合工具类
 * 提供集合相关的常用操作方法
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class CollectionUtil extends CollUtil {

    public  static <E, T> List<T> convert(List<E> list, Function<E, T> fun) {
        return list.stream().map(fun).collect(Collectors.toList());
    }

    /**
     * 判断集合是否为空
     *
     * @param collection 集合
     * @return 是否为空
     */
    public static boolean isEmpty(Collection<?> collection) {
        return collection == null || collection.isEmpty();
    }

    /**
     * 判断Map是否为空
     *
     * @param map Map
     * @return 是否为空
     */
    public static boolean isEmpty(Map<?, ?> map) {
        return map == null || map.isEmpty();
    }

    /**
     * 判断数组是否为空
     *
     * @param array 数组
     * @param <T> 数组元素类型
     * @return 是否为空
     */
    public static <T> boolean isEmpty(T[] array) {
        return array == null || array.length == 0;
    }

    /**
     * 判断集合是否不为空
     *
     * @param collection 集合
     * @return 是否不为空
     */
    public static boolean isNotEmpty(Collection<?> collection) {
        return !isEmpty(collection);
    }

    /**
     * 判断Map是否不为空
     *
     * @param map Map
     * @return 是否不为空
     */
    public static boolean isNotEmpty(Map<?, ?> map) {
        return !isEmpty(map);
    }

    /**
     * 判断数组是否不为空
     *
     * @param array 数组
     * @param <T> 数组元素类型
     * @return 是否不为空
     */
    public static <T> boolean isNotEmpty(T[] array) {
        return !isEmpty(array);
    }

    /**
     * 判断数组中是否包含指定对象
     *
     * @param array 数组
     * @param element 要查找的对象
     * @param <T> 数组元素类型
     * @return 是否包含
     */
    public static <T> boolean contains(T[] array, T element) {
        if (isEmpty(array)) {
            return false;
        }
        for (T t : array) {
            if (ObjectUtil.equal(t, element)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判断数组中是否包含指定对象（忽略大小写，仅适用于字符串数组）
     *
     * @param array 字符串数组
     * @param element 要查找的字符串
     * @return 是否包含
     */
    public static boolean containsIgnoreCase(String[] array, String element) {
        if (isEmpty(array)) {
            return false;
        }
        for (String str : array) {
            if (StrUtil.equalsIgnoreCase(str, element)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判断数组中是否包含所有指定对象
     *
     * @param array 数组
     * @param elements 要查找的对象数组
     * @param <T> 数组元素类型
     * @return 是否包含所有
     */
    public static <T> boolean containsAll(T[] array, T[] elements) {
        if (isEmpty(array) || isEmpty(elements)) {
            return false;
        }
        for (T element : elements) {
            if (!contains(array, element)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 判断数组中是否包含任意一个指定对象
     *
     * @param array 数组
     * @param elements 要查找的对象数组
     * @param <T> 数组元素类型
     * @return 是否包含任意一个
     */
    public static <T> boolean containsAny(T[] array, T[] elements) {
        if (isEmpty(array) || isEmpty(elements)) {
            return false;
        }
        for (T element : elements) {
            if (contains(array, element)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判断数组中是否不包含任何指定对象
     *
     * @param array 数组
     * @param elements 要查找的对象数组
     * @param <T> 数组元素类型
     * @return 是否不包含任何
     */
    public static <T> boolean containsNone(T[] array, T[] elements) {
        if (isEmpty(array) || isEmpty(elements)) {
            return true;
        }
        for (T element : elements) {
            if (contains(array, element)) {
                return false;
            }
        }
        return true;
    }
}
