package com.hnumi.obe.common.util;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * 课程学年学期转换器
 * 提供入学年份与课程执行学期之间的转换功能
 * 
 * 学期规律说明：
 * - 学生通常在秋季学期（9月）入学
 * - 课程执行学期1-8对应大学四年的8个学期
 * - 第1学期：入学年份秋季学期
 * - 第2学期：入学年份+1春季学期
 * - 第3学期：入学年份+1秋季学期
 * - 第4学期：入学年份+2春季学期
 * - ...以此类推
 *
 * <AUTHOR>
 * @since 2025-07-22
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class CourseAcademicSemesterConverter {

    /**
     * 最小课程执行学期
     */
    private static final int MIN_COURSE_SEMESTER = 1;

    /**
     * 最大课程执行学期
     */
    private static final int MAX_COURSE_SEMESTER = 8;

    /**
     * 根据入学年份和课程执行学期获取执行年份和学期信息
     *
     * @param enrollmentYear    入学年份（如2023表示2023年9月入学）
     * @param courseSemester    课程执行学期（1-8）
     * @return 课程执行的学年学期信息
     */
    public static AcademicSemester getExecutionSemester(Integer enrollmentYear, Integer courseSemester) {
        if (enrollmentYear == null || courseSemester == null) {
            return null;
        }

        validateCourseSemester(courseSemester);

        // 计算执行年份和月份
        int executionYear;
        int executionMonth;

        if (courseSemester % 2 == 1) {
            // 奇数学期为秋季学期（9月）
            int yearOffset = (courseSemester - 1) / 2;
            executionYear = enrollmentYear + yearOffset;
            executionMonth = 9; // 秋季学期
        } else {
            // 偶数学期为春季学期（3月）
            int yearOffset = courseSemester / 2;
            executionYear = enrollmentYear + yearOffset;
            executionMonth = 3; // 春季学期
        }

        return AcademicSemesterUtil.getAcademicSemester(executionYear, executionMonth);
    }

    /**
     * 根据执行年份和执行学期获取可能的入学年份信息
     * 注意：由于一个执行学期可能对应多个不同的课程学期，此方法返回所有可能的入学年份
     *
     * @param executionYear     执行年份
     * @param executionSemester 执行学期（1-春季，2-秋季）
     * @return 所有可能的入学年份学年学期信息列表
     */
    public static List<AcademicSemester> getPossibleEnrollmentSemesters(Integer executionYear, Integer executionSemester) {
        if (executionYear == null || executionSemester == null) {
            return new ArrayList<>();
        }

        validateExecutionSemester(executionSemester);

        List<AcademicSemester> possibleEnrollments = new ArrayList<>();

        // 根据执行学期类型，计算所有可能的课程学期
        if (executionSemester == 1) {
            // 春季学期：可能是第2,4,6,8学期
            for (int courseSemester = 2; courseSemester <= 8; courseSemester += 2) {
                Integer enrollmentYear = calculateEnrollmentYear(executionYear, executionSemester, courseSemester);
                possibleEnrollments.add(AcademicSemesterUtil.getAcademicSemester(enrollmentYear, 9));
            }
        } else {
            // 秋季学期：可能是第1,3,5,7学期
            for (int courseSemester = 1; courseSemester <= 7; courseSemester += 2) {
                Integer enrollmentYear = calculateEnrollmentYear(executionYear, executionSemester, courseSemester);
                possibleEnrollments.add(AcademicSemesterUtil.getAcademicSemester(enrollmentYear, 9));
            }
        }

        return possibleEnrollments;
    }

    /**
     * 根据执行年份、执行学期和课程学期获取入学年份信息
     *
     * @param executionYear     执行年份
     * @param executionSemester 执行学期（1-春季，2-秋季）
     * @param courseSemester    课程执行学期（1-8）
     * @return 入学年份的学年学期信息
     */
    public static AcademicSemester getEnrollmentSemester(Integer executionYear, Integer executionSemester,
                                                        Integer courseSemester) {
        if (executionYear == null || executionSemester == null || courseSemester == null) {
            return null;
        }

        validateCourseSemester(courseSemester);
        validateExecutionSemester(executionSemester);

        // 验证执行学期和课程学期的匹配性
        if ((executionSemester == 1 && courseSemester % 2 != 0) ||
            (executionSemester == 2 && courseSemester % 2 == 0)) {
            throw new IllegalArgumentException("执行学期类型与课程学期不匹配");
        }

        // 计算入学年份
        Integer enrollmentYear = calculateEnrollmentYear(executionYear, executionSemester, courseSemester);

        // 入学通常在秋季学期（9月）
        return AcademicSemesterUtil.getAcademicSemester(enrollmentYear, 9);
    }

    /**
     * 根据执行学年学期信息获取入学年份学期信息
     *
     * @param executionSemester 执行学年学期信息
     * @param courseSemester    课程执行学期（1-8）
     * @return 入学年份的学年学期信息
     */
    public static AcademicSemester getEnrollmentSemester(AcademicSemester executionSemester, Integer courseSemester) {
        if (executionSemester == null || courseSemester == null) {
            return null;
        }

        validateCourseSemester(courseSemester);

        // 确定执行学期类型
        Integer executionSemesterType = executionSemester.isSpring() ? 1 : 2;

        return getEnrollmentSemester(executionSemester.getYear(), executionSemesterType, courseSemester);
    }

    /**
     * 根据执行学年学期信息获取所有可能的入学年份学期信息
     *
     * @param executionSemester 执行学年学期信息
     * @return 所有可能的入学年份学年学期信息列表
     */
    public static List<AcademicSemester> getPossibleEnrollmentSemesters(AcademicSemester executionSemester) {
        if (executionSemester == null) {
            return new ArrayList<>();
        }

        // 确定执行学期类型
        Integer executionSemesterType = executionSemester.isSpring() ? 1 : 2;

        return getPossibleEnrollmentSemesters(executionSemester.getYear(), executionSemesterType);
    }

    /**
     * 根据入学年份和执行学年学期信息计算课程执行学期
     *
     * @param enrollmentYear    入学年份
     * @param executionSemester 执行学年学期信息
     * @return 课程执行学期（1-8），如果无法计算则返回null
     */
    public static Integer calculateCourseSemester(Integer enrollmentYear, AcademicSemester executionSemester) {
        if (enrollmentYear == null || executionSemester == null) {
            return null;
        }

        int yearDiff = executionSemester.getYear() - enrollmentYear;
        
        if (executionSemester.isAutumn()) {
            // 秋季学期：奇数学期
            return yearDiff * 2 + 1;
        } else {
            // 春季学期：偶数学期
            return yearDiff * 2;
        }
    }

    /**
     * 获取指定入学年份的所有课程学期信息
     *
     * @param enrollmentYear 入学年份
     * @return 8个学期的学年学期信息数组
     */
    public static AcademicSemester[] getAllCourseSemesters(Integer enrollmentYear) {
        if (enrollmentYear == null) {
            return new AcademicSemester[0];
        }

        AcademicSemester[] semesters = new AcademicSemester[MAX_COURSE_SEMESTER];
        
        for (int i = 1; i <= MAX_COURSE_SEMESTER; i++) {
            semesters[i - 1] = getExecutionSemester(enrollmentYear, i);
        }
        
        return semesters;
    }

    /**
     * 判断指定的执行学期是否在有效的课程学期范围内
     *
     * @param enrollmentYear    入学年份
     * @param executionSemester 执行学年学期信息
     * @return true-在有效范围内，false-超出范围
     */
    public static boolean isValidCourseSemester(Integer enrollmentYear, AcademicSemester executionSemester) {
        Integer courseSemester = calculateCourseSemester(enrollmentYear, executionSemester);
        return courseSemester != null && 
               courseSemester >= MIN_COURSE_SEMESTER && 
               courseSemester <= MAX_COURSE_SEMESTER;
    }

    /**
     * 获取学生当前应该所在的课程学期
     *
     * @param enrollmentYear 入学年份
     * @return 当前课程学期，如果已毕业则返回null
     */
    public static Integer getCurrentCourseSemester(Integer enrollmentYear) {
        if (enrollmentYear == null) {
            return null;
        }

        AcademicSemester currentSemester = AcademicSemesterUtil.getCurrentAcademicSemester();
        Integer courseSemester = calculateCourseSemester(enrollmentYear, currentSemester);
        
        // 检查是否在有效范围内
        if (courseSemester != null && 
            courseSemester >= MIN_COURSE_SEMESTER && 
            courseSemester <= MAX_COURSE_SEMESTER) {
            return courseSemester;
        }
        
        return null; // 已毕业或尚未入学
    }

    /**
     * 判断学生是否已毕业
     *
     * @param enrollmentYear 入学年份
     * @return true-已毕业，false-在读或尚未入学
     */
    public static boolean isGraduated(Integer enrollmentYear) {
        if (enrollmentYear == null) {
            return false;
        }

        AcademicSemester currentSemester = AcademicSemesterUtil.getCurrentAcademicSemester();
        Integer courseSemester = calculateCourseSemester(enrollmentYear, currentSemester);
        
        return courseSemester != null && courseSemester > MAX_COURSE_SEMESTER;
    }

    /**
     * 判断学生是否尚未入学
     *
     * @param enrollmentYear 入学年份
     * @return true-尚未入学，false-已入学
     */
    public static boolean isNotEnrolledYet(Integer enrollmentYear) {
        if (enrollmentYear == null) {
            return true;
        }

        AcademicSemester currentSemester = AcademicSemesterUtil.getCurrentAcademicSemester();
        Integer courseSemester = calculateCourseSemester(enrollmentYear, currentSemester);
        
        return courseSemester != null && courseSemester < MIN_COURSE_SEMESTER;
    }

    /**
     * 计算入学年份
     *
     * @param executionYear     执行年份
     * @param executionSemester 执行学期（1-春季，2-秋季）
     * @param courseSemester    课程执行学期（1-8）
     * @return 入学年份
     */
    private static Integer calculateEnrollmentYear(Integer executionYear, Integer executionSemester, 
                                                  Integer courseSemester) {
        if (executionSemester == 1) {
            // 春季学期：偶数课程学期
            int yearOffset = courseSemester / 2;
            return executionYear - yearOffset;
        } else {
            // 秋季学期：奇数课程学期
            int yearOffset = (courseSemester - 1) / 2;
            return executionYear - yearOffset;
        }
    }

    /**
     * 验证课程执行学期的有效性
     *
     * @param courseSemester 课程执行学期
     */
    private static void validateCourseSemester(Integer courseSemester) {
        if (courseSemester < MIN_COURSE_SEMESTER || courseSemester > MAX_COURSE_SEMESTER) {
            throw new IllegalArgumentException(
                String.format("课程执行学期必须在%d-%d之间", MIN_COURSE_SEMESTER, MAX_COURSE_SEMESTER));
        }
    }

    /**
     * 验证执行学期的有效性
     *
     * @param executionSemester 执行学期
     */
    private static void validateExecutionSemester(Integer executionSemester) {
        if (executionSemester != 1 && executionSemester != 2) {
            throw new IllegalArgumentException("执行学期必须为1（春季）或2（秋季）");
        }
    }
}
