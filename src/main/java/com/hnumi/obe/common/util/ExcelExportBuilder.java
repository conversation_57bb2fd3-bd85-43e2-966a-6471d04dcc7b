package com.hnumi.obe.common.util;

import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.ArrayList;

/**
 * Excel导出建造者类
 * 提供链式配置API，用于构建ExcelExportConfig对象
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Slf4j
public class ExcelExportBuilder {
    
    private List<?> dataList;
    private Class<?> dataClass;
    private List<String> headers;
    private String unlockRange;
    private String sheetName;
    private boolean autoSize = true;
    private boolean includeHeader = true;
    private ExcelExportConfig.StyleConfig styleConfig;
    
    /**
     * 私有构造函数，使用静态方法创建实例
     */
    private ExcelExportBuilder() {
        this.styleConfig = ExcelExportConfig.getDefaultStyleConfig();
    }
    
    /**
     * 创建新的建造者实例
     * @return ExcelExportBuilder实例
     */
    public static ExcelExportBuilder create() {
        return new ExcelExportBuilder();
    }
    
    /**
     * 设置数据列表
     * @param dataList 数据对象列表
     * @return 当前建造者实例
     */
    public ExcelExportBuilder setData(List<?> dataList) {
        this.dataList = dataList;
        return this;
    }
    
    /**
     * 设置数据类型Class对象
     * @param dataClass 数据类型Class对象
     * @return 当前建造者实例
     */
    public ExcelExportBuilder setDataClass(Class<?> dataClass) {
        this.dataClass = dataClass;
        return this;
    }
    
    /**
     * 设置自定义表头
     * @param headers 表头列表
     * @return 当前建造者实例
     */
    public ExcelExportBuilder setHeaders(List<String> headers) {
        this.headers = headers;
        return this;
    }
    
    /**
     * 设置自定义表头（可变参数）
     * @param headers 表头数组
     * @return 当前建造者实例
     */
    public ExcelExportBuilder setHeaders(String... headers) {
        if (headers != null) {
            this.headers = new ArrayList<>();
            for (String header : headers) {
                this.headers.add(header);
            }
        }
        return this;
    }
    
    /**
     * 设置取消锁定的单元格范围
     * @param range 单元格范围，如"A1:D2"
     * @return 当前建造者实例
     */
    public ExcelExportBuilder setUnlockRange(String range) {
        this.unlockRange = range;
        return this;
    }
    
    /**
     * 设置工作表保护密码
     * @param password 保护密码
     * @return 当前建造者实例
     */
    public ExcelExportBuilder setProtectionPassword(String password) {
        // 这里需要在ExcelExportConfig中添加protectionPassword字段
        // 暂时记录日志，后续完善
        log.info("设置保护密码功能将在ExcelExportConfig完善后启用");
        return this;
    }
    
    /**
     * 设置工作表名称
     * @param sheetName 工作表名称
     * @return 当前建造者实例
     */
    public ExcelExportBuilder setSheetName(String sheetName) {
        this.sheetName = sheetName;
        return this;
    }
    
    /**
     * 设置是否自动调整列宽
     * @param autoSize 是否自动调整列宽
     * @return 当前建造者实例
     */
    public ExcelExportBuilder setAutoSize(boolean autoSize) {
        this.autoSize = autoSize;
        return this;
    }
    
    /**
     * 设置是否包含表头
     * @param includeHeader 是否包含表头
     * @return 当前建造者实例
     */
    public ExcelExportBuilder setIncludeHeader(boolean includeHeader) {
        this.includeHeader = includeHeader;
        return this;
    }
    
    /**
     * 设置样式配置
     * @param styleConfig 样式配置对象
     * @return 当前建造者实例
     */
    public ExcelExportBuilder setStyleConfig(ExcelExportConfig.StyleConfig styleConfig) {
        this.styleConfig = styleConfig;
        return this;
    }
    
    /**
     * 设置表头背景色
     * @param backgroundColor 背景色（十六进制颜色值）
     * @return 当前建造者实例
     */
    public ExcelExportBuilder setHeaderBackgroundColor(String backgroundColor) {
        if (this.styleConfig == null) {
            this.styleConfig = ExcelExportConfig.getDefaultStyleConfig();
        }
        this.styleConfig.setHeaderBackgroundColor(backgroundColor);
        return this;
    }
    
    /**
     * 设置表头字体颜色
     * @param fontColor 字体颜色（十六进制颜色值）
     * @return 当前建造者实例
     */
    public ExcelExportBuilder setHeaderFontColor(String fontColor) {
        if (this.styleConfig == null) {
            this.styleConfig = ExcelExportConfig.getDefaultStyleConfig();
        }
        this.styleConfig.setHeaderFontColor(fontColor);
        return this;
    }
    
    /**
     * 设置表头字体大小
     * @param fontSize 字体大小
     * @return 当前建造者实例
     */
    public ExcelExportBuilder setHeaderFontSize(short fontSize) {
        if (this.styleConfig == null) {
            this.styleConfig = ExcelExportConfig.getDefaultStyleConfig();
        }
        this.styleConfig.setHeaderFontSize(fontSize);
        return this;
    }
    
    /**
     * 设置表头是否加粗
     * @param bold 是否加粗
     * @return 当前建造者实例
     */
    public ExcelExportBuilder setHeaderBold(boolean bold) {
        if (this.styleConfig == null) {
            this.styleConfig = ExcelExportConfig.getDefaultStyleConfig();
        }
        this.styleConfig.setHeaderBold(bold);
        return this;
    }
    
    /**
     * 设置数据行字体大小
     * @param fontSize 字体大小
     * @return 当前建造者实例
     */
    public ExcelExportBuilder setDataFontSize(short fontSize) {
        if (this.styleConfig == null) {
            this.styleConfig = ExcelExportConfig.getDefaultStyleConfig();
        }
        this.styleConfig.setDataFontSize(fontSize);
        return this;
    }
    
    /**
     * 设置是否显示网格线
     * @param showGridLines 是否显示网格线
     * @return 当前建造者实例
     */
    public ExcelExportBuilder setShowGridLines(boolean showGridLines) {
        if (this.styleConfig == null) {
            this.styleConfig = ExcelExportConfig.getDefaultStyleConfig();
        }
        this.styleConfig.setShowGridLines(showGridLines);
        return this;
    }
    
    /**
     * 设置默认列宽
     * @param columnWidth 列宽
     * @return 当前建造者实例
     */
    public ExcelExportBuilder setDefaultColumnWidth(int columnWidth) {
        if (this.styleConfig == null) {
            this.styleConfig = ExcelExportConfig.getDefaultStyleConfig();
        }
        this.styleConfig.setDefaultColumnWidth(columnWidth);
        return this;
    }
    
    /**
     * 构建ExcelExportConfig对象
     * @return 配置对象
     */
    public ExcelExportConfig build() {
        if (dataList == null || dataList.isEmpty()) {
            log.warn("数据列表为空，将创建空的配置对象");
        }
        
        return ExcelExportConfig.builder()
                .dataList(dataList)
                .dataClass(dataClass)
                .headers(headers)
                .unlockRange(unlockRange)
                .sheetName(sheetName)
                .autoSize(autoSize)
                .includeHeader(includeHeader)
                .styleConfig(styleConfig)
                .build();
    }
    
    /**
     * 快速构建配置并导出
     * @return ExcelWriter对象
     */
    public cn.hutool.poi.excel.ExcelWriter export() {
        ExcelExportConfig config = build();
        return ExcelUtil.export(config);
    }
} 