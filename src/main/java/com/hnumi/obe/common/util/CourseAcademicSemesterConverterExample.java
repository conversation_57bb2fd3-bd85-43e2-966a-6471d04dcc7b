package com.hnumi.obe.common.util;

import java.util.List;

/**
 * 课程学年学期转换器使用示例
 * 演示如何使用CourseAcademicSemesterConverter进行各种转换操作
 *
 * <AUTHOR>
 * @since 2025-07-22
 */
public class CourseAcademicSemesterConverterExample {

    /**
     * 演示基本转换功能
     */
    public static void demonstrateBasicConversion() {
        System.out.println("=== 课程学年学期转换器基本功能演示 ===");

        // 假设学生2023年9月入学
        Integer enrollmentYear = 2023;
        System.out.println("学生入学年份: " + enrollmentYear + "年9月");
        System.out.println();

        // 1. 根据入学年份和课程执行学期获取执行年份和学期信息
        System.out.println("1. 根据入学年份和课程执行学期获取执行学年学期:");
        for (int courseSemester = 1; courseSemester <= 8; courseSemester++) {
            AcademicSemester executionSemester = CourseAcademicSemesterConverter
                .getExecutionSemester(enrollmentYear, courseSemester);
            System.out.printf("  第%d学期: %s%n", courseSemester, executionSemester);
        }
        System.out.println();

        // 2. 根据执行学年学期获取入学年份信息
        System.out.println("2. 根据执行学年学期获取入学年份信息:");

        // 示例：2024年春季学期是第2学期
        AcademicSemester spring2024 = AcademicSemesterUtil.getAcademicSemester(2024, 3);
        AcademicSemester enrollmentSemester1 = CourseAcademicSemesterConverter
            .getEnrollmentSemester(spring2024, 2);
        System.out.printf("  执行学期: %s (第2学期) -> 入学学期: %s%n",
                         spring2024, enrollmentSemester1);

        // 示例：2025年秋季学期是第5学期
        AcademicSemester autumn2025 = AcademicSemesterUtil.getAcademicSemester(2025, 9);
        AcademicSemester enrollmentSemester2 = CourseAcademicSemesterConverter
            .getEnrollmentSemester(autumn2025, 5);
        System.out.printf("  执行学期: %s (第5学期) -> 入学学期: %s%n",
                         autumn2025, enrollmentSemester2);

        // 3. 根据执行学期获取所有可能的入学年份（不指定具体课程学期）
        System.out.println();
        System.out.println("3. 根据执行学期获取所有可能的入学年份:");

        List<AcademicSemester> possibleEnrollments = CourseAcademicSemesterConverter
            .getPossibleEnrollmentSemesters(spring2024);
        System.out.printf("  执行学期: %s 的所有可能入学年份:%n", spring2024);
        for (int i = 0; i < possibleEnrollments.size(); i++) {
            int courseSemester = (i + 1) * 2; // 春季学期对应第2,4,6,8学期
            System.out.printf("    第%d学期 -> 入学: %s%n", courseSemester, possibleEnrollments.get(i));
        }

        System.out.println();
    }

    /**
     * 演示课程学期计算功能
     */
    public static void demonstrateCourseSemesterCalculation() {
        System.out.println("=== 课程学期计算功能演示 ===");

        Integer enrollmentYear = 2023;
        System.out.println("学生入学年份: " + enrollmentYear + "年9月");
        System.out.println();

        // 1. 计算指定执行学期对应的课程学期
        System.out.println("1. 计算执行学期对应的课程学期:");
        
        AcademicSemester[] testSemesters = {
            AcademicSemesterUtil.getAcademicSemester(2023, 9),  // 第1学期
            AcademicSemesterUtil.getAcademicSemester(2024, 3),  // 第2学期
            AcademicSemesterUtil.getAcademicSemester(2025, 9),  // 第5学期
            AcademicSemesterUtil.getAcademicSemester(2026, 3),  // 第6学期
            AcademicSemesterUtil.getAcademicSemester(2027, 3)   // 第8学期
        };

        for (AcademicSemester semester : testSemesters) {
            Integer courseSemester = CourseAcademicSemesterConverter
                .calculateCourseSemester(enrollmentYear, semester);
            System.out.printf("  %s -> 第%d学期%n", semester, courseSemester);
        }

        System.out.println();

        // 2. 获取学生当前应该所在的课程学期
        Integer currentCourseSemester = CourseAcademicSemesterConverter
            .getCurrentCourseSemester(enrollmentYear);
        if (currentCourseSemester != null) {
            System.out.println("2. 学生当前应该所在的课程学期: 第" + currentCourseSemester + "学期");
        } else {
            System.out.println("2. 学生当前状态: 已毕业或尚未入学");
        }

        System.out.println();
    }

    /**
     * 演示学生状态判断功能
     */
    public static void demonstrateStudentStatus() {
        System.out.println("=== 学生状态判断功能演示 ===");

        // 测试不同入学年份的学生状态
        Integer[] testEnrollmentYears = {2020, 2021, 2023, 2025};

        for (Integer year : testEnrollmentYears) {
            System.out.printf("入学年份: %d年9月%n", year);
            
            boolean isGraduated = CourseAcademicSemesterConverter.isGraduated(year);
            boolean isNotEnrolledYet = CourseAcademicSemesterConverter.isNotEnrolledYet(year);
            Integer currentSemester = CourseAcademicSemesterConverter.getCurrentCourseSemester(year);
            
            System.out.printf("  是否已毕业: %s%n", isGraduated);
            System.out.printf("  是否尚未入学: %s%n", isNotEnrolledYet);
            if (currentSemester != null) {
                System.out.printf("  当前课程学期: 第%d学期%n", currentSemester);
            } else {
                System.out.println("  当前课程学期: 无（已毕业或尚未入学）");
            }
            System.out.println();
        }
    }

    /**
     * 演示完整学期规划功能
     */
    public static void demonstrateFullSemesterPlanning() {
        System.out.println("=== 完整学期规划功能演示 ===");

        Integer enrollmentYear = 2023;
        System.out.println("学生入学年份: " + enrollmentYear + "年9月");
        System.out.println("完整的8个学期规划:");

        // 获取所有课程学期信息
        AcademicSemester[] allSemesters = CourseAcademicSemesterConverter
            .getAllCourseSemesters(enrollmentYear);

        for (int i = 0; i < allSemesters.length; i++) {
            AcademicSemester semester = allSemesters[i];
            int courseSemester = i + 1;
            
            // 判断是否为当前学期
            Integer currentSemester = CourseAcademicSemesterConverter
                .getCurrentCourseSemester(enrollmentYear);
            String status = "";
            if (currentSemester != null && currentSemester == courseSemester) {
                status = " (当前学期)";
            } else if (currentSemester != null && courseSemester < currentSemester) {
                status = " (已完成)";
            } else if (currentSemester != null && courseSemester > currentSemester) {
                status = " (未来学期)";
            }

            System.out.printf("  第%d学期: %s%s%n", courseSemester, semester, status);
        }

        System.out.println();
    }

    /**
     * 演示有效性验证功能
     */
    public static void demonstrateValidation() {
        System.out.println("=== 有效性验证功能演示 ===");

        Integer enrollmentYear = 2023;
        System.out.println("学生入学年份: " + enrollmentYear + "年9月");
        System.out.println();

        // 测试不同执行学期的有效性
        AcademicSemester[] testSemesters = {
            AcademicSemesterUtil.getAcademicSemester(2022, 9),  // 入学前
            AcademicSemesterUtil.getAcademicSemester(2023, 9),  // 第1学期
            AcademicSemesterUtil.getAcademicSemester(2025, 9),  // 第5学期
            AcademicSemesterUtil.getAcademicSemester(2027, 3),  // 第8学期
            AcademicSemesterUtil.getAcademicSemester(2027, 9)   // 毕业后
        };

        System.out.println("执行学期有效性验证:");
        for (AcademicSemester semester : testSemesters) {
            boolean isValid = CourseAcademicSemesterConverter
                .isValidCourseSemester(enrollmentYear, semester);
            Integer courseSemester = CourseAcademicSemesterConverter
                .calculateCourseSemester(enrollmentYear, semester);
            
            System.out.printf("  %s -> 第%d学期, 有效: %s%n", 
                             semester, courseSemester, isValid);
        }

        System.out.println();
    }

    /**
     * 演示反向转换功能
     */
    public static void demonstrateReverseConversion() {
        System.out.println("=== 反向转换功能演示 ===");

        // 已知执行信息，推算入学信息
        System.out.println("根据执行年份和学期推算入学信息:");
        
        // 示例1：2024年春季学期，第2学期
        AcademicSemester enrollment1 = CourseAcademicSemesterConverter
            .getEnrollmentSemester(2024, 1, 2); // 春季学期用1表示
        System.out.printf("  2024年春季学期(第2学期) -> 入学: %s%n", enrollment1);

        // 示例2：2025年秋季学期，第5学期
        AcademicSemester enrollment2 = CourseAcademicSemesterConverter
            .getEnrollmentSemester(2025, 2, 5); // 秋季学期用2表示
        System.out.printf("  2025年秋季学期(第5学期) -> 入学: %s%n", enrollment2);

        // 示例3：2026年春季学期，第6学期
        AcademicSemester enrollment3 = CourseAcademicSemesterConverter
            .getEnrollmentSemester(2026, 1, 6); // 春季学期用1表示
        System.out.printf("  2026年春季学期(第6学期) -> 入学: %s%n", enrollment3);

        System.out.println();
    }

    /**
     * 主方法，运行所有演示
     */
    public static void main(String[] args) {
        demonstrateBasicConversion();
        demonstrateCourseSemesterCalculation();
        demonstrateStudentStatus();
        demonstrateFullSemesterPlanning();
        demonstrateValidation();
        demonstrateReverseConversion();
    }
}
