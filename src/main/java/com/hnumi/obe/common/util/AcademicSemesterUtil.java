package com.hnumi.obe.common.util;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 学年学期转换工具类
 * 提供学年学期相关的转换和计算功能
 *
 * <AUTHOR>
 * @since 2025-07-22
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class AcademicSemesterUtil {

    /**
     * 春季学期开始月份
     */
    private static final int SPRING_START_MONTH = 1;

    /**
     * 春季学期结束月份
     */
    private static final int SPRING_END_MONTH = 8;

    /**
     * 秋季学期开始月份
     */
    private static final int AUTUMN_START_MONTH = 9;

    /**
     * 秋季学期结束月份
     */
    private static final int AUTUMN_END_MONTH = 12;

    /**
     * 根据当前时间获取当前学年学期
     *
     * @return 当前学年学期信息
     */
    public static AcademicSemester getCurrentAcademicSemester() {
        LocalDate now = LocalDate.now();
        return getAcademicSemester(now.getYear(), now.getMonthValue());
    }

    /**
     * 根据指定时间获取学年学期
     *
     * @param dateTime 指定时间
     * @return 学年学期信息
     */
    public static AcademicSemester getAcademicSemester(LocalDateTime dateTime) {
        if (dateTime == null) {
            return null;
        }
        return getAcademicSemester(dateTime.getYear(), dateTime.getMonthValue());
    }

    /**
     * 根据指定日期获取学年学期
     *
     * @param date 指定日期
     * @return 学年学期信息
     */
    public static AcademicSemester getAcademicSemester(LocalDate date) {
        if (date == null) {
            return null;
        }
        return getAcademicSemester(date.getYear(), date.getMonthValue());
    }

    /**
     * 根据年份和月份获取学年学期
     *
     * @param year  年份
     * @param month 月份
     * @return 学年学期信息
     */
    public static AcademicSemester getAcademicSemester(Integer year, Integer month) {
        if (year == null || month == null) {
            return null;
        }
        
        if (month < 1 || month > 12) {
            throw new IllegalArgumentException("月份必须在1-12之间");
        }

        return new AcademicSemester(year, month);
    }

    /**
     * 判断指定年月是否为春季学期
     *
     * @param year  年份
     * @param month 月份
     * @return true-春季学期，false-秋季学期
     */
    public static boolean isSpring(Integer year, Integer month) {
        if (month == null) {
            return false;
        }
        return month >= SPRING_START_MONTH && month <= SPRING_END_MONTH;
    }

    /**
     * 判断指定年月是否为秋季学期
     *
     * @param year  年份
     * @param month 月份
     * @return true-秋季学期，false-春季学期
     */
    public static boolean isAutumn(Integer year, Integer month) {
        if (month == null) {
            return false;
        }
        return month >= AUTUMN_START_MONTH && month <= AUTUMN_END_MONTH;
    }

    /**
     * 获取指定学年的所有学期
     *
     * @param academicYear 学年开始年份（如2024表示2024-2025学年）
     * @return 该学年的所有学期列表
     */
    public static List<AcademicSemester> getSemestersInAcademicYear(Integer academicYear) {
        if (academicYear == null) {
            return new ArrayList<>();
        }

        List<AcademicSemester> semesters = new ArrayList<>();
        
        // 秋季学期（学年开始）
        semesters.add(new AcademicSemester(academicYear, AUTUMN_START_MONTH));
        
        // 春季学期（学年结束）
        semesters.add(new AcademicSemester(academicYear + 1, SPRING_START_MONTH));
        
        return semesters;
    }

    /**
     * 获取下一个学期
     *
     * @param currentSemester 当前学期
     * @return 下一个学期
     */
    public static AcademicSemester getNextSemester(AcademicSemester currentSemester) {
        if (currentSemester == null) {
            return null;
        }

        if (currentSemester.isSpring()) {
            // 春季学期的下一个学期是秋季学期
            return new AcademicSemester(currentSemester.getYear(), AUTUMN_START_MONTH);
        } else {
            // 秋季学期的下一个学期是下一年的春季学期
            return new AcademicSemester(currentSemester.getYear() + 1, SPRING_START_MONTH);
        }
    }

    /**
     * 获取上一个学期
     *
     * @param currentSemester 当前学期
     * @return 上一个学期
     */
    public static AcademicSemester getPreviousSemester(AcademicSemester currentSemester) {
        if (currentSemester == null) {
            return null;
        }

        if (currentSemester.isSpring()) {
            // 春季学期的上一个学期是上一年的秋季学期
            return new AcademicSemester(currentSemester.getYear() - 1, AUTUMN_START_MONTH);
        } else {
            // 秋季学期的上一个学期是春季学期
            return new AcademicSemester(currentSemester.getYear(), SPRING_START_MONTH);
        }
    }

    /**
     * 比较两个学期的先后顺序
     *
     * @param semester1 学期1
     * @param semester2 学期2
     * @return 负数表示semester1在semester2之前，0表示相同，正数表示semester1在semester2之后
     */
    public static int compareSemesters(AcademicSemester semester1, AcademicSemester semester2) {
        if (semester1 == null && semester2 == null) {
            return 0;
        }
        if (semester1 == null) {
            return -1;
        }
        if (semester2 == null) {
            return 1;
        }

        // 先比较学年
        int yearCompare = semester1.getAcademicYear().compareTo(semester2.getAcademicYear());
        if (yearCompare != 0) {
            return yearCompare;
        }

        // 学年相同，比较学期（秋季学期在前，春季学期在后）
        if (semester1.isAutumn() && semester2.isSpring()) {
            return -1;
        } else if (semester1.isSpring() && semester2.isAutumn()) {
            return 1;
        } else {
            return 0;
        }
    }

    /**
     * 格式化学年学期为字符串
     *
     * @param academicYear 学年开始年份
     * @param semesterType 学期类型
     * @return 格式化后的字符串
     */
    public static String formatAcademicSemester(Integer academicYear, String semesterType) {
        if (academicYear == null || semesterType == null) {
            return null;
        }
        return String.format("%d-%d学年%s", academicYear, academicYear + 1, semesterType);
    }

    /**
     * 解析学年学期字符串
     *
     * @param academicSemesterString 学年学期字符串（如：2024-2025学年春季学期）
     * @return 学年学期信息，解析失败返回null
     */
    public static AcademicSemester parseAcademicSemester(String academicSemesterString) {
        if (academicSemesterString == null || academicSemesterString.trim().isEmpty()) {
            return null;
        }

        try {
            // 简单的解析逻辑，可根据实际需求调整
            if (academicSemesterString.contains("春季学期")) {
                String yearPart = academicSemesterString.split("学年")[0];
                String[] years = yearPart.split("-");
                Integer academicYear = Integer.parseInt(years[0]);
                return new AcademicSemester(academicYear + 1, SPRING_START_MONTH);
            } else if (academicSemesterString.contains("秋季学期")) {
                String yearPart = academicSemesterString.split("学年")[0];
                String[] years = yearPart.split("-");
                Integer academicYear = Integer.parseInt(years[0]);
                return new AcademicSemester(academicYear, AUTUMN_START_MONTH);
            }
        } catch (Exception e) {
            // 解析失败，返回null
        }

        return null;
    }
}
