package com.hnumi.obe.common.util;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 学年学期工具类使用示例
 * 演示如何使用AcademicSemesterUtil和AcademicSemester类
 *
 * <AUTHOR>
 * @since 2025-07-22
 */
public class AcademicSemesterExample {

    /**
     * 演示基本用法
     */
    public static void demonstrateBasicUsage() {
        System.out.println("=== 学年学期工具类基本用法演示 ===");

        // 1. 获取当前学年学期
        AcademicSemester current = AcademicSemesterUtil.getCurrentAcademicSemester();
        System.out.println("当前学年学期: " + current);
        System.out.println("当前学年: " + current.getAcademicYearString());
        System.out.println("当前学期类型: " + current.getSemesterType());
        System.out.println("是否为春季学期: " + current.isSpring());
        System.out.println("学期编号: " + current.getSemesterCode());

        System.out.println();

        // 2. 根据指定年月获取学年学期
        AcademicSemester autumn2024 = AcademicSemesterUtil.getAcademicSemester(2024, 10);
        System.out.println("2024年10月: " + autumn2024);

        AcademicSemester spring2025 = AcademicSemesterUtil.getAcademicSemester(2025, 3);
        System.out.println("2025年3月: " + spring2025);

        System.out.println();

        // 3. 根据日期获取学年学期
        LocalDate date1 = LocalDate.of(2024, 9, 1);
        AcademicSemester semester1 = AcademicSemesterUtil.getAcademicSemester(date1);
        System.out.println("2024-09-01: " + semester1);

        LocalDateTime dateTime1 = LocalDateTime.of(2025, 2, 15, 10, 30);
        AcademicSemester semester2 = AcademicSemesterUtil.getAcademicSemester(dateTime1);
        System.out.println("2025-02-15 10:30: " + semester2);

        System.out.println();
    }

    /**
     * 演示高级功能
     */
    public static void demonstrateAdvancedFeatures() {
        System.out.println("=== 学年学期工具类高级功能演示 ===");

        // 1. 获取指定学年的所有学期
        List<AcademicSemester> semesters2024 = AcademicSemesterUtil.getSemestersInAcademicYear(2024);
        System.out.println("2024-2025学年的所有学期:");
        for (AcademicSemester semester : semesters2024) {
            System.out.println("  " + semester);
        }

        System.out.println();

        // 2. 学期导航
        AcademicSemester currentSemester = AcademicSemesterUtil.getAcademicSemester(2024, 10);
        System.out.println("当前学期: " + currentSemester);

        AcademicSemester nextSemester = AcademicSemesterUtil.getNextSemester(currentSemester);
        System.out.println("下一个学期: " + nextSemester);

        AcademicSemester previousSemester = AcademicSemesterUtil.getPreviousSemester(currentSemester);
        System.out.println("上一个学期: " + previousSemester);

        System.out.println();

        // 3. 学期比较
        AcademicSemester semester1 = AcademicSemesterUtil.getAcademicSemester(2024, 9);  // 2024-2025秋季
        AcademicSemester semester2 = AcademicSemesterUtil.getAcademicSemester(2025, 3);  // 2024-2025春季
        AcademicSemester semester3 = AcademicSemesterUtil.getAcademicSemester(2025, 9);  // 2025-2026秋季

        System.out.println("学期比较:");
        System.out.println(semester1 + " vs " + semester2 + ": " + 
                          AcademicSemesterUtil.compareSemesters(semester1, semester2));
        System.out.println(semester2 + " vs " + semester3 + ": " + 
                          AcademicSemesterUtil.compareSemesters(semester2, semester3));

        System.out.println();
    }

    /**
     * 演示字符串解析和格式化
     */
    public static void demonstrateStringOperations() {
        System.out.println("=== 字符串解析和格式化演示 ===");

        // 1. 格式化学年学期
        String formatted1 = AcademicSemesterUtil.formatAcademicSemester(2024, "秋季学期");
        String formatted2 = AcademicSemesterUtil.formatAcademicSemester(2024, "春季学期");
        System.out.println("格式化结果1: " + formatted1);
        System.out.println("格式化结果2: " + formatted2);

        System.out.println();

        // 2. 解析学年学期字符串
        AcademicSemester parsed1 = AcademicSemesterUtil.parseAcademicSemester("2024-2025学年秋季学期");
        AcademicSemester parsed2 = AcademicSemesterUtil.parseAcademicSemester("2024-2025学年春季学期");
        System.out.println("解析结果1: " + parsed1);
        System.out.println("解析结果2: " + parsed2);

        System.out.println();
    }

    /**
     * 演示实际应用场景
     */
    public static void demonstrateRealWorldUsage() {
        System.out.println("=== 实际应用场景演示 ===");

        // 场景1: 判断当前是否为招生季（假设秋季学期为招生季）
        AcademicSemester current = AcademicSemesterUtil.getCurrentAcademicSemester();
        boolean isEnrollmentSeason = current.isAutumn();
        System.out.println("当前是否为招生季: " + isEnrollmentSeason);

        // 场景2: 计算学生当前所在学期（假设学生2023年9月入学）
        AcademicSemester enrollmentSemester = AcademicSemesterUtil.getAcademicSemester(2023, 9);
        AcademicSemester currentSemester = AcademicSemesterUtil.getCurrentAcademicSemester();
        
        System.out.println("学生入学学期: " + enrollmentSemester);
        System.out.println("当前学期: " + currentSemester);

        // 场景3: 生成学期选择列表（最近3个学年）
        System.out.println("最近3个学年的学期列表:");
        for (int year = 2022; year <= 2024; year++) {
            List<AcademicSemester> semesters = AcademicSemesterUtil.getSemestersInAcademicYear(year);
            for (AcademicSemester semester : semesters) {
                System.out.println("  " + semester);
            }
        }

        System.out.println();
    }

    /**
     * 主方法，运行所有演示
     */
    public static void main(String[] args) {
        demonstrateBasicUsage();
        demonstrateAdvancedFeatures();
        demonstrateStringOperations();
        demonstrateRealWorldUsage();
    }
}
