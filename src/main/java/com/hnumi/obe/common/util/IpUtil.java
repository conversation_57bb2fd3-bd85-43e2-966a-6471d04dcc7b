package com.hnumi.obe.common.util;

import lombok.extern.slf4j.Slf4j;
import org.lionsoul.ip2region.xdb.Searcher;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * IP地址位置信息工具类
 * 用于解析IP地址获取地理位置信息
 * 支持缓存机制以提高性能
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Slf4j
@Component
public final class IpUtil {
    /**
     * 缓存大小限制
     */
    private static final int MAX_CACHE_SIZE = 1000;
    
    /**
     * IP位置信息缓存
     * key: IP地址
     * value: 位置信息Map
     */
    private static final Map<String, Map<String, String>> locationCache = new ConcurrentHashMap<>();
    
    /**
     * 当前缓存大小
     */
    private static final AtomicInteger cacheSize = new AtomicInteger(0);
    
    /**
     * IP地址解析器
     */
    private static Searcher searcher;

    /**
     * IP库文件名
     */
    private static final String IP_DB_FILE_NAME = "ip2region.xdb";

    static {
        try {
            // 尝试从多个位置加载IP库文件
            String dbPath = findIpDbFile();
            if (dbPath != null) {
                searcher = Searcher.newWithFileOnly(dbPath);
                log.info("IP地址解析器初始化成功，使用文件: {}", dbPath);
            } else {
                log.warn("无法找到IP地址库文件，IP地址解析功能将不可用");
            }
        } catch (Exception e) {
            log.error("IP地址解析器初始化失败", e);
        }
    }

    /**
     * 查找IP库文件
     * 按以下顺序查找:
     * 1. 项目根目录
     * 2. 用户根目录
     * 3. 临时目录
     * 4. classpath中的资源文件
     *
     * @return IP库文件路径，如果找不到返回null
     */
    private static String findIpDbFile() {
        // 检查项目根目录
        String path = IP_DB_FILE_NAME;
        if (checkFileExists(path)) {
            return path;
        }

        // 检查用户根目录
        path = System.getProperty("user.dir") + File.separator + IP_DB_FILE_NAME;
        if (checkFileExists(path)) {
            return path;
        }

        // 检查临时目录
        path = System.getProperty("java.io.tmpdir") + File.separator + IP_DB_FILE_NAME;
        if (checkFileExists(path)) {
            return path;
        }

        // 尝试从classpath资源加载
        try {
            return extractResourceToTemp();
        } catch (IOException e) {
            log.error("从classpath资源提取IP库文件失败", e);
            return null;
        }
    }

    /**
     * 检查文件是否存在
     *
     * @param filePath 文件路径
     * @return 是否存在
     */
    private static boolean checkFileExists(String filePath) {
        if (filePath == null) {
            return false;
        }
        Path path = Paths.get(filePath);
        boolean exists = Files.exists(path) && Files.isReadable(path);
        if (exists) {
            log.info("找到IP库文件: {}", filePath);
        }
        return exists;
    }

    /**
     * 从classpath资源提取文件到临时目录
     *
     * @return 提取后的文件路径
     * @throws IOException 提取失败时抛出
     */
    private static String extractResourceToTemp() throws IOException {
        try {
            ClassPathResource resource = new ClassPathResource("ip2region.xdb");
            if (!resource.exists()) {
                log.warn("classpath中不存在IP库资源文件");
                return null;
            }

            String tempFile = System.getProperty("java.io.tmpdir") + File.separator + IP_DB_FILE_NAME;
            try (InputStream inputStream = resource.getInputStream();
                 FileOutputStream outputStream = new FileOutputStream(tempFile)) {
                byte[] buffer = new byte[1024];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
            }
            log.info("已将IP库资源文件提取到临时目录: {}", tempFile);
            return tempFile;
        } catch (IOException e) {
            log.error("提取IP库资源文件失败", e);
            throw e;
        }
    }

    /**
     * 获取IP地址位置信息
     * 优先从缓存中获取，缓存未命中时进行解析
     *
     * @param ip IP地址
     * @return 位置信息Map，包含国家、省份、城市信息
     */
    public static Map<String, String> getLocation(String ip) {
        if (!StringUtils.hasText(ip)) {
            log.warn("IP地址为空");
            return null;
        }

        // 先从缓存中获取
        Map<String, String> location = locationCache.get(ip);
        if (location != null) {
            log.debug("IP地址位置信息命中缓存: ip={}", ip);
            return location;
        }

        // 缓存未命中，进行解析
        location = parseLocation(ip);
        if (location != null) {
            // 如果缓存已满，移除最早添加的项
            if (cacheSize.get() >= MAX_CACHE_SIZE) {
                String firstKey = locationCache.keySet().iterator().next();
                locationCache.remove(firstKey);
                cacheSize.decrementAndGet();
                log.debug("缓存已满，移除最早项: ip={}", firstKey);
            }
            // 添加到缓存
            locationCache.put(ip, location);
            cacheSize.incrementAndGet();
            log.debug("IP地址位置信息添加到缓存: ip={}, location={}", ip, location);
        }
        return location;
    }

    /**
     * 解析IP地址位置信息
     * 使用ip2region库进行解析
     *
     * @param ip IP地址
     * @return 位置信息Map，解析失败返回null
     */
    private static Map<String, String> parseLocation(String ip) {
        if (searcher == null) {
            log.warn("IP地址解析器未初始化，无法解析IP位置信息");
            return Map.of("country", "未知", "province", "未知", "city", "未知");
        }

        try {
            String region = searcher.search(ip);
            String[] parts = region.split("\\|");
            Map<String, String> location = new ConcurrentHashMap<>();
            if (parts.length >= 5) {
                location.put("country", parts[0]);
                location.put("province", parts[2]);
                location.put("city", parts[3]);
                log.debug("IP地址解析成功: ip={}, location={}", ip, location);
            } else {
                log.warn("IP地址解析结果格式异常: ip={}, region={}", ip, region);
            }
            return location;
        } catch (Exception e) {
            log.error("IP地址解析失败: ip={}", ip, e);
            return Map.of("country", "未知", "province", "未知", "city", "未知");
        }
    }

    /**
     * 清理缓存
     * 清空所有缓存的IP位置信息
     */
    public static void clearCache() {
        locationCache.clear();
        cacheSize.set(0);
        log.info("IP地址位置信息缓存已清理");
    }
} 