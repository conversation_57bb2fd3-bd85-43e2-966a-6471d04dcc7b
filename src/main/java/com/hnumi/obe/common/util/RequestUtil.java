package com.hnumi.obe.common.util;

import cn.dev33.satoken.session.SaSession;
import cn.dev33.satoken.stp.StpUtil;
import com.alibaba.fastjson2.JSONObject;
import com.hnumi.obe.base.service.ITeacherService;
import com.hnumi.obe.common.CommonProperties;
import com.hnumi.obe.common.Constants;
import com.hnumi.obe.common.entity.ResultCode;
import com.hnumi.obe.system.service.IBaseUserService;
import com.hnumi.obe.common.entity.LoginSessionVO;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.io.IOException;
import java.io.PrintWriter;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

import static com.hnumi.obe.common.exception.ServiceExceptionUtil.exception;

/**
 * 请求工具类
 * 用于获取请求相关的各种信息
 * 包括用户信息、IP地址、位置信息等
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Slf4j
@Component
public final class RequestUtil {

    /**
     * 用户服务
     */
    private static IBaseUserService baseUserService;
    private static ITeacherService teacherService;
    
    @Autowired
    public void setBaseUserService(IBaseUserService baseUserService) {
        RequestUtil.baseUserService = baseUserService;
    }

    @Autowired
    public void setTeacherService(ITeacherService teacherService) {
        RequestUtil.teacherService = teacherService;
    }

    /**
     * 获取当前的登录用户ID
     *
     * @return 当前登录的用户ID
     * @throws IllegalStateException 用户未登录时抛出
     */
    public static Long getUserId() {
        if (StpUtil.isLogin()) {
            return StpUtil.getLoginIdAsLong();
        } else {
            return null;
        }
    }

    /**
     * 获取当前登录用户的租户ID
     *
     * @return 当前登录用户的租户ID，未设置时返回0
     */
    public static Long getTenantId() {
        try {
            Long userId = RequestUtil.getUserId();
            String key = CommonProperties.REDIS_KEY_PREFIX_TENANT + ":" + userId;
            Object o = RedisUtil.get(key);
            if (o != null) {
                return Long.parseLong(o.toString());
            }
            log.debug("未找到租户ID: userId={}", userId);
            return 0L;
        } catch (Exception e) {
            log.error("获取租户ID失败", e);
            return 0L;
        }
    }

    /**
     * 获取当前登录用户信息
     *
     * @return 当前登录用户信息，未找到时返回null
     */
    public static LoginSessionVO getCurrentUser() {
        SaSession session = StpUtil.getSession();
        Object o = session.get(Constants.LOGIN_USER_SESSION_KEY);
        if(o != null){
            JSONObject object = (JSONObject) o;
            return object.to(LoginSessionVO.class);
        }
        throw exception(ResultCode.NOT_LOGIN);
    }

    /**
     * 获取登录用户信息的拓展表数据ID，比如教师ID或学生ID
     * @return 教师ID或者学生ID
     */
    public static Long getExtendId() {
        return getCurrentUser().getId();
    }

    /**
     * 获取当前登录用户名
     *
     * @return 当前登录用户名，未找到时返回"system"
     */
    public static String getCurrentUsername() {
        LoginSessionVO user = getCurrentUser();
        return user != null ? user.getRealName() : "system";
    }

    /**
     * 获取请求信息
     *
     * @return 请求信息，未找到时返回null
     */
    public static HttpServletRequest getRequest() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes == null) {
            log.warn("未找到请求信息");
        }
        return attributes != null ? attributes.getRequest() : null;
    }

    /**
     * 获取响应信息
     *
     * @return 响应信息，未找到时返回null
     */
    public static HttpServletResponse getResponse() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes == null) {
            log.warn("未找到响应信息");
        }
        return attributes != null ? attributes.getResponse() : null;
    }

    /**
     * 获取请求IP地址
     * 按以下顺序获取：
     * 1. X-Forwarded-For
     * 2. Proxy-Client-IP
     * 3. WL-Proxy-Client-IP
     * 4. RemoteAddr
     *
     * @return IP地址，未找到时返回"unknown"
     */
    public static String getIpAddress() {
        HttpServletRequest request = getRequest();
        if (request == null) {
            return "unknown";
        }

        String[] headers = {
            "X-Forwarded-For",
            "Proxy-Client-IP",
            "WL-Proxy-Client-IP"
        };

        String ip = null;
        for (String header : headers) {
            ip = request.getHeader(header);
            if (isValidIp(ip)) {
                break;
            }
        }

        if (!isValidIp(ip)) {
            ip = request.getRemoteAddr();
        }

        log.debug("获取到IP地址: {}", ip);
        return ip;
    }

    /**
     * 检查IP地址是否有效
     *
     * @param ip IP地址
     * @return 是否有效
     */
    private static boolean isValidIp(String ip) {
        return ip != null && !ip.isEmpty() && !"unknown".equalsIgnoreCase(ip);
    }

    /**
     * 获取IP地址位置信息
     * 使用IpUtil工具类获取位置信息
     *
     * @param ip IP地址
     * @return 位置信息，解析失败时返回空Map
     */
    public static Map<String, String> getIpLocation(String ip) {
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            return Map.of();
        }
        return IpUtil.getLocation(ip);
    }

    /**
     * 获取请求方法
     *
     * @return 请求方法，如GET、POST等
     */
    public static String getMethod() {
        HttpServletRequest request = getRequest();
        return request != null ? request.getMethod() : "";
    }

    /**
     * 获取请求URL
     *
     * @return 完整的请求URL
     */
    public static String getRequestUrl() {
        HttpServletRequest request = getRequest();
        if (request == null) {
            return "";
        }
        return request.getRequestURL().toString();
    }

    /**
     * 获取请求URI
     *
     * @return 请求URI
     */
    public static String getRequestUri() {
        HttpServletRequest request = getRequest();
        return request != null ? request.getRequestURI() : "";
    }

    /**
     * 获取请求参数Map
     *
     * @return 请求参数Map
     */
    public static Map<String, String> getParameterMap() {
        HttpServletRequest request = getRequest();
        if (request == null) {
            return Map.of();
        }

        Map<String, String> params = new HashMap<>();
        Enumeration<String> names = request.getParameterNames();
        while (names.hasMoreElements()) {
            String name = names.nextElement();
            params.put(name, request.getParameter(name));
        }
        return params;
    }

    /**
     * 获取请求头Map
     *
     * @return 请求头Map
     */
    public static Map<String, String> getHeaderMap() {
        HttpServletRequest request = getRequest();
        if (request == null) {
            return Map.of();
        }

        Map<String, String> headers = new HashMap<>();
        Enumeration<String> names = request.getHeaderNames();
        while (names.hasMoreElements()) {
            String name = names.nextElement();
            headers.put(name, request.getHeader(name));
        }
        return headers;
    }

    /**
     * 获取请求头值
     *
     * @param name 请求头名称
     * @return 请求头值
     */
    public static String getHeader(String name) {
        HttpServletRequest request = getRequest();
        return request != null ? request.getHeader(name) : null;
    }

    /**
     * 获取请求参数值
     *
     * @param name 参数名称
     * @return 参数值
     */
    public static String getParameter(String name) {
        HttpServletRequest request = getRequest();
        return request != null ? request.getParameter(name) : null;
    }

    /**
     * 判断是否是Ajax请求
     *
     * @return 是否是Ajax请求
     */
    public static boolean isAjaxRequest() {
        HttpServletRequest request = getRequest();
        if (request == null) {
            return false;
        }
        String requestedWith = request.getHeader("X-Requested-With");
        return "XMLHttpRequest".equals(requestedWith);
    }

    /**
     * 判断是否是移动设备
     *
     * @return 是否是移动设备
     */
    public static boolean isMobileDevice() {
        HttpServletRequest request = getRequest();
        if (request == null) {
            return false;
        }
        String userAgent = request.getHeader(HttpHeaders.USER_AGENT);
        if (StringUtil.isBlank(userAgent)) {
            return false;
        }
        return userAgent.matches("(?i).*(android|bb\\d+|meego).+mobile|avantgo|bada\\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge "
                + "|maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\\/|plucker|pocket|psp|series(4|6)0|symbian"
                + "|treo|up\\.(browser|link)|vodafone|wap|windows ce|xda|xiino.*");
    }

    /**
     * 向响应写入JSON数据
     *
     * @param response 响应对象
     * @param data JSON数据
     */
    public static void writeJson(HttpServletResponse response, String data) {
        if (response == null || StringUtil.isBlank(data)) {
            return;
        }
        try {
            response.setContentType(MediaType.APPLICATION_JSON_VALUE);
            response.setCharacterEncoding("UTF-8");
            try (PrintWriter writer = response.getWriter()) {
                writer.write(data);
                writer.flush();
            }
        } catch (IOException e) {
            log.error("写入JSON响应失败", e);
        }
    }

    /**
     * 获取请求的Content-Type
     *
     * @return Content-Type
     */
    public static String getContentType() {
        HttpServletRequest request = getRequest();
        return request != null ? request.getContentType() : null;
    }

    /**
     * 获取请求的字符编码
     *
     * @return 字符编码
     */
    public static String getCharacterEncoding() {
        HttpServletRequest request = getRequest();
        return request != null ? request.getCharacterEncoding() : null;
    }

    /**
     * 获取请求的上下文路径
     *
     * @return 上下文路径
     */
    public static String getContextPath() {
        HttpServletRequest request = getRequest();
        return request != null ? request.getContextPath() : "";
    }

    /**
     * 获取请求的查询字符串
     *
     * @return 查询字符串
     */
    public static String getQueryString() {
        HttpServletRequest request = getRequest();
        return request != null ? request.getQueryString() : null;
    }
}
