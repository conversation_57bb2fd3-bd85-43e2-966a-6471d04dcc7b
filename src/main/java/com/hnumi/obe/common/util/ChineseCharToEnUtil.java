package com.hnumi.obe.common.util;

import lombok.extern.slf4j.Slf4j;
import net.sourceforge.pinyin4j.PinyinHelper;
import net.sourceforge.pinyin4j.format.HanyuPinyinCaseType;
import net.sourceforge.pinyin4j.format.HanyuPinyinOutputFormat;
import net.sourceforge.pinyin4j.format.HanyuPinyinToneType;
import net.sourceforge.pinyin4j.format.HanyuPinyinVCharType;
import net.sourceforge.pinyin4j.format.exception.BadHanyuPinyinOutputFormatCombination;
import org.springframework.util.StringUtils;

/**
 * 中文字符转拼音工具类
 * 提供中文转拼音、获取拼音首字母等功能
 * 使用pinyin4j库实现
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Slf4j
public final class ChineseCharToEnUtil {
    
    private ChineseCharToEnUtil() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * 默认拼音输出格式
     */
    private static final HanyuPinyinOutputFormat DEFAULT_FORMAT = new HanyuPinyinOutputFormat();

    static {
        DEFAULT_FORMAT.setCaseType(HanyuPinyinCaseType.LOWERCASE);
        DEFAULT_FORMAT.setToneType(HanyuPinyinToneType.WITHOUT_TONE);
        DEFAULT_FORMAT.setVCharType(HanyuPinyinVCharType.WITH_V);
    }

    /**
     * 将字符串中的中文转化为拼音，其他字符不变
     *
     * @param inputString 输入字符串
     * @return 转换后的拼音字符串
     * @throws IllegalArgumentException 输入字符串为空时抛出
     */
    public static String getPingYin(String inputString) {
        if (!StringUtils.hasText(inputString)) {
            throw new IllegalArgumentException("输入字符串不能为空");
        }

        char[] input = inputString.trim().toCharArray();
        StringBuilder output = new StringBuilder();

        try {
            for (char c : input) {
                if (Character.toString(c).matches("[\\u4E00-\\u9FA5]+")) {
                    String[] temp = PinyinHelper.toHanyuPinyinStringArray(c, DEFAULT_FORMAT);
                    if (temp != null && temp.length > 0) {
                        output.append(temp[0]);
                    }
                } else {
                    output.append(c);
                }
            }
            log.debug("中文转拼音: input={}, output={}", inputString, output);
            return output.toString();
        } catch (BadHanyuPinyinOutputFormatCombination e) {
            log.error("中文转拼音失败: input={}", inputString, e);
            throw new IllegalStateException("中文转拼音失败", e);
        }
    }

    /**
     * 获取汉字串拼音首字母，英文字符不变
     *
     * @param chinese 汉字串
     * @return 汉语拼音首字母
     * @throws IllegalArgumentException 输入字符串为空时抛出
     */
    public static String getFirstSpell(String chinese) {
        if (!StringUtils.hasText(chinese)) {
            throw new IllegalArgumentException("输入字符串不能为空");
        }

        StringBuilder pybf = new StringBuilder();
        char[] arr = chinese.toCharArray();

        for (char c : arr) {
            if (c > 128) {
                try {
                    String[] temp = PinyinHelper.toHanyuPinyinStringArray(c, DEFAULT_FORMAT);
                    if (temp != null && temp.length > 0) {
                        pybf.append(temp[0].charAt(0));
                    }
                } catch (BadHanyuPinyinOutputFormatCombination e) {
                    log.error("获取拼音首字母失败: char={}", c, e);
                    throw new IllegalStateException("获取拼音首字母失败", e);
                }
            } else {
                pybf.append(c);
            }
        }

        String result = pybf.toString().replaceAll("\\W", "").trim();
        log.debug("获取拼音首字母: input={}, output={}", chinese, result);
        return result;
    }

    /**
     * 获取汉字串拼音，英文字符不变
     *
     * @param chinese 汉字串
     * @return 汉语拼音
     * @throws IllegalArgumentException 输入字符串为空时抛出
     */
    public static String getFullSpell(String chinese) {
        if (!StringUtils.hasText(chinese)) {
            throw new IllegalArgumentException("输入字符串不能为空");
        }

        StringBuilder pybf = new StringBuilder();
        char[] arr = chinese.toCharArray();

        for (char c : arr) {
            if (c > 128) {
                try {
                    String[] temp = PinyinHelper.toHanyuPinyinStringArray(c, DEFAULT_FORMAT);
                    if (temp != null && temp.length > 0) {
                        pybf.append(temp[0]);
                    }
                } catch (BadHanyuPinyinOutputFormatCombination e) {
                    log.error("获取完整拼音失败: char={}", c, e);
                    throw new IllegalStateException("获取完整拼音失败", e);
                }
            } else {
                pybf.append(c);
            }
        }

        String result = pybf.toString();
        log.debug("获取完整拼音: input={}, output={}", chinese, result);
        return result;
    }
}
