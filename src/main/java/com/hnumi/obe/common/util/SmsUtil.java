package com.hnumi.obe.common.util;

import com.alibaba.fastjson2.JSONObject;
import com.aliyuncs.CommonRequest;
import com.aliyuncs.CommonResponse;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.http.MethodType;
import com.aliyuncs.profile.DefaultProfile;
import com.hnumi.obe.common.CommonProperties;
import com.hnumi.obe.common.enums.SmsTemplateCode;
import com.hnumi.obe.common.entity.ResultCode;
import lombok.extern.slf4j.Slf4j;

import static com.hnumi.obe.common.exception.ServiceExceptionUtil.exception;

/**
 * 短信工具类
 * 用于生成验证码和发送短信
 * 支持登录和注册两种短信模板
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Slf4j
public final class SmsUtil {
    
    private SmsUtil() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * 生成5位数字验证码
     *
     * @return 5位数字验证码
     */
    public static String getPhoneCode() {
        return StringUtil.randomNumber(5);
    }

    /**
     * 发送短信验证码
     *
     * @param phone 手机号
     * @param code 验证码
     * @param templateCode 短信模板类型
     * @throws RuntimeException 发送失败时抛出异常
     */
    public static void sendCode(String phone, String code, SmsTemplateCode templateCode) {
        if (phone == null || code == null || templateCode == null) {
            throw exception(ResultCode.PHONE_PARAM_ERROR);
        }

        // 创建阿里云客户端
        DefaultProfile profile = DefaultProfile.getProfile(
                CommonProperties.SMS_REGION_ID,
                CommonProperties.SMS_ACCESS_KEY_ID,
                CommonProperties.SMS_ACCESS_SECRET);
        IAcsClient client = new DefaultAcsClient(profile);

        // 构建请求
        CommonRequest request = new CommonRequest();
        request.setSysMethod(MethodType.POST);
        request.setSysDomain("dysmsapi.aliyuncs.com");
        request.setSysAction("SendSms");
        request.setSysVersion("2017-05-25");
        request.putQueryParameter("RegionId", CommonProperties.SMS_REGION_ID);
        request.putQueryParameter("PhoneNumbers", phone);
        request.putQueryParameter("SignName", CommonProperties.SMS_SIGN_NAME);

        // 设置模板代码
        String templateCodeValue = switch (templateCode) {
            case LOGIN -> CommonProperties.SMS_LOGIN_TEMPLATE_CODE;
            case REGISTER -> CommonProperties.SMS_REGISTER_TEMPLATE_CODE;
        };
        request.putQueryParameter("TemplateCode", templateCodeValue);

        // 设置模板参数
        request.putQueryParameter("TemplateParam", "{\"code\":" + code + "}");

        try {
            // 发送请求
            CommonResponse response = client.getCommonResponse(request);
            String data = response.getData();
            JSONObject jsonObject = JSONObject.parse(data);
            
            // 检查响应
            String responseCode = jsonObject.getString("Code");
            String message = jsonObject.getString("Message");
            
            if (!"OK".equals(responseCode)) {
                log.error("短信发送失败: phone={}, code={}, templateCode={}, error={}", 
                    phone, code, templateCode, message);
                throw exception(ResultCode.PHONE_CODE_SEND_ERROR.getCode(), message);
            }
            
            log.info("短信发送成功: phone={}, code={}, templateCode={}", 
                phone, code, templateCode);
                
        } catch (ClientException e) {
            log.error("短信发送异常: phone={}, code={}, templateCode={}", 
                phone, code, templateCode, e);
            throw exception(ResultCode.PHONE_CODE_SEND_ERROR);
        }
    }
}
