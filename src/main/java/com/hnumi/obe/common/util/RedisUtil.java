package com.hnumi.obe.common.util;

import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.DataType;
import org.springframework.data.redis.core.BoundListOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * Redis工具类
 * 提供Redis操作的各种方法，包括：
 * 1. 基本操作：设置、获取、删除、过期时间等
 * 2. Hash操作：hget、hset、hdel等
 * 3. Set操作：sadd、smembers、srem等
 * 4. List操作：lpush、lrange、lpop等
 * 5. 其他操作：keys、type、ttl等
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Component
@Slf4j
public final class RedisUtil {

    /**
     * Redis模板
     */
    private static RedisTemplate<String, Object> redisTemplate;

    @Autowired
    public void setRedisTemplate(RedisTemplate<String, Object> redisTemplate) {
        RedisUtil.redisTemplate = redisTemplate;
    }

    /**
     * 指定缓存过期时间
     *
     * @param key  键
     * @param time 过期时间(秒)
     * @return 返回是否设置成功
     */
    public static boolean expire(String key, long time) {
        try {
            if (time > 0) {
                redisTemplate.expire(key, time, TimeUnit.SECONDS);
                log.debug("设置过期时间成功: key={}, time={}", key, time);
            }
            return true;
        } catch (Exception e) {
            log.error("设置过期时间失败: key={}, time={}", key, time, e);
            return false;
        }
    }

    /**
     * 根据key获取过期时间
     *
     * @param key 键 不能为null
     * @return 时间(秒) 返回0代表为永久有效
     */
    public static long getExpire(String key) {
        try {
            return redisTemplate.getExpire(key, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("获取过期时间失败: key={}", key, e);
            return 0;
        }
    }

    /**
     * 判断key是否存在
     *
     * @param key 键
     * @return true 存在 false不存在
     */
    public static boolean hasKey(String key) {
        try {
            return redisTemplate.hasKey(key);
        } catch (Exception e) {
            log.error("判断key是否存在失败: key={}", key, e);
            return false;
        }
    }

    /**
     * 删除缓存
     *
     * @param key 可以传一个值或多个
     */
    public static void del(String... key) {
        if (key != null && key.length > 0) {
            try {
                if (key.length == 1) {
                    redisTemplate.delete(key[0]);
                    log.debug("删除缓存成功: key={}", key[0]);
                } else {
                    redisTemplate.delete(Arrays.asList(key));
                    log.debug("批量删除缓存成功: keys={}", Arrays.toString(key));
                }
            } catch (Exception e) {
                log.error("删除缓存失败: keys={}", Arrays.toString(key), e);
            }
        }
    }

    /**
     * 获取缓存
     *
     * @param key 键
     * @return 值
     */
    public static Object get(String key) {
        try {
            return key == null ? null : redisTemplate.opsForValue().get(key);
        } catch (Exception e) {
            log.error("获取缓存失败: key={}", key, e);
            return null;
        }
    }

    /**
     * 设置缓存
     *
     * @param key   键
     * @param value 值
     * @return true成功 false失败
     */
    public static boolean set(String key, Object value) {
        try {
            redisTemplate.opsForValue().set(key, value);
            log.debug("设置缓存成功: key={}", key);
            return true;
        } catch (Exception e) {
            log.error("设置缓存失败: key={}", key, e);
            return false;
        }
    }

    /**
     * 设置缓存并设置过期时间
     *
     * @param key   键
     * @param value 值
     * @param time  时间(秒) time要大于0 如果time小于等于0 将设置无限期
     * @return true成功 false失败
     */
    public static boolean set(String key, Object value, long time) {
        try {
            if (time > 0) {
                redisTemplate.opsForValue().set(key, value, time, TimeUnit.SECONDS);
                log.debug("设置缓存成功(带过期时间): key={}, time={}", key, time);
            } else {
                set(key, value);
            }
            return true;
        } catch (Exception e) {
            log.error("设置缓存失败: key={}, time={}", key, time, e);
            return false;
        }
    }

    /**
     * 递增
     *
     * @param key   键
     * @param delta 要增加几(大于0)
     * @return 递增后的值
     * @throws RuntimeException 递增因子必须大于0
     */
    public static long incr(String key, long delta) {
        if (delta < 0) {
            throw new RuntimeException("递增因子必须大于0");
        }
        try {
            return redisTemplate.opsForValue().increment(key, delta);
        } catch (Exception e) {
            log.error("递增失败: key={}, delta={}", key, delta, e);
            throw e;
        }
    }

    /**
     * 递减
     *
     * @param key   键
     * @param delta 要减少几(大于0)
     * @return 递减后的值
     * @throws RuntimeException 递减因子必须大于0
     */
    public static long decr(String key, long delta) {
        if (delta < 0) {
            throw new RuntimeException("递减因子必须大于0");
        }
        try {
            return redisTemplate.opsForValue().decrement(key, delta);
        } catch (Exception e) {
            log.error("递减失败: key={}, delta={}", key, delta, e);
            throw e;
        }
    }

    /**
     * 获取hash中的属性
     *
     * @param key  键 不能为null
     * @param item 项 不能为null
     * @return 值
     */
    public static Object hget(String key, String item) {
        try {
            return redisTemplate.opsForHash().get(key, item);
        } catch (Exception e) {
            log.error("获取hash属性失败: key={}, item={}", key, item, e);
            return null;
        }
    }

    /**
     * 获取hashKey对应的所有键值
     *
     * @param key 键
     * @return 对应的多个键值
     */
    public static Map<Object, Object> hmget(String key) {
        try {
            return redisTemplate.opsForHash().entries(key);
        } catch (Exception e) {
            log.error("获取hash所有键值失败: key={}", key, e);
            return Collections.emptyMap();
        }
    }

    /**
     * HashSet
     *
     * @param key 键
     * @param map 对应多个键值
     * @return true成功 false失败
     */
    public static boolean hmset(String key, Map<String, Object> map) {
        try {
            redisTemplate.opsForHash().putAll(key, map);
            log.debug("设置hash缓存成功: key={}", key);
            return true;
        } catch (Exception e) {
            log.error("设置hash缓存失败: key={}", key, e);
            return false;
        }
    }

    /**
     * HashSet 并设置时间
     *
     * @param key  键
     * @param map  对应多个键值
     * @param time 时间(秒)
     * @return true成功 false失败
     */
    public static boolean hmset(String key, Map<String, Object> map, long time) {
        try {
            redisTemplate.opsForHash().putAll(key, map);
            if (time > 0) {
                expire(key, time);
            }
            log.debug("设置hash缓存成功(带过期时间): key={}, time={}", key, time);
            return true;
        } catch (Exception e) {
            log.error("设置hash缓存失败: key={}, time={}", key, time, e);
            return false;
        }
    }

    /**
     * 向一张hash表中放入数据,如果不存在将创建
     *
     * @param key   键
     * @param item  项
     * @param value 值
     * @return true成功 false失败
     */
    public static boolean hset(String key, String item, Object value) {
        try {
            redisTemplate.opsForHash().put(key, item, value);
            log.debug("设置hash属性成功: key={}, item={}", key, item);
            return true;
        } catch (Exception e) {
            log.error("设置hash属性失败: key={}, item={}", key, item, e);
            return false;
        }
    }

    /**
     * 向一张hash表中放入数据,如果不存在将创建
     *
     * @param key   键
     * @param item  项
     * @param value 值
     * @param time  时间(秒) 注意:如果已存在的hash表有时间,这里将会替换原有的时间
     * @return true成功 false失败
     */
    public static boolean hset(String key, String item, Object value, long time) {
        try {
            redisTemplate.opsForHash().put(key, item, value);
            if (time > 0) {
                expire(key, time);
            }
            log.debug("设置hash属性成功(带过期时间): key={}, item={}, time={}", key, item, time);
            return true;
        } catch (Exception e) {
            log.error("设置hash属性失败: key={}, item={}, time={}", key, item, time, e);
            return false;
        }
    }

    /**
     * 删除hash表中的值
     *
     * @param key  键 不能为null
     * @param item 项 可以使多个 不能为null
     */
    public static void hdel(String key, Object... item) {
        try {
            redisTemplate.opsForHash().delete(key, item);
            log.debug("删除hash属性成功: key={}, items={}", key, Arrays.toString(item));
        } catch (Exception e) {
            log.error("删除hash属性失败: key={}, items={}", key, Arrays.toString(item), e);
        }
    }

    /**
     * 判断hash表中是否有该项的值
     *
     * @param key  键 不能为null
     * @param item 项 不能为null
     * @return true存在 false不存在
     */
    public static boolean hHasKey(String key, String item) {
        try {
            return redisTemplate.opsForHash().hasKey(key, item);
        } catch (Exception e) {
            log.error("判断hash属性是否存在失败: key={}, item={}", key, item, e);
            return false;
        }
    }

    /**
     * hash递增 如果不存在,就会创建一个 并把新增后的值返回
     *
     * @param key  键
     * @param item 项
     * @param by   要增加几(大于0)
     * @return 递增后的值
     */
    public static double hincr(String key, String item, double by) {
        try {
            return redisTemplate.opsForHash().increment(key, item, by);
        } catch (Exception e) {
            log.error("hash递增失败: key={}, item={}, by={}", key, item, by, e);
            throw e;
        }
    }

    /**
     * hash递减
     *
     * @param key  键
     * @param item 项
     * @param by   要减少记(大于0)
     * @return 递减后的值
     */
    public static double hdecr(String key, String item, double by) {
        try {
            return redisTemplate.opsForHash().increment(key, item, -by);
        } catch (Exception e) {
            log.error("hash递减失败: key={}, item={}, by={}", key, item, by, e);
            throw e;
        }
    }

    /**
     * 根据key获取Set中的所有值
     *
     * @param key 键
     * @return 值
     */
    public static Set<Object> sMembers(String key) {
        try {
            return redisTemplate.opsForSet().members(key);
        } catch (Exception e) {
            log.error("获取Set所有值失败: key={}", key, e);
            return Collections.emptySet();
        }
    }

    /**
     * 根据value从一个set中查询,是否存在
     *
     * @param key   键
     * @param value 值
     * @return true存在 false不存在
     */
    public static boolean sIsMember(String key, Object value) {
        try {
            return redisTemplate.opsForSet().isMember(key, value);
        } catch (Exception e) {
            log.error("判断Set成员是否存在失败: key={}, value={}", key, value, e);
            return false;
        }
    }

    /**
     * 将数据放入set缓存
     *
     * @param key    键
     * @param values 值 可以是多个
     * @return 成功个数
     */
    public static long sAdd(String key, Object... values) {
        try {
            return redisTemplate.opsForSet().add(key, values);
        } catch (Exception e) {
            log.error("添加Set成员失败: key={}, values={}", key, Arrays.toString(values), e);
            return 0;
        }
    }

    /**
     * 将set数据放入缓存
     *
     * @param key    键
     * @param time   时间(秒)
     * @param values 值 可以是多个
     * @return 成功个数
     */
    public static long sAddAndTime(String key, long time, Object... values) {
        try {
            Long count = redisTemplate.opsForSet().add(key, values);
            if (time > 0) {
                expire(key, time);
            }
            return count;
        } catch (Exception e) {
            log.error("添加Set成员失败(带过期时间): key={}, time={}, values={}", key, time, Arrays.toString(values), e);
            return 0;
        }
    }

    /**
     * 获取set缓存的长度
     *
     * @param key 键
     * @return 长度
     */
    public static long sGetSetSize(String key) {
        try {
            return redisTemplate.opsForSet().size(key);
        } catch (Exception e) {
            log.error("获取Set长度失败: key={}", key, e);
            return 0;
        }
    }

    /**
     * 移除值为value的
     *
     * @param key    键
     * @param values 值 可以是多个
     * @return 移除的个数
     */
    public static long setRemove(String key, Object... values) {
        try {
            return redisTemplate.opsForSet().remove(key, values);
        } catch (Exception e) {
            log.error("移除Set成员失败: key={}, values={}", key, Arrays.toString(values), e);
            return 0;
        }
    }

    /**
     * 获取list缓存的内容
     *
     * @param key   键
     * @param start 开始
     * @param end   结束 0 到 -1代表所有值
     * @return 列表
     */
    public static List<Object> lGet(String key, long start, long end) {
        try {
            return redisTemplate.opsForList().range(key, start, end);
        } catch (Exception e) {
            log.error("获取List内容失败: key={}, start={}, end={}", key, start, end, e);
            return Collections.emptyList();
        }
    }

    /**
     * 获取list缓存的长度
     *
     * @param key 键
     * @return 长度
     */
    public static long lGetListSize(String key) {
        try {
            return redisTemplate.opsForList().size(key);
        } catch (Exception e) {
            log.error("获取List长度失败: key={}", key, e);
            return 0;
        }
    }

    /**
     * 通过索引 获取list中的值
     *
     * @param key   键
     * @param index 索引 index>=0时， 0 表头，1 第二个元素，依次类推；index<0时，-1，表尾，-2倒数第二个元素，依次类推
     * @return 值
     */
    public static Object lGetIndex(String key, long index) {
        try {
            return redisTemplate.opsForList().index(key, index);
        } catch (Exception e) {
            log.error("获取List索引值失败: key={}, index={}", key, index, e);
            return null;
        }
    }

    /**
     * 将list放入缓存
     *
     * @param key   键
     * @param value 值
     * @return 是否成功
     */
    public static boolean lSet(String key, Object value) {
        try {
            redisTemplate.opsForList().rightPush(key, value);
            return true;
        } catch (Exception e) {
            log.error("设置List值失败: key={}", key, e);
            return false;
        }
    }

    /**
     * 将list放入缓存
     *
     * @param key   键
     * @param value 值
     * @param time  时间(秒)
     * @return 是否成功
     */
    public static boolean lSet(String key, Object value, long time) {
        try {
            redisTemplate.opsForList().rightPush(key, value);
            if (time > 0) {
                expire(key, time);
            }
            return true;
        } catch (Exception e) {
            log.error("设置List值失败(带过期时间): key={}, time={}", key, time, e);
            return false;
        }
    }

    /**
     * 将list放入缓存
     *
     * @param key   键
     * @param value 值
     * @return 是否成功
     */
    public static boolean lSet(String key, List<Object> value) {
        try {
            redisTemplate.opsForList().rightPushAll(key, value);
            return true;
        } catch (Exception e) {
            log.error("设置List值失败: key={}", key, e);
            return false;
        }
    }

    /**
     * 将list放入缓存
     *
     * @param key   键
     * @param value 值
     * @param time  时间(秒)
     * @return 是否成功
     */
    public static boolean lSet(String key, List<Object> value, long time) {
        try {
            redisTemplate.opsForList().rightPushAll(key, value);
            if (time > 0) {
                expire(key, time);
            }
            return true;
        } catch (Exception e) {
            log.error("设置List值失败(带过期时间): key={}, time={}", key, time, e);
            return false;
        }
    }

    /**
     * 根据索引修改list中的某条数据
     *
     * @param key   键
     * @param index 索引
     * @param value 值
     * @return 是否成功
     */
    public static boolean lUpdateIndex(String key, long index, Object value) {
        try {
            redisTemplate.opsForList().set(key, index, value);
            return true;
        } catch (Exception e) {
            log.error("更新List索引值失败: key={}, index={}", key, index, e);
            return false;
        }
    }

    /**
     * 移除N个值为value
     *
     * @param key   键
     * @param count 移除多少个
     * @param value 值
     * @return 移除的个数
     */
    public static long lRemove(String key, long count, Object value) {
        try {
            return redisTemplate.opsForList().remove(key, count, value);
        } catch (Exception e) {
            log.error("移除List值失败: key={}, count={}, value={}", key, count, value, e);
            return 0;
        }
    }

    /**
     * 获取匹配的key
     *
     * @param pattern 匹配模式
     * @return 匹配的key集合
     */
    public static Set<String> keys(String pattern) {
        try {
            return redisTemplate.keys(pattern);
        } catch (Exception e) {
            log.error("获取匹配key失败: pattern={}", pattern, e);
            return Collections.emptySet();
        }
    }

    /**
     * 发布消息
     *
     * @param channel 频道
     * @param message 消息
     */
    public static void convertAndSend(String channel, Object message) {
        try {
            redisTemplate.convertAndSend(channel, message);
            log.debug("发布消息成功: channel={}", channel);
        } catch (Exception e) {
            log.error("发布消息失败: channel={}", channel, e);
        }
    }

    /**
     * 将值添加到列表右侧，并设置过期时间
     *
     * @param listKey  列表键
     * @param duration 过期时间(秒)
     * @param values   值
     */
    public static void addToListRight(String listKey, long duration, Object... values) {
        try {
            BoundListOperations<String, Object> boundListOps = redisTemplate.boundListOps(listKey);
            boundListOps.rightPushAll(values);
            if (duration > 0) {
                boundListOps.expire(duration, TimeUnit.SECONDS);
            }
            log.debug("添加列表值成功: key={}, duration={}", listKey, duration);
        } catch (Exception e) {
            log.error("添加列表值失败: key={}, duration={}", listKey, duration, e);
        }
    }

    /**
     * 获取列表范围
     *
     * @param listKey 列表键
     * @param start   开始索引
     * @param end     结束索引
     * @return 列表
     */
    public static List<Object> rangeList(String listKey, long start, long end) {
        try {
            return redisTemplate.boundListOps(listKey).range(start, end);
        } catch (Exception e) {
            log.error("获取列表范围失败: key={}, start={}, end={}", listKey, start, end, e);
            return Collections.emptyList();
        }
    }

    /**
     * 从列表右侧弹出值
     *
     * @param listKey 列表键
     * @return 弹出的值
     */
    public static Object rightPop(String listKey) {
        try {
            return redisTemplate.boundListOps(listKey).rightPop();
        } catch (Exception e) {
            log.error("从列表右侧弹出值失败: key={}", listKey, e);
            return null;
        }
    }

    /**
     * 获取key的类型
     *
     * @param key 键
     * @return 类型
     */
    public static DataType type(String key) {
        try {
            return redisTemplate.type(key);
        } catch (Exception e) {
            log.error("获取key类型失败: key={}", key, e);
            return null;
        }
    }

    /**
     * 获取key的剩余生存时间
     *
     * @param key 键
     * @return 剩余生存时间(秒)
     */
    public static long ttl(String key) {
        try {
            return redisTemplate.getExpire(key);
        } catch (Exception e) {
            log.error("获取key剩余生存时间失败: key={}", key, e);
            return -1;
        }
    }

    /**
     * 移除key的过期时间
     *
     * @param key 键
     * @return 是否成功
     */
    public static boolean persist(String key) {
        try {
            return redisTemplate.persist(key);
        } catch (Exception e) {
            log.error("移除key过期时间失败: key={}", key, e);
            return false;
        }
    }

    /**
     * 判断key是否存在
     *
     * @param key 键
     * @return 是否存在
     */
    public static boolean exists(String key) {
        try {
            return redisTemplate.hasKey(key);
        } catch (Exception e) {
            log.error("判断key是否存在失败: key={}", key, e);
            return false;
        }
    }

    /**
     * 重命名key
     *
     * @param oldKey 旧键
     * @param newKey 新键
     */
    public static void rename(String oldKey, String newKey) {
        try {
            redisTemplate.rename(oldKey, newKey);
            log.debug("重命名key成功: oldKey={}, newKey={}", oldKey, newKey);
        } catch (Exception e) {
            log.error("重命名key失败: oldKey={}, newKey={}", oldKey, newKey, e);
        }
    }

    /**
     * 获取key的类型字符串
     *
     * @param key 键
     * @return 类型字符串
     */
    public static String typeOf(String key) {
        try {
            return redisTemplate.type(key).name();
        } catch (Exception e) {
            log.error("获取key类型字符串失败: key={}", key, e);
            return null;
        }
    }
}
