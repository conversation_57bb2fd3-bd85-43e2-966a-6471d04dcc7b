package com.hnumi.obe.common.util;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List;
import java.util.Map;
import java.util.HashMap;

/**
 * Excel导出配置类
 * 用于配置Excel导出的各种参数，包括数据、表头、样式等
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExcelExportConfig {
    
    /**
     * 数据对象列表
     */
    private List<?> dataList;
    
    /**
     * 数据类型Class对象（用于注解驱动的导出）
     */
    private Class<?> dataClass;
    
    /**
     * 自定义表头列表
     * 如果为空，则使用注解或字段名作为表头
     */
    private List<String> headers;
    
    /**
     * 表头注释映射
     * key: 列索引（从0开始）或列名
     * value: 注释内容
     */
    private Map<String, String> headerComments;
    
    /**
     * 取消锁定的单元格范围
     * 格式：如"A1:D2"、"B3:C10"等
     * 为空则不进行锁定控制
     */
    private String unlockRange;
    
    /**
     * 工作表保护密码
     * 为空则不设置密码保护
     */
    private String protectionPassword;
    
    /**
     * 工作表名称
     */
    private String sheetName;
    
    /**
     * 是否自动调整列宽
     */
    private boolean autoSize;
    
    /**
     * 是否包含表头
     */
    private boolean includeHeader;
    
    /**
     * 样式配置
     */
    private StyleConfig styleConfig;
    
    /**
     * 样式配置内部类
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class StyleConfig {
        /**
         * 表头背景色（十六进制颜色值）
         */
        private String headerBackgroundColor;
        
        /**
         * 表头字体颜色（十六进制颜色值）
         */
        private String headerFontColor;
        
        /**
         * 表头字体大小
         */
        private short headerFontSize;
        
        /**
         * 表头是否加粗
         */
        private boolean headerBold;
        
        /**
         * 数据行字体大小
         */
        private short dataFontSize;
        
        /**
         * 是否显示网格线
         */
        private boolean showGridLines;
        
        /**
         * 默认列宽
         */
        private int defaultColumnWidth;
    }
    
    /**
     * 添加表头注释
     * @param columnIndex 列索引（从0开始）
     * @param comment 注释内容
     * @return 当前配置对象
     */
    public ExcelExportConfig addHeaderComment(int columnIndex, String comment) {
        if (headerComments == null) {
            headerComments = new HashMap<>();
        }
        headerComments.put(String.valueOf(columnIndex), comment);
        return this;
    }
    
    /**
     * 添加表头注释
     * @param columnName 列名
     * @param comment 注释内容
     * @return 当前配置对象
     */
    public ExcelExportConfig addHeaderComment(String columnName, String comment) {
        if (headerComments == null) {
            headerComments = new HashMap<>();
        }
        headerComments.put(columnName, comment);
        return this;
    }
    
    /**
     * 设置取消锁定的单元格范围
     * @param range 单元格范围，如"A1:D2"
     * @return 当前配置对象
     */
    public ExcelExportConfig setUnlockRange(String range) {
        this.unlockRange = range;
        return this;
    }
    
    /**
     * 设置工作表保护密码
     * @param password 保护密码
     * @return 当前配置对象
     */
    public ExcelExportConfig setProtectionPassword(String password) {
        this.protectionPassword = password;
        return this;
    }


    
    /**
     * 验证配置的有效性
     * @return 是否有效
     */
    public boolean isValid() {
        return dataList != null && !dataList.isEmpty();
    }
    
    /**
     * 获取默认样式配置
     * @return 默认样式配置
     */
    public static StyleConfig getDefaultStyleConfig() {
        return StyleConfig.builder()
                .headerBackgroundColor("#E6F3FF")
                .headerFontColor("#000000")
                .headerFontSize((short) 12)
                .headerBold(true)
                .dataFontSize((short) 11)
                .showGridLines(true)
                .defaultColumnWidth(15)
                .build();
    }
} 