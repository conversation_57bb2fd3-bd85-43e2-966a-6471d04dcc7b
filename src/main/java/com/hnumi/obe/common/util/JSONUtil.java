package com.hnumi.obe.common.util;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;


/**
 * 考核方法详情工具类
 */
@Component
@Slf4j
public class JSONUtil {

    private static  final ObjectMapper objectMapper = new ObjectMapper();


    /**
     * 解析JSON数据为指定类型的列表（类型安全版本）
     * @param json JSON字符串
     * @param clazz 目标类型的Class对象
     * @param <U> 目标类型
     * @return 指定类型的列表
     */
    public static <U> List<U> parseJson(String json, Class<U> clazz) {
        try {
            if (json == null || json.trim().isEmpty()) {
                return new ArrayList<>();
            }

            JavaType listType = objectMapper.getTypeFactory().constructCollectionType(List.class, clazz);
            return objectMapper.readValue(json, listType);
        } catch (Exception e) {
            log.error("解析JSON失败，json: {}, targetClass: {}, error: {}", json, clazz.getSimpleName(), e.getMessage());
            return new ArrayList<>();
        }
    }

    /**
     * 将考核方法详情转换为JSON字符串
     * @param assessmentMethodDetails 考核方法详情列表
     * @return JSON字符串
     */
    public static <T> String convertToJson(List<T> assessmentMethodDetails) {
        try {
            return objectMapper.writeValueAsString(assessmentMethodDetails);
        } catch (Exception e) {
            log.error("转换为JSON失败: {}", e.getMessage());
            return "[]";
        }
    }

    /**
     * 将JSON字符串转换为指定类型的对象
     * @param json JSON字符串
     * @param clazz 目标类型
     * @param <R> 返回类型
     * @return 转换后的对象
     */
    public static <R> R parseJsonToObject(String json, Class<R> clazz) {
        try {
            return objectMapper.readValue(json, clazz);
        } catch (Exception e) {
            log.error("解析JSON为对象失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 将对象转换为JSON字符串
     * @param object 要转换的对象
     * @return JSON字符串
     */
    public static String objectToJson(Object object) {
        try {
            return objectMapper.writeValueAsString(object);
        } catch (Exception e) {
            log.error("对象转换为JSON失败: {}", e.getMessage());
            return "{}";
        }
    }

    public static Map<Integer, BigDecimal> parseObject(String assessmentWeight, TypeReference<Map<Integer, BigDecimal>> typeReference) {
        try {
            return objectMapper.readValue(assessmentWeight, typeReference);
        } catch (Exception e) {
            log.error("解析JSON为对象失败: {}", e.getMessage());
            return null;
        }
    }
}