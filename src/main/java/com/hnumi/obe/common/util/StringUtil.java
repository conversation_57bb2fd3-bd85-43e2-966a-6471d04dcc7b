package com.hnumi.obe.common.util;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;

import java.util.List;

public class StringUtil extends StrUtil {

    public static boolean containsIgnoreCase(List<String> col, String str) {
        for (String c : col) {
            if (c.equalsIgnoreCase(str)) {
                return true;
            }
        }
        return false;
    }

    public static String randomNumber(int length) {
        return RandomUtil.randomNumbers(length);
    }

    public static String setNotBlank(String value, String defaultValue) {
        return isNotBlank(value) ? value : defaultValue;
    }
}

