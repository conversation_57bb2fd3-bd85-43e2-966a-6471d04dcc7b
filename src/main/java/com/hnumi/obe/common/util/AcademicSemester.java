package com.hnumi.obe.common.util;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * 学年学期信息类
 * 用于表示学年学期的相关信息
 *
 * <AUTHOR>
 * @since 2025-07-22
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AcademicSemester {

    /**
     * 年份
     */
    private Integer year;

    /**
     * 月份
     */
    private Integer month;

    /**
     * 执行年份（学年开始年份）
     */
    private Integer academicYear;

    /**
     * 学期类型（春季学期/秋季学期）
     */
    private String semesterType;

    /**
     * 学年学期字符串表示（如：2024-2025学年春季学期）
     */
    private String academicSemesterString;

    /**
     * 构造函数：根据年份和月份创建学年学期信息
     *
     * @param year  年份
     * @param month 月份
     */
    public AcademicSemester(Integer year, Integer month) {
        this.year = year;
        this.month = month;
        calculateAcademicInfo();
    }

    /**
     * 计算学年学期信息
     */
    private void calculateAcademicInfo() {
        if (year == null || month == null) {
            return;
        }

        // 根据月份判断学期类型和学年
        if (month >= 9 && month <= 12) {
            // 9-12月为秋季学期，学年开始年份为当前年份
            this.academicYear = year;
            this.semesterType = "秋季学期";
        } else if (month >= 1 && month <= 8) {
            // 1-8月为春季学期，学年开始年份为上一年
            this.academicYear = year - 1;
            this.semesterType = "春季学期";
        } else {
            throw new IllegalArgumentException("月份必须在1-12之间");
        }

        // 构建学年学期字符串
        this.academicSemesterString = String.format("%d-%d学年%s", 
                this.academicYear, this.academicYear + 1, this.semesterType);
    }

    /**
     * 判断是否为春季学期
     *
     * @return true-春季学期，false-秋季学期
     */
    public boolean isSpring() {
        return "春季学期".equals(semesterType);
    }

    /**
     * 判断是否为秋季学期
     *
     * @return true-秋季学期，false-春季学期
     */
    public boolean isAutumn() {
        return "秋季学期".equals(semesterType);
    }

    /**
     * 获取学期编号（1-春季学期，2-秋季学期）
     *
     * @return 学期编号
     */
    public Integer getSemesterCode() {
        return isSpring() ? 1 : 2;
    }

    /**
     * 获取完整的学年字符串（如：2024-2025）
     *
     * @return 学年字符串
     */
    public String getAcademicYearString() {
        if (academicYear == null) {
            return null;
        }
        return String.format("%d-%d", academicYear, academicYear + 1);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        AcademicSemester that = (AcademicSemester) o;
        return Objects.equals(year, that.year) && 
               Objects.equals(month, that.month) && 
               Objects.equals(academicYear, that.academicYear) && 
               Objects.equals(semesterType, that.semesterType);
    }

    @Override
    public int hashCode() {
        return Objects.hash(year, month, academicYear, semesterType);
    }

    @Override
    public String toString() {
        return academicSemesterString != null ? academicSemesterString : 
               String.format("AcademicSemester{year=%d, month=%d}", year, month);
    }
}
