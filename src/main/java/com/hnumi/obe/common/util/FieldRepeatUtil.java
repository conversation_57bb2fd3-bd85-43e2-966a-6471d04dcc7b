package com.hnumi.obe.common.util;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 字段重复检查工具类
 * 用于检查实体类中指定字段的值是否在数据库中已存在
 * 支持单个字段和多个字段的重复检查
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
@Slf4j
public final class FieldRepeatUtil {

    /**
     * 字段缓存
     * key: 类名.字段名
     * value: 字段对象
     */
    private static final Map<String, Field> fieldCache = new ConcurrentHashMap<>();
    
    /**
     * 列名缓存
     * key: 类名.字段名
     * value: 数据库列名
     */
    private static final Map<String, String> columnNameCache = new ConcurrentHashMap<>();

    /**
     * 实体类中id字段
     */
    private String idColumnName;

    /**
     * 实体类中id的值
     */
    private Object idColumnValue;

    /**
     * 检查实体类中指定字段的值是否重复
     *
     * @param fields 需要验证的字段数组
     * @param entity 实体类对象
     * @return 如果字段值不重复返回true，否则返回false
     * @throws IllegalArgumentException 参数错误时抛出
     * @throws IllegalAccessException 如果无法访问字段值
     */
    public Boolean fieldRepeat(String[] fields, Object entity) throws IllegalAccessException {
        if (entity == null) {
            throw new IllegalArgumentException("实体对象不能为空");
        }
        if (!(entity instanceof Model)) {
            throw new IllegalArgumentException("实体对象必须是Model类型");
        }

        try {
            // 没有校验的值返回true
            if (CollectionUtil.isEmpty(fields)) {
                return true;
            }

            checkUpdateOrSave(entity);
            return checkRepeat(fields, entity);
        } catch (IllegalAccessException e) {
            log.error("字段访问失败: entity={}, fields={}", entity.getClass().getName(), 
                Arrays.toString(fields), e);
            throw e;
        }
    }

    /**
     * 通过传入的实体类中 @TableId 注解的值是否为空，来判断是更新还是保存
     * 将值id值和id列名赋值
     *
     * @param entity 实体类对象
     * @throws IllegalAccessException 如果无法访问字段值
     */
    private void checkUpdateOrSave(Object entity) throws IllegalAccessException {
        Field[] fields = getAllFields(entity.getClass());
        for (Field field : fields) {
            field.setAccessible(true);
            if (field.isAnnotationPresent(TableId.class)) {
                TableId tableId = field.getAnnotation(TableId.class);
                Object value = field.get(entity);
                if (value != null) {
                    idColumnName = tableId.value();
                    idColumnValue = value;
                    log.debug("获取到ID字段: name={}, value={}", idColumnName, idColumnValue);
                }
                break;
            }
        }
    }

    /**
     * 检查字段值是否重复
     *
     * @param fields 需要检查的字段数组
     * @param entity 实体类对象
     * @return 如果字段值不重复返回true，否则返回false
     * @throws IllegalAccessException 如果无法访问字段值
     */
    private Boolean checkRepeat(String[] fields, Object entity) throws IllegalAccessException {
        QueryWrapper<Object> queryWrapper = new QueryWrapper<>();
        Map<String, Object> columnMap = getColumns(fields, entity);
        
        // 构建查询条件
        for (Map.Entry<String, Object> entry : columnMap.entrySet()) {
            queryWrapper.eq(entry.getKey(), entry.getValue());
        }
        
        // 如果是更新操作，需要排除自身
        if (idColumnValue != null) {
            queryWrapper.ne(idColumnName, idColumnValue);
        }
        
        // 限制只查询一条记录
        queryWrapper.last("LIMIT 1");
        
        // 执行查询
        Model model = (Model) entity;
        List<?> resultList = model.selectList(queryWrapper);
        
        boolean isEmpty = CollectionUtil.isEmpty(resultList);
        log.debug("字段重复检查结果: entity={}, fields={}, 是否重复={}", 
            entity.getClass().getName(), Arrays.toString(fields), !isEmpty);
        
        return isEmpty;
    }

    /**
     * 获取实体类中指定字段对应的数据库列名和值
     *
     * @param fields 需要获取的字段数组
     * @param entity 实体类对象
     * @return 字段名和值的映射
     * @throws IllegalAccessException 如果无法访问字段值
     */
    private Map<String, Object> getColumns(String[] fields, Object entity) throws IllegalAccessException {
        Field[] fieldList = getAllFields(entity.getClass());
        Map<String, Object> columnMap = new HashMap<>();
        
        for (Field field : fieldList) {
            field.setAccessible(true);
            if (CollectionUtil.contains(fields, field.getName())) {
                addFieldToMap(columnMap, field, entity);
            }
        }
        
        return columnMap;
    }

    /**
     * 将字段信息添加到映射中
     *
     * @param columnMap 列字段映射
     * @param field 字段
     * @param entity 实体类对象
     * @throws IllegalAccessException 如果无法访问字段值
     */
    private void addFieldToMap(Map<String, Object> columnMap, Field field, Object entity)
            throws IllegalAccessException {
        try {
            if (field.isAnnotationPresent(TableField.class)) {
                TableField tableField = field.getAnnotation(TableField.class);
                Object value = field.get(entity);
                columnMap.put(tableField.value(), value);
                log.debug("添加字段到映射: field={}, column={}, value={}", 
                    field.getName(), tableField.value(), value);
            }
        } catch (IllegalAccessException e) {
            log.error("获取字段的值错误: field={}", field.getName(), e);
            throw e;
        }
    }

    /**
     * 获取类的所有字段，包括父类字段
     *
     * @param clazz 类
     * @return 所有字段数组
     */
    private Field[] getAllFields(Class<?> clazz) {
        List<Field> fieldList = new ArrayList<>();
        while (clazz != null && clazz != Object.class) {
            fieldList.addAll(Arrays.asList(clazz.getDeclaredFields()));
            clazz = clazz.getSuperclass();
        }
        return fieldList.toArray(new Field[0]);
    }

    /**
     * 检查指定类的指定字段值是否重复
     *
     * @param field 字段名
     * @param value 字段值
     * @param clazz 实体类类型
     * @return 如果字段值不重复返回true，否则返回false
     * @throws IllegalArgumentException 参数错误时抛出
     * @throws IllegalStateException 如果无法实例化类或访问字段
     */
    public Boolean fieldRepeatByClass(String field, Object value, Class<? extends Model> clazz) {
        if (!StringUtils.hasText(field)) {
            throw new IllegalArgumentException("字段名不能为空");
        }
        if (clazz == null) {
            throw new IllegalArgumentException("类对象不能为空");
        }

        try {
            // 获取字段对应的数据库列名
            Field fieldObj = getField(field, clazz);
            if (fieldObj == null) {
                log.error("字段不存在: {}.{}", clazz.getSimpleName(), field);
                return false;
            }

            String columnName = getColumnName(fieldObj);
            log.debug("检查字段重复: {}.{} (列名: {})", clazz.getSimpleName(), field, columnName);

            // 构建查询条件
            QueryWrapper<Object> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq(columnName, value);
            queryWrapper.last("LIMIT 1");

            // 执行查询
            Model model = clazz.getDeclaredConstructor().newInstance();
            List resultList = model.selectList(queryWrapper);
            
            boolean isEmpty = CollectionUtil.isEmpty(resultList);
            log.debug("字段重复检查结果: {}.{} = {}, 是否重复: {}", 
                clazz.getSimpleName(), field, value, !isEmpty);
            
            return isEmpty;
        } catch (InstantiationException | IllegalAccessException | NoSuchMethodException
                 | SecurityException | IllegalArgumentException | InvocationTargetException e) {
            String errorMessage = String.format("检查字段重复时发生错误: %s.%s", clazz.getSimpleName(), field);
            log.error(errorMessage, e);
            throw new IllegalStateException(errorMessage, e);
        }
    }

    /**
     * 获取指定类中的字段
     * 优先从缓存中获取，缓存未命中时进行解析
     *
     * @param fieldName 字段名
     * @param clazz 类类型
     * @return 字段对象，如果不存在返回null
     */
    private Field getField(String fieldName, Class<? extends Model> clazz) {
        String cacheKey = clazz.getName() + "." + fieldName;
        return fieldCache.computeIfAbsent(cacheKey, k -> {
            try {
                Field field = clazz.getDeclaredField(fieldName);
                log.debug("获取字段: {}.{}", clazz.getSimpleName(), fieldName);
                return field;
            } catch (NoSuchFieldException e) {
                log.error("类 {} 中不存在字段 {}", clazz.getSimpleName(), fieldName);
                return null;
            }
        });
    }

    /**
     * 获取字段对应的数据库列名
     * 优先从缓存中获取，缓存未命中时进行解析
     * 如果字段有@TableField注解，则使用注解的value值
     * 否则使用字段名作为列名
     *
     * @param field 字段对象
     * @return 数据库列名
     */
    private String getColumnName(Field field) {
        String cacheKey = field.getDeclaringClass().getName() + "." + field.getName();
        return columnNameCache.computeIfAbsent(cacheKey, k -> {
            field.setAccessible(true);
            TableField tableField = field.getAnnotation(TableField.class);
            String columnName = tableField != null && StringUtils.hasText(tableField.value()) 
                ? tableField.value() 
                : field.getName();
            log.debug("获取列名: {}.{} -> {}", 
                field.getDeclaringClass().getSimpleName(), field.getName(), columnName);
            return columnName;
        });
    }

    /**
     * 清理字段缓存
     */
    public static void clearCache() {
        fieldCache.clear();
        columnNameCache.clear();
        log.info("字段缓存已清理");
    }
}