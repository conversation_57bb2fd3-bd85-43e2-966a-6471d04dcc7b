package com.hnumi.obe.common.util;

import com.hnumi.obe.common.config.Argon2Properties;
import com.password4j.Argon2Function;
import com.password4j.Password;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.security.SecureRandom;
import java.util.Base64;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Pattern;

/**
 * 密码工具类
 * 提供密码加密、验证、生成等功能
 * 线程安全
 */
@Component
public class PasswordUtil {
    private static int SALT_LENGTH = 16;
    private static final int MAX_CACHE_SIZE = 1000;
    private static final int MAX_RETRY_COUNT = 3;
    private static final int DEFAULT_PASSWORD_LENGTH = 12;
    private static final int MIN_PASSWORD_LENGTH = 8;
    public static String DEFAULT_PASSWORD = "123456";

    // 使用ThreadLocal确保每个线程使用独立的SecureRandom实例
    private static final ThreadLocal<SecureRandom> SECURE_RANDOM = ThreadLocal.withInitial(SecureRandom::new);

    // 密码强度验证的正则表达式
    private static final Pattern PASSWORD_PATTERN = Pattern.compile(
            "^(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z])(?=.*[@#$%^&+=])(?=\\S+$).{8,}$"
    );

    // 缓存最近使用的哈希结果，避免重复计算
    private static final ConcurrentHashMap<String, String> PASSWORD_CACHE = new ConcurrentHashMap<>();
    private static final AtomicInteger CACHE_SIZE = new AtomicInteger(0);

    private static Argon2Function function;

    @Autowired
    public PasswordUtil(Argon2Function function, Argon2Properties properties) {
        // 设置 Argon2Function
        PasswordUtil.function = function;

        // 设置盐值长度，确保在合理范围内
        int saltLength = properties.getSalt();
        if (saltLength < 8) {
            saltLength = 8;
        } else if (saltLength > 32) {
            saltLength = 32;
        }
        PasswordUtil.SALT_LENGTH = saltLength;

        // 设置默认密码，确保不为空且长度合适
        if (StringUtil.isNotBlank(properties.getPassword())) {
            PasswordUtil.DEFAULT_PASSWORD = properties.getPassword();
        }

        // 初始化缓存
        clearCache();
    }

    /**
     * 哈希密码 (使用Argon2)
     * 线程安全
     *
     * @param plainPassword 明文密码
     * @return 哈希后的密码
     */
    public static String hash(String plainPassword) {
        return hash(plainPassword, null);
    }

    /**
     * 使用盐值哈希密码
     * 线程安全
     *
     * @param plainPassword 明文密码
     * @param salt 盐值
     * @return 哈希后的密码
     */
    public static String hash(String plainPassword, String salt) {
        if (plainPassword == null || plainPassword.isEmpty()) {
            throw new IllegalArgumentException("密码不能为空");
        }

        String cacheKey = "hash:" + plainPassword + (salt != null ? ":" + salt : "");
        return PASSWORD_CACHE.computeIfAbsent(cacheKey, k -> {
            String result;
            if (StringUtil.isNotBlank(salt)) {
                result = Password.hash(plainPassword).addSalt(salt).with(function).getResult();
            } else {
                result = Password.hash(plainPassword).with(function).getResult();
            }

            manageCacheSize();
            return result;
        });
    }

    /**
     * 验证密码
     * 线程安全
     *
     * @param plainPassword 明文密码
     * @param hashedPassword 哈希后的密码
     * @return 验证结果
     */
    public static boolean verify(String plainPassword, String hashedPassword) {
        return verify(plainPassword, hashedPassword, null);
    }

    /**
     * 验证带盐值的密码
     * 线程安全
     *
     * @param plainPassword 明文密码
     * @param hashedPassword 哈希后的密码
     * @param salt 盐值
     * @return 验证结果
     */
    public static boolean verify(String plainPassword, String hashedPassword, String salt) {
        if (plainPassword == null || hashedPassword == null) {
            return false;
        }
        if (StringUtil.isNotBlank(salt)) {
            return Password.check(plainPassword, hashedPassword).addSalt(salt).with(function);
        } else {
            return Password.check(plainPassword, hashedPassword).with(function);
        }
    }

    /**
     * 生成随机盐值
     * 线程安全
     *
     * @return Base64编码的盐值
     */
    public static String generateSalt() {
        byte[] salt = new byte[SALT_LENGTH];
        SECURE_RANDOM.get().nextBytes(salt);
        return Base64.getEncoder().encodeToString(salt);
    }

    /**
     * 验证密码强度
     * 线程安全
     *
     * @param password 待验证的密码
     * @return 密码强度是否符合要求
     */
    public static boolean isPasswordStrong(String password) {
        if (password == null || password.length() < MIN_PASSWORD_LENGTH) {
            return true;
        }
        return !PASSWORD_PATTERN.matcher(password).matches();
    }

    /**
     * 生成随机密码
     * 线程安全
     *
     * @param length 密码长度
     * @return 生成的随机密码
     */
    public static String generateRandomPassword(int length) {
        if (length < MIN_PASSWORD_LENGTH) {
            length = DEFAULT_PASSWORD_LENGTH;
        }

        StringBuilder password = new StringBuilder();
        SecureRandom random = SECURE_RANDOM.get();
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%&_";

        // 确保密码包含至少一个大写字母
        password.append(chars.substring(0, 26).charAt(random.nextInt(26)));
        // 确保密码包含至少一个小写字母
        password.append(chars.substring(26, 52).charAt(random.nextInt(26)));
        // 确保密码包含至少一个数字
        password.append(chars.substring(52, 62).charAt(random.nextInt(10)));
        // 确保密码包含至少一个特殊字符
        password.append(chars.substring(62).charAt(random.nextInt(chars.length() - 62)));

        // 填充剩余长度
        for (int i = 4; i < length; i++) {
            int index = random.nextInt(chars.length());
            password.append(chars.charAt(index));
        }

        // 打乱密码字符顺序
        char[] passwordArray = password.toString().toCharArray();
        for (int i = passwordArray.length - 1; i > 0; i--) {
            int j = random.nextInt(i + 1);
            char temp = passwordArray[i];
            passwordArray[i] = passwordArray[j];
            passwordArray[j] = temp;
        }

        return new String(passwordArray);
    }

    /**
     * 重置密码（生成新密码并返回）
     * 线程安全
     *
     * @return 新生成的密码
     * @throws IllegalStateException 如果无法生成符合强度要求的密码
     */
    public static String resetPassword() {
        int retryCount = 0;
        String newPassword;
        do {
            newPassword = generateRandomPassword(DEFAULT_PASSWORD_LENGTH);
            retryCount++;
        } while (isPasswordStrong(newPassword) && retryCount < MAX_RETRY_COUNT);

        if (isPasswordStrong(newPassword)) {
            throw new IllegalStateException("无法生成符合强度要求的密码");
        }
        return newPassword;
    }

    /**
     * 管理缓存大小
     * 线程安全
     */
    private static void manageCacheSize() {
        if (CACHE_SIZE.incrementAndGet() > MAX_CACHE_SIZE) {
            PASSWORD_CACHE.clear();
            CACHE_SIZE.set(0);
        }
    }

    /**
     * 清理缓存
     * 线程安全
     */
    public static void clearCache() {
        PASSWORD_CACHE.clear();
        CACHE_SIZE.set(0);
    }
}
