package com.hnumi.obe.common.exception;

import com.hnumi.obe.common.entity.ResultCode;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 服务异常类
 * 用于封装业务逻辑异常
 * 支持链式调用设置异常信息
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public final class ServiceException extends RuntimeException implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 业务错误码
     */
    private Integer code;

    /**
     * 错误提示信息
     */
    private String message;

    /**
     * 空构造方法
     * 避免反序列化问题
     */
    public ServiceException() {
        super();
    }

    /**
     * 根据响应码枚举创建异常
     *
     * @param resultCode 响应码枚举
     */
    public ServiceException(ResultCode resultCode) {
        super(resultCode.getMsg());
        this.code = resultCode.getCode();
        this.message = resultCode.getMsg();
    }

    /**
     * 根据错误码和消息创建异常
     *
     * @param code 错误码
     * @param message 错误消息
     */
    public ServiceException(Integer code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }
}