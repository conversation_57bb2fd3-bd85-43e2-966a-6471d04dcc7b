package com.hnumi.obe.common.exception;

import com.hnumi.obe.common.entity.ResultCode;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * API异常工具类
 * 用于创建和管理API调用过程中的异常信息
 * 支持异常消息的模板化和参数化
 * 专门处理API调用相关的异常，如接口调用失败、参数验证失败等
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Slf4j
public final class APIExceptionUtil extends BaseExceptionUtil {
    private static final APIExceptionUtil INSTANCE = new APIExceptionUtil();

    private APIExceptionUtil() {
        // 私有构造函数
    }

    @Override
    protected String getExceptionType() {
        return "API";
    }

    /**
     * API错误码提示模板
     * 使用ConcurrentHashMap保证线程安全
     */
    private static final ConcurrentMap<Integer, String> API_MESSAGES = new ConcurrentHashMap<>();


    /**
     * 添加API错误码与提示信息的映射
     *
     * @param code 错误码
     * @param message 提示信息
     */
    public static void put(Integer code, String message) {
        if (code != null && message != null) {
            API_MESSAGES.put(code, message);
        }
    }

    /**
     * 删除API错误码与提示信息的映射
     *
     * @param code 错误码
     * @param message 提示信息
     * @return 是否删除成功
     */
    public static boolean delete(Integer code, String message) {
        return code != null && message != null && API_MESSAGES.remove(code, message);
    }

    // ========== 静态方法 ==========

    /**
     * 创建指定结果码的 APIException 异常
     *
     * @param resultCode 结果码
     * @return 异常
     */
    public static APIException exception(ResultCode resultCode) {
        String messagePattern = INSTANCE.getMessagePattern(resultCode);
        return createException(resultCode.getCode(), messagePattern);
    }

    /**
     * 创建指定结果码的 APIException 异常，支持参数格式化
     *
     * @param resultCode 结果码
     * @param params 格式化参数
     * @return 异常
     */
    public static APIException exception(ResultCode resultCode, Object... params) {
        String messagePattern = INSTANCE.getMessagePattern(resultCode);
        return createException(resultCode.getCode(), messagePattern, params);
    }

    /**
     * 创建指定编号的 APIException 异常
     *
     * @param code 错误编号
     * @return 异常
     */
    public static APIException exception(Integer code) {
        return createException(code, API_MESSAGES.get(code));
    }

    /**
     * 创建指定编号和消息的 APIException 异常
     *
     * @param code 错误编号
     * @param message 错误消息
     * @return 异常
     */
    public static APIException exception(Integer code, String message) {
        return createException(code, message);
    }

    /**
     * 创建指定编号的 APIException 异常，支持参数格式化
     *
     * @param code 错误编号
     * @param params 格式化参数
     * @return 异常
     */
    public static APIException exception(Integer code, Object... params) {
        return createException(code, API_MESSAGES.get(code), params);
    }

    /**
     * 创建 APIException 异常
     *
     * @param code 错误编号
     * @param messagePattern 消息模板
     * @param params 格式化参数
     * @return 异常
     */
    private static APIException createException(Integer code, String messagePattern, Object... params) {
        String message = INSTANCE.formatMessage(code, messagePattern, params);
        return new APIException(code, message);
    }

    // ========== 格式化方法 ==========

    /**
     * 将错误编号对应的消息使用参数进行格式化
     *
     * @param code 错误编号
     * @param messagePattern 消息模板
     * @param params 格式化参数
     * @return 格式化后的提示信息
     */
    private static String doFormat(String code, String messagePattern, Object... params) {
        if (messagePattern == null) {
            log.error("[doFormat][API错误码({})对应的提示模板为空]", code);
            return null;
        }

        if (params == null || params.length == 0) {
            return messagePattern;
        }

        StringBuilder messageBuilder = new StringBuilder(messagePattern.length() + 50);
        int currentIndex = 0;
        int paramIndex = 0;

        while (paramIndex < params.length) {
            int placeholderIndex = messagePattern.indexOf("{}", currentIndex);
            if (placeholderIndex == -1) {
                log.error("[doFormat][参数过多：API错误码({}), 提示模板({}), 参数({})]", 
                    code, messagePattern, params);
                if (currentIndex == 0) {
                    return messagePattern;
                }
                messageBuilder.append(messagePattern.substring(currentIndex));
                return messageBuilder.toString();
            }

            messageBuilder.append(messagePattern, currentIndex, placeholderIndex);
            messageBuilder.append(params[paramIndex]);
            currentIndex = placeholderIndex + 2;
            paramIndex++;
        }

        if (messagePattern.indexOf("{}", currentIndex) != -1) {
            log.error("[doFormat][参数过少：API错误码({}), 提示模板({}), 参数({})]", 
                code, messagePattern, params);
        }

        messageBuilder.append(messagePattern.substring(currentIndex));
        return messageBuilder.toString();
    }
} 