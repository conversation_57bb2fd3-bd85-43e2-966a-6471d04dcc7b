package com.hnumi.obe.common.exception;

import com.hnumi.obe.common.entity.ResultCode;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * 基础异常工具类
 * 提供异常处理的通用功能
 * 支持异常消息的模板化和参数化
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Slf4j
public abstract class BaseExceptionUtil {
    /**
     * 错误码提示模板
     * 使用ConcurrentHashMap保证线程安全
     */
    protected static final ConcurrentMap<Integer, String> messages = new ConcurrentHashMap<>();

    /**
     * 获取异常类型名称
     * 用于日志输出
     *
     * @return 异常类型名称
     */
    protected abstract String getExceptionType();

    /**
     * 批量添加错误码与提示信息的映射
     *
     * @param messages 错误码与提示信息的映射
     */
    public static void putAll(Map<Integer, String> messages) {
        if (messages != null && !messages.isEmpty()) {
            BaseExceptionUtil.messages.putAll(messages);
        }
    }

    /**
     * 添加错误码与提示信息的映射
     *
     * @param code 错误码
     * @param message 提示信息
     */
    public static void put(Integer code, String message) {
        if (code != null && message != null) {
            BaseExceptionUtil.messages.put(code, message);
        }
    }

    /**
     * 删除错误码与提示信息的映射
     *
     * @param code 错误码
     * @param message 提示信息
     * @return 是否删除成功
     */
    public static boolean delete(String code, String message) {
        return code != null && message != null && BaseExceptionUtil.messages.remove(code, message);
    }

    /**
     * 格式化异常消息
     *
     * @param code 错误编号
     * @param messagePattern 消息模板
     * @param params 格式化参数
     * @return 格式化后的消息
     */
    protected String formatMessage(Integer code, String messagePattern, Object... params) {
        if (messagePattern == null) {
            log.error("[formatMessage][{}错误码({})对应的提示模板为空]", getExceptionType(), code);
            return null;
        }

        if (params == null || params.length == 0) {
            return messagePattern;
        }

        StringBuilder messageBuilder = new StringBuilder(messagePattern.length() + 50);
        int currentIndex = 0;
        int paramIndex = 0;

        while (paramIndex < params.length) {
            int placeholderIndex = messagePattern.indexOf("{}", currentIndex);
            if (placeholderIndex == -1) {
                log.error("[formatMessage][参数过多：{}错误码({}), 提示模板({}), 参数({})]", 
                    getExceptionType(), code, messagePattern, params);
                if (currentIndex == 0) {
                    return messagePattern;
                }
                messageBuilder.append(messagePattern.substring(currentIndex));
                return messageBuilder.toString();
            }

            messageBuilder.append(messagePattern, currentIndex, placeholderIndex);
            messageBuilder.append(params[paramIndex]);
            currentIndex = placeholderIndex + 2;
            paramIndex++;
        }

        if (messagePattern.indexOf("{}", currentIndex) != -1) {
            log.error("[formatMessage][参数过少：{}错误码({}), 提示模板({}), 参数({})]", 
                getExceptionType(), code, messagePattern, params);
        }

        messageBuilder.append(messagePattern.substring(currentIndex));
        return messageBuilder.toString();
    }

    /**
     * 获取消息模板
     *
     * @param resultCode 结果码
     * @return 消息模板
     */
    protected String getMessagePattern(ResultCode resultCode) {
        return messages.getOrDefault(resultCode.getCode(), resultCode.getMsg());
    }
} 