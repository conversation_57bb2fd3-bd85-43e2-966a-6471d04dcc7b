package com.hnumi.obe.common.exception;

import com.hnumi.obe.common.entity.ResultCode;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * API异常类
 * 用于封装API调用过程中的异常
 * 继承自RuntimeException，支持运行时异常处理
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@EqualsAndHashCode(callSuper = true)
@Data
public final class APIException extends RuntimeException implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 业务错误码
     */
    private Integer code;

    /**
     * 错误提示信息
     */
    private String message;

    /**
     * 空构造方法
     * 避免反序列化问题
     */
    public APIException() {
        super();
    }

    /**
     * 根据响应码枚举创建异常
     *
     * @param resultCode 响应码枚举
     */
    public APIException(ResultCode resultCode) {
        super(resultCode.getMsg());
        this.code = resultCode.getCode();
        this.message = resultCode.getMsg();
    }

    /**
     * 根据错误码和消息创建异常
     *
     * @param code 错误码
     * @param message 错误消息
     */
    public APIException(Integer code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }
}