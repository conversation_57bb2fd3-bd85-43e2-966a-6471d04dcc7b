package com.hnumi.obe.common;

import com.hnumi.obe.common.config.OBEProperties;
import jakarta.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 公共属性配置类
 * 用于管理系统中使用的各种配置属性
 * 包括Redis键前缀、资源模板、短信服务等配置
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Component
public class CommonProperties {
    /**
     * Redis相关配置
     */
    public static String REDIS_KEY_PREFIX_PHONE_CODE; // 手机验证码Redis键前缀
    public static String REDIS_KEY_PREFIX_TENANT;     // 租户信息Redis键前缀

    /**
     * 短信服务配置
     */
    public static String SMS_SIGN_NAME;               // 短信签名
    public static String SMS_REGION_ID;               // 短信服务区域ID
    public static String SMS_ACCESS_KEY_ID;           // 短信服务访问密钥ID
    public static String SMS_ACCESS_SECRET;           // 短信服务访问密钥
    public static String SMS_REGISTER_TEMPLATE_CODE;  // 注册短信模板代码
    public static String SMS_LOGIN_TEMPLATE_CODE;     // 登录短信模板代码

    @Autowired
    private OBEProperties properties;

    /**
     * 初始化配置属性
     * 在Spring容器启动时自动执行
     * 从GDMSProperties中加载配置并设置到静态变量
     */
    @PostConstruct
    private void init() {
        // Redis配置初始化
        CommonProperties.REDIS_KEY_PREFIX_PHONE_CODE = properties.getRedisKeyPrefixPhoneCode();
        CommonProperties.REDIS_KEY_PREFIX_TENANT = properties.getRedisKeyPrefixTenant();

        // 短信服务配置初始化
        CommonProperties.SMS_SIGN_NAME = properties.getSms().getSignName();
        CommonProperties.SMS_REGION_ID = properties.getSms().getRegionId();
        CommonProperties.SMS_ACCESS_KEY_ID = properties.getSms().getAccessKeyId();
        CommonProperties.SMS_ACCESS_SECRET = properties.getSms().getAccessSecret();
        CommonProperties.SMS_REGISTER_TEMPLATE_CODE = properties.getSms().getRegisterTemplateCode();
        CommonProperties.SMS_LOGIN_TEMPLATE_CODE = properties.getSms().getLoginTemplateCode();
    }
}
