package com.hnumi.obe.base.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 导出学生信息
 */
@Data
public class StudentExportVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 学生信息
     */
    @ExcelProperty("学生姓名")
    private String studentName;
    /**
     * 学号
     */
    @ExcelProperty("学号")
    private Long studentNumber;
    /**
     * 性别
     */
    @ExcelProperty("性别")
    private String gender;
    /**
     * 手机号
     */
    @ExcelProperty("手机号")
    private String phone;
    /**
     * 邮箱
     */
    @ExcelProperty("邮箱")
    private String email;
    /**
     * 入学年份
     */
    @ExcelProperty("入学年份")
    private String entranceYear;
    /**
     * 学籍状态
     */
    @ExcelProperty("学籍状态")
    private String studentStatus;
    /**
     * 所属学院
     */
    @ExcelProperty("学院名称")
    private String academyName;
    /**
     * 所属专业
     */
    @ExcelProperty("专业名称")
    private String majorName;
    /**
     * 所属班级
     */
    @ExcelProperty("班级名称")
    private String className;
    /**
     * 状态
     */
    @ExcelProperty("状态")
    private String status;
}
