package com.hnumi.obe.base.vo;

import com.hnumi.obe.system.vo.UserVO;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 学生表 视图对象（VO）
 * 
 * VO（View Object）用于展示层，把某个指定页面（或组件）的所有数据封装起来
 * 主要用于：
 * 1. 展示层数据封装
 * 2. 数据格式转换（如日期格式化）
 * 3. 数据脱敏处理
 * 4. 前端展示优化
 */
@Data
public class StudentVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 学生id
     */
    private Long id;
    
    /**
     * 学号
     */
    private String studentNumber;
    
    /**
     * 用户基本信息
     */
    private UserVO user;
    
    /**
     * 班级信息
     */
    private ClassVO classInfo;
    
    /**
     * 专业信息
     */
    private MajorVO major;
    
    /**
     * 学院信息
     */
    private AcademyVO academy;
    
    /**
     * 入学年份
     */
    private String entranceYear;
    
    /**
     * 学籍状态，-1表示毕业，0表示在读
     */
    private Integer studentStatus;
    
    /**
     * 记录状态{0:正常状态；-1:删除状态；}
     */
    private Integer status;
    
    /**
     * 记录创建者
     */
    private Long creator;
    
    /**
     * 记录创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 记录最后修改者
     */
    private Long modifier;
    
    /**
     * 记录最后修改时间
     */
    private LocalDateTime modifyTime;
    
    // 为了兼容前端，提供一些便捷的getter方法
    
    /**
     * 学生姓名（来自用户信息）
     */
    public String getName() {
        return user != null ? user.getName() : null;
    }
    
    /**
     * 性别（来自用户信息）
     */
    public Integer getGender() {
        return user != null ? user.getGender() : null;
    }
    
    /**
     * 手机号（来自用户信息）
     */
    public String getPhone() {
        return user != null ? user.getPhone() : null;
    }
    
    /**
     * 邮箱（来自用户信息）
     */
    public String getEmail() {
        return user != null ? user.getEmail() : null;
    }
    
    /**
     * 班级ID
     */
    public Long getClassId() {
        return classInfo != null ? classInfo.getClassId() : null;
    }
    
    /**
     * 班级名称
     */
    public String getClassValue() {
        return classInfo != null ? classInfo.getClassName() : null;
    }
    
    /**
     * 专业ID
     */
    public Long getMajorId() {
        return major != null ? major.getMajorId() : null;
    }
    
    /**
     * 专业名称
     */
    public String getMajorName() {
        return major != null ? major.getMajorName() : null;
    }
    
    /**
     * 学院ID
     */
    public Long getCollegeId() {
        return academy != null ? academy.getAcademyId() : null;
    }
    
    /**
     * 学院名称
     */
    public String getCollege() {
        return academy != null ? academy.getAcademyName() : null;
    }
    
    /**
     * 班级信息VO
     */
    @Data
    public static class ClassVO implements Serializable {
        private Long classId;
        private String className;
        private String classCode;
        private Integer grade;
    }
    
    /**
     * 专业信息VO
     */
    @Data
    public static class MajorVO implements Serializable {
        private Long majorId;
        private String majorName;
        private String majorCode;
        private Long academyId;
    }
    
    /**
     * 学院信息VO
     */
    @Data
    public static class AcademyVO implements Serializable {
        private Long academyId;
        private String academyName;
        private String academyCode;
    }
} 