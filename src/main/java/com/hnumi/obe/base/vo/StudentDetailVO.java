package com.hnumi.obe.base.vo;

import com.hnumi.obe.system.vo.UserVO;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 学生详细信息视图对象（VO）
 * 
 * 专门用于学生详细信息展示，包含完整的关联信息
 * 主要用于：
 * 1. 学生详情页面展示
 * 2. 学生信息编辑表单回显
 * 3. 包含创建人和修改人的详细信息
 */
@Data
public class StudentDetailVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 学生id
     */
    private Long id;
    
    /**
     * 学号
     */
    private String studentNumber;
    
    /**
     * 用户基本信息
     */
    private UserVO user;
    
    /**
     * 班级信息
     */
    private ClassDetailVO classInfo;
    
    /**
     * 专业信息
     */
    private MajorDetailVO major;
    
    /**
     * 学院信息
     */
    private AcademyDetailVO academy;
    
    /**
     * 入学年份
     */
    private String entranceYear;
    
    /**
     * 学籍状态，-1表示毕业，0表示在读
     */
    private Integer studentStatus;
    
    /**
     * 学籍状态文本
     */
    private String studentStatusText;
    
    /**
     * 记录状态{0:正常状态；-1:删除状态；1:停用状态；}
     */
    private Integer status;
    
    /**
     * 记录状态文本
     */
    private String statusText;
    
    /**
     * 记录创建者ID
     */
    private Long creator;
    
    /**
     * 记录创建者姓名
     */
    private String creatorName;
    
    /**
     * 记录创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 记录最后修改者ID
     */
    private Long modifier;
    
    /**
     * 记录最后修改者姓名
     */
    private String modifierName;
    
    /**
     * 记录最后修改时间
     */
    private LocalDateTime modifyTime;
    
    // 为了兼容前端，提供一些便捷的getter方法
    
    /**
     * 获取学生姓名
     */
    public String getName() {
        return user != null ? user.getName() : null;
    }
    
    /**
     * 获取性别
     */
    public Integer getGender() {
        return user != null ? user.getGender() : null;
    }
    
    /**
     * 获取性别文本
     */
    public String getGenderText() {
        Integer gender = getGender();
        if (gender == null) return "未知";
        return switch (gender) {
            case 1 -> "男";
            case 2 -> "女";
            default -> "保密";
        };
    }
    
    /**
     * 获取班级ID
     */
    public Long getClassId() {
        return classInfo != null ? classInfo.getClassId() : null;
    }
    
    /**
     * 获取班级名称
     */
    public String getClassName() {
        return classInfo != null ? classInfo.getClassName() : null;
    }
    
    /**
     * 获取专业ID
     */
    public Long getMajorId() {
        return major != null ? major.getMajorId() : null;
    }
    
    /**
     * 获取专业名称
     */
    public String getMajorName() {
        return major != null ? major.getMajorName() : null;
    }
    
    /**
     * 获取学院ID
     */
    public Long getCollegeId() {
        return academy != null ? academy.getAcademyId() : null;
    }
    
    /**
     * 获取学院名称
     */
    public String getCollege() {
        return academy != null ? academy.getAcademyName() : null;
    }
    
    /**
     * 班级详细信息VO
     */
    @Data
    public static class ClassDetailVO implements Serializable {
        private Long classId;
        private String className;
        private String classCode;
        private Integer grade;
        private String description;
        private Integer studentCount;
    }
    
    /**
     * 专业详细信息VO
     */
    @Data
    public static class MajorDetailVO implements Serializable {
        private Long majorId;
        private String majorName;
        private String majorCode;
        private Long academyId;
        private String description;
        private Integer duration; // 学制
    }
    
    /**
     * 学院详细信息VO
     */
    @Data
    public static class AcademyDetailVO implements Serializable {
        private Long academyId;
        private String academyName;
        private String academyCode;
        private String description;
        private String contactPhone;
        private String contactEmail;
    }
} 