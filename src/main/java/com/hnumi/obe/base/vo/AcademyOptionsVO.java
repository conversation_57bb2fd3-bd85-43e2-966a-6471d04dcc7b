package com.hnumi.obe.base.vo;

import lombok.Data;

import java.io.Serializable;

@Data
public class AcademyOptionsVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 学院id
     * 字段类型: LOng
     * 字段名称：collageId
     * 展示说明：用于选择专业对应的学院的接口id字段
     */
    private Long value;
    /**
     * 学院名称
     * 字段类型: String
     * 字段名称：collageName
     * 展示说明：用于用于选择专业对应的学院的接口学院名字段
     */
    private String label;
}
