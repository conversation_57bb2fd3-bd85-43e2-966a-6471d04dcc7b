package com.hnumi.obe.base.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 学院导出VO
 *
 * 用于导出专业到Excel表
 */
@Data
public class MajorExportVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 专业名称
     * 字段类型：String
     * 字段名称：专业名称
     * 展示说明：用于导出专业展示专业名称
     */
    @ExcelProperty("专业名称")
    private String majorName;
    /**
     * 专业代码
     * 字段类型：String
     * 字段名称：专业代码
     * 展示说明：用于导出专业展示专业代码
     */
    @ExcelProperty("专业代码")
    private String majorCode;
    /**
     * 专业概况
     * 字段类型：String
     * 字段名称：专业概况
     * 展示说明：用于导出专业展示专业概况
     */
    @ExcelProperty("专业概况")
    private String professionalOverview;
    /**
     * 所属学院名称
     * 字段类型：String
     * 字段名称：所属学院名称
     * 展示说明：用于导出专业展示所属学院名称
     */
    @ExcelProperty("学院名称")
    private String academyName;
    /**
     * 所属学院院长名称
     * 字段类型：String
     * 字段名称：所属学院院长名称
     * 展示说明：用于导出专业展示所属学院院长名称
     */
    @ExcelProperty("院长名称")
    private String academyLeaderName;
    /**
     * 状态
     * 字段类型：String
     * 字段名称：状态
     * 展示说明：用于导出专业展示状态
     */
    @ExcelProperty("状态")
    private String status;
}
