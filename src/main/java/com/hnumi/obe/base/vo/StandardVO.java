package com.hnumi.obe.base.vo;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 工程教育认证标准库表 视图对象（VO）
 * 
 * VO（View Object）用于展示层，把某个指定页面（或组件）的所有数据封装起来
 * 主要用于：
 * 1. 展示层数据封装
 * 2. 数据格式转换（如日期格式化）
 * 3. 数据脱敏处理
 * 4. 前端展示优化
 */
@Data
public class StandardVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 工程教育认证标准库ID
     * 字段类型：BInteger
     * 字段名称：id
     * 数据库字段：id
     * 展示说明：用于前端展示的工程教育认证标准库ID
     */
    private Integer id;
    /**
     * 专业ID
     * 字段类型：Long
     * 字段名称：standardVersion
     * 数据库字段：standard_version
     * 展示说明：用于前端展示的专业ID
     */
    private Long standardVersion;
    /**
     * 班级名称
     * 字段类型：String
     * 字段名称：standardName
     * 数据库字段：standard_name
     * 展示说明：用于前端展示的班级名称
     */
    private String standardName;
    /**
     * 工程认证指标点描述
     * 字段类型：String
     * 字段名称：standardDescription
     * 数据库字段：standard_description
     * 展示说明：用于前端展示的工程认证指标点描述
     */
    private String standardDescription;

    /**
     * 科
     */
    private String disciplineType;

    /**
     * 发布日期
     */
    private LocalDate releaseDate;
    /**
     * 指标点或者工程认证版本，parent_id=0是工程认证版本，非0是对应的认证版本的指标
     * 字段类型：Integer
     * 字段名称：parentId
     * 数据库字段：parent_id
     * 展示说明：用于前端展示的指标点或者工程认证版本，parent_id=0是工程认证版本，非0是对应的认证版本的指标
     */
    private Integer parentId;
    /**
     * 记录状态{0:正常状态；-1:删除状态；1:停用状态；}
     * 字段类型：Integer
     * 字段名称：status
     * 数据库字段：status
     * 展示说明：用于前端展示的记录状态{0:正常状态；-1:删除状态；1:停用状态；}
     */
    private Integer status;
    /**
     * 记录创建人
     * 字段类型：Long
     * 字段名称：creator
     * 数据库字段：creator
     * 展示说明：用于前端展示的记录创建人
     */
    private Long creator;
    /**
     * 记录创建时间
     * 字段类型：LocalDateTime
     * 字段名称：createTime
     * 数据库字段：create_time
     * 展示说明：用于前端展示的记录创建时间
     */
    private LocalDateTime createTime;
    /**
     * 记录最后修改人
     * 字段类型：Long
     * 字段名称：modifier
     * 数据库字段：modifier
     * 展示说明：用于前端展示的记录最后修改人
     */
    private Long modifier;
    /**
     * 记录最后修改时间
     * 字段类型：LocalDateTime
     * 字段名称：modifyTime
     * 数据库字段：modify_time
     * 展示说明：用于前端展示的记录最后修改时间
     */
    private LocalDateTime modifyTime;

    List<StandardVO> requirements;
} 