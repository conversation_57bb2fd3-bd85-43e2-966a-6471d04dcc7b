package com.hnumi.obe.base.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 学院统计信息
 * <p>
 * 用于前端展示学院统计数据
 */
@Data
public class AcademyStatisticsVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 学部总数
     * 字段类型：Long
     * 字段名称：学院总数
     * 展示说明：展示学院总数
     */
    private Long totalAcademies;
    /**
     * 活跃学院数
     * 字段类型：Long
     * 字段名称：活跃学院总数
     * 展示说明：展示活跃学院总数
     */
    private Long activeAcademies;
    /**
     * 专业总数
     * 字段类型：Long
     * 字段名称：专业总数
     * 展示说明：展示专业总数
     */
    private Long totalMajors;
    /**
     * 班级总数
     * 字段类型：Long
     * 字段名称：班级总数
     * 展示说明：展示班级总数
     */
    private Long totalClasses;
    /**
     * 学院详情列表
     * 字段类型：List
     * 字段名称：学院详情列表
     * 展示说明：展示学院详情列表
     */
    List<AcademyVO> academyDetails;
}
