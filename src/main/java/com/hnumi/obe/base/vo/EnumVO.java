package com.hnumi.obe.base.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class EnumVO {

    private Map<String, Map<String, String>> map;
    private Map<String, List<Option<String>>> list;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Option<T> {
        private String label;
        private T value;
    }
}
