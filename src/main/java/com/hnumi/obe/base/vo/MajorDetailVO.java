package com.hnumi.obe.base.vo;

import com.hnumi.obe.base.entity.Academy;
import com.hnumi.obe.base.entity.Major;
import com.hnumi.obe.base.entity.Teacher;
import com.hnumi.obe.system.entity.BaseUser;

import java.time.LocalDateTime;

public record MajorDetailVO(
        Long id,
        String name,
        String professionalOverview,
        String code,
        Long academyId,
        String academyName,
        Long directorId,
        String directorName,
        String discipline,
        Long studentTotalCount,
        Long studentInSchoolCount,
        Long courseTotalCount,
        Long planCount,
        LocalDateTime createTime,
        LocalDateTime modifyTime

) {
    public static MajorDetailVO from(Major major, Academy academy, BaseUser user, Teacher teacher) {
        return new MajorDetailVO(
            major.getMajorId(),
            major.getMajorName(),
            major.getProfessionalOverview(),
            major.getMajorCode(),
            major.getAcademyId(),
            academy.getAcademyName(),
            user.getId(),
            user.getRealName(),
            major.getDiscipline(),
            0L,
            0L,
            0L,
            0L,
            major.getCreateTime(),
            major.getModifyTime()
        );
    }
}
