package com.hnumi.obe.base.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 导出教师信息
 */
@Data
public class TeacherExportVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 教师姓名
     */
    @ExcelProperty("教师姓名")
    private String teacherName;
    /**
     * 教师工号
     */
    @ExcelProperty("教师工号")
    private String teacherNumber;
    /**
     * 性别
     */
    @ExcelProperty("性别")
    private String gender;
    /**
     * 教师职称
     */
    @ExcelProperty("职称")
    private String teacherTitle;
    /**
     * 所属学院
     */
    @ExcelProperty("所属学院")
    private String academyName;
    /**
     * 手机号
     */
    @ExcelProperty("手机号")
    private String phone;
    /**
     * 状态
     */
    @ExcelProperty("状态")
    private String status;
}
