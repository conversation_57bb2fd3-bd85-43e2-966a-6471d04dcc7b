package com.hnumi.obe.base.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 学生导入结果VO
 */
@Data
public class StudentImportResultVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 是否成功（无错误）
     */
    private boolean success;
    
    /**
     * 成功导入的数量
     */
    private int successCount;
    
    /**
     * 失败的数量
     */
    private int failCount;
    
    /**
     * 总数量
     */
    private int totalCount;
    
    /**
     * 成功消息（当success=true时使用）
     */
    private String successMessage;
    
    /**
     * 错误信息列表（当success=false时使用）
     */
    private List<String> errorMessages;
    
    /**
     * 创建成功结果
     */
    public static StudentImportResultVO success(int successCount, int totalCount) {
        StudentImportResultVO result = new StudentImportResultVO();
        result.setSuccess(true);
        result.setSuccessCount(successCount);
        result.setFailCount(0);
        result.setTotalCount(totalCount);
        result.setSuccessMessage(String.format("导入完成，成功：%d条", successCount));
        return result;
    }
    
    /**
     * 创建失败结果
     */
    public static StudentImportResultVO failure(int successCount, int failCount, int totalCount, List<String> errorMessages) {
        StudentImportResultVO result = new StudentImportResultVO();
        result.setSuccess(false);
        result.setSuccessCount(successCount);
        result.setFailCount(failCount);
        result.setTotalCount(totalCount);
        result.setErrorMessages(errorMessages);
        return result;
    }
} 