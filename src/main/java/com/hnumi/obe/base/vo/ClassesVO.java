package com.hnumi.obe.base.vo;

import com.hnumi.obe.system.entity.BaseUser;
import com.hnumi.obe.system.vo.UserVO;
import lombok.Data;
import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 班级表 视图对象（VO）
 * 
 * VO（View Object）用于展示层，把某个指定页面（或组件）的所有数据封装起来
 * 主要用于：
 * 1. 展示层数据封装
 * 2. 数据格式转换（如日期格式化）
 * 3. 数据脱敏处理
 * 4. 前端展示优化
 */
@Data
public class ClassesVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 班级ID
     * 字段类型：Long
     * 字段名称：classId
     * 数据库字段：class_id
     * 展示说明：用于前端展示的班级ID
     */
    private Long classId;
    /**
     * 专业ID
     * 字段类型：Long
     * 字段名称：majorId
     * 数据库字段：major_id
     * 展示说明：用于前端展示的专业ID
     */
    private Long majorId;
    /**
     * 班级名称
     * 字段类型：String
     * 字段名称：className
     * 数据库字段：class_name
     * 展示说明：用于前端展示的班级名称
     */
    private String className;
    /**
     * 入学年份
     * 字段类型：LocalDate
     * 字段名称：entranceYear
     * 数据库字段：entrance_year
     * 展示说明：用于前端展示的入学年份
     */
    private String entranceYear;

    /**
     * 班主任ID
     * 字段类型：Long
     * 字段名称：headteacherId
     * 数据库字段：headteacher_id
     * 展示说明：用于前端展示的班主任ID
     */
    private Long headteacherId;
    /**
     * 班主任信息
     * 字段类型：UserVO
     * 字段名称：user
     * 数据库字段：headteacher_id（关联用户表）
     * 展示说明：用于前端展示的班主任信息
     */

    private UserVO headteacher;

    private UserVO user;
    /**
     * 学生人数
     * 字段类型：Integer
     * 字段名称：studentNumber
     * 数据库字段：student_number
     * 展示说明：用于前端展示的学生人数
     */
    private Integer studentNumber;
    /**
     * 班级状态 -1:毕业 0:在读
     * 字段类型：Integer
     * 字段名称：classStatus
     * 数据库字段：class_status
     * 展示说明：用于前端展示的班级状态 -1:毕业 0:在读
     */
    private Integer classStatus;
    /**
     * 记录状态{0:正常状态；-1:删除状态；1:停用状态；1:停用状态；}
     * 字段类型：Integer
     * 字段名称：status
     * 数据库字段：status
     * 展示说明：用于前端展示的记录状态{0:正常状态；-1:删除状态；1:停用状态；}
     */
    private Integer status;
    /**
     * 记录创建人
     * 字段类型：Long
     * 字段名称：creator
     * 数据库字段：creator
     * 展示说明：用于前端展示的记录创建人
     */
    private Long creator;
    /**
     * 记录创建时间
     * 字段类型：LocalDateTime
     * 字段名称：createTime
     * 数据库字段：create_time
     * 展示说明：用于前端展示的记录创建时间
     */
    private LocalDateTime createTime;
    /**
     * 记录最后修改人
     * 字段类型：Long
     * 字段名称：modifier
     * 数据库字段：modifier
     * 展示说明：用于前端展示的记录最后修改人
     */
    private Long modifier;
    /**
     * 记录最后修改时间
     * 字段类型：LocalDateTime
     * 字段名称：modifyTime
     * 数据库字段：modify_time
     * 展示说明：用于前端展示的记录最后修改时间
     */
    private LocalDateTime modifyTime;
} 