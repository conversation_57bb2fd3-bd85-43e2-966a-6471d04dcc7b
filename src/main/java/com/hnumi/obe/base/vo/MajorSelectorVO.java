package com.hnumi.obe.base.vo;

import lombok.Data;

import java.io.Serializable;

@Data
public class MajorSelectorVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 专业ID
     */
    private Long majorId;

    /**
     * 专业名称
     */
    private String majorName;

    /**
     * 专业代码
     */
    private String majorCode;

    /**
     * 专业概述
     */
    private String professionalOverview;

    /**
     * 专业方向
     */
    private String discipline;

    /**
     * 授课门数
     */
    private Integer courseCount;

    /**
     * 班级数
     */
    private Integer classCount;

    /**
     * 学期数
     */
    private Integer semesterCount;

    /**
     * 学院ID
     */
    private Long academyId;

    /**
     * 学院名称
     */
    private String academyName;

}