package com.hnumi.obe.base.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * 获取院长选项VO
 *
 * 用于前端显示可作为为院长的老师选项
 */
@Data
public class DeanOptionsVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 院长ID
     * 字段类型：Long
     * 字段名称：deanId
     * 展示说明：用于前端选择可作为院长的选项
     */
    private Long value;
    /**
     * 院长姓名
     * 字段类型：String
     * 字段名称：label
     * 展示说明：用于前端选择可作为院长的选项
     */
    private String label;
    
    /**
     * 当前担任院长的学院ID
     * 字段类型：Long
     * 字段名称：currentAcademyId
     * 展示说明：该教师当前担任院长的学院ID，如果未担任院长则为null
     */
    private Long currentAcademyId;
    
    /**
     * 当前担任院长的学院名称
     * 字段类型：String
     * 字段名称：currentAcademyName
     * 展示说明：该教师当前担任院长的学院名称，如果未担任院长则为null
     */
    private String currentAcademyName;
}
