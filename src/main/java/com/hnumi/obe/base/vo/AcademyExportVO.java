package com.hnumi.obe.base.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 学院导出VO
 *
 * 用于导出学院到Excel文件
 */
@Data
public class AcademyExportVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 学院名称
     * 字段类型: String
     * 字段名称：academyName
     * 展示说明：用于导出学院时的学院名称列
     */
    @ExcelProperty("学院名称")
    private String academyName;
    /**
     * 院长名称
     * 字段类型: String
     * 字段名称：academyPresident
     * 展示说明：用于导出学院时的院长名称列
     */
    @ExcelProperty("院长名称")
    private String academyPresident;
    /**
     * 状态
     * 字段类型: String
     * 字段名称：status
     * 展示说明：用于导出学院时的状态列
     */
    @ExcelProperty("状态")
    private String status;
}
