package com.hnumi.obe.base.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 专业导入结果视图对象
 * 用于统一返回导入操作的结果信息
 */
@Data
public class MajorImportResultVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 导入是否成功（部分成功也算成功）
     */
    private boolean success;

    /**
     * 成功导入的数量
     */
    private int successCount;

    /**
     * 失败的数量
     */
    private int failCount;

    /**
     * 总数量
     */
    private int totalCount;

    /**
     * 成功时的消息
     */
    private String successMessage;

    /**
     * 错误信息列表（失败时）
     */
    private List<String> errorMessages;

    /**
     * 创建完全成功结果
     */
    public static MajorImportResultVO success(int successCount, int totalCount) {
        MajorImportResultVO result = new MajorImportResultVO();
        result.setSuccess(true);
        result.setSuccessCount(successCount);
        result.setFailCount(0);
        result.setTotalCount(totalCount);
        result.setSuccessMessage(String.format("导入成功！共导入 %d 条专业数据", successCount));
        return result;
    }

    /**
     * 创建部分成功结果
     */
    public static MajorImportResultVO partialSuccess(int successCount, int failCount, int totalCount, List<String> errorMessages) {
        MajorImportResultVO result = new MajorImportResultVO();
        result.setSuccess(true); // 部分成功也算成功
        result.setSuccessCount(successCount);
        result.setFailCount(failCount);
        result.setTotalCount(totalCount);
        result.setSuccessMessage(String.format("导入完成！成功导入 %d 条，失败 %d 条专业数据", successCount, failCount));
        result.setErrorMessages(errorMessages);
        return result;
    }

    /**
     * 创建完全失败结果
     */
    public static MajorImportResultVO failure(int totalCount, List<String> errorMessages) {
        MajorImportResultVO result = new MajorImportResultVO();
        result.setSuccess(false);
        result.setSuccessCount(0);
        result.setFailCount(totalCount);
        result.setTotalCount(totalCount);
        result.setErrorMessages(errorMessages);
        return result;
    }
} 