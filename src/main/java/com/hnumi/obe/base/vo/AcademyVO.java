package com.hnumi.obe.base.vo;

import lombok.Data;
import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 院系表 视图对象（VO）
 * 
 * VO（View Object）用于展示层，把某个指定页面（或组件）的所有数据封装起来
 * 主要用于：
 * 1. 展示层数据封装
 * 2. 数据格式转换（如日期格式化）
 * 3. 数据脱敏处理
 * 4. 前端展示优化
 */
@Data
public class AcademyVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 学院id
     * 字段类型：Long
     * 字段名称：id
     * 数据库字段：id
     * 展示说明：用于前端展示的学院id
     */
    private Long id;
    /**
     * 学院名称
     * 字段类型：String
     * 字段名称：name
     * 数据库字段：academy_name
     * 展示说明：用于前端展示的学院名称
     */
    private String name;
    /**
     * 院系负责人
     * 字段类型：String
     * 字段名称：dean
     * 数据库字段：dean
     * 展示说明：用于前端展示的院系负责人
     */
    private String dean;
    /**
     * 院系负责人 ID
     * 字段类型：Long
     * 字段名称：academyPresidentId
     * 数据库字段：academy_president_id
     * 展示说明：用于前端关联的院系负责人 ID
     */
    private Long academyPresidentId;
    /**
     * 记录状态
     * 字段类型：Integer
     * 字段名称：status
     * 数据库字段：status
     * 展示说明：用于前端展示的记录状态（0:正常，1:停用，-1:删除）
     */
    private Integer status;
    /**
     * 记录创建人
     * 字段类型：Long
     * 字段名称：creator
     * 数据库字段：creator
     * 展示说明：用于前端展示的记录创建人
     */
    private String creator;
    /**
     * 记录创建时间
     * 字段类型：LocalDateTime
     * 字段名称：createTime
     * 数据库字段：create_time
     * 展示说明：用于前端展示的记录创建时间
     */
    private LocalDateTime createTime;
    /**
     * 记录最后修改人
     * 字段类型：Long
     * 字段名称：modifier
     * 数据库字段：modifier
     * 展示说明：用于前端展示的记录最后修改人
     */
    private String updater;
    /**
     * 记录最后修改时间
     * 字段类型：LocalDateTime
     * 字段名称：modifyTime
     * 数据库字段：modify_time
     * 展示说明：用于前端展示的记录最后修改时间
     */
    private LocalDateTime updateTime;
    /**
     * 院系下的专业数量
     * 字段类型：Integer
     * 字段名称：majors
     * 展示说明：用于前端展示的院系下的专业数量
     */
    private Integer majors;
    /**
     * 院系下的班级数量
     * 字段类型：Integer
     * 字段名称：classes
     * 展示说明：用于前端展示的院系下的班级数量
     */
    private Integer classes;
    /**
     * 院系下的学生数量
     * 字段类型：Integer
     * 字段名称：students
     * 展示说明：用于前端展示的院系下的学生数量
     */
    private Integer students;
    /**
     * 院系下的教师数量
     * 字段类型：Integer
     * 字段名称：teachers
     * 展示说明：用于前端展示的院系下的教师数量
     */
    private Integer teachers;
} 