package com.hnumi.obe.base.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 教师导入结果视图对象
 * 用于统一返回导入操作的结果信息
 */
@Data
public class TeacherImportResultVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 导入是否成功
     */
    private boolean success;

    /**
     * 成功导入的数量
     */
    private int successCount;

    /**
     * 失败的数量
     */
    private int failCount;

    /**
     * 总数量
     */
    private int totalCount;

    /**
     * 成功时的消息
     */
    private String successMessage;

    /**
     * 错误信息列表（失败时）
     */
    private List<String> errorMessages;

    /**
     * 创建成功结果
     */
    public static TeacherImportResultVO success(int successCount, int totalCount) {
        TeacherImportResultVO result = new TeacherImportResultVO();
        result.setSuccess(true);
        result.setSuccessCount(successCount);
        result.setFailCount(0);
        result.setTotalCount(totalCount);
        result.setSuccessMessage(String.format("导入成功！共导入 %d 条教师数据", successCount));
        return result;
    }

    /**
     * 创建失败结果
     */
    public static TeacherImportResultVO failure(int successCount, int failCount, int totalCount, List<String> errorMessages) {
        TeacherImportResultVO result = new TeacherImportResultVO();
        result.setSuccess(false);
        result.setSuccessCount(successCount);
        result.setFailCount(failCount);
        result.setTotalCount(totalCount);
        result.setErrorMessages(errorMessages);
        return result;
    }
} 