package com.hnumi.obe.base.vo;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 专业信息表 视图对象（VO）
 * 
 * VO（View Object）用于展示层，把某个指定页面（或组件）的所有数据封装起来
 * 主要用于：
 * 1. 展示层数据封装
 * 2. 数据格式转换（如日期格式化）
 * 3. 数据脱敏处理
 * 4. 前端展示优化
 */
@Data
public class MajorVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 专业id
     * 字段类型：Long
     * 字段名称：majorId
     * 数据库字段：major_id
     * 展示说明：用于前端展示的专业id
     */
    private Long id;
    /**
     * 专业名称
     * 字段类型：String
     * 字段名称：majorName
     * 数据库字段：major_name
     * 展示说明：用于前端展示的专业名称
     */
    private String name;
    /**
     * 专业概述
     * 字段类型：String
     * 字段名称：professionalOverview
     * 数据库字段：professional_overview
     * 展示说明：用于前端展示的专业概述
     */
    private String professionalOverview;
    /**
     * 专业代码
     * 字段类型：String
     * 字段名称：majorCode
     * 数据库字段：major_code
     * 展示说明：用于前端展示的专业代码
     */
    private String code;
    /**
     * 专业负责人，教师名称（兼容性字段，实际数据从directorInfo获取）
     * 字段类型：String
     * 字段名称：director
     * 数据库字段：academy_leader_id
     * 展示说明：用于前端展示的专业负责人，教师id
     */
    private String director;

    private String discipline;
    /**
     * 记录状态{0:正常状态；-1:删除状态；1:停用状态；}
     * 字段类型：Integer
     * 字段名称：status
     * 数据库字段：status
     * 展示说明：用于前端展示的记录状态{0:正常状态；-1:删除状态；1:停用状态；}
     */
    private Integer status;
    /**
     * 记录创建人
     * 字段类型：Long
     * 字段名称：creator
     * 数据库字段：creator
     * 展示说明：用于前端展示的记录创建人
     */
    private String creator;
    /**
     * 记录创建时间
     * 字段类型：LocalDateTime
     * 字段名称：createTime
     * 数据库字段：create_time
     * 展示说明：用于前端展示的记录创建时间
     */
    private LocalDateTime createTime;
    /**
     * 记录最后修改人
     * 字段类型：Long
     * 字段名称：modifier
     * 数据库字段：modifier
     * 展示说明：用于前端展示的记录最后修改人
     */
    private String updater;
    /**
     * 记录最后修改时间
     * 字段类型：LocalDateTime
     * 字段名称：modifyTime
     * 数据库字段：modify_time
     * 展示说明：用于前端展示的记录最后修改时间
     */
    private LocalDateTime updateTime;
    /**
     * 课程数
     * 字段类型：Integer
     * 字段名称：courses
     * 数据库字段：courses
     * 展示说明：用于前端展示的课程数
     */
    private Integer courses;
    /**
     * 班级数
     * 字段类型：Integer
     * 字段名称：classes
     * 数据库字段：classes
     * 展示说明：用于前端展示的班级数
     */
    private Integer classes;
    /**
     * 学生数
     * 字段类型：Integer
     * 字段名称：students
     * 数据库字段：students
     * 展示说明：用于前端展示的学生数
     */
    private Integer students;
    /**
     * 学院名称（兼容性字段，实际数据从college获取）
     * 字段类型：String
     * 字段名称：collegeName
     * 数据库字段：college_name
     * 展示说明：用于前端展示的学院名称
     */
    private String collegeName;
    
    /**
     * 学院信息对象
     */
    private CollegeVO college;
    
    /**
     * 专业负责人信息对象
     */
    private DirectorVO directorInfo;
    
    // 为了兼容现有前端代码，提供便捷的getter方法
    
    /**
     * 获取学院ID（兼容性方法）
     */
    public Long getCollegeId() {
        return college != null ? college.getCollegeId() : null;
    }
    
    /**
     * 设置学院ID（兼容性方法）
     */
    public void setCollegeId(Long collegeId) {
        if (college == null) {
            college = new CollegeVO();
        }
        college.setCollegeId(collegeId);
    }
    
    /**
     * 获取专业负责人ID（兼容性方法）
     */
    public Long getDirectorId() {
        return directorInfo != null ? directorInfo.getDirectorId() : null;
    }
    
    /**
     * 设置专业负责人ID（兼容性方法）
     */
    public void setDirectorId(Long directorId) {
        if (directorInfo == null) {
            directorInfo = new DirectorVO();
        }
        directorInfo.setDirectorId(directorId);
    }
    
    /**
     * 学院信息VO
     */
    @Data
    public static class CollegeVO implements Serializable {
        private Long collegeId;
        private String collegeName;
        private String collegeCode;
    }
    
    /**
     * 专业负责人信息VO
     */
    @Data
    public static class DirectorVO implements Serializable {
        private Long directorId;
        private String directorName;
        private String directorNumber;
        private String directorTitle;
    }
} 