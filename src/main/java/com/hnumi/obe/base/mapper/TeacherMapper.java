package com.hnumi.obe.base.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hnumi.obe.base.entity.Teacher;
import com.hnumi.obe.base.vo.AcademyLeaderOptionsVO;
import com.hnumi.obe.base.vo.DeanOptionsVO;
import com.hnumi.obe.base.vo.EnumVO;
import com.hnumi.obe.base.vo.TeacherTitleOptionsVO;
import com.hnumi.obe.base.vo.TeacherVO;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * 教师表 Mapper 接口
 */
@Mapper
public interface TeacherMapper extends BaseMapper<Teacher> {

    @Select("select count(1) from base_teacher where academy_id = #{id} and status = 0")
    Integer getTeacherCountByAcademyId(Long id);

    @Select("select teacher_title as teacherTitle,teacher_title as teacherValue from base_teacher where status = 0 group by teacher_title")
    List<TeacherTitleOptionsVO> getTeacherTitleOptions();

    /**
     * 分页查询教师详细信息（关联基本用户信息表）
     *
     * @param page        分页参数
     * @param academyId   学院ID
     * @param teacherName 教师姓名（模糊查询）
     * @param number      工号（模糊查询）
     * @param title       职称
     * @param gender      性别
     * @param status      记录状态
     * @return 教师详细信息分页结果
     */
    Page<TeacherVO> selectTeacherPageWithDetails(
            Page<TeacherVO> page,
            @Param("academyId") Long academyId,
            @Param("teacherName") String teacherName,
            @Param("number") String number,
            @Param("title") String title,
            @Param("gender") Integer gender,
            @Param("status") Integer status);

    /**
     * 根据ID查询教师详细信息（关联基本用户信息表）
     *
     * @param teacherId 教师ID
     * @return 教师详细信息
     */
    TeacherVO selectTeacherDetailById(@Param("teacherId") Long teacherId);

    /**
     * 查询教师列表用于导出（关联基本用户信息表）
     *
     * @param academyId     学院ID
     * @param teacherName   教师姓名
     * @param teacherNumber 工号
     * @param title         职称
     * @param gender        性别
     * @param status        记录状态
     * @return 教师详细信息列表
     */
    List<TeacherVO> selectTeacherListForExport(
            @Param("academyId") Long academyId,
            @Param("teacherName") String teacherName,
            @Param("teacherNumber") String teacherNumber,
            @Param("teacher_title") String title,
            @Param("gender") Integer gender,
            @Param("status") Integer status);

    /**
     * 根据用户ID获取用户姓名（兼容旧代码）
     *
     * @param userId 用户ID
     * @return 用户姓名
     */
    @Select("SELECT u.real_name FROM sys_base_user u WHERE u.id = #{userId}")
    String getNameById(@Param("userId") Long userId);

    /**
     * 获取院长选项列表
     *
     * @return 院长选项列表
     */
    List<DeanOptionsVO> getDeanOptions();

    /**
     * 获取学院负责人选项列表
     *
     * @return 学院负责人选项列表
     */
    List<AcademyLeaderOptionsVO> getAcademyLeaderOptions();

    @Select("""
            select sbu.id as value, sbu.real_name as label from
            (select base_user_id, academy_id from base_teacher where academy_id = #{academyId} ) bt left join
            (select id, real_name from sys_base_user where user_type = '1' ) sbu
             on sbu.id = bt.base_user_id
            """)
    List<EnumVO.Option<String>> getTeacherOptionsListByAcademyId(@Param("academyId") Long academyId);

    List<TeacherVO> listTeacherOptions();

    /**
     * 批量查询教师详细信息
     * 使用 @MapKey 注解直接返回 Map 结构，以教师ID为key，TeacherVO为value
     *
     * @param teacherIds 教师ID列表
     * @return 教师ID到教师详细信息的映射
     */
    @MapKey("id")
    Map<Long, TeacherVO> selectTeacherMapByIds(@Param("teacherIds") List<Long> teacherIds);
}
