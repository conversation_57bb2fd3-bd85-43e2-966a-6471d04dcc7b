package com.hnumi.obe.base.mapper;

import com.hnumi.obe.base.entity.Student;
import com.hnumi.obe.base.vo.StudentVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * 学生表 Mapper 接口
 */
@Mapper
public interface StudentMapper extends BaseMapper<Student> {

    @Select("""
            SELECT COUNT(1)
            FROM base_student
            WHERE academy_id = #{academyId}
                AND status = 0
            """)
    Integer getStudentCountByAcademyId(Long id);

    @Select("""
            SELECT COUNT(1)
            FROM base_student
            WHERE major_id = #{majorId}
                AND status = 0
            """)
    Integer getStudentCountByMajorId(Long id);
    
    /**
     * 分页查询学生详细信息（关联基本用户信息表）
     * 
     * @param page 分页参数
     * @param academyId 学院ID
     * @param majorId 专业ID
     * @param classId 班级ID
     * @param studentStatus 学籍状态
     * @param gender 性别
     * @param entranceYear 入学年份
     * @param status 记录状态
     * @param studentName 学生姓名（模糊查询）
     * @param number 学号（模糊查询）
     * @param phone 手机号（模糊查询）
     * @param email 邮箱（模糊查询）
     * @return 学生详细信息分页结果
     */
    Page<StudentVO> selectStudentPageWithDetails(
        Page<StudentVO> page,
        @Param("academyId") Long academyId,
        @Param("majorId") Long majorId,
        @Param("classId") Long classId,
        @Param("studentStatus") Integer studentStatus,
        @Param("gender") Integer gender,
        @Param("entranceYear") String entranceYear,
        @Param("status") Integer status,
        @Param("studentName") String studentName,
        @Param("number") String number,
        @Param("phone") String phone,
        @Param("email") String email
    );
    
    /**
     * 根据ID查询学生详细信息（关联基本用户信息表）
     * 
     * @param studentId 学生ID
     * @return 学生详细信息
     */
    StudentVO selectStudentDetailById(@Param("studentId") Long studentId);
    
    /**
     * 查询学生列表用于导出（关联基本用户信息表）
     * 
     * @param academyId 学院ID
     * @param majorId 专业ID
     * @param classId 班级ID
     * @param studentStatus 学籍状态
     * @param gender 性别
     * @param entranceYear 入学年份
     * @param status 记录状态
     * @return 学生详细信息列表
     */
    List<StudentVO> selectStudentListForExport(
        @Param("academyId") Long academyId,
        @Param("majorId") Long majorId,
        @Param("classId") Long classId,
        @Param("studentStatus") Integer studentStatus,
        @Param("gender") Integer gender,
        @Param("entranceYear") String entranceYear,
        @Param("status") Integer status
    );

    /**
     * 根据教学任务ID查询学生列表（使用子查询）
     *
     * @param taskId 教学任务ID
     * @return 学生列表
     */
    @Select("""
            SELECT s.*
            FROM base_student s
            WHERE s.class_id IN (
                SELECT twc.class_id
                FROM task_worklist_classes twc
                WHERE twc.task_id = #{taskId}
            )
            AND s.status = 0
            ORDER BY s.student_number
            """)
    List<Student> selectStudentsByTaskId(@Param("taskId") Long taskId);

    /**
     * 根据教学任务ID查询学生数量（使用子查询）
     *
     * @param taskId 教学任务ID
     * @return 学生数量
     */
    @Select("""
            SELECT COUNT(*)
            FROM base_student s
            WHERE s.class_id IN (
                SELECT twc.class_id
                FROM task_worklist_classes twc
                WHERE twc.task_id = #{taskId}
            )
            AND s.status = 0
            """)
    int countStudentsByTaskId(@Param("taskId") Long taskId);
    /**
     * 首先获得课程下的所有任务ID和班级 ID，再拉取课程下的所有学生，注意尽量考虑数据库访问的性能优化
     * 这个方法主要用于获取课程下的学生列表
     */
    @Select("""
            SELECT s.*
            FROM base_student s
            WHERE s.class_id IN (
                SELECT twc.class_id
                FROM task_worklist_classes twc
                WHERE twc.task_id IN (
                    SELECT task_id
                    FROM task_worklist
                    WHERE course_id = #{courseId}
                )
            )
            AND s.status = 0
            ORDER BY s.student_number
            """)
    List<Student> selectStudentsByCourseId(@Param("courseId") Long courseId);


    /**
     * 根据班级ID统计学生数量
     *
     * @param classId 班级ID
     * @return 学生数量
     */
    @Select("""
            SELECT COUNT(*)
            FROM base_student
            WHERE class_id = #{classId}
            AND status = 0
            """)
    int countStudentsByClassId(@Param("classId") Long classId);

    /**
     * 根据班级ID列表批量统计学生数量
     *
     * @param classIds 班级ID列表
     * @return 班级ID和学生数量的映射列表
     */
    List<Map<String, Object>> countStudentsByClassIds(@Param("classIds") List<Long> classIds);

    /**
     * 学生人数统计结果类
     * 注意：这个类现在主要用于其他查询，班级学生人数统计已优化为子查询方式
     */
    class StudentCountResult {
        private Long classId;
        private Integer studentCount;

        public StudentCountResult() {}

        public StudentCountResult(Long classId, Integer studentCount) {
            this.classId = classId;
            this.studentCount = studentCount;
        }

        public Long getClassId() {
            return classId;
        }

        public void setClassId(Long classId) {
            this.classId = classId;
        }

        public Integer getStudentCount() {
            return studentCount;
        }

        public void setStudentCount(Integer studentCount) {
            this.studentCount = studentCount;
        }
    }
}
