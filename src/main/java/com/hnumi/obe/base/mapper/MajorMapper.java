package com.hnumi.obe.base.mapper;

import com.hnumi.obe.base.entity.Major;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hnumi.obe.base.vo.MajorSelectorVO;
import com.hnumi.obe.base.vo.MajorVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 专业信息表 Mapper 接口
 */
@Mapper
public interface MajorMapper extends BaseMapper<Major> {

    /**
     * 分页查询专业详细信息（关联学院和用户信息表）
     * 
     * @param page 分页参数
     * @param majorName 专业名称（模糊查询）
     * @param majorCode 专业代码（模糊查询）
     * @param academyId 学院ID
     * @param academyLeaderId 专业负责人ID
     * @param status 记录状态
     * @param createTimeStart 创建时间开始
     * @param createTimeEnd 创建时间结束
     * @return 专业详细信息分页结果
     */
    IPage<MajorVO> pageListWithDetails(
        Page<MajorVO> page,
        @Param("majorName") String majorName,
        @Param("majorCode") String majorCode,
        @Param("academyId") Long academyId,
        @Param("academyLeaderId") Long academyLeaderId,
        @Param("status") Integer status,
        @Param("createTimeStart") LocalDateTime createTimeStart,
        @Param("createTimeEnd") LocalDateTime createTimeEnd
    );

    /**
     * 根据ID查询专业详细信息（关联学院和用户信息表）
     * 
     * @param majorId 专业ID
     * @return 专业详细信息
     */
    MajorVO selectMajorDetailById(@Param("majorId") Long majorId);

    /**
     * 查询专业列表用于导出（关联学院和用户信息表）
     * 
     * @param majorName 专业名称
     * @param majorCode 专业代码
     * @param academyId 学院ID
     * @param academyLeaderId 专业负责人ID
     * @param status 记录状态
     * @param createTimeStart 创建时间开始
     * @param createTimeEnd 创建时间结束
     * @return 专业详细信息列表
     */
    List<MajorVO> selectMajorListForExport(
        @Param("majorName") String majorName,
        @Param("majorCode") String majorCode,
        @Param("academyId") Long academyId,
        @Param("academyLeaderId") Long academyLeaderId,
        @Param("status") Integer status,
        @Param("createTimeStart") LocalDateTime createTimeStart,
        @Param("createTimeEnd") LocalDateTime createTimeEnd
    );

    // 保留现有的简单查询方法
    @Select("select count(1) from base_major where academy_id = #{academyId}")
    Integer getMajorCountByAcademyId(Long academyId);

    @Select("select count(1) from tp_course where major_id = #{majorId}")
    Integer getCourseCountByMajorId(Long majorId);

    @Select("select major_name as name from base_major where major_id = #{id}")
    String getMajorNameById(Long majorId);

    Long countByClassOrCourse(Long majorId);

    List<MajorSelectorVO> listMajorSelectorByTeacherId(Long teacherId);

    @Select("select academy_id from base_major where major_id = #{majorId}")
    Long getAcademyIdByMajorId(Long majorId);

    List<MajorSelectorVO> listMajorSelectorByCourseLeader(Long teacherId);
}
