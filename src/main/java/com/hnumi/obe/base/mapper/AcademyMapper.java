package com.hnumi.obe.base.mapper;

import com.hnumi.obe.base.entity.Academy;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hnumi.obe.base.vo.AcademyOptionsVO;
import com.hnumi.obe.base.vo.AcademyVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 院系表 Mapper 接口
 */
@Mapper
public interface AcademyMapper extends BaseMapper<Academy> {

    @Select("select id AS value , academy_name AS label from base_academy where status = 0 ")
    List<AcademyOptionsVO> getAcademyOptionsForMajor();

    @Select("select id AS value , academy_name AS label from base_academy where id = #{id} ")
    AcademyOptionsVO getAcademyOptionById(Long id);

    @Select("select academy_name from base_academy where id = #{id} ")
    String getNameById(Long academyId);

    /**
     * 分页查询学院详细信息（关联基本用户信息表）
     * 
     * @param page 分页参数
     * @param academyName 学院名称（模糊查询）
     * @param academyPresidentId 院长ID
     * @param status 记录状态
     * @return 学院详细信息分页结果
     */
    IPage<AcademyVO> pageListWithDetails(
        Page<AcademyVO> page,
        @Param("academyName") String academyName,
        @Param("academyPresidentId") Long academyPresidentId,
        @Param("status") Integer status
    );
}
