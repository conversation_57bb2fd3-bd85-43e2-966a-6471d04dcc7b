package com.hnumi.obe.base.mapper;

import com.hnumi.obe.base.entity.Classes;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * 班级表 Mapper 接口
 */
@Mapper
public interface ClassesMapper extends BaseMapper<Classes> {

    @Select("""
            SELECT COUNT(1)
            FROM base_classes
            WHERE major_id IN (
                SELECT base_major.major_id
                FROM base_major
                WHERE academy_id = #{academyId}
            )
            """)
    int getClassesCountByAcademyId(Long academyId);

    @Select("""
            SELECT class_name as name
            FROM base_classes
            WHERE class_id = #{id}
            """)
    String getClassNameById(Long classId);

    /**
     * 根据专业ID和入学年份查询班级列表，同时使用子查询统计学生人数
     * 一次SQL查询完成所有数据获取，性能最优
     *
     * @param majorId 专业ID
     * @param entranceYear 入学年份
     * @return 包含学生人数的班级列表
     */
    @Select("""
            SELECT
                c.class_id,
                c.major_id,
                c.class_name,
                c.entrance_year,
                c.headteacher_id,
                c.class_status,
                c.status,
                c.create_time,
                c.modify_time,
                c.creator,
                c.modifier,
                COALESCE(
                    (SELECT COUNT(*)
                     FROM base_student s
                     WHERE s.class_id = c.class_id
                       AND s.status = 0),
                    0
                ) as student_number
            FROM base_classes c
            WHERE c.major_id = #{majorId}
                AND c.entrance_year = #{entranceYear}
                AND c.status = 0
            ORDER BY c.class_name
            """)
    List<Classes> selectClassesWithStudentCountByMajorAndYear(
            @Param("majorId") Long majorId,
            @Param("entranceYear") String entranceYear);

    /**
     * 根据条件统计各班级的实际学生人数
     * 用于同步班级表中的冗余学生人数字段
     *
     * @param academyId 学院ID（可选）
     * @param majorId 专业ID（可选）
     * @param classId 班级ID（可选）
     * @return 班级ID和实际学生人数的映射
     */
    List<Classes> getStudentCountByConditions(
            @Param("academyId") Long academyId,
            @Param("majorId") Long majorId,
            @Param("classId") Long classId);

    /**
     * 批量更新班级的学生人数
     *
     * @param classId 班级ID
     * @param studentNumber 学生人数
     * @return 更新影响的行数
     */
    int updateStudentNumber(@Param("classId") Long classId, @Param("studentNumber") Integer studentNumber);

    /**
     * 预览同步班级人数：返回班级ID、名称、原人数、实际人数
     * @param academyId 学院ID
     * @param majorId 专业ID
     * @param classId 班级ID
     * @return List<Map<String, Object>>
     */
    List<Map<String, Object>> previewSyncStudentCount(
            @Param("academyId") Long academyId,
            @Param("majorId") Long majorId,
            @Param("classId") Long classId);
}
