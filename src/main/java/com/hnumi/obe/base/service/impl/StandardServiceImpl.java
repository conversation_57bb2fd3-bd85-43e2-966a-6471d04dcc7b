package com.hnumi.obe.base.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hnumi.obe.base.dto.StandardCreateDTO;
import com.hnumi.obe.base.entity.Standard;
import com.hnumi.obe.base.mapper.StandardMapper;
import com.hnumi.obe.base.mapstruct.StandardConvert;
import com.hnumi.obe.base.service.IStandardService;
import com.hnumi.obe.base.vo.StandardVO;
import com.hnumi.obe.common.util.RequestUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 工程教育认证标准库表 服务实现类
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Service
public class StandardServiceImpl extends ServiceImpl<StandardMapper, Standard> implements IStandardService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createStandardWithIndicators(StandardCreateDTO createDTO) {
        Long userId = RequestUtil.getUserId();

        // 1. 先插入工程认证版本（parent_id=0）
        Standard standardVersion = new Standard();
        standardVersion.setStandardVersion(createDTO.getStandardVersion());
        standardVersion.setStandardName(createDTO.getStandardName());
        standardVersion.setStandardDescription(createDTO.getStandardDescription());
        standardVersion.setReleaseDate(createDTO.getReleaseDate());
        standardVersion.setDisciplineType(createDTO.getDisciplineType());
        standardVersion.setParentId(0L); // 0表示工程认证版本
        standardVersion.setStatus(0);
        standardVersion.setCreateTime(LocalDateTime.now());
        standardVersion.setModifyTime(LocalDateTime.now());
        standardVersion.setCreator(userId);
        standardVersion.setModifier(userId);


        // 插入标准版本
        boolean saved = this.save(standardVersion);
        if (!saved) {
            throw new RuntimeException("插入工程认证标准版本失败");
        }

        // 获取插入后的ID
        Long standardVersionId = standardVersion.getId();

        // 2. 再插入指标点（parent_id=版本ID）
        if (createDTO.getRequirements() != null && !createDTO.getRequirements().isEmpty()) {
            List<Standard> indicators = new ArrayList<>();

            for (StandardCreateDTO indicatorDTO : createDTO.getRequirements()) {
                Standard indicator = new Standard();
                indicator.setStandardVersion(createDTO.getStandardVersion());
                indicator.setStandardName(indicatorDTO.getStandardName());
                indicator.setStandardDescription(indicatorDTO.getStandardDescription());
                indicator.setParentId(standardVersionId); // 指向标准版本ID
                indicator.setStatus(indicatorDTO.getStatus() != null ? indicatorDTO.getStatus() : 0);
                indicator.setCreateTime(LocalDateTime.now());
                indicator.setModifyTime(LocalDateTime.now());
                indicator.setCreator(userId);
                indicator.setModifier(userId);

                indicators.add(indicator);
            }

            // 批量插入指标点
            boolean batchSaved = this.saveBatch(indicators);
            if (!batchSaved) {
                throw new RuntimeException("批量插入工程认证指标点失败");
            }
        }

        return standardVersionId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteStandardById(Long id) {
        // 先查询记录信息
        Standard standard = this.getById(id);
        if (standard == null) {
            throw new RuntimeException("标准记录不存在");
        }

        standard.setStatus(-1);
        standard.setModifyTime(LocalDateTime.now());
        standard.setModifier(RequestUtil.getUserId());
        return this.updateById(standard);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateStandardRecord(StandardCreateDTO updateDto) {
        final Long userId = RequestUtil.getUserId();
        final LocalDateTime now = LocalDateTime.now();

        // 1. 更新标准版本信息
        Standard standardVersion = this.getById(updateDto.getId());
        if (standardVersion == null) {
            throw new RuntimeException("标准版本不存在");
        }

        standardVersion.setStandardVersion(updateDto.getStandardVersion());
        standardVersion.setStandardName(updateDto.getStandardName());
        standardVersion.setStandardDescription(updateDto.getStandardDescription());
        standardVersion.setReleaseDate(updateDto.getReleaseDate());
        standardVersion.setDisciplineType(updateDto.getDisciplineType());
        standardVersion.setStatus(updateDto.getStatus());
        standardVersion.setModifyTime(now);
        standardVersion.setModifier(userId);

        boolean updated = this.updateById(standardVersion);
        if (!updated) {
            throw new RuntimeException("更新标准版本失败");
        }

        // 获取数据库中现有的指标点
        List<Standard> existingIndicators = this.getIndicatorsByParentId(updateDto.getId());
        Set<Long> existingIds = existingIndicators.stream()
                .map(Standard::getId)
                .collect(Collectors.toSet());

        // 2. 处理指标点
        if (CollectionUtils.isNotEmpty(updateDto.getRequirements())) {
            // 处理每个提交的指标点
            List<Standard> indicatorsToSave = new ArrayList<>();
            List<Long> submittedIds = new ArrayList<>();

            for (StandardCreateDTO indicatorDTO : updateDto.getRequirements()) {
                if (indicatorDTO.getId() != null) {
                    // 更新现有指标点
                    Standard existingIndicator = existingIndicators.stream()
                            .filter(i -> i.getId().equals(indicatorDTO.getId()))
                            .findFirst()
                            .orElse(null);

                    if (existingIndicator != null) {
                        existingIndicator.setStandardName(indicatorDTO.getStandardName());
                        existingIndicator.setStandardDescription(indicatorDTO.getStandardDescription());
                        existingIndicator.setStatus(indicatorDTO.getStatus());
                        existingIndicator.setModifyTime(now);
                        existingIndicator.setModifier(userId);
                        indicatorsToSave.add(existingIndicator);
                        submittedIds.add(existingIndicator.getId());
                    }
                } else {
                    // 新增指标点
                    Standard newIndicator = new Standard();
                    newIndicator.setStandardVersion(updateDto.getStandardVersion());
                    newIndicator.setStandardName(indicatorDTO.getStandardName());
                    newIndicator.setStandardDescription(indicatorDTO.getStandardDescription());
                    newIndicator.setParentId(updateDto.getId());
                    newIndicator.setStatus(0);
                    newIndicator.setCreateTime(now);
                    newIndicator.setModifyTime(now);
                    newIndicator.setCreator(userId);
                    newIndicator.setModifier(userId);
                    indicatorsToSave.add(newIndicator);
                }
            }

            // 找出需要删除的指标点（在数据库中存在但提交数据中不存在的）
            List<Long> idsToDelete = existingIds.stream()
                    .filter(id -> !submittedIds.contains(id))
                    .toList();

            // 执行批量删除操作（软删除）
            if (!idsToDelete.isEmpty()) {
                List<Standard> standardsToDelete = idsToDelete.stream()
                    .map(id -> {
                        Standard standard = new Standard();
                        standard.setId(id);
                        standard.setStatus(-1);
                        standard.setModifyTime(now);
                        standard.setModifier(userId);
                        return standard;
                    })
                    .toList();
                this.updateBatchById(standardsToDelete);
            }

            // 批量保存或更新指标点
            if (!indicatorsToSave.isEmpty()) {
                boolean batchSaved = this.saveOrUpdateBatch(indicatorsToSave);
                if (!batchSaved) {
                    throw new RuntimeException("批量保存指标点失败");
                }
            }
        } else {
            // 删除所有指标点
            if (CollectionUtils.isNotEmpty(existingIndicators)) {
                List<Standard> indicatorsToDelete = existingIndicators.stream()
                    .map(i -> {
                        Standard standard = new Standard();
                        standard.setId(i.getId());
                        standard.setStatus(-1);
                        standard.setModifyTime(now);
                        return standard;
                    })
                    .toList();
                this.updateBatchById(indicatorsToDelete);
            }
        }

        return true;
    }

    @Override
    public List<Standard> getIndicatorsByParentId(Long parentId) {
        LambdaQueryWrapper<Standard> wrapper = Wrappers.lambdaQuery(Standard.class);
        wrapper.eq(Standard::getParentId, parentId)
               .eq(Standard::getStatus, 0) // 只查询正常状态的指标点
               .orderByAsc(Standard::getCreateTime);
        return this.list(wrapper);
    }

    @Override
    public StandardVO getStandardWithIndicators(Long id) {
        Standard standard = getById(id);
        List<Standard> indicators = getIndicatorsByParentId(id);
        StandardVO vo = StandardConvert.INSTANCE.toVO(standard);
        List<StandardVO> vo1 = StandardConvert.INSTANCE.toVO(indicators);
        vo.setRequirements(vo1);
        return vo;
    }

    @Override
    public List<Long> getIndicatorIdsByStandardId(Long standardId) {
        LambdaQueryWrapper<Standard> wrapper = Wrappers.lambdaQuery(Standard.class);
        wrapper.eq(Standard::getParentId, standardId)
               .eq(Standard::getStatus, 0) // 只查询正常状态的指标点
               .orderByAsc(Standard::getId); // 按ID排序确保结果一致

        return this.list(wrapper).stream()
                .map(Standard::getId)
                .collect(Collectors.toList());
    }
}
