package com.hnumi.obe.base.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hnumi.obe.base.dto.MajorQueryDTO;
import com.hnumi.obe.base.entity.Major;
import com.hnumi.obe.base.vo.*;

import java.util.List;

/**
* 专业信息表 服务类
*
*/
public interface IMajorService extends IService<Major> {

    boolean deleteById(Long id);

    boolean stopMajorUsingById(Long id);

    List<MajorExportVO> getMajorList(MajorQueryDTO query);

    MajorVO getDetailById(Long id);

    Page<MajorVO> pageList(MajorQueryDTO query);

    List<AcademyLeaderOptionsVO> getAcademyLeaderOptions();

    Major getMajorByAcademyLeaderId(Long userId);

    /**
     * 根据专业负责人ID获取所有专业列表
     * @param userId 专业负责人ID
     * @return 专业列表
     */
    List<Major> getMajorsByAcademyLeaderId(Long userId);

    /**
     * 批量导入专业信息
     * @param majorList 专业信息列表
     * @return 是否导入成功
     */
    boolean importMajors(List<Major> majorList);

    /**
     * 批量导入专业数据（优化版）
     * 支持部分成功导入，提供详细的错误信息
     * @param majors 专业信息列表
     * @return 导入结果
     */
    MajorImportResultVO importMajorsOptimized(List<Major> majors);

    List<MajorDetailVO> getMyMajors(Long userId);

    List<MajorVO> getMajorByCollegeId(Long collegeId);

    List<MajorSelectorVO> listMajorSelectorByTeacherId(Long teacherId);

    MajorVO getMajorByCourseId(Long courseId);

    List<MajorSelectorVO> listMajorSelectorByCourseLeader(Long teacherId);
}
