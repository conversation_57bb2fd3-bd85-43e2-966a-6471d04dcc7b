package com.hnumi.obe.base.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hnumi.obe.base.dto.MajorQueryDTO;
import com.hnumi.obe.base.entity.Academy;
import com.hnumi.obe.base.entity.Major;
import com.hnumi.obe.base.entity.Teacher;
import com.hnumi.obe.base.mapper.AcademyMapper;
import com.hnumi.obe.base.mapper.MajorMapper;
import com.hnumi.obe.base.mapper.TeacherMapper;
import com.hnumi.obe.base.mapstruct.MajorConvert;
import com.hnumi.obe.base.service.IAcademyService;
import com.hnumi.obe.base.service.IMajorService;
import com.hnumi.obe.base.service.ITeacherService;
import com.hnumi.obe.base.vo.*;
import com.hnumi.obe.common.entity.ResultCode;
import com.hnumi.obe.common.exception.ServiceException;
import com.hnumi.obe.common.util.CollectionUtil;
import com.hnumi.obe.common.util.StringUtil;
import com.hnumi.obe.system.entity.BaseUser;
import com.hnumi.obe.system.mapper.BaseUserMapper;
import com.hnumi.obe.system.service.IBaseUserService;
import com.hnumi.obe.tp.entity.Course;
import com.hnumi.obe.tp.service.ICourseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static com.hnumi.obe.common.exception.ServiceExceptionUtil.exception;

/**
 * 专业信息表 服务实现类
 */
@Slf4j
@Service
public class MajorServiceImpl extends ServiceImpl<MajorMapper, Major> implements IMajorService {

    @Autowired
    private MajorMapper majorMapper;
    @Autowired
    private AcademyMapper academyMapper;
    @Autowired
    private TeacherMapper teacherMapper;
    @Autowired
    private BaseUserMapper baseUserMapper;
    @Autowired
    private IAcademyService academyService;
    @Autowired
    private ITeacherService teacherService;
    @Autowired
    private IBaseUserService baseUserService;
    @Autowired
    private ICourseService courseService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteById(Long id) {
        log.info("开始删除专业，ID：{}", id);
        
        // 检查专业是否存在
        Major existingMajor = getById(id);
        if (existingMajor == null) {
            throw exception(ResultCode.MAJOR_NOT_EXISTS);
        }
        
        // 检查专业是否正在使用（有班级或课程）
        Long classCount = majorMapper.countByClassOrCourse(id);
        
        if (classCount > 0) {
            throw exception(ResultCode.MAJOR_IN_USE);
        }
        
        // 逻辑删除
        Major major = new Major();
        major.setMajorId(id);
        major.setStatus(-1);
        boolean result = updateById(major);
        
        if (result) {
            log.info("专业删除成功，ID：{}，名称：{}", id, existingMajor.getMajorName());
        } else {
            log.error("专业删除失败，ID：{}", id);
        }
        
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean stopMajorUsingById(Long id) {
        log.info("开始停用专业，ID：{}", id);
        
        // 检查专业是否存在
        Major existingMajor = getById(id);
        if (existingMajor == null) {
            throw exception(ResultCode.MAJOR_NOT_EXISTS);
        }
        
        // 停用专业
        Major major = new Major();
        major.setMajorId(id);
        major.setStatus(1);
        boolean result = updateById(major);
        
        if (result) {
            log.info("专业停用成功，ID：{}，名称：{}", id, existingMajor.getMajorName());
        } else {
            log.error("专业停用失败，ID：{}", id);
        }
        
        return result;
    }

    @Override
    public List<MajorExportVO> getMajorList(MajorQueryDTO query) {
        List<MajorVO> majorVOList = majorMapper.selectMajorListForExport(
            query.getMajorName(),
            query.getMajorCode(),
            query.getAcademyId(),
            query.getAcademyLeaderId(),
            query.getStatus(),
            query.getCreateTimeStart(),
            query.getCreateTimeEnd()
        );
        
        // 转换为导出VO
        return majorVOList.stream().map(majorVO -> {
            MajorExportVO exportVO = new MajorExportVO();
            BeanUtils.copyProperties(majorVO, exportVO);
            
            // 设置学院名称
            if (majorVO.getCollege() != null) {
                exportVO.setAcademyName(majorVO.getCollege().getCollegeName());
            }
            
            // 设置专业负责人名称
            if (majorVO.getDirectorInfo() != null) {
                exportVO.setAcademyLeaderName(majorVO.getDirectorInfo().getDirectorName());
            }
            
            // 设置状态文本
            String statusText = "未知";
            switch (majorVO.getStatus()) {
                case 0:
                    statusText = "正常";
                    break;
                case -1:
                    statusText = "删除";
                    break;
                case 1:
                    statusText = "禁用";
                    break;
                default:
                    statusText = "未知";
                    break;
            }
            exportVO.setStatus(statusText);
            
            return exportVO;
        }).collect(Collectors.toList());
    }

    @Override
    public MajorVO getDetailById(Long id) {
        log.info("获取专业详情，ID：{}", id);
        
        MajorVO majorVO = majorMapper.selectMajorDetailById(id);
        if (majorVO == null) {
            log.warn("专业不存在，ID：{}", id);
            throw exception(ResultCode.MAJOR_NOT_EXISTS);
        }
        
        log.info("获取专业详情成功，ID：{}，名称：{}", id, majorVO.getName());
        return majorVO;
    }

    @Override
    public Page<MajorVO> pageList(MajorQueryDTO query) {
        Page<MajorVO> page = new Page<>(query.getCurrent(), query.getPageSize());
        
        IPage<MajorVO> result = majorMapper.pageListWithDetails(
            page,
            query.getMajorName(),
            query.getMajorCode(),
            query.getAcademyId(),
            query.getAcademyLeaderId(),
            query.getStatus(),
            query.getCreateTimeStart(),
            query.getCreateTimeEnd()
        );
        
        return (Page<MajorVO>) result;
    }

    @Override
    public List<AcademyLeaderOptionsVO> getAcademyLeaderOptions() {
        return teacherMapper.getAcademyLeaderOptions();
    }

    @Override
    public Major getMajorByAcademyLeaderId(Long userId) {
        LambdaQueryWrapper<Major> wrapper = Wrappers.lambdaQuery(Major.class);
        wrapper.eq(Major::getAcademyLeaderId, userId).eq(Major::getStatus, 0);

        // 使用list()方法获取所有匹配的记录，然后返回第一个
        List<Major> majors = list(wrapper);
        if (majors.isEmpty()) {
            return null;
        }

        // 如果有多个结果，记录警告日志并返回第一个
        if (majors.size() > 1) {
            log.warn("发现多个专业具有相同的负责人ID: {}, 返回第一个专业: {}", userId, majors.get(0).getMajorId());
        }

        return majors.get(0);
    }

    @Override
    public List<Major> getMajorsByAcademyLeaderId(Long userId) {
        LambdaQueryWrapper<Major> wrapper = Wrappers.lambdaQuery(Major.class);
        wrapper.eq(Major::getAcademyLeaderId, userId).eq(Major::getStatus, 0);
        return list(wrapper);
    }

    @Override
    public boolean importMajors(List<Major> majorList) {
        if (majorList == null || majorList.isEmpty()) {
            return false;
        }

        // 获取所有学院名称
        Set<String> academyNames = majorList.stream()
                .map(Major::getAcademyName)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        // 查询已存在的学院
        LambdaQueryWrapper<Academy> academyWrapper = Wrappers.lambdaQuery(Academy.class);
        academyWrapper.in(Academy::getAcademyName, academyNames);
        List<Academy> existingAcademies = academyMapper.selectList(academyWrapper);
        Map<String, Long> academyNameToIdMap = existingAcademies.stream()
                .collect(Collectors.toMap(Academy::getAcademyName, Academy::getId));

        // 获取所有教师工号和姓名
        Set<String> teacherJobNumbers = majorList.stream()
                .map(Major::getDirectorJobNumber)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        Set<String> teacherNames = majorList.stream()
                .map(Major::getDirectorName)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        // 查询已存在的教师信息
        // 1. 先通过工号查询教师表
        LambdaQueryWrapper<Teacher> teacherWrapper = Wrappers.lambdaQuery(Teacher.class);
        teacherWrapper.in(Teacher::getTeacherNumber, teacherJobNumbers);
        List<Teacher> teachersByNumber = teacherMapper.selectList(teacherWrapper);
        
        // 2. 通过姓名查询用户表
        LambdaQueryWrapper<BaseUser> userWrapper = Wrappers.lambdaQuery(BaseUser.class);
        userWrapper.in(BaseUser::getRealName, teacherNames);
        List<BaseUser> usersByName = baseUserMapper.selectList(userWrapper);

        // 3. 获取用户ID对应的教师信息
        Set<Long> userIds = usersByName.stream()
                .map(BaseUser::getId)
                .collect(Collectors.toSet());
        LambdaQueryWrapper<Teacher> teacherByUserIdWrapper = Wrappers.lambdaQuery(Teacher.class);
        teacherByUserIdWrapper.in(Teacher::getUserId, userIds);
        List<Teacher> teachersByUserId = teacherMapper.selectList(teacherByUserIdWrapper);

        // 合并所有教师信息
        List<Teacher> allTeachers = new ArrayList<>();
        allTeachers.addAll(teachersByNumber);
        allTeachers.addAll(teachersByUserId);

        // 构建工号和用户ID的映射
        Map<String, Long> teacherNumberToUserIdMap = allTeachers.stream()
                .collect(Collectors.toMap(
                    Teacher::getTeacherNumber,
                    Teacher::getUserId,
                    (v1, v2) -> v1
                ));

        // 构建姓名和用户ID的映射
        Map<String, Long> teacherNameToUserIdMap = usersByName.stream()
                .collect(Collectors.toMap(
                    BaseUser::getRealName,
                    BaseUser::getId,
                    (v1, v2) -> v1
                ));

        // 处理每个专业
        for (Major major : majorList) {
            // 处理学院
            String academyName = major.getAcademyName();
            if (academyName != null) {
                Long academyId = academyNameToIdMap.get(academyName);
                if (academyId == null) {
                    // 创建新学院
                    Academy newAcademy = new Academy();
                    newAcademy.setAcademyName(academyName);
                    newAcademy.setStatus(0);
                    academyMapper.insert(newAcademy);
                    academyId = newAcademy.getId();
                    academyNameToIdMap.put(academyName, academyId);
                }
                major.setAcademyId(academyId);
            }

            // 处理专业负责人
            String jobNumber = major.getDirectorJobNumber();
            String directorName = major.getDirectorName();
            Long directorId = null;

            if (jobNumber != null) {
                // 优先通过工号匹配
                directorId = teacherNumberToUserIdMap.get(jobNumber);
            } else if (directorName != null) {
                // 其次通过姓名匹配
                directorId = teacherNameToUserIdMap.get(directorName);
            }

            major.setAcademyLeaderId(directorId);
            major.setStatus(0);
        }

        // 批量保存专业信息
        return saveBatch(majorList);
    }

    /**
     * 批量导入专业数据（优化版）
     * 支持部分成功导入，提供详细的错误信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public MajorImportResultVO importMajorsOptimized(List<Major> majors) {
        if (majors == null || majors.isEmpty()) {
            return MajorImportResultVO.failure(0, List.of("导入数据为空"));
        }

        log.info("开始导入专业数据，共{}条记录", majors.size());
        
        // 第一阶段：数据验证和预处理
        List<String> validationErrors = new ArrayList<>();
        List<Major> validMajors = new ArrayList<>();
        
        for (int i = 0; i < majors.size(); i++) {
            Major major = majors.get(i);
            int rowNum = i + 1;
            
            try {
                // 基础数据验证
                if (StringUtil.isBlank(major.getMajorName())) {
                    validationErrors.add(String.format("第%d行：专业名称不能为空", rowNum));
                    continue;
                }
                
                if (StringUtil.isBlank(major.getMajorCode())) {
                    validationErrors.add(String.format("第%d行：专业代码不能为空", rowNum));
                    continue;
                }
                
                // 数据清理
                major.setMajorName(major.getMajorName().trim());
                major.setMajorCode(major.getMajorCode().trim().toUpperCase());
                if (major.getAcademyName() != null) {
                    major.setAcademyName(major.getAcademyName().trim());
                }
                if (major.getDirectorJobNumber() != null) {
                    major.setDirectorJobNumber(major.getDirectorJobNumber().trim());
                }
                if (major.getDirectorName() != null) {
                    major.setDirectorName(major.getDirectorName().trim());
                }
                
                // 长度验证
                if (major.getMajorName().length() > 100) {
                    validationErrors.add(String.format("第%d行：专业名称长度不能超过100个字符", rowNum));
                    continue;
                }
                
                if (major.getMajorCode().length() > 20) {
                    validationErrors.add(String.format("第%d行：专业代码长度不能超过20个字符", rowNum));
                    continue;
                }
                
                validMajors.add(major);
                
            } catch (Exception e) {
                validationErrors.add(String.format("第%d行：数据处理异常 - %s", rowNum, e.getMessage()));
            }
        }
        
        // 检查专业代码重复（数据库中已存在的）
        List<String> duplicateCodes = checkDuplicateMajorCodes(validMajors);
        for (String code : duplicateCodes) {
            validationErrors.add(String.format("专业代码 '%s' 已存在", code));
        }
        
        // 检查导入数据内部的专业代码重复
        Set<String> codeSet = new HashSet<>();
        for (Major major : validMajors) {
            if (!codeSet.add(major.getMajorCode())) {
                validationErrors.add(String.format("专业代码 '%s' 在导入数据中重复", major.getMajorCode()));
            }
        }
        
        // 如果有致命错误（专业代码重复），不允许导入
        if (!validationErrors.isEmpty()) {
            log.warn("数据验证失败，共{}个错误", validationErrors.size());
            return MajorImportResultVO.failure(majors.size(), validationErrors);
        }
        
        // 第二阶段：处理学院和专业负责人信息
        List<String> processingErrors = new ArrayList<>();
        List<Major> processedMajors = new ArrayList<>();
        
        for (int i = 0; i < validMajors.size(); i++) {
            Major major = validMajors.get(i);
            int rowNum = i + 1;
            boolean hasError = false;
            
            try {
                // 处理学院信息
                if (StringUtil.isNotBlank(major.getAcademyName())) {
                    Long academyId = processAcademyInfo(major.getAcademyName());
                    major.setAcademyId(academyId);
                }
                
                // 处理专业负责人信息
                if (StringUtil.isNotBlank(major.getDirectorJobNumber()) || StringUtil.isNotBlank(major.getDirectorName())) {
                    Long directorId = processDirectorInfo(major.getDirectorJobNumber(), major.getDirectorName());
                    if (directorId == null) {
                        processingErrors.add(String.format("第%d行：专业负责人信息不存在（工号：%s，姓名：%s）", 
                            rowNum, major.getDirectorJobNumber(), major.getDirectorName()));
                        hasError = true;
                    } else {
                        major.setAcademyLeaderId(directorId);
                    }
                }
                
                // 设置默认值
                major.setStatus(0); // 默认启用
                
                if (!hasError) {
                    processedMajors.add(major);
                }
                
            } catch (Exception e) {
                processingErrors.add(String.format("第%d行：处理异常 - %s", rowNum, e.getMessage()));
            }
        }
        
        // 第三阶段：批量保存成功的数据
        int successCount = 0;
        if (!processedMajors.isEmpty()) {
            try {
                boolean saveResult = saveBatch(processedMajors);
                if (saveResult) {
                    successCount = processedMajors.size();
                    log.info("成功导入{}条专业数据", successCount);
                } else {
                    log.error("批量保存专业数据失败");
                    processingErrors.add("批量保存专业数据失败");
                }
            } catch (Exception e) {
                log.error("保存专业数据异常", e);
                processingErrors.add("保存专业数据异常：" + e.getMessage());
            }
        }
        
        // 返回结果
        int failCount = processingErrors.size();
        if (successCount > 0 && failCount > 0) {
            // 部分成功
            return MajorImportResultVO.partialSuccess(successCount, failCount, majors.size(), processingErrors);
        } else if (successCount > 0) {
            // 完全成功
            return MajorImportResultVO.success(successCount, majors.size());
        } else {
            // 完全失败
            return MajorImportResultVO.failure(majors.size(), processingErrors);
        }
    }

    @Override
    public List<MajorDetailVO> getMyMajors(Long userId) {
        Teacher teacher = teacherService.getByTeacherId(userId);
        if (teacher == null) {
            throw new ServiceException(ResultCode.LOGIN_USER_NOT_FOUND);
        }
        Long academyId = teacher.getAcademyId();
        List<Major> majorList = getMajorList(academyId, userId);
        if (CollectionUtil.isEmpty(majorList)) {
            return List.of();
        }
        BaseUser user = baseUserService.getById(userId);
        Academy academy = academyService.getById(academyId);

        // todo fix me: 查询统计信息
        return majorList.stream().map(major -> MajorDetailVO.from(major, academy, user, teacher)).toList();
    }

    @Override
    public List<MajorVO> getMajorByCollegeId(Long collegeId) {
        LambdaQueryWrapper<Major> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(Major::getAcademyId, collegeId)
               .eq(Major::getStatus, 0); // 只查询正常状态的专业
        return MajorConvert.INSTANCE.toVO(list(wrapper));
    }

    @Override
    public List<MajorSelectorVO> listMajorSelectorByTeacherId(Long teacherId) {
        return majorMapper.listMajorSelectorByTeacherId(teacherId);
    }

    @Override
    public MajorVO getMajorByCourseId(Long courseId) {
        Course course = courseService.getById(courseId);
        if(course != null){
            Major major = majorMapper.selectById(course.getMajorId());
            return MajorConvert.INSTANCE.toVO(major);
        }
        return null;
    }

    @Override
    public List<MajorSelectorVO> listMajorSelectorByCourseLeader(Long teacherId) {
        return majorMapper.listMajorSelectorByCourseLeader(teacherId);
    }

    private List<Major> getMajorList(Long academyId, Long academyLeaderId) {
        LambdaQueryWrapper<Major> wrapper = Wrappers.lambdaQuery(Major.class);
        wrapper.eq(Major::getAcademyId, academyId)
               .eq(Major::getAcademyLeaderId, academyLeaderId)
               .eq(Major::getStatus, 0);
        return list(wrapper);
    }

    /**
     * 检查专业代码重复
     */
    private List<String> checkDuplicateMajorCodes(List<Major> majors) {
        if (majors == null || majors.isEmpty()) {
            return new ArrayList<>();
        }
        
        List<String> majorCodes = majors.stream()
                .map(Major::getMajorCode)
                .collect(Collectors.toList());
        
        LambdaQueryWrapper<Major> wrapper = Wrappers.lambdaQuery(Major.class);
        wrapper.in(Major::getMajorCode, majorCodes)
               .ne(Major::getStatus, -1); // 排除已删除的专业
        
        List<Major> existingMajors = list(wrapper);
        return existingMajors.stream()
                .map(Major::getMajorCode)
                .collect(Collectors.toList());
    }
    
    /**
     * 处理学院信息（不存在则创建）
     */
    private Long processAcademyInfo(String academyName) {
        // 先查询是否存在
        LambdaQueryWrapper<Academy> wrapper = Wrappers.lambdaQuery(Academy.class);
        wrapper.eq(Academy::getAcademyName, academyName)
               .ne(Academy::getStatus, -1);
        Academy existingAcademy = academyMapper.selectOne(wrapper);
        
        if (existingAcademy != null) {
            return existingAcademy.getId();
        }
        
        // 不存在则创建
        Academy newAcademy = new Academy();
        newAcademy.setAcademyName(academyName);
        newAcademy.setStatus(0);
        academyMapper.insert(newAcademy);
        
        log.info("自动创建学院：{}", academyName);
        return newAcademy.getId();
    }
    
    /**
     * 处理专业负责人信息（工号优先，姓名备选）
     */
    private Long processDirectorInfo(String jobNumber, String directorName) {
        Long userId = null;
        
        // 优先通过工号查询
        if (StringUtil.isNotBlank(jobNumber)) {
            LambdaQueryWrapper<Teacher> teacherWrapper = Wrappers.lambdaQuery(Teacher.class);
            teacherWrapper.eq(Teacher::getTeacherNumber, jobNumber);
            Teacher teacher = teacherMapper.selectOne(teacherWrapper);
            if (teacher != null) {
                userId = teacher.getUserId();
            }
        }
        
        // 如果工号查询失败，尝试通过姓名查询
        if (userId == null && StringUtil.isNotBlank(directorName)) {
            LambdaQueryWrapper<BaseUser> userWrapper = Wrappers.lambdaQuery(BaseUser.class);
            userWrapper.eq(BaseUser::getRealName, directorName);
            BaseUser user = baseUserMapper.selectOne(userWrapper);
            
            if (user != null) {
                // 验证该用户是否在教师表中存在
                LambdaQueryWrapper<Teacher> teacherWrapper = Wrappers.lambdaQuery(Teacher.class);
                teacherWrapper.eq(Teacher::getUserId, user.getId());
                Teacher teacher = teacherMapper.selectOne(teacherWrapper);
                if (teacher != null) {
                    userId = user.getId();
                }
            }
        }
        
        return userId;
    }
}
