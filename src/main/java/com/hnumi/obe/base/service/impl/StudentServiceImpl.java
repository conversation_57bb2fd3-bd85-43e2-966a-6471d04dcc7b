package com.hnumi.obe.base.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hnumi.obe.base.dto.StudentDTO;
import com.hnumi.obe.base.dto.StudentImportDTO;
import com.hnumi.obe.base.dto.StudentQueryDTO;
import com.hnumi.obe.base.entity.Academy;
import com.hnumi.obe.base.entity.Classes;
import com.hnumi.obe.base.entity.Major;
import com.hnumi.obe.base.entity.Student;
import com.hnumi.obe.base.mapper.*;
import com.hnumi.obe.base.mapstruct.StudentConvert;
import com.hnumi.obe.base.service.IStudentService;
import com.hnumi.obe.base.service.IClassesService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hnumi.obe.base.vo.StudentDetailVO;
import com.hnumi.obe.base.vo.StudentExportVO;
import com.hnumi.obe.base.vo.StudentImportResultVO;
import com.hnumi.obe.base.vo.StudentVO;
import com.hnumi.obe.common.util.ExcelUtil;
import com.hnumi.obe.common.util.PasswordUtil;
import com.hnumi.obe.common.util.StringUtil;
import com.hnumi.obe.system.entity.BaseUser;
import com.hnumi.obe.system.mapper.BaseUserMapper;
import com.hnumi.obe.system.mapstruct.UserConvert;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.DigestUtils;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 学生表 服务实现类
 */
@Slf4j
@Service
public class StudentServiceImpl extends ServiceImpl<StudentMapper, Student> implements IStudentService {
    @Autowired
    private AcademyMapper academyMapper;
    @Autowired
    private TeacherMapper teacherMapper;
    @Autowired
    private MajorMapper majorMapper;
    @Autowired
    private ClassesMapper classesMapper;
    @Autowired
    private BaseUserMapper baseUserMapper;
    @Autowired
    private IClassesService classesService;
    
    // 数据校验正则表达式
    private static final Pattern PHONE_PATTERN = Pattern.compile("^1[3-9]\\d{9}$");
    private static final Pattern EMAIL_PATTERN = Pattern.compile("^[\\w-]+(\\.[\\w-]+)*@[\\w-]+(\\.[\\w-]+)+$");
    private static final Pattern STUDENT_NUMBER_PATTERN = Pattern.compile("^[0-9]{8,20}$");
    @Autowired
    private StudentMapper studentMapper;

    @Override
    public Object deleteStudentById(Long id) {
        // 先查询学生信息，获取班级ID
        Student existingStudent = this.getById(id);
        Long classId = null;
        if (existingStudent != null) {
            classId = existingStudent.getClassId();
        }
        
        Student student = new Student();
        student.setStudentId(id);
        student.setStatus(-1);
        boolean result = updateById(student);
        
        // 同步更新班级学生数
        if (result && classId != null) {
            updateClassStudentCount(classId);
        }
        
        return result;
    }

    @Override
    public Object stopStudentUsingById(Long id) {
        Student student = new Student();
        student.setStudentId(id);
        student.setStatus(1);
        return updateById(student);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Object addStudent(StudentDTO dto) {
        // 先创建基本用户信息
        BaseUser baseUser = new BaseUser();
        if (dto.getUser() != null) {
            BeanUtils.copyProperties(dto.getUser(), baseUser);
        }
        // 生成新的盐值和默认密码
        String salt = PasswordUtil.generateSalt();
        String defaultPassword = PasswordUtil.DEFAULT_PASSWORD;
        String hashedPassword = PasswordUtil.hash(defaultPassword, salt);
        baseUser.setSalt(salt);
        baseUser.setPassword(hashedPassword);
        baseUser.setStatus(0); // 正常状态
        baseUserMapper.insert(baseUser);
        
        // 创建学生信息
        Student student = new Student();
        student.setUserId(baseUser.getId());
        student.setStudentNumber(dto.getNumber());
        student.setClassId(dto.getClassId());
        student.setMajorId(dto.getMajorId());
        student.setAcademyId(dto.getAcademyId());
        student.setEntranceYear(dto.getEntranceYear());
        student.setStudentStatus(dto.getStudentStatus());
        student.setStatus(0); // 正常状态
        student.setStudentName(dto.getStudentName());
        student.setGender(dto.getGender());
        boolean result = save(student);
        
        // 同步更新班级学生数
        if (result && dto.getClassId() != null) {
            updateClassStudentCount(dto.getClassId());
        }
        
        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Object updateStudent(StudentDTO dto) {
        // 1. 更新基本用户信息
        Long userId = null;
        if (dto.getUser() != null && dto.getUser().getId() != null && !dto.getUser().getId().isEmpty()) {
            // 前端有传递 userId
            userId = Long.valueOf(dto.getUser().getId());
        } else {
            // 前端未传递 userId，通过 studentId 查询
            Student student = this.getById(dto.getStudentId());
            if (student == null) {
                throw new IllegalArgumentException("未找到对应的学生信息");
            }
            userId = student.getUserId();
        }
        BaseUser baseUser = baseUserMapper.selectById(userId);
        if (baseUser == null) {
            throw new IllegalArgumentException("未找到对应的用户信息");
        }
        // 只更新允许编辑的字段
        if (dto.getUser() != null) {
            baseUser.setPhone(dto.getUser().getPhone());
            baseUser.setEmail(dto.getUser().getEmail());
            baseUser.setUsername(dto.getUser().getUsername());
            baseUser.setRealName(dto.getUser().getRealName());
            baseUser.setGender(dto.getUser().getGender());
        }
        // 其他字段如密码、盐值等不在此处更新
        int userUpdate = baseUserMapper.updateById(baseUser);
        if (userUpdate <= 0) {
            throw new RuntimeException("更新用户信息失败");
        }

        // 2. 更新学生信息
        Student student = this.getById(dto.getStudentId());
        if (student == null) {
            throw new IllegalArgumentException("未找到对应的学生信息");
        }
        
        // 保存原始班级ID，用于后续比较
        Long originalClassId = student.getClassId();
        Long newClassId = dto.getClassId();
        
        student.setStudentNumber(dto.getNumber());
        student.setClassId(dto.getClassId());
        student.setMajorId(dto.getMajorId());
        student.setAcademyId(dto.getAcademyId());
        student.setEntranceYear(dto.getEntranceYear());
        student.setStudentStatus(dto.getStudentStatus());
        student.setStatus(dto.getStatus());
        student.setStudentName(dto.getStudentName());
        student.setGender(dto.getGender());
        int studentUpdate = this.baseMapper.updateById(student);
        if (studentUpdate <= 0) {
            throw new RuntimeException("更新学生信息失败");
        }
        
        // 同步更新班级学生数（处理班级变更情况）
        // 如果班级ID发生变化，需要更新两个班级的学生数
        if (newClassId != null && !newClassId.equals(originalClassId)) {
            log.info("学生 {} 班级变更：从班级 {} 转到班级 {}", dto.getStudentId(), originalClassId, newClassId);
            // 更新旧班级学生数
            if (originalClassId != null) {
                updateClassStudentCount(originalClassId);
            }
            // 更新新班级学生数
            updateClassStudentCount(newClassId);
        } else if (newClassId != null) {
            // 班级ID没有变化，只更新当前班级
            log.info("学生 {} 班级未变更，更新班级 {} 学生数", dto.getStudentId(), newClassId);
            updateClassStudentCount(newClassId);
        }
        
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Object importStudent(InputStream inputStream) {
        try {
            List<StudentImportDTO> list = ExcelUtil.readAll(inputStream, StudentImportDTO.class);
            
            if (list.isEmpty()) {
                return StudentImportResultVO.failure(0, 1, 1, List.of("导入数据为空"));
            }
            
            // 数据验证和预处理
            List<BaseUser> baseUserList = new ArrayList<>();
            List<Student> studentList = new ArrayList<>();
            List<String> errorMessages = new ArrayList<>();
            
            for (int i = 0; i < list.size(); i++) {
                StudentImportDTO item = list.get(i);
                try {
                    // 基础数据验证
                    validateBasicData(item, i + 1);
                    
                    // 格式验证
                    validateDataFormat(item, i + 1);
                    
                    // 检查学号是否已存在
                    if (isStudentNumberExists(item.getNumber())) {
                        errorMessages.add(String.format("第%d行：学号已存在：%s", i + 1, item.getNumber()));
                        continue;
                    }
                    
                    // 检查用户名是否已存在
                    if (isUsernameExists(item.getNumber())) {
                        errorMessages.add(String.format("第%d行：用户名已存在：%s", i + 1, item.getNumber()));
                        continue;
                    }
                    
                    // 检查手机号是否已存在
                    if (StringUtil.isNotBlank(item.getPhone()) && isPhoneExists(item.getPhone())) {
                        errorMessages.add(String.format("第%d行：手机号已存在：%s", i + 1, item.getPhone()));
                        continue;
                    }
                    
                    // 检查邮箱是否已存在
                    if (StringUtil.isNotBlank(item.getEmail()) && isEmailExists(item.getEmail())) {
                        errorMessages.add(String.format("第%d行：邮箱已存在：%s", i + 1, item.getEmail()));
                        continue;
                    }
                    
                    // 根据名称查询对应的ID，如果不存在则自动创建
                    Long academyId = getAcademyIdByName(item.getAcademyName());
                    Long majorId = getMajorIdByName(item.getMajorName(), academyId);
                    Long classId = getClassIdByName(item.getClassName(), majorId, item.getEntranceYear());
                    
                    // 设置查询到的ID
                    item.setAcademyId(academyId);
                    item.setMajorId(majorId);
                    item.setClassId(classId);
                    
                    // 准备基本用户信息
                    BaseUser baseUser = new BaseUser();
                    baseUser.setRealName(item.getStudentName());
                    baseUser.setUsername(item.getNumber()); // 用学号作为用户名
                    baseUser.setGender("男".equals(item.getGender()) ? 1 : 2);
                    baseUser.setPhone(item.getPhone());
                    baseUser.setEmail(item.getEmail());
                    
                    String password = DigestUtils.md5DigestAsHex("123456".getBytes());
                    baseUser.setPassword(password);
                    baseUser.setStatus(0);
                    baseUserList.add(baseUser);
                    
                    // 准备学生信息（暂时不设置userId，批量插入用户后再设置）
                    Student student = new Student();
                    student.setStudentNumber(item.getNumber());
                    student.setClassId(classId);
                    student.setMajorId(majorId);
                    student.setAcademyId(academyId);
                    student.setEntranceYear(item.getEntranceYear());

                    Integer studentStatus = switch (item.getStudentStatus()) {
                        case "在读" -> 0;
                        case "休学" -> 1;
                        case "退学" -> 2;
                        case "毕业" -> -1;
                        default -> 0;
                    };
                    student.setStudentStatus(studentStatus);
                    student.setStatus(0);
                    student.setStudentName(item.getStudentName());
                    student.setGender("男".equals(item.getGender()) ? 1 : 2);
                    studentList.add(student);
                    
                } catch (Exception e) {
                    errorMessages.add(String.format("第%d行：%s", i + 1, e.getMessage()));
                }
            }
            
            if (!baseUserList.isEmpty()) {
                // 批量插入基本用户信息
                for (BaseUser baseUser : baseUserList) {
                    baseUserMapper.insert(baseUser);
                }
                
                // 设置学生信息的userId并批量插入
                for (int i = 0; i < studentList.size(); i++) {
                    studentList.get(i).setUserId(baseUserList.get(i).getId());
                }
                saveBatch(studentList);
                
                // 同步更新班级学生人数
                List<Long> classIds = studentList.stream()
                    .map(Student::getClassId)
                    .distinct()
                    .collect(Collectors.toList());
                if (!classIds.isEmpty()) {
                    for (Long classId : classIds) {
                        updateClassStudentCount(classId);
                    }
                }
            }
            
            int successCount = baseUserList.size();
            int failCount = list.size() - successCount;
            int totalCount = list.size();
            
            log.info("学生数据导入完成，成功：{}条，失败：{}条", successCount, failCount);
            
            // 根据是否有错误返回不同的结果
            if (errorMessages.isEmpty()) {
                return StudentImportResultVO.success(successCount, totalCount);
            } else {
                return StudentImportResultVO.failure(successCount, failCount, totalCount, errorMessages);
            }
            
        } catch (Exception e) {
            log.error("导入过程发生异常：", e);
            return StudentImportResultVO.failure(0, 1, 1, List.of("导入失败：" + e.getMessage()));
        }
    }
    
    /**
     * 基础数据验证
     */
    private void validateBasicData(StudentImportDTO item, int rowNumber) {
        if (StringUtil.isBlank(item.getNumber())) {
            throw new RuntimeException("学号不能为空");
        }
        if (StringUtil.isBlank(item.getStudentName())) {
            throw new RuntimeException("学生姓名不能为空");
        }
        if (StringUtil.isBlank(item.getGender())) {
            throw new RuntimeException("性别不能为空");
        }
        if (!"男".equals(item.getGender()) && !"女".equals(item.getGender())) {
            throw new RuntimeException("性别只能填写'男'或'女'");
        }
        if (StringUtil.isBlank(item.getPhone())) {
            throw new RuntimeException("手机号不能为空");
        }
        if (StringUtil.isBlank(item.getEmail())) {
            throw new RuntimeException("邮箱不能为空");
        }
        if (StringUtil.isBlank(item.getAcademyName())) {
            throw new RuntimeException("学院名称不能为空");
        }
        if (StringUtil.isBlank(item.getMajorName())) {
            throw new RuntimeException("专业名称不能为空");
        }
        if (StringUtil.isBlank(item.getClassName())) {
            throw new RuntimeException("班级名称不能为空");
        }
        if (StringUtil.isBlank(item.getEntranceYear())) {
            throw new RuntimeException("入学年份不能为空");
        }
        if (StringUtil.isBlank(item.getStudentStatus())) {
            throw new RuntimeException("学籍状态不能为空");
        }
    }
    
    /**
     * 数据格式验证
     */
    private void validateDataFormat(StudentImportDTO item, int rowNumber) {
        // 学号格式验证
        if (!STUDENT_NUMBER_PATTERN.matcher(item.getNumber()).matches()) {
            throw new RuntimeException("学号格式不正确，应为8-20位数字");
        }
        
        // 手机号格式验证（如果填写了）
        if (StringUtil.isNotBlank(item.getPhone()) && !PHONE_PATTERN.matcher(item.getPhone()).matches()) {
            throw new RuntimeException("手机号格式不正确");
        }
        
        // 邮箱格式验证（如果填写了）
        if (StringUtil.isNotBlank(item.getEmail()) && !EMAIL_PATTERN.matcher(item.getEmail()).matches()) {
            throw new RuntimeException("邮箱格式不正确");
        }
        
        // 入学年份格式验证
        try {
            int year = Integer.parseInt(item.getEntranceYear());
            if (year < 1900 || year > 2100) {
                throw new RuntimeException("入学年份不合理");
            }
        } catch (NumberFormatException e) {
            throw new RuntimeException("入学年份格式不正确");
        }
        
        // 学籍状态验证
        if (!"在读".equals(item.getStudentStatus()) && 
            !"休学".equals(item.getStudentStatus()) && 
            !"退学".equals(item.getStudentStatus()) && 
            !"毕业".equals(item.getStudentStatus())) {
            throw new RuntimeException("学籍状态只能填写'在读'、'休学'、'退学'或'毕业'");
        }
    }
    
    /**
     * 检查学号是否已存在
     */
    private boolean isStudentNumberExists(String number) {
        LambdaQueryWrapper<Student> wrapper = Wrappers.lambdaQuery(Student.class);
        wrapper.eq(Student::getStudentNumber, number);
        return count(wrapper) > 0;
    }
    
    /**
     * 检查用户名是否已存在
     */
    private boolean isUsernameExists(String username) {
        LambdaQueryWrapper<BaseUser> wrapper = Wrappers.lambdaQuery(BaseUser.class);
        wrapper.eq(BaseUser::getUsername, username);
        return baseUserMapper.selectCount(wrapper) > 0;
    }
    
    /**
     * 检查手机号是否已存在
     */
    private boolean isPhoneExists(String phone) {
        LambdaQueryWrapper<BaseUser> wrapper = Wrappers.lambdaQuery(BaseUser.class);
        wrapper.eq(BaseUser::getPhone, phone);
        return baseUserMapper.selectCount(wrapper) > 0;
    }
    
    /**
     * 检查邮箱是否已存在
     */
    private boolean isEmailExists(String email) {
        LambdaQueryWrapper<BaseUser> wrapper = Wrappers.lambdaQuery(BaseUser.class);
        wrapper.eq(BaseUser::getEmail, email);
        return baseUserMapper.selectCount(wrapper) > 0;
    }
    
    /**
     * 根据学院名称查询学院ID，如果不存在则自动创建
     */
    private Long getAcademyIdByName(String academyName) {
        LambdaQueryWrapper<Academy> wrapper = Wrappers.lambdaQuery(Academy.class);
        wrapper.eq(Academy::getAcademyName, academyName)
               .eq(Academy::getStatus, 0)
               .last("LIMIT 1");
        Academy academy = academyMapper.selectOne(wrapper);
        
        if (academy != null) {
            return academy.getId();
        }
        
        // 学院不存在，自动创建
        return createAcademy(academyName);
    }
    
    /**
     * 创建新学院
     */
    private Long createAcademy(String academyName) {
        log.info("自动创建学院：{}", academyName);
        Academy academy = new Academy();
        academy.setAcademyName(academyName);
        academy.setStatus(0); // 正常状态
        // academyPresidentId 暂时不设置，后续在学院管理中完善
        
        academyMapper.insert(academy);
        log.info("学院创建成功，ID：{}，名称：{}", academy.getId(), academyName);
        return academy.getId();
    }
    
    /**
     * 根据专业名称和学院ID查询专业ID，如果不存在则自动创建
     */
    private Long getMajorIdByName(String majorName, Long academyId) {
        LambdaQueryWrapper<Major> wrapper = Wrappers.lambdaQuery(Major.class);
        wrapper.eq(Major::getMajorName, majorName)
               .eq(Major::getAcademyId, academyId)
               .eq(Major::getStatus, 0)
               .last("LIMIT 1");
        Major major = majorMapper.selectOne(wrapper);
        
        if (major != null) {
            return major.getMajorId();
        }
        
        // 专业不存在，自动创建
        return createMajor(majorName, academyId);
    }
    
    /**
     * 创建新专业
     */
    private Long createMajor(String majorName, Long academyId) {
        log.info("自动创建专业：{}，所属学院ID：{}", majorName, academyId);
        Major major = new Major();
        major.setMajorName(majorName);
        major.setAcademyId(academyId);
        major.setStatus(0); // 正常状态
        // majorCode 可以根据规则生成，这里暂时不设置
        // academyLeaderId 暂时不设置，后续在专业管理中完善
        
        majorMapper.insert(major);
        log.info("专业创建成功，ID：{}，名称：{}，所属学院ID：{}", major.getMajorId(), majorName, academyId);
        return major.getMajorId();
    }
    
    /**
     * 根据班级名称和专业ID查询班级ID，如果不存在则自动创建
     */
    private Long getClassIdByName(String className, Long majorId, String entranceYear) {
        LambdaQueryWrapper<Classes> wrapper = Wrappers.lambdaQuery(Classes.class);
        wrapper.eq(Classes::getClassName, className)
               .eq(Classes::getMajorId, majorId)
               .eq(Classes::getStatus, 0)
               .last("LIMIT 1");
        Classes classes = classesMapper.selectOne(wrapper);
        
        if (classes != null) {
            return classes.getClassId();
        }
        // 班级不存在，自动创建
        return createClass(className, majorId, entranceYear);
    }
    
    /**
     * 创建新班级
     */
    private Long createClass(String className, Long majorId, String entranceYear) {
        log.info("自动创建班级：{}，所属专业ID：{}，入学年份：{}", className, majorId, entranceYear);
        Classes classes = new Classes();
        classes.setClassName(className);
        classes.setMajorId(majorId);
        classes.setStatus(0); // 正常状态
        classes.setClassStatus(0); // 在读状态
        classes.setStudentNumber(0); // 初始学生人数为0
        
        // 设置入学年份
        classes.setEntranceYear(entranceYear);
        // headteacherId 暂时不设置，后续在班级管理中完善
        classesMapper.insert(classes);
        log.info("班级创建成功，ID：{}，名称：{}，所属专业ID：{}，入学年份：{}", 
                classes.getClassId(), className, majorId, classes.getEntranceYear());
        return classes.getClassId();
    }
    
    /**
     * 更新单个班级的学生人数
     */
    private void updateClassStudentCount(Long classId) {
        try {
            // 统计该班级的学生数量
            int studentCount = baseMapper.countStudentsByClassId(classId);
            
            // 更新班级表中的学生数量
            Classes classes = classesMapper.selectById(classId);
            if (classes != null) {
                classes.setStudentNumber(studentCount);
                classesMapper.updateById(classes);
                log.info("班级 {} 学生数量已更新为: {}", classId, studentCount);
            } else {
                log.warn("未找到班级ID: {}", classId);
            }
        } catch (Exception e) {
            log.error("更新班级 {} 学生数量失败: {}", classId, e.getMessage(), e);
        }
    }
    
    /**
     * 更新班级学生人数
     */
    private void updateClassStudentCount(List<Student> studentList) {
        // 按班级ID分组统计
        Map<Long, Long> classStudentCountMap = studentList.stream()
            .collect(Collectors.groupingBy(Student::getClassId, Collectors.counting()));
        
        // 更新每个班级的学生人数
        for (Map.Entry<Long, Long> entry : classStudentCountMap.entrySet()) {
            Long classId = entry.getKey();
            Long addCount = entry.getValue();
            
            // 查询当前班级学生人数
            LambdaQueryWrapper<Classes> wrapper = Wrappers.lambdaQuery(Classes.class);
            wrapper.eq(Classes::getClassId, classId);
            Classes classes = classesMapper.selectOne(wrapper);
            
            if (classes != null) {
                int currentCount = classes.getStudentNumber() != null ? classes.getStudentNumber() : 0;
                classes.setStudentNumber(currentCount + addCount.intValue());
                classesMapper.updateById(classes);
            }
        }
    }

    @Override
    public List<StudentExportVO> getStudentList(StudentQueryDTO query) {
        // 使用新的关联查询方法
        List<StudentVO> studentVOList = baseMapper.selectStudentListForExport(
            query.getAcademyId(),
            query.getMajorId(),
            query.getClassId(),
            query.getStudentStatus(),
            query.getGender(),
            query.getEntranceYear(),
            query.getStatus()
        );
        
        // 转换为导出VO
        return studentVOList.stream().map(studentVO -> {
            StudentExportVO exportVO = new StudentExportVO();
            exportVO.setStudentName(studentVO.getName());
            exportVO.setStudentNumber(Long.valueOf(studentVO.getStudentNumber()));
            exportVO.setGender(studentVO.getGender() == 1 ? "男" : "女");
            exportVO.setPhone(studentVO.getPhone());
            exportVO.setEmail(studentVO.getEmail());
            exportVO.setEntranceYear(studentVO.getEntranceYear());
            
            String studentStatus = switch (studentVO.getStudentStatus()) {
                case 0 -> "在读";
                case 1 -> "休学";
                case 2 -> "退学";
                case -1 -> "毕业";
                default -> "未知";
            };
            exportVO.setStudentStatus(studentStatus);
            
            String status = switch (studentVO.getStatus()) {
                case 0 -> "正常";
                case 1 -> "停用";
                case -1 -> "删除";
                default -> "未知";
            };
            exportVO.setStatus(status);
            
            exportVO.setAcademyName(studentVO.getCollege());
            exportVO.setMajorName(studentVO.getMajorName());
            exportVO.setClassName(studentVO.getClassValue());
            
            return exportVO;
        }).toList();
    }

    @Override
    public Page<StudentVO> pageStudent(StudentQueryDTO query) {
        Page<StudentVO> page = new Page<>(query.getCurrent(), query.getSize());
        
        // 使用新的关联查询方法
        return baseMapper.selectStudentPageWithDetails(
            page,
            query.getAcademyId(),
            query.getMajorId(),
            query.getClassId(),
            query.getStudentStatus(),
            query.getGender(),
            query.getEntranceYear(),
            query.getStatus(),
            query.getStudentName(),
            query.getStudentNumber(),
            query.getPhone(),
            query.getEmail()
        );
    }

    @Override
    public Object getStudentDetailById(Long id) {
        // 使用新的关联查询方法
        StudentVO studentVO = baseMapper.selectStudentDetailById(id);
        
        if (studentVO == null) {
            return null;
        }
        
        // 转换为详细信息VO
        StudentDetailVO detailVO = new StudentDetailVO();
        BeanUtils.copyProperties(studentVO, detailVO);
        
        // 设置状态文本
        detailVO.setStudentStatusText(getStudentStatusText(studentVO.getStudentStatus()));
        detailVO.setStatusText(getStatusText(studentVO.getStatus()));
        
        return detailVO;
    }

    @Override
    public List<Object> listTree() {
        List<Object> treeData = new ArrayList<>();

        // 构建根节点
        Map<String, Object> rootNode = new HashMap<>();
        rootNode.put("id", "all");
        rootNode.put("label", "全部学生");
        rootNode.put("type", "school");
        rootNode.put("studentCount", 0);
        rootNode.put("children", new ArrayList<>());

        // 获取所有学院
        LambdaQueryWrapper<Academy> academyWrapper = Wrappers.lambdaQuery(Academy.class);
        academyWrapper.eq(Academy::getStatus, 0); // 只查询正常状态的学院
        academyWrapper.orderByAsc(Academy::getId);
        List<Academy> academies = academyMapper.selectList(academyWrapper);

        int totalStudentCount = 0;
        List<Object> collegeNodes = new ArrayList<>();

        for (Academy academy : academies) {
            Map<String, Object> collegeNode = new HashMap<>();
            collegeNode.put("id", "college_" + academy.getId());
            collegeNode.put("dbId", academy.getId()); // 数据库中的真实ID
            collegeNode.put("label", academy.getAcademyName());
            collegeNode.put("type", "college");
            collegeNode.put("studentCount", 0);
            collegeNode.put("children", new ArrayList<>());

            // 获取该学院下的所有专业
            LambdaQueryWrapper<Major> majorWrapper = Wrappers.lambdaQuery(Major.class);
            majorWrapper.eq(Major::getAcademyId, academy.getId());
            majorWrapper.eq(Major::getStatus, 0); // 只查询正常状态的专业
            majorWrapper.orderByAsc(Major::getMajorId);
            List<Major> majors = majorMapper.selectList(majorWrapper);

            int collegeStudentCount = 0;
            List<Object> majorNodes = new ArrayList<>();

            for (Major major : majors) {
                Map<String, Object> majorNode = new HashMap<>();
                majorNode.put("id", "major_" + major.getMajorId());
                majorNode.put("dbId", major.getMajorId()); // 数据库中的真实ID
                majorNode.put("label", major.getMajorName());
                majorNode.put("type", "major");
                majorNode.put("collegeDbId", academy.getId()); // 所属学院的数据库ID
                majorNode.put("studentCount", 0);
                majorNode.put("children", new ArrayList<>());

                // 获取该专业下的所有班级
                LambdaQueryWrapper<Classes> classWrapper = Wrappers.lambdaQuery(Classes.class);
                classWrapper.eq(Classes::getMajorId, major.getMajorId());
                classWrapper.eq(Classes::getStatus, 0); // 只查询正常状态的班级
                classWrapper.orderByAsc(Classes::getClassId);
                List<Classes> classes = classesMapper.selectList(classWrapper);

                int majorStudentCount = 0;
                List<Object> classNodes = new ArrayList<>();

                for (Classes classItem : classes) {
                    Map<String, Object> classNode = new HashMap<>();
                    classNode.put("id", "class_" + classItem.getClassId());
                    classNode.put("dbId", classItem.getClassId()); // 数据库中的真实ID
                    classNode.put("label", classItem.getClassName());
                    classNode.put("type", "class");
                    classNode.put("majorDbId", major.getMajorId()); // 所属专业的数据库ID
                    classNode.put("collegeDbId", academy.getId()); // 所属学院的数据库ID
                    classNode.put("studentCount", classItem.getStudentNumber() != null ? classItem.getStudentNumber() : 0);
                    classNode.put("entranceYear", classItem.getEntranceYear());

                    majorStudentCount += classItem.getStudentNumber() != null ? classItem.getStudentNumber() : 0;
                    classNodes.add(classNode);
                }

                majorNode.put("studentCount", majorStudentCount);
                majorNode.put("children", classNodes);
                collegeStudentCount += majorStudentCount;
                majorNodes.add(majorNode);
            }

            collegeNode.put("studentCount", collegeStudentCount);
            collegeNode.put("children", majorNodes);
            totalStudentCount += collegeStudentCount;
            collegeNodes.add(collegeNode);
        }

        rootNode.put("studentCount", totalStudentCount);
        rootNode.put("children", collegeNodes);
        treeData.add(rootNode);

        return treeData;
    }

    @Override
    public StudentVO getStudentByUserId(Long id) {
        LambdaQueryWrapper<Student> wrapper = Wrappers.lambdaQuery(Student.class);
        wrapper.eq(Student::getUserId, id);
        Student student = getOne(wrapper,false);
        if (student != null) {
            StudentVO vo = StudentConvert.INSTANCE.toVO(student);
            BaseUser user = baseUserMapper.selectById(id);
            vo.setUser(UserConvert.INSTANCE.toVO(user));
            return vo;
        }
        return null;
    }

    @Override
    public List<Student> getStudentsByTaskId(Long taskId) {
        if (taskId == null) {
            return new ArrayList<>();
        }
        return baseMapper.selectStudentsByTaskId(taskId);
    }

    @Override
    public List<Student> getStudentsByCourseId(Long courseId){
        //首先获得课程下的所有任务ID和班级 ID，再拉取课程下的所有学生
        return studentMapper.selectStudentsByCourseId(courseId);
    }

    @Override
    public int getStudentCountByTaskId(Long taskId) {
        if (taskId == null) {
            return 0;
        }
        return baseMapper.countStudentsByTaskId(taskId);
    }

    /**
     * 获取学籍状态文本
     */
    private String getStudentStatusText(Integer studentStatus) {
        if (studentStatus == null) return "未知";
        return switch (studentStatus) {
            case 0 -> "在读";
            case 1 -> "休学";
            case 2 -> "退学";
            case -1 -> "毕业";
            default -> "未知";
        };
    }
    
    /**
     * 获取记录状态文本
     */
    private String getStatusText(Integer status) {
        if (status == null) return "未知";
        return switch (status) {
            case 0 -> "正常";
            case 1 -> "停用";
            case -1 -> "删除";
            default -> "未知";
        };
    }

    @Override
    public Object syncClassStudentCount(Long classId) {
        if (classId == null) {
            return false;
        }
        
        try {
            log.info("开始手动同步班级 {} 的学生数", classId);
            
            // 统计该班级下正常状态的学生数量
            int actualStudentCount = baseMapper.countStudentsByClassId(classId);
            
            // 更新班级表的学生数字段
            Classes classes = new Classes();
            classes.setClassId(classId);
            classes.setStudentNumber(actualStudentCount);
            
            int updateResult = classesMapper.updateById(classes);
            
            if (updateResult > 0) {
                log.info("班级 {} 学生数同步成功，实际学生数：{}", classId, actualStudentCount);
                return true;
            } else {
                log.warn("班级 {} 学生数同步失败，可能班级不存在", classId);
                return false;
            }
            
        } catch (Exception e) {
            log.error("手动同步班级 {} 学生数失败", classId, e);
            return false;
        }
    }

    @Override
    public Object syncAllClassStudentCount() {
        try {
            log.info("开始手动同步所有班级的学生数");
            
            // 调用班级服务的同步方法
            Map<String, Object> result = classesService.syncStudentCount(null, null, null);
            
            log.info("所有班级学生数同步完成，结果：{}", result);
            return result;
            
        } catch (Exception e) {
            log.error("手动同步所有班级学生数失败", e);
            return false;
        }
    }
}
