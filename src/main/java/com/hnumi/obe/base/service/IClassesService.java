package com.hnumi.obe.base.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hnumi.obe.base.dto.ClassesQueryDTO;
import com.hnumi.obe.base.entity.Classes;
import com.hnumi.obe.base.vo.ClassesVO;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
* 班级表 服务类
*
*/
public interface IClassesService extends IService<Classes> {

    Object deleteById(Long id);

    Object stopClassesUsingById(Long id);

    /**
     * 分页查询班级列表（包含班主任信息）
     *
     * @param query 查询条件
     * @return 分页结果
     */
    Page<ClassesVO> pageClasses(ClassesQueryDTO query);

    /**
     * 获取班级详情（包含班主任信息）
     *
     * @param id 班级ID
     * @return 班级详情
     */
    ClassesVO getClassesDetail(Long id);

    List<Classes> getClassByMajorAndEntranceYear(Long majorId, Integer entranceYear);

    List<ClassesVO> listClassByMajorId(Long majorId);

    Map<Long, Classes> getMap(Set<Long> ids);

    /**
     * 根据班级ID获取班级名称
     * @param classId 班级ID
     * @return 班级名称
     */
    String getClassNameById(Long classId);

    /**
     * 根据班级ID获取学生数量
     * @param classId 班级ID
     * @return 学生数量
     */
    Integer getStudentCountByClassId(Long classId);

    /**
     * 批量获取班级信息（包含班级名称和学生数量）
     * @param classIds 班级ID列表
     * @return 班级信息Map，key为班级ID，value为班级信息
     */
    Map<Long, ClassesVO> getClassesInfoByIds(List<Long> classIds);

    /**
     * 预览同步班级人数
     * 返回班级ID、名称、原人数、实际人数
     */
    List<Map<String, Object>> previewSyncStudentCount(Long academyId, Long majorId, Long classId);

    /**
     * 同步班级人数（全事务，部分失败则全部回滚）
     * @return 详细结果
     */
    Map<String, Object> syncStudentCount(Long academyId, Long majorId, Long classId);
}
