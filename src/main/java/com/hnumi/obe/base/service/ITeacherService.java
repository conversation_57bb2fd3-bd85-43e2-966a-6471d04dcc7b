package com.hnumi.obe.base.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hnumi.obe.base.dto.TeacherDTO;
import com.hnumi.obe.base.dto.TeacherQueryDTO;
import com.hnumi.obe.base.entity.Teacher;
import com.hnumi.obe.base.vo.EnumVO;
import com.hnumi.obe.base.vo.TeacherExportVO;
import com.hnumi.obe.base.vo.TeacherTitleOptionsVO;
import com.hnumi.obe.base.vo.TeacherVO;
import com.hnumi.obe.system.entity.Role;

import java.io.InputStream;
import java.util.List;
import java.util.Map;

/**
 * 教师表 服务类
 *
 */
public interface ITeacherService extends IService<Teacher> {

    Object importTeacher(InputStream inputStream);

    List<TeacherExportVO> exportTeacher(TeacherQueryDTO query);


    Object addTeacher(TeacherDTO dto);

    Object updateTeacher(TeacherDTO dto);

    Object deleteTeacherById(Long id);


    Object stopTeacherUsingById(Long id);

    Boolean enableTeacherById(Long id);
    List<TeacherTitleOptionsVO> getTeacherTitleOptions();

    Page<TeacherVO> pageTeacher(TeacherQueryDTO query);

    TeacherVO getDetailById(Long id);

    TeacherVO getByTeacherNumber(String teacherNumber);

    TeacherVO getByUserId(Long userId);

    Teacher getByTeacherId(Long teacherId);

    List<Role> getRoleByTeacherNumber(String teacherNumber);

    List<EnumVO.Option<String>> getTeacherByAcademyId(Long academyId);

    List<EnumVO.Option<String>> getTeacherByMajorId(Long majorId);

    Boolean batchDeleteTeachers(List<Long> ids);


    Boolean resetPassword(Long id);

    /**
     * 获取教师统计信息
     *
     * @return 统计信息
     */
    Object getTeacherStatistics();

    /**
     * 获取教师组织结构树
     */
    Object getTeacherTree();

    List<TeacherVO> getTeacherOptions();

    /**
     * 批量获取教师信息
     *
     * @param teacherIds 教师ID列表
     * @return 教师ID到教师信息的映射
     */
    Map<Long, TeacherVO> getTeacherInfoByIds(List<Long> teacherIds);

    /**
     * 批量查询教师详细信息（优化版本）
     * 使用单次数据库查询获取所有教师的完整信息，包括用户信息和学院信息
     *
     * @param teacherIds 教师ID列表
     * @return 教师ID到教师详细信息的映射，key为教师ID，value为TeacherVO
     */
    Map<Long, TeacherVO> getTeacherMapByIds(List<Long> teacherIds);

    /**
     *
     * @param taskId
     * @return
     */
    //List<TeacherVO> getTeachersByTaskId(Long taskId);
}
