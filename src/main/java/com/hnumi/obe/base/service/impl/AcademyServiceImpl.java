package com.hnumi.obe.base.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hnumi.obe.base.dto.AcademyQueryDTO;
import com.hnumi.obe.base.dto.AcademyDeanDTO;
import com.hnumi.obe.base.dto.AcademyDTO;
import com.hnumi.obe.base.entity.Academy;
import com.hnumi.obe.base.mapper.*;
import com.hnumi.obe.base.mapstruct.AcademyConvert;
import com.hnumi.obe.base.service.IAcademyService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hnumi.obe.base.vo.*;
import com.hnumi.obe.common.util.StringUtil;
import com.hnumi.obe.common.util.RequestUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Objects;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 院系表 服务实现类
 */
@Service
public class AcademyServiceImpl extends ServiceImpl<AcademyMapper, Academy> implements IAcademyService {

    private static final Logger log = LoggerFactory.getLogger(AcademyServiceImpl.class);

    @Autowired
    private TeacherMapper teacherMapper;
    @Autowired
    private AcademyMapper academyMapper;
    @Autowired
    private MajorMapper majorMapper;
    @Autowired
    private ClassesMapper classesMapper;
    @Autowired
    private StudentMapper studentMapper;

    @Override
    public Object deleteById(Long id) {
        Academy academy = new Academy();
        academy.setId(id);
        academy.setStatus(-1);
        return updateById(academy);
    }

    @Override
    public Object getAcademyDetailById(Long id) {
        Academy academy = getById(id);
        AcademyVO academyVO = AcademyConvert.INSTANCE.toVO(academy);
        // 直接返回数值状态，由前端处理状态文本转换
        academyVO.setStatus(academy.getStatus());
        academyVO.setDean(teacherMapper.getNameById(academy.getAcademyPresidentId()));
        //todo updater和creator的姓名是从teacher表中获取还是user表
        academyVO.setCreator(teacherMapper.getNameById(academy.getCreator()));
        academyVO.setUpdater(teacherMapper.getNameById(academy.getModifier()));
        academyVO.setMajors(majorMapper.getMajorCountByAcademyId(id));
        academyVO.setClasses(classesMapper.getClassesCountByAcademyId(id));
        return academyVO;
    }

    @Override
    public Object stopAcademyUsingById(Long id) {
        Academy academy = new Academy();
        academy.setId(id);
        academy.setStatus(1);
        return updateById(academy);
    }

    @Override
    public Object enableAcademyById(Long id) {
        Academy academy = new Academy();
        academy.setId(id);
        academy.setStatus(0);
        return updateById(academy);
    }

    @Override
    public Object getDeanOptions() {
        //todo roleId
        List<DeanOptionsVO> deanOptions = teacherMapper.getDeanOptions();
        return deanOptions;
    }

    @Transactional
    @Override
    public List<AcademyExportVO> getAcademyList(AcademyQueryDTO query) {
        LambdaQueryWrapper<Academy> wrapper = Wrappers.lambdaQuery(Academy.class);
        wrapper.like(StringUtil.isNotBlank(query.getAcademyName()), Academy::getAcademyName, query.getAcademyName())
                .eq(!Objects.isNull(query.getAcademyPresidentId()), Academy::getAcademyPresidentId, query.getAcademyPresidentId())
                .eq(!Objects.isNull(query.getStatus()), Academy::getStatus, query.getStatus());
        List<Academy> academyList = list(wrapper);
        //用Stream流的方式转换为AcademyExportVO
        List<AcademyExportVO> academyExportVOList = academyList.stream().map(academy -> {
            AcademyExportVO academyExportVO = new AcademyExportVO();
            academyExportVO.setAcademyName(academy.getAcademyName());
            // todo roleId,user表 or teacher表
            String academyPresident = teacherMapper.getNameById(academy.getAcademyPresidentId());
            academyExportVO.setAcademyPresident(academyPresident);
            Integer statusValue = academy.getStatus();
            String status = "未知";
            if (statusValue != null) {
                switch (statusValue) {
                    case 0:
                        status = "正常";
                        break;
                    case -1:
                        status = "删除";
                        break;
                    case 1:
                        status = "禁用";
                        break;
                    default:
                        status = "未知";
                        break;
                }
            }
            academyExportVO.setStatus(status);
            return academyExportVO;
        }).toList();
        return academyExportVOList;
    }

    @Override
    public List<AcademyOptionsVO> getAcademyOptionsForMajor() {
        return academyMapper.getAcademyOptionsForMajor();
    }

    @Override
    public AcademyOptionsVO getAcademyOptionById(Long id) {
        return academyMapper.getAcademyOptionById(id);
    }

    @Override
    public IPage<AcademyVO> pageList(AcademyQueryDTO query) {
        // 使用自定义SQL查询，直接返回完整的AcademyVO数据
        Page<AcademyVO> page = new Page<>(query.getCurrent(), query.getSize());
        return academyMapper.pageListWithDetails(
            page,
            query.getAcademyName(),
            query.getAcademyPresidentId(),
            query.getStatus()
        );
    }

    @Override
    public Object getAcademyStatistics(Long id) {
        AcademyStatisticsVO academyStatisticsVO = new AcademyStatisticsVO();

        // 设置学院总数（所有学院）
        academyStatisticsVO.setTotalAcademies(academyMapper.selectCount(null));

        // 设置活跃学院数（status为0的学院）
        academyStatisticsVO.setActiveAcademies(academyMapper.selectCount(
                Wrappers.lambdaQuery(Academy.class).eq(Academy::getStatus, 0)
        ));

        // 根据id查询对应的专业数和班级数
        if (id != null) {
            academyStatisticsVO.setTotalMajors((long) majorMapper.getMajorCountByAcademyId(id));
            academyStatisticsVO.setTotalClasses((long) classesMapper.getClassesCountByAcademyId(id));
        } else {
            academyStatisticsVO.setTotalMajors(majorMapper.selectCount(null));
            academyStatisticsVO.setTotalClasses(classesMapper.selectCount(null));
        }

        // 根据id查询对应的学院实体
        LambdaQueryWrapper<Academy> wrapper = Wrappers.lambdaQuery(Academy.class);
        if (id != null) {
            wrapper.eq(Academy::getId, id);
        }
        List<Academy> academyList = list(wrapper);

        // 转换为VO对象
        List<AcademyVO> academyDetails = academyList.stream().map(academy -> {
            AcademyVO academyVO = new AcademyVO();
            academyVO.setId(academy.getId());
            academyVO.setName(academy.getAcademyName());
            academyVO.setMajors(majorMapper.getMajorCountByAcademyId(academy.getId()));
            academyVO.setClasses(classesMapper.getClassesCountByAcademyId(academy.getId()));
            academyVO.setStudents(studentMapper.getStudentCountByAcademyId(academy.getId()));
            academyVO.setTeachers(teacherMapper.getTeacherCountByAcademyId(academy.getId()));
            return academyVO;
        }).toList();
        academyStatisticsVO.setAcademyDetails(academyDetails);
        return academyStatisticsVO;
    }

    @Override
    public Academy checkDeanConflict(Long userId, Long currentAcademyId) {
        LambdaQueryWrapper<Academy> wrapper = Wrappers.lambdaQuery(Academy.class);
        wrapper.eq(Academy::getAcademyPresidentId, userId)
               .ne(Academy::getId, currentAcademyId)
               .ne(Academy::getStatus, -1); // 排除已删除的学院
        return getOne(wrapper);
    }

    @Override
    @Transactional
    public Object setDean(AcademyDeanDTO setDeanDTO) {
        log.info("开始设置院长：学院ID={}, 用户ID={}", setDeanDTO.getAcademyId(), setDeanDTO.getDeanUserId());
        
        // 检查该用户是否已经是其他学院的院长
        Academy conflictAcademy = checkDeanConflict(setDeanDTO.getDeanUserId(), setDeanDTO.getAcademyId());
        
        // 如果存在冲突，先清除原学院的院长职位
        if (conflictAcademy != null) {
            log.info("发现院长冲突：用户ID={}已经是学院ID={}的院长，将清除原职位", 
                setDeanDTO.getDeanUserId(), conflictAcademy.getId());
            
            // 使用LambdaUpdateWrapper明确设置字段为null
            LambdaUpdateWrapper<Academy> updateWrapper = Wrappers.lambdaUpdate(Academy.class);
            updateWrapper.eq(Academy::getId, conflictAcademy.getId())
                        .set(Academy::getAcademyPresidentId, null);
            boolean clearResult = update(updateWrapper);
            log.info("清除原院长职位结果：{}", clearResult);
        } else {
            log.info("未发现院长冲突，直接设置新院长");
        }
        
        // 设置新的院长
        Academy academy = new Academy();
        academy.setId(setDeanDTO.getAcademyId());
        academy.setAcademyPresidentId(setDeanDTO.getDeanUserId());
        boolean setResult = updateById(academy);
        log.info("设置新院长结果：{}", setResult);
        
        return setResult;
    }

    @Override
    @Transactional
    public Object updateAcademy(AcademyDTO academyDTO) {
        log.info("开始更新学院信息：学院ID={}, 院长ID={}", academyDTO.getId(), academyDTO.getAcademyPresidentId());
        
        // 获取当前学院信息
        Academy currentAcademy = getById(academyDTO.getId());
        if (currentAcademy == null) {
            log.error("学院不存在：ID={}", academyDTO.getId());
            throw new RuntimeException("学院不存在");
        }
        
        // 校验学院名称是否已存在（排除自身）
        if (isAcademyNameExists(academyDTO.getAcademyName(), academyDTO.getId())) {
            throw new RuntimeException("学院名称 '" + academyDTO.getAcademyName() + "' 已存在，请使用其他名称");
        }
        
        // 检查院长是否发生变更
        Long oldPresidentId = currentAcademy.getAcademyPresidentId();
        Long newPresidentId = academyDTO.getAcademyPresidentId();
        
        // 如果院长发生了变更且新院长不为空，需要检查冲突
        if (!Objects.equals(oldPresidentId, newPresidentId) && newPresidentId != null) {
            log.info("检测到院长变更：从{}变更为{}", oldPresidentId, newPresidentId);
            
            // 检查新院长是否已经是其他学院的院长
            Academy conflictAcademy = checkDeanConflict(newPresidentId, academyDTO.getId());
            
            if (conflictAcademy != null) {
                log.info("发现院长冲突：用户ID={}已经是学院ID={}的院长，将清除原职位", 
                    newPresidentId, conflictAcademy.getId());
                
                // 使用LambdaUpdateWrapper明确设置字段为null
                LambdaUpdateWrapper<Academy> updateWrapper = Wrappers.lambdaUpdate(Academy.class);
                updateWrapper.eq(Academy::getId, conflictAcademy.getId())
                            .set(Academy::getAcademyPresidentId, null);
                boolean clearResult = update(updateWrapper);
                log.info("清除原院长职位结果：{}", clearResult);
            }
        }
        
        // 更新学院信息
        Academy entity = AcademyConvert.INSTANCE.toEntity(academyDTO);
        entity.setModifier(RequestUtil.getUserId());
        entity.setModifyTime(LocalDateTime.now());
        boolean updateResult = updateById(entity);
        log.info("更新学院信息结果：{}", updateResult);
        
        return updateResult;
    }

    @Override
    public boolean isAcademyNameExists(String academyName, Long excludeId) {
        LambdaQueryWrapper<Academy> wrapper = Wrappers.lambdaQuery(Academy.class);
        wrapper.eq(Academy::getAcademyName, academyName)
               .ne(Academy::getStatus, -1); // 排除已删除的学院
        
        if (excludeId != null) {
            wrapper.ne(Academy::getId, excludeId); // 排除指定ID（用于更新时排除自身）
        }
        
        return count(wrapper) > 0;
    }

    @Override
    public List<String> checkDuplicateAcademyNames(List<Academy> academies) {
        if (academies == null || academies.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 提取学院名称列表用于数据库查询
        List<String> academyNames = academies.stream()
                .map(Academy::getAcademyName)
                .collect(Collectors.toList());
        
        // 检查数据库中已存在的学院名称
        LambdaQueryWrapper<Academy> wrapper = Wrappers.lambdaQuery(Academy.class);
        wrapper.in(Academy::getAcademyName, academyNames)
               .ne(Academy::getStatus, -1); // 排除已删除的学院
        
        List<Academy> existingAcademies = list(wrapper);
        List<String> duplicateNames = existingAcademies.stream()
                .map(Academy::getAcademyName)
                .collect(Collectors.toList());
        
        // 检查导入数据内部的重复
        Set<String> nameSet = new HashSet<>();
        Set<String> internalDuplicates = new HashSet<>();
        
        for (Academy academy : academies) {
            if (!nameSet.add(academy.getAcademyName())) {
                internalDuplicates.add(academy.getAcademyName());
            }
        }
        
        // 合并数据库重复和内部重复
        duplicateNames.addAll(internalDuplicates);
        
        return duplicateNames.stream().distinct().collect(Collectors.toList());
    }

    /**
     * 批量导入学院数据
     * 参考教师导入的实现，提供详细的导入结果
     */
    @Override
    @Transactional
    public AcademyImportResultVO importAcademies(List<Academy> academies) {
        if (academies == null || academies.isEmpty()) {
            return AcademyImportResultVO.failure(0, 0, 0, List.of("导入数据为空"));
        }

        log.info("开始导入学院数据，共{}条记录", academies.size());
        // 第一阶段：全量数据验证（不进行任何数据库操作）
        List<String> validationErrors = new ArrayList<>();
        List<Academy> validatedAcademies = new ArrayList<>();
        
        for (int i = 0; i < academies.size(); i++) {
            Academy academy = academies.get(i);
            try {
                // 验证基础数据
                if (StringUtil.isBlank(academy.getAcademyName())) {
                    validationErrors.add(String.format("第%d行：学院名称不能为空", i + 1));
                    continue;
                }
                academy.setAcademyName(academy.getAcademyName().trim());
                // 验证学院名称长度
                if (academy.getAcademyName().length() > 255) {
                    validationErrors.add(String.format("第%d行：学院名称长度不能超过255个字符", i + 1));
                    continue;
                }

                validatedAcademies.add(academy);
                
            } catch (Exception e) {
                validationErrors.add(String.format("第%d行：%s", i + 1, e.getMessage()));
            }
        }
        
        // 检查重复名称（包括数据库中已存在的和导入数据内部重复的）
        List<String> duplicateNames = checkDuplicateAcademyNames(validatedAcademies);
        if (!duplicateNames.isEmpty()) {
            for (String duplicateName : duplicateNames) {
                validationErrors.add(String.format("学院名称 '%s' 已存在或在导入数据中重复", duplicateName));
            }
        }
        
        // 如果有验证错误，直接返回失败结果，不进行任何数据库操作
        if (!validationErrors.isEmpty()) {
            log.warn("数据验证失败，共{}个错误，不执行导入操作", validationErrors.size());
            return AcademyImportResultVO.failure(0, validationErrors.size(), academies.size(), validationErrors);
        }
        
        // 第二阶段：数据库操作（原子性：要么全部成功，要么全部失败）
        log.info("数据验证通过，开始执行数据库操作，共{}条有效记录", validatedAcademies.size());
        
        try {
            // 批量插入学院信息
            log.info("开始插入学院信息，共{}条", validatedAcademies.size());
            boolean insertResult = saveBatch(validatedAcademies);
            if (!insertResult) {
                throw new RuntimeException("学院信息批量插入失败");
            }
            log.info("学院信息插入完成");
            
            // 导入成功
            int successCount = validatedAcademies.size();
            log.info("学院数据导入成功，共导入{}条记录", successCount);
            
            return AcademyImportResultVO.success(successCount, academies.size());
            
        } catch (Exception e) {
            log.error("数据库操作失败，事务将回滚", e);
            // 抛出异常，触发事务回滚
            throw new RuntimeException("数据库操作失败：" + e.getMessage(), e);
        }
    }
}
