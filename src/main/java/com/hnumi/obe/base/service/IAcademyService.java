package com.hnumi.obe.base.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hnumi.obe.base.dto.AcademyDTO;
import com.hnumi.obe.base.dto.AcademyQueryDTO;
import com.hnumi.obe.base.dto.AcademyDeanDTO;
import com.hnumi.obe.base.entity.Academy;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hnumi.obe.base.vo.AcademyExportVO;
import com.hnumi.obe.base.vo.AcademyOptionsVO;
import com.hnumi.obe.base.vo.AcademyVO;
import com.hnumi.obe.base.vo.AcademyImportResultVO;

import java.util.List;

/**
 * 院系表 服务类
 */
public interface IAcademyService extends IService<Academy> {

    Object deleteById(Long id);

    Object getAcademyDetailById(Long id);

    Object stopAcademyUsingById(Long id);

    Object enableAcademyById(Long id);

    Object getDeanOptions();

    List<AcademyExportVO> getAcademyList(AcademyQueryDTO query);

    List<AcademyOptionsVO> getAcademyOptionsForMajor();

    AcademyOptionsVO getAcademyOptionById(Long id);

    IPage<AcademyVO> pageList(AcademyQueryDTO query);

    Object getAcademyStatistics(Long id);

    /**
     * 检查用户是否已经是其他学院的院长
     * 
     * @param userId 用户ID
     * @param currentAcademyId 当前要设置的学院ID
     * @return 如果是其他学院的院长，返回该学院信息；否则返回null
     */
    Academy checkDeanConflict(Long userId, Long currentAcademyId);

    /**
     * 设置学院院长
     * 
     * @param academyDeanDTO 学院院长参数
     * @return 操作结果
     */
    Object setDean(AcademyDeanDTO academyDeanDTO);

    /**
     * 更新学院信息
     * 
     * @param academyDTO 学院信息
     * @return 操作结果
     */
    Object updateAcademy(AcademyDTO academyDTO);

    /**
     * 检查学院名称是否已存在
     * 
     * @param academyName 学院名称
     * @param excludeId 排除的学院ID（用于更新时排除自身）
     * @return 如果存在返回true，否则返回false
     */
    boolean isAcademyNameExists(String academyName, Long excludeId);

    /**
     * 批量校验学院名称是否存在重复
     * 
     * @param academies 学院对象列表
     * @return 重复的学院名称列表
     */
    List<String> checkDuplicateAcademyNames(List<Academy> academies);

    /**
     * 批量导入学院数据
     * 
     * @param academies 学院对象列表
     * @return 导入结果
     */
    AcademyImportResultVO importAcademies(List<Academy> academies);
}
