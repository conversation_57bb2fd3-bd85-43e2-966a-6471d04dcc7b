package com.hnumi.obe.base.service.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.hnumi.obe.base.entity.Classes;
import com.hnumi.obe.base.mapper.ClassesMapper;
import com.hnumi.obe.base.service.IClassesService;
import com.hnumi.obe.base.dto.ClassesQueryDTO;
import com.hnumi.obe.base.vo.ClassesVO;
import com.hnumi.obe.base.mapstruct.ClassesConvert;
import com.hnumi.obe.common.util.StringUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import com.hnumi.obe.system.service.IBaseUserService;
import com.hnumi.obe.system.vo.UserVO;
import com.hnumi.obe.system.entity.BaseUser;
import com.hnumi.obe.system.mapstruct.UserConvert;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.ArrayList;
import java.util.Set;
import java.util.HashMap;
import java.util.stream.Collectors;
import org.springframework.transaction.annotation.Transactional;

/**
 * 班级表 服务实现类
 */
@Slf4j
@Service
public class ClassesServiceImpl extends ServiceImpl<ClassesMapper, Classes> implements IClassesService {

    @Autowired
    private IBaseUserService baseUserService;

    @Override
    public Object deleteById(Long id) {
        Classes classes = new Classes();
        classes.setClassId(id);
        classes.setStatus(-1);
        return updateById(classes);
    }

    @Override
    public Object stopClassesUsingById(Long id) {
        Classes classes = new Classes();
        classes.setClassId(id);
        classes.setClassStatus(1);
        return updateById(classes);
    }

    @Override
    public Page<ClassesVO> pageClasses(ClassesQueryDTO query) {
        try {
            log.info("开始分页查询班级列表，查询条件：{}", query);
            
            // 参数验证
            if (query == null) {
                log.warn("查询参数为空");
                return new Page<>();
            }
            
            // 确保分页参数有效
            if (query.getCurrent() < 1) {
                query.setCurrent(1);
            }
            if (query.getSize() < 1) {
                query.setSize(10);
            }
            if (query.getSize() > 100) {
                query.setSize(100); // 限制最大每页大小
            }
            
            log.info("分页参数：当前页={}，每页大小={}", query.getCurrent(), query.getSize());
            
            // 记录查询条件
            log.info("查询条件：班级名称={}，专业ID={}，入学年份={}，班主任ID={}，班级状态={}，记录状态={}", 
                    query.getClassName(), query.getMajorId(), query.getEntranceYear(), 
                    query.getHeadteacherId(), query.getClassStatus(), query.getStatus());
            
            // 验证查询条件
            if (StringUtil.isNotBlank(query.getClassName()) && query.getClassName().length() > 50) {
                log.warn("班级名称过长，已截断：{}", query.getClassName());
                query.setClassName(query.getClassName().substring(0, 50));
            }
            
            // 验证入学年份格式
            if (StringUtil.isNotBlank(query.getEntranceYear()) && !query.getEntranceYear().matches("\\d{4}")) {
                log.warn("入学年份格式不正确：{}", query.getEntranceYear());
                query.setEntranceYear(null);
            }
            
            // 验证状态值
            if (query.getStatus() != null && (query.getStatus() < -1 || query.getStatus() > 1)) {
                log.warn("记录状态值无效：{}", query.getStatus());
                query.setStatus(null);
            }
            
            if (query.getClassStatus() != null && (query.getClassStatus() < -1 || query.getClassStatus() > 0)) {
                log.warn("班级状态值无效：{}", query.getClassStatus());
                query.setClassStatus(null);
            }
            
            // 验证ID值
            if (query.getMajorId() != null && query.getMajorId() <= 0) {
                log.warn("专业ID无效：{}", query.getMajorId());
                query.setMajorId(null);
            }
            
            if (query.getHeadteacherId() != null && query.getHeadteacherId() <= 0) {
                log.warn("班主任ID无效：{}", query.getHeadteacherId());
                query.setHeadteacherId(null);
            }
            
            // 验证学生人数
            if (query.getStudentNumber() != null && query.getStudentNumber() < 0) {
                log.warn("学生人数无效：{}", query.getStudentNumber());
                query.setStudentNumber(null);
            }
            
            // 验证学生人数上限
            if (query.getStudentNumber() != null && query.getStudentNumber() > 200) {
                log.warn("学生人数过大：{}", query.getStudentNumber());
                query.setStudentNumber(null);
            }
            
            // 构建查询条件
            LambdaQueryWrapper<Classes> wrapper = Wrappers.lambdaQuery(Classes.class);
            
            // 添加查询条件
            wrapper.like(StringUtil.isNotBlank(query.getClassName()), Classes::getClassName, query.getClassName());
            wrapper.eq(query.getMajorId() != null, Classes::getMajorId, query.getMajorId());
            wrapper.eq(StringUtil.isNotBlank(query.getEntranceYear()), Classes::getEntranceYear, query.getEntranceYear());
            wrapper.eq(query.getHeadteacherId() != null, Classes::getHeadteacherId, query.getHeadteacherId());
            wrapper.eq(query.getClassStatus() != null, Classes::getClassStatus, query.getClassStatus());
            wrapper.eq(query.getStatus() != null, Classes::getStatus, query.getStatus());
            
            // 添加排序条件 - 按创建时间倒序
            wrapper.orderByDesc(Classes::getCreateTime);
            
            // 设置分页参数
            Page<Classes> page = query.getPage();
            // 执行分页查询
            List<Classes> classesList = list(page, wrapper);
            page.setRecords(classesList);
            
            // 确保数据不为空
            if (classesList == null) {
                classesList = new ArrayList<>();
            }
            
            log.info("查询到班级数据 {} 条", classesList.size());
            
            // 转换为VO并填充班主任信息
            Page<ClassesVO> resultPage = ClassesConvert.INSTANCE.toPageVO(page);
            
            // 确保分页信息正确
            resultPage.setCurrent(page.getCurrent());
            resultPage.setSize(page.getSize());
            resultPage.setTotal(page.getTotal());
            resultPage.setPages(page.getPages());
            
            // 获取所有班主任ID
            List<Long> headteacherIds = classesList.stream()
                    .map(Classes::getHeadteacherId)
                    .filter(id -> id != null && id > 0) // 过滤掉无效的ID
                    .distinct()
                    .collect(Collectors.toList());
            
            log.info("需要查询班主任信息的ID：{}", headteacherIds);
            
            // 批量查询班主任用户信息
            Map<Long, UserVO> userMap = null;
            if (!headteacherIds.isEmpty()) {
                try {
                    List<BaseUser> users = baseUserService.listByIds(headteacherIds);
                    userMap = users.stream()
                            .collect(Collectors.toMap(
                                    BaseUser::getId,
                                    UserConvert.INSTANCE::toVO,
                                    (existing, replacement) -> existing // 处理重复key的情况
                            ));
                    log.info("成功查询到班主任信息 {} 条", users.size());
                } catch (Exception e) {
                    log.error("查询班主任信息失败", e);
                    // 不抛出异常，继续处理班级数据
                }
            }
            
                    // 填充班主任信息到VO中
        final Map<Long, UserVO> finalUserMap = userMap;
        resultPage.getRecords().forEach(classesVO -> {
            if (classesVO.getHeadteacherId() != null && finalUserMap != null) {
                UserVO userVO = finalUserMap.get(classesVO.getHeadteacherId());
                if (userVO != null) {
                    classesVO.setUser(userVO);
                } else {
                    log.warn("未找到班主任信息，班主任ID：{}", classesVO.getHeadteacherId());
                }
            }
        });
            
            log.info("班级分页查询完成，返回数据 {} 条，总数据 {} 条", resultPage.getRecords().size(), resultPage.getTotal());
            return resultPage;
            
        } catch (Exception e) {
            log.error("分页查询班级列表失败，查询条件：{}", query, e);
            throw new RuntimeException("分页查询班级列表失败", e);
        }
    }

    @Override
    public ClassesVO getClassesDetail(Long id) {
        try {
            log.info("开始获取班级详情，班级ID：{}", id);
            
            // 参数验证
            if (id == null || id <= 0) {
                log.warn("班级ID无效：{}", id);
                return null;
            }
            
            // 获取班级信息
            Classes classes = getById(id);
            if (classes == null) {
                log.warn("班级不存在，ID：{}", id);
                return null;
            }
            
            // 转换为VO
            ClassesVO classesVO = ClassesConvert.INSTANCE.toVO(classes);
            
            // 获取班主任信息
            if (classes.getHeadteacherId() != null && classes.getHeadteacherId() > 0) {
                try {
                    BaseUser teacher = baseUserService.getById(classes.getHeadteacherId());
                    if (teacher != null) {
                        classesVO.setUser(UserConvert.INSTANCE.toVO(teacher));
                        log.info("成功获取班主任信息，班主任ID：{}", teacher.getId());
                    } else {
                        log.warn("班主任不存在，ID：{}", classes.getHeadteacherId());
                    }
                } catch (Exception e) {
                    log.error("获取班主任信息失败，班主任ID：{}", classes.getHeadteacherId(), e);
                    // 不抛出异常，继续处理班级数据
                }
            }
            
            log.info("班级详情获取完成，班级ID：{}", id);
            return classesVO;
            
        } catch (Exception e) {
            log.error("获取班级详情失败，班级ID：{}", id, e);
            throw new RuntimeException("获取班级详情失败", e);
        }
    }

    @Override
    public List<ClassesVO> listClassByMajorId(Long majorId) {
        LambdaQueryWrapper<Classes> wrapper = Wrappers.lambdaQuery(Classes.class)
                .eq(Classes::getMajorId, majorId)
                .eq(Classes::getStatus, 0) // 只查询有效的班级
                .orderByDesc(Classes::getCreateTime); // 按创建时间倒序
        return ClassesConvert.INSTANCE.toVO(list(wrapper));
    }

    @Override
    public Map<Long, Classes> getMap(Set<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Map.of();
        }
        LambdaQueryWrapper<Classes> queryWrapper = Wrappers.<Classes>lambdaQuery()
                .in(Classes::getClassId, ids)
                .eq(Classes::getStatus, 0);
        return list(queryWrapper).stream().collect(Collectors.toMap(Classes::getClassId, classes -> classes));
    }

    @Override
    public List<Classes> getClassByMajorAndEntranceYear(Long majorId, Integer entranceYear) {
        long startTime = System.currentTimeMillis();

        // 参数验证
        if (majorId == null || entranceYear == null) {
            log.warn("专业ID或入学年份为空，majorId: {}, entranceYear: {}", majorId, entranceYear);
            return new ArrayList<>();
        }

        log.info("开始查询专业ID: {} 入学年份: {} 的班级及学生人数", majorId, entranceYear);

        // 使用子查询一次性获取班级信息和学生人数，性能最优
        List<Classes> classesList = baseMapper.selectClassesWithStudentCountByMajorAndYear(
                majorId, entranceYear.toString());

        long endTime = System.currentTimeMillis();
        log.info("完成班级查询和学生人数统计，共 {} 个班级，耗时: {} ms",
                classesList.size(), (endTime - startTime));

        if (classesList.isEmpty()) {
            log.info("未找到专业ID: {} 入学年份: {} 的班级", majorId, entranceYear);
        }

        return classesList;
    }

    @Override
    public String getClassNameById(Long classId) {
        if (classId == null) {
            return null;
        }
        Classes classes = this.getById(classId);
        return classes != null ? classes.getClassName() : null;
    }

    @Override
    public Integer getStudentCountByClassId(Long classId) {
        if (classId == null) {
            return 0;
        }
        Classes classes = this.getById(classId);
        return classes != null && classes.getStudentNumber() != null ? classes.getStudentNumber() : 0;
    }

    @Override
    public Map<Long, ClassesVO> getClassesInfoByIds(List<Long> classIds) {
        if (CollectionUtils.isEmpty(classIds)) {
            return Map.of();
        }

        // 批量查询班级信息
        LambdaQueryWrapper<Classes> wrapper = Wrappers.lambdaQuery(Classes.class);
        wrapper.in(Classes::getClassId, classIds);
        wrapper.eq(Classes::getStatus, 0); // 只查询正常状态的班级

        List<Classes> classesList = this.list(wrapper);
        if (CollectionUtils.isEmpty(classesList)) {
            return Map.of();
        }

        // 转换为ClassesVO并返回Map
        return classesList.stream()
                .map(classes -> {
                    ClassesVO vo = ClassesConvert.INSTANCE.toVO(classes);
                    // 设置班主任信息（如果需要的话，这里可以批量查询班主任信息）
                    if (classes.getHeadteacherId() != null) {
                        BaseUser headteacher = baseUserService.getById(classes.getHeadteacherId());
                        if (headteacher != null) {
                            UserVO headteacherVO = UserConvert.INSTANCE.toVO(headteacher);
                            vo.setHeadteacher(headteacherVO);
                        }
                    }
                    return vo;
                })
                .collect(Collectors.toMap(ClassesVO::getClassId, vo -> vo, (existing, replacement) -> existing));
    }

    @Override
    public List<Map<String, Object>> previewSyncStudentCount(Long academyId, Long majorId, Long classId) {
        return baseMapper.previewSyncStudentCount(academyId, majorId, classId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> syncStudentCount(Long academyId, Long majorId, Long classId) {
        long startTime = System.currentTimeMillis();
        Map<String, Object> result = new HashMap<>();
        List<Map<String, Object>> previewList = previewSyncStudentCount(academyId, majorId, classId);
        int totalClasses = previewList.size();
        int updatedClasses = 0;
        int errorCount = 0;
        List<Map<String, Object>> successList = new ArrayList<>();
        List<Map<String, Object>> errorList = new ArrayList<>();
        try {
            for (Map<String, Object> item : previewList) {
                Long cid = (Long) item.get("classId");
                Integer actual = ((Number) item.get("actualStudentNumber")).intValue();
                try {
                    int updateResult = baseMapper.updateStudentNumber(cid, actual);
                    if (updateResult > 0) {
                        updatedClasses++;
                        successList.add(item);
                    } else {
                        errorCount++;
                        Map<String, Object> err = new HashMap<>(item);
                        err.put("errorMsg", "更新失败");
                        errorList.add(err);
                        throw new RuntimeException("班级ID:" + cid + " 更新失败");
                    }
                } catch (Exception e) {
                    errorCount++;
                    Map<String, Object> err = new HashMap<>(item);
                    err.put("errorMsg", e.getMessage());
                    errorList.add(err);
                    throw new RuntimeException("班级ID:" + cid + " 更新失败: " + e.getMessage());
                }
            }
            long duration = System.currentTimeMillis() - startTime;
            result.put("success", true);
            result.put("totalClasses", totalClasses);
            result.put("updatedClasses", updatedClasses);
            result.put("errorCount", errorCount);
            result.put("successList", successList);
            result.put("errorList", errorList);
            result.put("duration", duration);
            result.put("message", String.format("全部成功！共处理%d个班级。", totalClasses));
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            result.put("success", false);
            result.put("totalClasses", totalClasses);
            result.put("updatedClasses", updatedClasses);
            result.put("errorCount", errorCount);
            result.put("successList", successList);
            result.put("errorList", errorList);
            result.put("duration", duration);
            result.put("message", String.format("同步失败，%d条成功，%d条失败：%s", updatedClasses, errorCount, e.getMessage()));
            throw new RuntimeException(result.get("message").toString());
        }
        return result;
    }
}
