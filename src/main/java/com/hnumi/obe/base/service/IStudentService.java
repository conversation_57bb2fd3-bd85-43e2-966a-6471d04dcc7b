package com.hnumi.obe.base.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hnumi.obe.base.dto.StudentDTO;
import com.hnumi.obe.base.dto.StudentQueryDTO;
import com.hnumi.obe.base.entity.Student;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hnumi.obe.base.vo.StudentExportVO;
import com.hnumi.obe.base.vo.StudentVO;

import java.io.InputStream;
import java.util.List;

/**
 * 学生表 服务类
 */
public interface IStudentService extends IService<Student> {
    Object deleteStudentById(Long id);

    Object stopStudentUsingById(Long id);

    Object addStudent(StudentDTO dto);
    Object updateStudent(StudentDTO dto);

    Object importStudent(InputStream inputStream);

    List<StudentExportVO> getStudentList(StudentQueryDTO query);

    Page<StudentVO> pageStudent(StudentQueryDTO query);

    Object getStudentDetailById(Long id);

    List<Object> listTree();

    StudentVO getStudentByUserId(Long id);

    /**
     * 根据教学任务ID查询学生列表
     *
     * @param taskId 教学任务ID
     * @return 学生列表
     */
    List<Student> getStudentsByTaskId(Long taskId);

    /**
     * 根据课程ID查询学生列表
     *
     * @param courseId 教学任务ID
     * @return 学生列表
     */
    List<Student> getStudentsByCourseId(Long courseId);
    /**
     * 根据教学任务ID查询学生数量
     *
     * @param taskId 教学任务ID
     * @return 学生数量
     */
    int getStudentCountByTaskId(Long taskId);

    /**
     * 手动同步指定班级的学生数
     *
     * @param classId 班级ID
     * @return 同步结果
     */
    Object syncClassStudentCount(Long classId);

    /**
     * 手动同步所有班级的学生数
     *
     * @return 同步结果
     */
    Object syncAllClassStudentCount();
}
