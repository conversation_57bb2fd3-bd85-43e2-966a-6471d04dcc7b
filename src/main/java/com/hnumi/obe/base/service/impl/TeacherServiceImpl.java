package com.hnumi.obe.base.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hnumi.obe.base.dto.TeacherDTO;
import com.hnumi.obe.base.dto.TeacherImportDTO;
import com.hnumi.obe.base.dto.TeacherQueryDTO;
import com.hnumi.obe.base.entity.Academy;
import com.hnumi.obe.base.entity.Major;
import com.hnumi.obe.base.entity.Teacher;
import com.hnumi.obe.base.entity.TeacherRoles;
import com.hnumi.obe.base.mapper.AcademyMapper;
import com.hnumi.obe.base.mapper.MajorMapper;
import com.hnumi.obe.base.mapper.TeacherMapper;
import com.hnumi.obe.base.mapper.TeacherRolesMapper;
import com.hnumi.obe.base.mapstruct.TeacherConvert;
import com.hnumi.obe.base.service.IMajorService;
import com.hnumi.obe.base.service.ITeacherService;
import com.hnumi.obe.base.vo.*;
import com.hnumi.obe.common.util.ExcelUtil;
import com.hnumi.obe.common.util.PasswordUtil;
import com.hnumi.obe.common.util.StringUtil;
import com.hnumi.obe.system.entity.BaseUser;
import com.hnumi.obe.system.entity.Role;
import com.hnumi.obe.system.mapper.BaseUserMapper;
import com.hnumi.obe.system.mapper.RoleMapper;
import com.hnumi.obe.system.mapstruct.UserConvert;
import com.hnumi.obe.system.vo.UserVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.DigestUtils;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 教师表 服务实现类
 */
@Service
@Slf4j
public class TeacherServiceImpl extends ServiceImpl<TeacherMapper, Teacher> implements ITeacherService {

    @Autowired
    private TeacherMapper teacherMapper;
    @Autowired
    private AcademyMapper academyMapper;
    @Autowired
    private TeacherRolesMapper teacherRolesMapper;
    @Autowired
    private RoleMapper roleMapper;
    @Autowired
    private BaseUserMapper baseUserMapper;

    @Autowired
    private MajorMapper majorMapper;


    @Transactional(rollbackFor = Exception.class)
    @Override
    public Object importTeacher(InputStream inputStream) {
        try {
            List<TeacherImportDTO> list = ExcelUtil.readAll(inputStream, TeacherImportDTO.class);

            if (list.isEmpty()) {
                return TeacherImportResultVO.failure(0, 1, 1, List.of("导入数据为空"));
            }

            log.info("开始导入教师数据，共{}条记录", list.size());

            // 第一阶段：全量数据验证（不进行任何数据库操作）
            List<String> validationErrors = new ArrayList<>();
            List<TeacherImportDTO> validatedItems = new ArrayList<>();

            for (int i = 0; i < list.size(); i++) {
                TeacherImportDTO item = list.get(i);
                try {
                    // 基础数据验证
                    validateBasicData(item, i + 1);

                    // 格式验证
                    validateDataFormat(item, i + 1);

                    // 检查工号是否已存在
                    if (isNumberExists(item.getNumber())) {
                        validationErrors.add(String.format("第%d行：工号已存在：%s", i + 1, item.getNumber()));
                        continue;
                    }

                    // 检查用户名是否已存在
                    if (isUsernameExists(item.getNumber())) {
                        validationErrors.add(String.format("第%d行：用户名已存在：%s", i + 1, item.getNumber()));
                        continue;
                    }

                    // 检查手机号是否已存在
                    if (StringUtil.isNotBlank(item.getPhone()) && isPhoneExists(item.getPhone())) {
                        validationErrors.add(String.format("第%d行：手机号已存在：%s", i + 1, item.getPhone()));
                        continue;
                    }

                    // 检查邮箱是否已存在
                    if (StringUtil.isNotBlank(item.getEmail()) && isEmailExists(item.getEmail())) {
                        validationErrors.add(String.format("第%d行：邮箱已存在：%s", i + 1, item.getEmail()));
                        continue;
                    }

                    // 根据名称查询学院ID，如果不存在则自动创建
                    Long academyId = getAcademyIdByName(item.getAcademyName());
                    item.setAcademyId(academyId);

                    // 验证通过，加入有效数据列表
                    validatedItems.add(item);

                } catch (Exception e) {
                    validationErrors.add(String.format("第%d行：%s", i + 1, e.getMessage()));
                }
            }

            // 如果有验证错误，直接返回失败结果，不进行任何数据库操作
            if (!validationErrors.isEmpty()) {
                log.warn("数据验证失败，共{}个错误，不执行导入操作", validationErrors.size());
                return TeacherImportResultVO.failure(0, validationErrors.size(), list.size(), validationErrors);
            }

            // 第二阶段：数据库操作（原子性：要么全部成功，要么全部失败）
            log.info("数据验证通过，开始执行数据库操作，共{}条有效记录", validatedItems.size());

            List<BaseUser> baseUserList = new ArrayList<>();
            List<Teacher> teacherList = new ArrayList<>();

            // 准备数据
            for (TeacherImportDTO item : validatedItems) {
                // 准备基本用户信息
                BaseUser baseUser = new BaseUser();
                baseUser.setRealName(item.getTeacherName());
                baseUser.setUsername(item.getNumber().toString()); // 用工号作为用户名
                baseUser.setGender("男".equals(item.getGender()) ? 1 : 2);
                baseUser.setPhone(item.getPhone());
                baseUser.setEmail(item.getEmail());
                // 生成新的盐值和默认密码
                String salt = PasswordUtil.generateSalt();
                String defaultPassword = PasswordUtil.DEFAULT_PASSWORD;
                String hashedPassword = PasswordUtil.hash(defaultPassword, salt);
                baseUser.setSalt(salt);
                baseUser.setPassword(hashedPassword);
                baseUser.setStatus(0);
                baseUserList.add(baseUser);

                // 准备教师信息（暂时不设置userId，批量插入用户后再设置）
                Teacher teacher = new Teacher();
                teacher.setTeacherNumber(item.getNumber());
                teacher.setTitle(item.getTitle());
                teacher.setAcademyId(item.getAcademyId());
                teacher.setStatus(0);
                teacher.setTeacherName(item.getTeacherName());
                teacher.setGender("男".equals(item.getGender()) ? 1 : 2);
                teacher.setPhone(item.getPhone());
                teacherList.add(teacher);
            }

            // 执行数据库操作（在同一个事务中）
            try {
                // 1. 批量插入基本用户信息
                log.info("开始插入基本用户信息，共{}条", baseUserList.size());
                for (BaseUser baseUser : baseUserList) {
                    baseUserMapper.insert(baseUser);
                }
                log.info("基本用户信息插入完成");

                // 2. 设置教师信息的userId并批量插入
                log.info("开始插入教师信息，共{}条", teacherList.size());
                for (int i = 0; i < teacherList.size(); i++) {
                    teacherList.get(i).setUserId(baseUserList.get(i).getId());
                }

                // 使用批量插入提高性能
                boolean teacherInsertResult = saveBatch(teacherList);
                if (!teacherInsertResult) {
                    throw new RuntimeException("教师信息批量插入失败");
                }
                log.info("教师信息插入完成");

                // 3. 导入成功
                int successCount = validatedItems.size();
                log.info("教师数据导入成功，共导入{}条记录", successCount);

                return TeacherImportResultVO.success(successCount, list.size());

            } catch (Exception e) {
                log.error("数据库操作失败，事务将回滚", e);
                // 抛出异常，触发事务回滚
                throw new RuntimeException("数据库操作失败：" + e.getMessage(), e);
            }

        } catch (Exception e) {
            log.error("导入过程发生异常", e);
            // 如果是运行时异常，直接抛出以触发事务回滚
            if (e instanceof RuntimeException) {
                throw e;
            }
            // 其他异常包装为运行时异常
            throw new RuntimeException("导入失败：" + e.getMessage(), e);
        }
    }

    @Override
    public List<TeacherExportVO> exportTeacher(TeacherQueryDTO query) {
        List<TeacherVO> teacherVOList = teacherMapper.selectTeacherListForExport(
                query.getAcademyId(),
                query.getTeacherName(),
                query.getTeacherNumber(),
                query.getTeacherTitle(),
                query.getGender(),
                query.getStatus()
        );

        return teacherVOList.stream().map(teacherVO -> {
            TeacherExportVO exportVO = new TeacherExportVO();
            exportVO.setTeacherName(teacherVO.getName());
            exportVO.setTeacherNumber(teacherVO.getNumber().toString());
            exportVO.setGender(teacherVO.getGender() == 1 ? "男" : "女");
            exportVO.setTeacherTitle(teacherVO.getTitle());
            exportVO.setAcademyName(teacherVO.getAcademyName());
            exportVO.setPhone(teacherVO.getPhone());

            String status = "未知";
            switch (teacherVO.getStatus()) {
                case 0:
                    status = "正常";
                    break;
                case -1:
                    status = "删除";
                    break;
                case 1:
                    status = "禁用";
                    break;
                default:
                    status = "未知";
                    break;
            }
            exportVO.setStatus(status);
            return exportVO;
        }).toList();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Object addTeacher(TeacherDTO dto) {

        // 检查工号是否已存在
        if (isNumberExists(dto.getNumber())) {
            throw new RuntimeException("工号已存在：" + dto.getNumber());
        }

        // 检查手机号是否已存在（如果不为空）
        if (StringUtil.isNotBlank(dto.getUser().getPhone()) && isPhoneExists(dto.getUser().getPhone())) {
            throw new RuntimeException("手机号已存在：" + dto.getUser().getPhone());
        }

        // 检查邮箱是否已存在（如果不为空）
        if (StringUtil.isNotBlank(dto.getUser().getEmail()) && isEmailExists(dto.getUser().getEmail())) {
            throw new RuntimeException("邮箱已存在：" + dto.getUser().getEmail());
        }

        // 创建基本用户信息
        BaseUser baseUser = new BaseUser();
        baseUser.setRealName(dto.getUser().getRealName());
        baseUser.setUsername(dto.getNumber()); // 用工号作为用户名
        baseUser.setGender(dto.getUser().getGender());
        baseUser.setPhone(dto.getUser().getPhone());
        baseUser.setEmail(dto.getUser().getEmail());
        baseUser.setUserType(1);

        // 生成新的盐值和默认密码
        String salt = PasswordUtil.generateSalt();
        String defaultPassword = PasswordUtil.DEFAULT_PASSWORD;
        String hashedPassword = PasswordUtil.hash(defaultPassword, salt);
        baseUser.setSalt(salt);
        baseUser.setPassword(hashedPassword);
        baseUser.setStatus(0);

        // 插入基本用户信息
        baseUserMapper.insert(baseUser);

        // 创建教师信息
        Teacher teacher = new Teacher();
        teacher.setUserId(baseUser.getId());
        teacher.setTeacherNumber(dto.getNumber());
        teacher.setTitle(dto.getTitle());
        teacher.setAcademyId(dto.getAcademyId());
        teacher.setStatus(0);
        teacher.setTeacherName(dto.getTeacherName());
        teacher.setGender(dto.getGender());
        teacher.setPhone(dto.getPhone());
        return save(teacher);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Object updateTeacher(TeacherDTO dto) {
        // 查询现有教师信息
        Teacher existingTeacher = getById(dto.getTeacherId());
        if (existingTeacher == null) {
            throw new RuntimeException("教师不存在");
        }

        // 更新基本用户信息
        BaseUser baseUser = baseUserMapper.selectById(existingTeacher.getUserId());
        if (baseUser != null) {
            baseUser.setRealName(dto.getUser().getRealName());
            baseUser.setGender(dto.getUser().getGender());
            baseUser.setPhone(dto.getUser().getPhone());
            baseUser.setEmail(dto.getUser().getEmail());
            baseUserMapper.updateById(baseUser);
        }

        // 更新教师信息
        Teacher teacher = new Teacher();
        teacher.setTeacherId(dto.getTeacherId());
        teacher.setTeacherNumber(dto.getNumber());
        teacher.setTitle(dto.getTitle());
        teacher.setAcademyId(dto.getAcademyId());

        return updateById(teacher);
    }

    @Override
    public Object deleteTeacherById(Long id) {
        Teacher teacher = new Teacher();
        teacher.setTeacherId(id);
        teacher.setStatus(-1);
        return updateById(teacher);
    }

    @Override
    public Object stopTeacherUsingById(Long id) {
        Teacher teacher = new Teacher();
        teacher.setTeacherId(id);
        teacher.setStatus(1);
        return updateById(teacher);
    }

    @Override
    public List<TeacherTitleOptionsVO> getTeacherTitleOptions() {
        return teacherMapper.getTeacherTitleOptions();
    }

    @Override
    public Page<TeacherVO> pageTeacher(TeacherQueryDTO query) {
        Page<TeacherVO> page = new Page<>(query.getCurrent(), query.getSize());
        return teacherMapper.selectTeacherPageWithDetails(
                page,
                query.getAcademyId(),
                query.getTeacherName(),
                query.getTeacherNumber(),
                query.getTeacherTitle(),
                query.getGender(),
                query.getStatus()
        );
    }

    @Override
    public TeacherVO getDetailById(Long id) {
        return teacherMapper.selectTeacherDetailById(id);
    }

    @Override
    public TeacherVO getByTeacherNumber(String teacherNumber) {
        // 构建查询条件
        LambdaQueryWrapper<Teacher> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Teacher::getTeacherNumber, teacherNumber)
                .eq(Teacher::getStatus, 0);
        // 查询教师信息
        Teacher teacher = teacherMapper.selectOne(wrapper);
        if (teacher == null) {
            return null;
        }

        // 使用详细查询方法获取完整信息
        return teacherMapper.selectTeacherDetailById(teacher.getTeacherId());
    }

    @Override
    public TeacherVO getByUserId(Long userId) {
        // 构建查询条件：通过base_user_id查询教师信息
        LambdaQueryWrapper<Teacher> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Teacher::getUserId, userId)
                .eq(Teacher::getStatus, 0);
        // 查询教师信息
        Teacher teacher = teacherMapper.selectOne(wrapper);
        if (teacher == null) {
            return null;
        }

        // 使用详细查询方法获取完整信息
        return teacherMapper.selectTeacherDetailById(teacher.getTeacherId());
    }

    @Override
    public Teacher getByTeacherId(Long teacherId) {
        LambdaQueryWrapper<Teacher> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Teacher::getUserId, teacherId)
                .eq(Teacher::getStatus, 0);
        // 查询教师信息
        return teacherMapper.selectOne(wrapper);
    }

    @Override
    public List<Role> getRoleByTeacherNumber(String teacherNumber) {
        // 查询教师角色关联信息
        LambdaQueryWrapper<TeacherRoles> rolesWrapper = new LambdaQueryWrapper<>();
        rolesWrapper.eq(TeacherRoles::getTeacherNumber, teacherNumber);
        List<TeacherRoles> teacherRoles = teacherRolesMapper.selectList(rolesWrapper);
        if (teacherRoles.isEmpty()) {
            return List.of();
        }

        // 获取角色列表
        List<Long> roleIds = teacherRoles.stream()
                .map(TeacherRoles::getRoleId)
                .toList();
        return roleMapper.selectBatchIds(roleIds);
    }

    @Override
    public List<EnumVO.Option<String>> getTeacherByAcademyId(Long academyId) {
        return teacherMapper.getTeacherOptionsListByAcademyId(academyId).stream().filter(item -> {
            if (item == null || item.getLabel() == null || item.getValue() == null) {
                log.error("data error!");
                return false;
            }
            return true;
        }).toList();
    }

    @Override
    public List<EnumVO.Option<String>> getTeacherByMajorId(Long majorId){
        return getTeacherByAcademyId(majorMapper.getAcademyIdByMajorId(majorId));
    }

    @Override
    public Boolean enableTeacherById(Long id) {
        Teacher teacher = new Teacher();
        teacher.setTeacherId(id);
        teacher.setStatus(0);
        return updateById(teacher);
    }

    @Override
    public Boolean batchDeleteTeachers(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return false;
        }

        // 批量逻辑删除
        List<Teacher> teachers = ids.stream()
                .map(id -> {
                    Teacher teacher = new Teacher();
                    teacher.setTeacherId(id);
                    teacher.setStatus(-1);
                    return teacher;
                })
                .toList();

        return updateBatchById(teachers);
    }

    @Override
    public Boolean resetPassword(Long id) {
        // 查询教师信息获取用户ID
        Teacher teacher = getById(id);
        if (teacher == null || teacher.getUserId() == null) {
            return false;
        }

        // 重置基本用户密码
        BaseUser baseUser = new BaseUser();
        baseUser.setId(teacher.getUserId());
        String password = DigestUtils.md5DigestAsHex("123456".getBytes());
        baseUser.setPassword(password);

        return baseUserMapper.updateById(baseUser) > 0;
    }

    @Override
    public Object getTeacherStatistics() {
        // 查询教师统计信息
        LambdaQueryWrapper<Teacher> wrapper = new LambdaQueryWrapper<>();
        wrapper.ne(Teacher::getStatus, -1); // 排除已删除的

        // 总教师数
        long totalCount = count(wrapper);

        // 正常状态教师数
        LambdaQueryWrapper<Teacher> normalWrapper = new LambdaQueryWrapper<>();
        normalWrapper.eq(Teacher::getStatus, 0);
        long normalCount = count(normalWrapper);

        // 停用状态教师数
        LambdaQueryWrapper<Teacher> disabledWrapper = new LambdaQueryWrapper<>();
        disabledWrapper.eq(Teacher::getStatus, 1);
        long disabledCount = count(disabledWrapper);

        // 各学院教师分布
        List<Teacher> allTeachers = list(wrapper);
        Map<Long, Long> academyCountMap = allTeachers.stream()
                .filter(teacher -> teacher.getAcademyId() != null) // 过滤null值
                .collect(Collectors.groupingBy(Teacher::getAcademyId, Collectors.counting()));

        // 各职称分布
        Map<String, Long> titleCountMap = allTeachers.stream()
                .filter(teacher -> teacher.getTitle() != null && !teacher.getTitle().isEmpty()) // 过滤null和空值
                .collect(Collectors.groupingBy(Teacher::getTitle, Collectors.counting()));

        // 构建统计结果
        Map<String, Object> statistics = new HashMap<>();
        statistics.put("totalCount", totalCount);
        statistics.put("normalCount", normalCount);
        statistics.put("disabledCount", disabledCount);
        statistics.put("academyDistribution", academyCountMap);
        statistics.put("titleDistribution", titleCountMap);

        return statistics;
    }

    @Override
    public Object getTeacherTree() {
        // 获取所有有效教师
        LambdaQueryWrapper<Teacher> wrapper = new LambdaQueryWrapper<>();
        wrapper.ne(Teacher::getStatus, -1); // 排除已删除的
        wrapper.ne(Teacher::getStatus, -1); // 排除已删除的
        List<Teacher> allTeachers = list(wrapper);
        LambdaQueryWrapper<Academy> academyWrapper = new LambdaQueryWrapper<>();
        academyWrapper.eq(Academy::getStatus, 0);
        // 获取所有学院信息
        List<Map<String, Object>> academies = academyMapper.selectList(academyWrapper).stream()
                .map(academy -> {
                    Map<String, Object> academyMap = new HashMap<>();
                    academyMap.put("id", "college_" + academy.getId());
                    academyMap.put("label", academy.getAcademyName());
                    academyMap.put("type", "college");
                    academyMap.put("academyId", academy.getId());

                    // 统计该学院的教师数量
                    long teacherCount = allTeachers.stream()
                            .filter(teacher -> Objects.equals(teacher.getAcademyId(), academy.getId()))
                            .count();
                    academyMap.put("teacherCount", teacherCount);

                    // 这里可以添加专业信息，暂时留空，因为没有专业表
                    academyMap.put("children", new ArrayList<>());

                    return academyMap;
                })
                .collect(Collectors.toList());

        // 统计总教师数
        long totalTeacherCount = allTeachers.size();

        // 构建根节点
        Map<String, Object> root = new HashMap<>();
        root.put("id", "all");
        root.put("label", "全部教师");
        root.put("type", "school");
        root.put("teacherCount", totalTeacherCount);
        root.put("children", academies);

        return List.of(root);
    }

    @Override
    public List<TeacherVO> getTeacherOptions() {
        return teacherMapper.listTeacherOptions();
    }

    // ========== 私有辅助方法 ==========

    /**
     * 验证基础数据
     */
    private void validateBasicData(TeacherImportDTO item, int rowNum) {
        if (StringUtil.isBlank(item.getTeacherName())) {
            throw new RuntimeException("教师姓名不能为空");
        }
        if (StringUtil.isBlank(item.getNumber())) {
            throw new RuntimeException("工号不能为空");
        }
        if (StringUtil.isBlank(item.getGender())) {
            throw new RuntimeException("性别不能为空");
        }
        if (StringUtil.isBlank(item.getTitle())) {
            throw new RuntimeException("职称不能为空");
        }
        if (StringUtil.isBlank(item.getAcademyName())) {
            throw new RuntimeException("学院名称不能为空");
        }
    }

    /**
     * 验证数据格式
     */
    private void validateDataFormat(TeacherImportDTO item, int rowNum) {
        // 验证工号格式（数字）
        try {
            Long.valueOf(item.getNumber());
        } catch (NumberFormatException e) {
            throw new RuntimeException("工号格式不正确，必须为数字");
        }

        // 验证性别
        if (!"男".equals(item.getGender()) && !"女".equals(item.getGender())) {
            throw new RuntimeException("性别必须为'男'或'女'");
        }

        // 验证手机号格式（如果不为空）
        if (StringUtil.isNotBlank(item.getPhone()) && !item.getPhone().matches("^1[3-9]\\d{9}$")) {
            throw new RuntimeException("手机号格式不正确");
        }

        // 验证邮箱格式（如果不为空）
        if (StringUtil.isNotBlank(item.getEmail()) && !item.getEmail().matches("^[\\w.-]+@[\\w.-]+\\.[a-zA-Z]{2,}$")) {
            throw new RuntimeException("邮箱格式不正确");
        }
    }

    /**
     * 检查工号是否已存在（排除已删除的教师）
     */
    private boolean isNumberExists(String number) {
        LambdaQueryWrapper<Teacher> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Teacher::getTeacherNumber, Integer.valueOf(number));
        wrapper.ne(Teacher::getStatus, -1); // 排除已删除的教师
        return count(wrapper) > 0;
    }

    /**
     * 检查用户名是否已存在（排除已删除的用户）
     */
    private boolean isUsernameExists(String username) {
        LambdaQueryWrapper<BaseUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BaseUser::getUsername, username);
        wrapper.eq(BaseUser::getDeleted, false); // 排除已删除的用户
        return baseUserMapper.selectCount(wrapper) > 0;
    }

    /**
     * 检查手机号是否已存在（排除已删除的用户）
     */
    private boolean isPhoneExists(String phone) {
        LambdaQueryWrapper<BaseUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BaseUser::getPhone, phone);
        wrapper.eq(BaseUser::getDeleted, false); // 排除已删除的用户
        return baseUserMapper.selectCount(wrapper) > 0;
    }

    /**
     * 检查邮箱是否已存在（排除已删除的用户）
     */
    private boolean isEmailExists(String email) {
        LambdaQueryWrapper<BaseUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BaseUser::getEmail, email);
        wrapper.eq(BaseUser::getDeleted, false); // 排除已删除的用户
        return baseUserMapper.selectCount(wrapper) > 0;
    }

    /**
     * 根据学院名称获取学院ID，如果不存在则创建
     */
    private Long getAcademyIdByName(String academyName) {
        LambdaQueryWrapper<Academy> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Academy::getAcademyName, academyName);
        Academy academy = academyMapper.selectOne(wrapper);

        if (academy != null) {
            return academy.getId();
        }

        // 如果学院不存在，创建新学院
        Academy newAcademy = new Academy();
        newAcademy.setAcademyName(academyName);
        newAcademy.setStatus(0);
        academyMapper.insert(newAcademy);

        log.info("自动创建学院：{}", academyName);
        return newAcademy.getId();
    }

    /**
     * 生成学院代码
     */
    private String generateAcademyCode(String academyName) {
        // 简单的代码生成逻辑，可以根据需要调整
        return "AUTO_" + System.currentTimeMillis();
    }

    /**
     * 验证教师基础数据
     */
    private void validateTeacherBasicData(TeacherDTO dto) {
        if (dto.getUser() == null) {
            throw new RuntimeException("用户信息不能为空");
        }
        if (StringUtil.isBlank(dto.getUser().getRealName())) {
            throw new RuntimeException("教师姓名不能为空");
        }
        if (dto.getNumber() == null) {
            throw new RuntimeException("工号不能为空");
        }
        if (dto.getUser().getGender() == null) {
            throw new RuntimeException("性别不能为空");
        }
        if (StringUtil.isBlank(dto.getTitle())) {
            throw new RuntimeException("职称不能为空");
        }
        if (dto.getAcademyId() == null) {
            throw new RuntimeException("学院ID不能为空");
        }
    }

    /**
     * 验证教师数据格式
     */
    private void validateTeacherDataFormat(TeacherDTO dto) {
        // 验证手机号格式（如果不为空）
        if (StringUtil.isNotBlank(dto.getUser().getPhone()) && !dto.getUser().getPhone().matches("^1[3-9]\\d{9}$")) {
            throw new RuntimeException("手机号格式不正确");
        }

        // 验证邮箱格式（如果不为空）
        if (StringUtil.isNotBlank(dto.getUser().getEmail()) && !dto.getUser().getEmail().matches("^[\\w.-]+@[\\w.-]+\\.[a-zA-Z]{2,}$")) {
            throw new RuntimeException("邮箱格式不正确");
        }
    }

//    @Override
//   public  List<TeacherVO> getTeachersByTaskId(Long taskId){
//// 查询教师任务关联信息}
//        LambdaQueryWrapper<TeacherRoles> rolesWrapper = new LambdaQueryWrapper<>();
//        rolesWrapper.eq(TeacherRoles::getTaskId, taskId);
//        List<TeacherRoles> teacherRoles = teacherRolesMapper.selectList(rolesWrapper);
//        if (teacherRoles.isEmpty()) {
//            return List.of();
//        }
//
//        // 获取教师ID列表
//        List<Long> teacherIds = teacherRoles.stream()
//                .map(TeacherRoles::getTeacherId)
//                .distinct()
//                .toList();
//
//        // 批量查询教师信息
//        return this.lambdaQuery()
//                .in(Teacher::getTeacherId, teacherIds)
//                .eq(Teacher::getStatus, 0)  // 只查询正常状态的记录
//                .list()
//                .stream()
//                .map(TeacherConvert.INSTANCE::toVO)
//                .toList();
//    }
    /**
     * 批量获取教师信息
     *
     * @param teacherIds 教师ID列表
     * @return 教师ID到教师信息的映射
     */
    @Override
    public Map<Long, TeacherVO> getTeacherInfoByIds(List<Long> teacherIds) {
        if (teacherIds == null || teacherIds.isEmpty()) {
            return new HashMap<>();
        }

        // 去重
        List<Long> distinctIds = teacherIds.stream()
                .distinct()
                .collect(Collectors.toList());

        // 批量查询教师信息
        List<Teacher> teachers = this.lambdaQuery()
                .in(Teacher::getUserId, distinctIds)
                .eq(Teacher::getStatus, 0)  // 只查询正常状态的记录
                .list();

        if (teachers.isEmpty()) {
            return new HashMap<>();
        }

        // 批量查询用户基本信息
        List<Long> userIds = teachers.stream()
                .map(Teacher::getUserId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        Map<Long, BaseUser> userMap = new HashMap<>();
        if (!userIds.isEmpty()) {
            LambdaQueryWrapper<BaseUser> userWrapper = new LambdaQueryWrapper<>();
            userWrapper.in(BaseUser::getId, userIds);
            userWrapper.eq(BaseUser::getDeleted, false);
            List<BaseUser> users = baseUserMapper.selectList(userWrapper);

            userMap = users.stream()
                    .collect(Collectors.toMap(BaseUser::getId, user -> user, (u1, u2) -> u1));
        }

        // 转换为TeacherVO并构建映射
        Map<Long, TeacherVO> result = new HashMap<>(teachers.size());
        for (Teacher teacher : teachers) {
            TeacherVO vo = TeacherConvert.INSTANCE.toVO(teacher);

            // 设置学院信息
            if (teacher.getAcademyId() != null) {
                TeacherVO.AcademyVO academyVO = new TeacherVO.AcademyVO();
                academyVO.setAcademyId(teacher.getAcademyId());
                // 注: 这里没有获取学院名称，如果需要可以通过academyMapper查询
                vo.setAcademy(academyVO);
            }

            // 设置用户基本信息 - 放入user字段中
            if (teacher.getUserId() != null) {
                BaseUser user = userMap.get(teacher.getUserId());
                if (user != null) {
                    UserVO userVO = UserConvert.INSTANCE.toVO(user);
                    vo.setUser(userVO);
                }
            }

            result.put(teacher.getTeacherId(), vo);
        }

        return result;
    }

    @Override
    public Map<Long, TeacherVO> getTeacherMapByIds(List<Long> teacherIds) {
        log.debug("开始批量查询教师详细信息，教师ID列表: {}", teacherIds);

        // 参数验证
        if (teacherIds == null || teacherIds.isEmpty()) {
            log.debug("教师ID列表为空，返回空Map");
            return new HashMap<>();
        }

        // 去重处理
        List<Long> distinctIds = teacherIds.stream()
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        if (distinctIds.isEmpty()) {
            log.debug("去重后教师ID列表为空，返回空Map");
            return new HashMap<>();
        }

        log.debug("去重后的教师ID列表: {}，数量: {}", distinctIds, distinctIds.size());

        try {
            // 使用 Mapper 的批量查询方法，直接返回 Map 结构
            Map<Long, TeacherVO> teacherMap = teacherMapper.selectTeacherMapByIds(distinctIds);

            log.info("成功批量查询教师信息，请求数量: {}，返回数量: {}", distinctIds.size(), teacherMap.size());

            // 记录未找到的教师ID（用于调试）
            if (teacherMap.size() < distinctIds.size()) {
                List<Long> notFoundIds = distinctIds.stream()
                        .filter(id -> !teacherMap.containsKey(id))
                        .collect(Collectors.toList());
                log.warn("以下教师ID未找到对应记录: {}", notFoundIds);
            }

            return teacherMap;

        } catch (Exception e) {
            log.error("批量查询教师信息失败，教师ID列表: {}", distinctIds, e);
            throw new RuntimeException("批量查询教师信息失败", e);
        }
    }
}

/*
 * TODO: 需要修复以下问题：
 * 1. AcademyServiceImpl中引用的teacherMapper.getNameById()方法不存在
 * 2. AcademyServiceImpl中引用的teacherMapper.getDeanOptions()方法不存在
 * 3. MajorServiceImpl中引用的teacherMapper.getNameById()方法不存在
 * 4. MajorServiceImpl中引用的Teacher.getTeacherNumber()和Teacher.getTeacherName()方法不存在
 * 5. AcademyServiceImpl中引用的teacherMapper.getAcademyLeaderOptions()方法不存在
 *
 * 这些方法需要根据新的架构重新实现，使用关联查询获取用户基本信息。
 */
