package com.hnumi.obe.base.service;

import com.hnumi.obe.base.entity.Standard;
import com.hnumi.obe.base.dto.StandardCreateDTO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hnumi.obe.base.vo.StandardVO;

import java.util.List;

/**
 * 工程教育认证标准库表 服务类
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
public interface IStandardService extends IService<Standard> {

    /**
     * 创建标准版本和对应的指标点
     * 先插入工程认证版本（parent_id=0），再插入指标点（parent_id=版本ID）
     *
     * @param createDTO 创建DTO，包含标准版本信息和指标点列表
     * @return 创建的标准版本ID
     */
    Long createStandardWithIndicators(StandardCreateDTO createDTO);

    /**
     * 删除标准记录
     * parent_id=0的内容（认证版本）：逻辑删除，设置status=-1
     * parent_id!=0的内容（指标点）：物理删除
     *
     * @param id 标准记录ID
     * @return 删除结果
     */
    boolean deleteStandardById(Long id);

    /**
     * 更新标准记录
     *
     * @param updateDTO 标准记录
     * @return 更新结果
     */
    boolean updateStandardRecord(StandardCreateDTO updateDTO);

    /**
     * 根据父ID查询指标点列表
     *
     * @param parentId 父ID（标准版本ID）
     * @return 指标点列表
     */
    List<Standard> getIndicatorsByParentId(Long parentId);

    /**
     * 带指标查询
     *
     * @param id 主键
     * @return 带有指标的值
     */
    StandardVO getStandardWithIndicators(Long id);

    /**
     * 获取指定标准下的所有指标ID列表
     * @param standardId 标准ID
     * @return 指标ID列表（按ID排序）
     */
    List<Long> getIndicatorIdsByStandardId(Long standardId);
}
