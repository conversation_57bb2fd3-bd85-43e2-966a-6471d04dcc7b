package com.hnumi.obe.base.mapstruct;

import com.hnumi.obe.base.vo.TeacherExportVO;
import com.hnumi.obe.common.mapstruct.BaseConvert;
import com.hnumi.obe.base.entity.Teacher;
import com.hnumi.obe.base.vo.TeacherVO;
import com.hnumi.obe.base.dto.TeacherDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

/**
 * 教师表 对象转换器
 * <p>
 * 使用 MapStruct 实现对象之间的转换
 * 主要功能：
 * 1. 实体类与DTO之间的转换
 * 2. 实体类与VO之间的转换
 * 3. 集合对象的批量转换
 * 4. 自定义字段映射规则
 * <p>
 * 使用说明：
 * 1. 通过 INSTANCE 获取转换器实例
 * 2. 调用相应的转换方法进行对象转换
 * 3. 支持自定义字段映射规则
 * 4. 支持集合对象的批量转换
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface TeacherConvert extends BaseConvert<TeacherVO, Teacher> {
    /**
     * 转换器实例
     * 使用方式：TeacherConvert.INSTANCE.toVO(entity)
     */
    TeacherConvert INSTANCE = Mappers.getMapper(TeacherConvert.class);

    Teacher toEntity(TeacherDTO dto);

    TeacherExportVO toExportVO(Teacher entity);

    @Mappings({
            @Mapping(source = "teacherId", target = "id"),
            @Mapping(source = "teacherNumber", target = "number"),
            @Mapping(source = "title", target = "title"),
            @Mapping(source = "status", target = "status"),
            @Mapping(source = "creator", target = "creator"),
            @Mapping(source = "createTime", target = "createTime"),
            @Mapping(source = "modifier", target = "modifier"),
            @Mapping(source = "modifyTime", target = "modifyTime")
    })
    TeacherVO toVO(Teacher entity);
} 