package com.hnumi.obe.base.mapstruct;

import com.hnumi.obe.common.mapstruct.BaseConvert;
import com.hnumi.obe.base.entity.Major;
import com.hnumi.obe.base.vo.MajorVO;
import com.hnumi.obe.base.dto.MajorDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

/**
 * 专业信息表 对象转换器
 * <p>
 * 使用 MapStruct 实现对象之间的转换
 * 主要功能：
 * 1. 实体类与DTO之间的转换
 * 2. 实体类与VO之间的转换
 * 3. 集合对象的批量转换
 * 4. 自定义字段映射规则
 * <p>
 * 使用说明：
 * 1. 通过 INSTANCE 获取转换器实例
 * 2. 调用相应的转换方法进行对象转换
 * 3. 支持自定义字段映射规则
 * 4. 支持集合对象的批量转换
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface MajorConvert extends BaseConvert<MajorVO, Major> {
    /**
     * 转换器实例
     * 使用方式：MajorConvert.INSTANCE.toVO(entity)
     */
    MajorConvert INSTANCE = Mappers.getMapper(MajorConvert.class);

    @Mappings({
            @Mapping(source = "id", target = "majorId"),
            @Mapping(source = "majorName", target = "majorName"),
            @Mapping(source = "majorCode", target = "majorCode"),
            @Mapping(source = "academyId", target = "academyId"),
            @Mapping(source = "academyLeaderId", target = "academyLeaderId"),
            @Mapping(source = "status", target = "status"),
            @Mapping(source = "creator", target = "creator"),
            @Mapping(source = "createTime", target = "createTime"),
            @Mapping(source = "modifier", target = "modifier"),
            @Mapping(source = "modifyTime", target = "modifyTime"),
            @Mapping(source = "professionalOverview", target = "professionalOverview")
    })
    Major toEntity(MajorDTO dto);

    @Mappings({
            @Mapping(source = "majorId", target = "id"),
            @Mapping(source = "academyId", target = "collegeId"),
            @Mapping(source = "academyLeaderId", target = "directorId"),
            @Mapping(source = "modifyTime", target = "updateTime"),
            @Mapping(source = "createTime", target = "createTime"),
            @Mapping(source = "majorName", target = "name"),
            @Mapping(source = "majorCode", target = "code"),
            @Mapping(source = "professionalOverview", target = "professionalOverview"),
            @Mapping(source = "status", target = "status")
    })
    MajorVO toVO(Major entity);
} 