package com.hnumi.obe.base.mapstruct;

import com.hnumi.obe.common.mapstruct.BaseConvert;
import com.hnumi.obe.base.entity.Standard;
import com.hnumi.obe.base.vo.StandardVO;
import com.hnumi.obe.base.dto.StandardDTO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

/**
 * 工程教育认证标准库表 对象转换器
 * 
 * 使用 MapStruct 实现对象之间的转换
 * 主要功能：
 * 1. 实体类与DTO之间的转换
 * 2. 实体类与VO之间的转换
 * 3. 集合对象的批量转换
 * 4. 自定义字段映射规则
 * 
 * 使用说明：
 * 1. 通过 INSTANCE 获取转换器实例
 * 2. 调用相应的转换方法进行对象转换
 * 3. 支持自定义字段映射规则
 * 4. 支持集合对象的批量转换
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface StandardConvert extends BaseConvert<StandardVO, Standard> {
    /**
     * 转换器实例
     * 使用方式：StandardConvert.INSTANCE.toVO(entity)
     */
    StandardConvert INSTANCE = Mappers.getMapper(StandardConvert.class);

    Standard toEntity(StandardDTO dto);
} 