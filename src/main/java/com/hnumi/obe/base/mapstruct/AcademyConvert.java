package com.hnumi.obe.base.mapstruct;

import com.hnumi.obe.common.mapstruct.BaseConvert;
import com.hnumi.obe.base.entity.Academy;
import com.hnumi.obe.base.vo.AcademyVO;
import com.hnumi.obe.base.dto.AcademyDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 院系表 对象转换器
 * 
 * 使用 MapStruct 实现对象之间的转换
 * 主要功能：
 * 1. 实体类与DTO之间的转换
 * 2. 实体类与VO之间的转换
 * 3. 集合对象的批量转换
 * 4. 自定义字段映射规则
 * 
 * 使用说明：
 * 1. 通过 INSTANCE 获取转换器实例
 * 2. 调用相应的转换方法进行对象转换
 * 3. 支持自定义字段映射规则
 * 4. 支持集合对象的批量转换
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface AcademyConvert extends BaseConvert<AcademyVO, Academy> {
    /**
     * 转换器实例
     * 使用方式：AcademyConvert.INSTANCE.toVO(entity)
     */
    AcademyConvert INSTANCE = Mappers.getMapper(AcademyConvert.class);

    Academy toEntity(AcademyDTO dto);

    @Mappings({
            @Mapping(source = "id", target = "id"),
            @Mapping(source = "academyName", target = "name"),
            @Mapping(source = "createTime", target = "createTime"),
            @Mapping(source = "modifier", target = "updater"),
            @Mapping(source = "modifyTime", target = "updateTime")
    })
    AcademyVO toVO(Academy entity);
} 