package com.hnumi.obe.base.mapstruct;

import com.hnumi.obe.common.mapstruct.BaseConvert;
import com.hnumi.obe.base.entity.Student;
import com.hnumi.obe.base.vo.StudentVO;
import com.hnumi.obe.base.dto.StudentDTO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

/**
 * 学生表 对象转换器
 * 
 * 使用 MapStruct 实现对象之间的转换
 * 主要功能：
 * 1. 实体类与DTO之间的转换
 * 2. 实体类与VO之间的转换
 * 3. 集合对象的批量转换
 * 4. 自定义字段映射规则
 * 
 * 使用说明：
 * 1. 通过 INSTANCE 获取转换器实例
 * 2. 调用相应的转换方法进行对象转换
 * 3. 支持自定义字段映射规则
 * 4. 支持集合对象的批量转换
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface StudentConvert extends BaseConvert<StudentVO, Student> {
    /**
     * 转换器实例
     * 使用方式：StudentConvert.INSTANCE.toVO(entity)
     */
    StudentConvert INSTANCE = Mappers.getMapper(StudentConvert.class);

    Student toEntity(StudentDTO dto);
} 