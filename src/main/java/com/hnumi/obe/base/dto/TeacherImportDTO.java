package com.hnumi.obe.base.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.hnumi.obe.common.annotation.ExcelColumn;
import lombok.Data;

import java.io.Serializable;

/**
 * 教师导入数据传输对象
 * 用于Excel导入教师信息
 */
@Data
public class TeacherImportDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 工号
     */
    @ExcelColumn("工号")
    private String number;

    /**
     * 教师姓名
     */
    @ExcelColumn("姓名")
    private String teacherName;

    /**
     * 性别
     */
    @ExcelColumn("性别")
    private String gender;

    /**
     * 手机号
     */
    @ExcelColumn("手机号")
    private String phone;

    /**
     * 邮箱
     */
    @ExcelColumn("邮箱")
    private String email;

    /**
     * 职称
     */
    @ExcelColumn("职称")
    private String title;

    /**
     * 学院名称
     */
    @ExcelColumn("学院名称")
    private String academyName;

    // 以下字段用于存储查询到的ID，不在Excel中
    private Long academyId;
}
