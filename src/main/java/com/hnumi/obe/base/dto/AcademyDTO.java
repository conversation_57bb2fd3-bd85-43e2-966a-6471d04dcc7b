package com.hnumi.obe.base.dto;

import com.hnumi.obe.common.valid.ValidGroup;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.apache.commons.math3.analysis.function.Add;
import org.apache.ibatis.annotations.Update;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 院系表 数据传输对象（DTO）
 * <p>
 * DTO（Data Transfer Object）用于服务层之间的数据传输
 * 主要用于：
 * 1. 封装请求参数
 * 2. 在不同服务层之间传递数据
 * 3. 隐藏实体类的敏感字段
 * 4. 优化数据传输结构
 */
@Data
public class AcademyDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 学院id
     * 字段类型：Long
     * 字段名称：id
     * 数据库字段：id
     */
    @NotNull(message = "学院id不能为空", groups = {ValidGroup.Update.class})
    private Long id;
    /**
     * 学院名称
     * 字段类型：String
     * 字段名称：academyName
     * 数据库字段：academy_name
     */
    @NotBlank(message = "学院名称不能为空", groups = {ValidGroup.Update.class, ValidGroup.Add.class})
    private String academyName;
    /**
     * 院系负责人id
     * 字段类型：Long
     * 字段名称：academyPresidentId
     * 数据库字段：academy_president_id
     */
    private Long academyPresidentId;
    /**
     * 记录状态{0:正常状态；-1:删除状态；1:停用状态；}
     * 字段类型：Integer
     * 字段名称：status
     * 数据库字段：status
     */
    @NotNull(message = "status不能为空", groups = {ValidGroup.Add.class})
    private Integer status;
} 