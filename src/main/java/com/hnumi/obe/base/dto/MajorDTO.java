package com.hnumi.obe.base.dto;

import com.hnumi.obe.common.valid.ValidGroup;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 专业信息表 数据传输对象（DTO）
 * <p>
 * DTO（Data Transfer Object）用于服务层之间的数据传输
 * 主要用于：
 * 1. 封装请求参数
 * 2. 在不同服务层之间传递数据
 * 3. 隐藏实体类的敏感字段
 * 4. 优化数据传输结构
 */
@Data
public class MajorDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 专业id
     * 字段类型：Long
     * 字段名称：majorId
     * 数据库字段：major_id
     */
    @NotNull(message = "majorId不能为空", groups = {ValidGroup.Update.class})
    private Long id;
    /**
     * 专业名称
     * 字段类型：String
     * 字段名称：majorName
     * 数据库字段：major_name
     */
    @NotBlank(message = "majorName不能为空", groups = {ValidGroup.Add.class, ValidGroup.Update.class})
    private String majorName;
    /**
     * 专业概述
     * 字段类型：String
     * 字段名称：professionalOverview
     * 数据库字段：professional_overview
     */
    private String professionalOverview;
    /**
     * 专业代码
     * 字段类型：String
     * 字段名称：majorCode
     * 数据库字段：major_code
     */
    @NotBlank(message = "majorCode不能为空", groups = {ValidGroup.Add.class, ValidGroup.Update.class})
    private String majorCode;
    /**
     * 学院id
     * 字段类型：Long
     * 字段名称：academyId
     * 数据库字段：academy_id
     */
    @NotNull(message = "academyId不能为空", groups = {ValidGroup.Add.class, ValidGroup.Update.class})
    private Long academyId;
    /**
     * 专业负责人，教师id
     * 字段类型：Long
     * 字段名称：academyLeaderId
     * 数据库字段：academy_leader_id
     */
    @NotNull(message = "academyLeaderId不能为空", groups = {ValidGroup.Add.class, ValidGroup.Update.class})
    private Long academyLeaderId;


    @NotNull(message = "学科类型不能为空", groups = {ValidGroup.Add.class, ValidGroup.Update.class})
    private String discipline;
    /**
     * 记录状态{0:正常状态；-1:删除状态；1:停用状态；}
     * 字段类型：Integer
     * 字段名称：status
     * 数据库字段：status
     */
    private Integer status;
    /**
     * 记录创建人
     * 字段类型：Long
     * 字段名称：creator
     * 数据库字段：creator
     */
    private Long creator;
    /**
     * 记录创建时间
     * 字段类型：LocalDateTime
     * 字段名称：createTime
     * 数据库字段：create_time
     */
    private LocalDateTime createTime;
    /**
     * 记录最后修改人
     * 字段类型：Long
     * 字段名称：modifier
     * 数据库字段：modifier
     */
    private Long modifier;
    /**
     * 记录最后修改时间
     * 字段类型：LocalDateTime
     * 字段名称：modifyTime
     * 数据库字段：modify_time
     */
    private LocalDateTime modifyTime;
} 