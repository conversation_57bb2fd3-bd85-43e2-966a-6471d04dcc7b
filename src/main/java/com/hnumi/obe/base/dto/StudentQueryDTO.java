package com.hnumi.obe.base.dto;

import com.hnumi.obe.base.entity.Student;
import com.hnumi.obe.common.entity.BasePage;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 学生表 数据查询参数对象（DTO）
 * 
 * QueryDTO（Data Transfer Object）用于前端向后端查询数据封装
 * 主要用于：
 * 1. 封装请求参数
 * 2. 在不同服务层之间传递数据
 * 3. 隐藏实体类的敏感字段
 * 4. 优化数据传输结构
 */
@Data
public class StudentQueryDTO extends BasePage<Student> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 学生id
     * 字段类型：Long
     * 字段名称：studentId
     * 数据库字段：student_id
     */
    private Long studentId;
    /**
     * 学生姓名
     * 字段类型：String
     * 字段名称：studentName
     * 数据库字段：student_name
     */
    private String studentName;
    /**
     * 学号
     * 字段类型：String
     * 字段名称：studentNumber
     * 数据库字段：student_number
     */
    private String studentNumber;
    /**
     * 学生性别，0表示女，1表示男
     * 字段类型：Integer
     * 字段名称：gender
     * 数据库字段：gender
     */
    private Integer gender;

    /**
     * 班级id
     * 字段类型：Long
     * 字段名称：classId
     * 数据库字段：class_id
     */
    private Long classId;
    /**
     * 专业id
     * 字段类型：Long
     * 字段名称：majorId
     * 数据库字段：major_id
     */
    private Long majorId;
    /**
     * 学院id
     * 字段类型：Long
     * 字段名称：academyId
     * 数据库字段：academy_id
     */
    private Long academyId;
    /**
     * 入学年份
     * 字段类型：String
     * 字段名称：entranceYear
     * 数据库字段：entrance_year
     */
    private String entranceYear;
    /**
     * 学籍状态，0在读，1休学，2退学，3毕业
     * 字段类型：Integer
     * 字段名称：studentStatus
     * 数据库字段：student_status
     */
    private Integer studentStatus;


    /**
     * 记录状态{0:正常状态；-1:删除状态；1:停用状态；}
     * 字段类型：Integer
     * 字段名称：status
     * 数据库字段：status
     */
    private Integer status = 0;
    
    /**
     * 手机号（用于搜索）
     * 字段类型：String
     * 字段名称：phone
     * 数据库字段：phone（来自sys_base_user表）
     */
    private String phone;
    
    /**
     * 邮箱（用于搜索）
     * 字段类型：String
     * 字段名称：email
     * 数据库字段：email（来自sys_base_user表）
     */
    private String email;
} 