package com.hnumi.obe.base.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.util.List;

/**
 * 工程教育认证标准库创建DTO
 * 用于新增标准版本和对应的指标点
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class StandardCreateDTO {

    private Long id;

    /**
     * 标准版本号
     */
    private Long standardVersion;

    /**
     * 标准名称
     */
    private String standardName;

    /**
     * 标准描述
     */
    private String standardDescription;

    /**
     * 记录状态{0:正常状态；-1:删除状态；1:停用状态；}
     */
    private Integer status;

    /**
     * 科
     */
    private String disciplineType;

    /**
     * 发布日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate releaseDate;

    /**
     * 指标点列表
     */
    private List<StandardCreateDTO> requirements;
}