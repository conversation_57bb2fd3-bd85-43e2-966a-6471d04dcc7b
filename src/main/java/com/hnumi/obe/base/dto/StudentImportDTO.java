package com.hnumi.obe.base.dto;

import com.hnumi.obe.common.annotation.ExcelColumn;
import lombok.Data;

import java.io.Serializable;

/**
 * 学生批量导入DTO
 */
@Data
public class StudentImportDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 学号
     */
    @ExcelColumn("学号")
    private String number;
    
    /**
     * 学生姓名
     */
    @ExcelColumn("姓名")
    private String studentName;
    
    /**
     * 性别
     */
    @ExcelColumn("性别")
    private String gender;
    
    /**
     * 手机号
     */
    @ExcelColumn("手机号")
    private String phone;
    
    /**
     * 邮箱
     */
    @ExcelColumn("邮箱")
    private String email;
    
    /**
     * 学院名称
     */
    @ExcelColumn("学院名称")
    private String academyName;
    
    /**
     * 专业名称
     */
    @ExcelColumn("专业名称")
    private String majorName;
    
    /**
     * 班级名称
     */
    @ExcelColumn("班级名称")
    private String className;
    
    /**
     * 入学年份
     */
    @ExcelColumn("入学年份")
    private String entranceYear;
    
    /**
     * 学籍状态
     */
    @ExcelColumn("学籍状态")
    private String studentStatus;
    
    // 以下字段用于存储查询到的ID，不从Excel读取
    private Long academyId;
    private Long majorId;
    private Long classId;
}
