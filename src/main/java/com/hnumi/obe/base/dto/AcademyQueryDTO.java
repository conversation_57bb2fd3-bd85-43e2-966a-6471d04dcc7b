package com.hnumi.obe.base.dto;

import com.hnumi.obe.common.entity.BasePage;
import com.hnumi.obe.base.entity.Academy;
import lombok.Data;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 院系表 数据查询参数对象（DTO）
 * 
 * QueryDTO（Data Transfer Object）用于前端向后端查询数据封装
 * 主要用于：
 * 1. 封装请求参数
 * 2. 在不同服务层之间传递数据
 * 3. 隐藏实体类的敏感字段
 * 4. 优化数据传输结构
 */
@Data
public class AcademyQueryDTO extends BasePage<Academy> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 学院名称
     * 字段类型：String
     * 字段名称：academyName
     * 数据库字段：academy_name
     */
    private String academyName;
    /**
     * 院系负责人id
     * 字段类型：Long
     * 字段名称：academyPresidentId
     * 数据库字段：academy_president_id
     */
    private Long academyPresidentId;
    /**
     * 记录状态{0:正常状态；-1:删除状态；1:停用状态；}
     * 字段类型：Integer
     * 字段名称：status
     * 数据库字段：status
     */
    private Integer status;
} 