package com.hnumi.obe.base.dto;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

/**
 * 学院院长 数据传输对象（DTO）
 * 
 * 用于学院院长相关的数据传输
 */
@Data
public class AcademyDeanDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 学院ID
     */
    @NotNull(message = "学院ID不能为空")
    private Long academyId;

    /**
     * 院长用户ID
     */
    @NotNull(message = "院长ID不能为空")
    private Long deanUserId;
} 