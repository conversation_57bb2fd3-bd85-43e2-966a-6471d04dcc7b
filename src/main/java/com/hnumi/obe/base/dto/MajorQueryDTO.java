package com.hnumi.obe.base.dto;

import com.hnumi.obe.common.entity.BasePage;
import com.hnumi.obe.base.entity.Major;
import lombok.Data;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 专业信息表 数据查询参数对象（DTO）
 * 
 * QueryDTO（Data Transfer Object）用于前端向后端查询数据封装
 * 主要用于：
 * 1. 封装请求参数
 * 2. 在不同服务层之间传递数据
 * 3. 隐藏实体类的敏感字段
 * 4. 优化数据传输结构
 */
@Data
public class MajorQueryDTO extends BasePage<Major> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 专业id
     * 字段类型：Long
     * 字段名称：majorId
     * 数据库字段：major_id
     */
    private Long majorId;
    /**
     * 专业名称
     * 字段类型：String
     * 字段名称：majorName
     * 数据库字段：major_name
     */
    private String majorName;
    /**
     * 专业概述
     * 字段类型：String
     * 字段名称：professionalOverview
     * 数据库字段：professional_overview
     */
    private String professionalOverview;
    /**
     * 专业代码
     * 字段类型：String
     * 字段名称：majorCode
     * 数据库字段：major_code
     */
    private String majorCode;
    /**
     * 学院id
     * 字段类型：Long
     * 字段名称：academyId
     * 数据库字段：academy_id
     */
    private Long academyId;
    /**
     * 专业负责人，教师id
     * 字段类型：Long
     * 字段名称：academyLeaderId
     * 数据库字段：academy_leader_id
     */
    private Long academyLeaderId;
    /**
     * 记录状态{0:正常状态；-1:删除状态；1:停用状态；}
     * 字段类型：Integer
     * 字段名称：status
     * 数据库字段：status
     */
    private Integer status;
    /**
     * 记录创建人
     * 字段类型：Long
     * 字段名称：creator
     * 数据库字段：creator
     */
    private Long creator;
    /**
     * 记录创建时间
     * 字段类型：LocalDateTime
     * 字段名称：createTime
     * 数据库字段：create_time
     */
    private LocalDateTime createTime;
    /**
     * 记录最后修改人
     * 字段类型：Long
     * 字段名称：modifier
     * 数据库字段：modifier
     */
    private Long modifier;
    /**
     * 记录最后修改时间
     * 字段类型：LocalDateTime
     * 字段名称：modifyTime
     * 数据库字段：modify_time
     */
    private LocalDateTime modifyTime;
    
    /**
     * 创建时间开始
     */
    private LocalDateTime createTimeStart;
    
    /**
     * 创建时间结束
     */
    private LocalDateTime createTimeEnd;
    
    /**
     * 每页显示数量
     */
    private int pageSize;
    /**
     * 当前页
     */
    private int current;
} 