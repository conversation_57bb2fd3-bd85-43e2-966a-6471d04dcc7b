package com.hnumi.obe.base.dto;

import com.hnumi.obe.common.valid.ValidGroup;
import com.hnumi.obe.system.dto.UserDTO;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 教师表 数据传输对象（DTO）
 * <p>
 * DTO（Data Transfer Object）用于服务层之间的数据传输
 * 主要用于：
 * 1. 封装请求参数
 * 2. 在不同服务层之间传递数据
 * 3. 隐藏实体类的敏感字段
 * 4. 优化数据传输结构
 */
@Data
public class TeacherDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 教师id
     */
    @NotNull(message = "教师id不能为空", groups = {ValidGroup.Update.class})
    private Long teacherId;
    
    /**
     * 工号
     */
    @NotBlank(message = "工号不能为空", groups = {ValidGroup.Add.class, ValidGroup.Update.class})
    private String number;
    
    /**
     * 用户基本信息
     */
    @Valid
    @NotNull(message = "用户基本信息不能为空", groups = {ValidGroup.Add.class, ValidGroup.Update.class})
    private UserDTO user;
    
    /**
     * 职称
     */
    @NotBlank(message = "职称不能为空", groups = {ValidGroup.Add.class, ValidGroup.Update.class})
    private String title;
    
    /**
     * 学院id
     */
    @NotNull(message = "学院id不能为空", groups = {ValidGroup.Add.class, ValidGroup.Update.class})
    private Long academyId;
    
    /**
     * 记录状态{0:正常状态；-1:删除状态；1:停用状态；}
     */
    private Integer status;
    
    /**
     * 记录创建者
     */
    private Long creator;
    
    /**
     * 记录创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 记录最后修改者
     */
    private Long modifier;
    
    /**
     * 记录最后修改时间
     */
    private LocalDateTime modifyTime;
    
    // 为了兼容现有代码，提供一些便捷的getter方法
    
    /**
     * 获取教师姓名
     */
    public String getTeacherName() {
        return user != null ? user.getRealName() : null;
    }
    
    /**
     * 设置教师姓名
     */
    public void setTeacherName(String teacherName) {
        if (user == null) {
            user = new UserDTO();
        }
        user.setRealName(teacherName);
    }
    
    /**
     * 获取性别
     */
    public Integer getGender() {
        return user != null ? user.getGender() : null;
    }
    
    /**
     * 设置性别
     */
    public void setGender(Integer gender) {
        if (user == null) {
            user = new UserDTO();
        }
        user.setGender(gender);
    }
    
    /**
     * 获取手机号
     */
    public String getPhone() {
        return user != null ? user.getPhone() : null;
    }
    
    /**
     * 设置手机号
     */
    public void setPhone(String phone) {
        if (user == null) {
            user = new UserDTO();
        }
        user.setPhone(phone);
    }
    
    /**
     * 获取邮箱
     */
    public String getEmail() {
        return user != null ? user.getEmail() : null;
    }
    
    /**
     * 设置邮箱
     */
    public void setEmail(String email) {
        if (user == null) {
            user = new UserDTO();
        }
        user.setEmail(email);
    }
    
    /**
     * 获取用户名
     */
    public String getUsername() {
        return user != null ? user.getUsername() : null;
    }
    
    /**
     * 设置用户名
     */
    public void setUsername(String username) {
        if (user == null) {
            user = new UserDTO();
        }
        user.setUsername(username);
    }
} 