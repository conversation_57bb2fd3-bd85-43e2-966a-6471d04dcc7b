package com.hnumi.obe.base.dto;

import com.hnumi.obe.common.entity.BasePage;
import com.hnumi.obe.base.entity.Teacher;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 教师表 数据查询参数对象（DTO）
 * <p>
 * QueryDTO（Data Transfer Object）用于前端向后端查询数据封装
 * 主要用于：
 * 1. 封装请求参数
 * 2. 在不同服务层之间传递数据
 * 3. 隐藏实体类的敏感字段
 * 4. 优化数据传输结构
 */
@Data
public class TeacherQueryDTO extends BasePage<Teacher> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 教师id
     * 字段类型：Long
     * 字段名称：teacherId
     * 数据库字段：teacher_id
     */
    private Long teacherId;
    /**
     * 教师姓名
     * 字段类型：String
     * 字段名称：teacherName
     * 数据库字段：teacher_name
     */
    private String teacherName;
    /**
     * 工号
     * 字段类型：String
     * 字段名称：teacherNumber
     * 数据库字段：teacher_number
     */
    private String teacherNumber;
    /**
     * 密码
     * 字段类型：String
     * 字段名称：password
     * 数据库字段：password
     */
    private String password;
    /**
     * 性别，0表示男，1表示女
     * 字段类型：Integer
     * 字段名称：gender
     * 数据库字段：gender
     */
    private Integer gender;
    /**
     * 职称
     * 字段类型：String
     * 字段名称：teacherTitle
     * 数据库字段：teacher_title
     */
    private String teacherTitle;
    /**
     * 学院id
     * 字段类型：Long
     * 字段名称：academyId
     * 数据库字段：academy_id
     */
    private Long academyId;
    /**
     * 联系方式
     * 字段类型：String
     * 字段名称：phone
     * 数据库字段：phone
     */
    private String phone;
    /**
     * 角色id
     * 字段类型：Long
     * 字段名称：roleId
     * 数据库字段：role_id
     */
    private Long roleId;
    /**
     * 记录状态{0:正常状态；-1:删除状态；1:停用状态；}
     * 字段类型：Integer
     * 字段名称：status
     * 数据库字段：status
     */
    private Integer status;
    /**
     * 记录创建者
     * 字段类型：Long
     * 字段名称：creator
     * 数据库字段：creator
     */
    private Long creator;
    /**
     * 记录
     * 字段类型：LocalDateTime
     * 字段名称：createTime
     * 数据库字段：create_time
     */
    private LocalDateTime createTime;
    /**
     * 记录最后修改者
     * 字段类型：Long
     * 字段名称：modifier
     * 数据库字段：modifier
     */
    private Long modifier;
    /**
     * 记录最后修改时间
     * 字段类型：LocalDateTime
     * 字段名称：modifyTime
     * 数据库字段：modify_time
     */
    private LocalDateTime modifyTime;
} 