package com.hnumi.obe.base.dto;

import com.hnumi.obe.common.valid.ValidGroup;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 班级表 数据传输对象（DTO）
 * 
 * DTO（Data Transfer Object）用于服务层之间的数据传输
 * 主要用于：
 * 1. 封装请求参数
 * 2. 在不同服务层之间传递数据
 * 3. 隐藏实体类的敏感字段
 * 4. 优化数据传输结构
 */
@Data
public class ClassesDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 班级ID
     * 字段类型：Long
     * 字段名称：classId
     * 数据库字段：class_id
     */
    @NotNull(message = "班级ID不能为空", groups = {ValidGroup.Update.class})
    private Long classId;
    /**
     * 专业ID
     * 字段类型：Long
     * 字段名称：majorId
     * 数据库字段：major_id
     */
    @NotNull(message = "专业ID不能为空", groups = {ValidGroup.Add.class,  ValidGroup.Update.class})
    private Long majorId;
    /**
     * 班级名称
     * 字段类型：String
     * 字段名称：className
     * 数据库字段：class_name
     */
    private String className;
    /**
     * 入学年份
     * 字段类型：LocalDate
     * 字段名称：entranceYear
     * 数据库字段：entrance_year
     */
    private String entranceYear;
    /**
     * 班主任ID
     * 字段类型：Long
     * 字段名称：headteacherId
     * 数据库字段：headteacher_id
     */
    private Long headteacherId;
    /**
     * 学生人数
     * 字段类型：Integer
     * 字段名称：studentNumber
     * 数据库字段：student_number
     */
    private Integer studentNumber;
    /**
     * 班级状态 -1:毕业 0:在读
     * 字段类型：Integer
     * 字段名称：classStatus
     * 数据库字段：class_status
     */
    private Integer classStatus;
    /**
     * 记录状态{0:正常状态；-1:删除状态；1:停用状态；}
     * 字段类型：Integer
     * 字段名称：status
     * 数据库字段：status
     */
    private Integer status;
    /**
     * 记录创建人
     * 字段类型：Long
     * 字段名称：creator
     * 数据库字段：creator
     */
    private Long creator;
    /**
     * 记录创建时间
     * 字段类型：LocalDateTime
     * 字段名称：createTime
     * 数据库字段：create_time
     */
    private LocalDateTime createTime;
    /**
     * 记录最后修改人
     * 字段类型：Long
     * 字段名称：modifier
     * 数据库字段：modifier
     */
    private Long modifier;
    /**
     * 记录最后修改时间
     * 字段类型：LocalDateTime
     * 字段名称：modifyTime
     * 数据库字段：modify_time
     */
    private LocalDateTime modifyTime;
} 