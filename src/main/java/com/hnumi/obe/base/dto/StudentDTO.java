package com.hnumi.obe.base.dto;

import com.hnumi.obe.common.valid.ValidGroup;
import com.hnumi.obe.system.dto.UserDTO;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 学生表 数据传输对象（DTO）
 * 
 * DTO（Data Transfer Object）用于服务层之间的数据传输
 * 主要用于：
 * 1. 封装请求参数
 * 2. 在不同服务层之间传递数据
 * 3. 隐藏实体类的敏感字段
 * 4. 优化数据传输结构
 */
@Data
public class StudentDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 学生id
     */
    @NotNull(message = "学生id不能为空",  groups = {ValidGroup.Update.class})
    private Long studentId;
    
    /**
     * 学号
     */
    @NotBlank(message = "学号不能为空",  groups = {ValidGroup.Add.class, ValidGroup.Update.class})
    private String number;
    
    /**
     * 用户基本信息
     */
    @Valid
    @NotNull(message = "用户基本信息不能为空", groups = {ValidGroup.Add.class, ValidGroup.Update.class})
    private UserDTO user;
    
    /**
     * 班级id
     */
    @NotNull(message = "班级id不能为空",  groups = {ValidGroup.Add.class, ValidGroup.Update.class})
    private Long classId;
    
    /**
     * 专业id
     */
    @NotNull(message = "专业id不能为空",  groups = {ValidGroup.Add.class, ValidGroup.Update.class})
    private Long majorId;
    
    /**
     * 学院id
     */
    @NotNull(message = "学院id不能为空",  groups = {ValidGroup.Add.class, ValidGroup.Update.class})
    private Long academyId;
    
    /**
     * 入学年份
     */
    @NotBlank(message = "入学年份不能为空",  groups = {ValidGroup.Add.class, ValidGroup.Update.class})
    private String entranceYear;
    
    /**
     * 学籍状态，-1表示毕业，0表示在读
     */
    @NotNull(message = "学籍状态不能为空",  groups = {ValidGroup.Add.class, ValidGroup.Update.class})
    private Integer studentStatus;
    
    /**
     * 角色id
     */
    private Long roleId;
    
    /**
     * 记录状态{0:正常状态；-1:删除状态；1:停用状态；}
     */
    private Integer status;
    
    /**
     * 记录创建者
     */
    private Long creator;
    
    /**
     * 记录创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 记录最后修改者
     */
    private Long modifier;
    
    /**
     * 记录最后修改时间
     */
    private LocalDateTime modifyTime;
    
    // 为了兼容现有代码，提供一些便捷的getter方法
    
    /**
     * 获取学生姓名
     */
    public String getStudentName() {
        return user != null ? user.getRealName() : null;
    }
    
    /**
     * 设置学生姓名
     */
    public void setStudentName(String studentName) {
        if (user == null) {
            user = new UserDTO();
        }
        user.setRealName(studentName);
    }
    
    /**
     * 获取性别
     */
    public Integer getGender() {
        return user != null ? user.getGender() : null;
    }
    
    /**
     * 设置性别
     */
    public void setGender(Integer gender) {
        if (user == null) {
            user = new UserDTO();
        }
        user.setGender(gender);
    }
    
    /**
     * 获取手机号
     */
    public String getPhone() {
        return user != null ? user.getPhone() : null;
    }
    
    /**
     * 设置手机号
     */
    public void setPhone(String phone) {
        if (user == null) {
            user = new UserDTO();
        }
        user.setPhone(phone);
    }
    
    /**
     * 获取邮箱
     */
    public String getEmail() {
        return user != null ? user.getEmail() : null;
    }
    
    /**
     * 设置邮箱
     */
    public void setEmail(String email) {
        if (user == null) {
            user = new UserDTO();
        }
        user.setEmail(email);
    }
    
    /**
     * 获取用户名
     */
    public String getUsername() {
        return user != null ? user.getUsername() : null;
    }
    
    /**
     * 设置用户名
     */
    public void setUsername(String username) {
        if (user == null) {
            user = new UserDTO();
        }
        user.setUsername(username);
    }
} 