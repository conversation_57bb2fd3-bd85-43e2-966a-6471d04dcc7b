package com.hnumi.obe.base.dto;

import lombok.Data;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 工程教育认证标准库表 数据传输对象（DTO）
 * 
 * DTO（Data Transfer Object）用于服务层之间的数据传输
 * 主要用于：
 * 1. 封装请求参数
 * 2. 在不同服务层之间传递数据
 * 3. 隐藏实体类的敏感字段
 * 4. 优化数据传输结构
 */
@Data
public class StandardDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 工程教育认证标准库ID
     * 字段类型：Integer
     * 字段名称：id
     * 数据库字段：id
     */
    private Integer id;
    /**
     * 专业ID
     * 字段类型：Long
     * 字段名称：standardVersion
     * 数据库字段：standard_version
     */
    private Long standardVersion;
    /**
     * 班级名称
     * 字段类型：String
     * 字段名称：standardName
     * 数据库字段：standard_name
     */
    private String standardName;
    /**
     * 工程认证指标点描述
     * 字段类型：String
     * 字段名称：standardDescription
     * 数据库字段：standard_description
     */
    private String standardDescription;
    /**
     * 指标点或者工程认证版本，parent_id=0是工程认证版本，非0是对应的认证版本的指标
     * 字段类型：Integer
     * 字段名称：parentId
     * 数据库字段：parent_id
     */
    private Integer parentId;
    /**
     * 记录状态{0:正常状态；-1:删除状态；1:停用状态；}
     * 字段类型：Integer
     * 字段名称：status
     * 数据库字段：status
     */
    private Integer status;
    /**
     * 记录创建人
     * 字段类型：Long
     * 字段名称：creator
     * 数据库字段：creator
     */
    private Long creator;
    /**
     * 记录创建时间
     * 字段类型：LocalDateTime
     * 字段名称：createTime
     * 数据库字段：create_time
     */
    private LocalDateTime createTime;
    /**
     * 记录最后修改人
     * 字段类型：Long
     * 字段名称：modifier
     * 数据库字段：modifier
     */
    private Long modifier;
    /**
     * 记录最后修改时间
     * 字段类型：LocalDateTime
     * 字段名称：modifyTime
     * 数据库字段：modify_time
     */
    private LocalDateTime modifyTime;
} 