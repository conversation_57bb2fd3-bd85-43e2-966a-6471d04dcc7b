package com.hnumi.obe.base.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDate;

/**
* 班级表
*
*/
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("base_classes")
public class Classes extends BaseEntity {

    /**
     * 班级ID
     */
    @TableId(value = "class_id", type = IdType.AUTO)
    private Long classId;

    /**
     * 专业ID
     */
    @TableField("major_id")
    private Long majorId;

    /**
     * 班级名称
     */
    @TableField("class_name")
    private String className;

    /**
     * 入学年份
     */
    @TableField("entrance_year")
    private String entranceYear;

    /**
     * 班主任ID
     */
    @TableField("headteacher_id")
    private Long headteacherId;

    /**
     * 学生人数
     */
    @TableField("student_number")
    private Integer studentNumber;

    /**
     * 班级状态 -1:毕业 0:在读
     */
    @TableField("class_status")
    private Integer classStatus;

    /**
     * 记录状态{0:正常状态；-1:删除状态；1:停用状态；}
     */
    @TableField("status")
    private Integer status;

}