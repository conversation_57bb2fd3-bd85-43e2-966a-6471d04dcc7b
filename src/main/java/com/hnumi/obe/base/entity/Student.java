package com.hnumi.obe.base.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
* 学生表
*
*/
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("base_student")
public class Student extends BaseEntity {

    /**
     * 学生id
     */
    @TableId(value = "student_id", type = IdType.AUTO)
    private Long studentId;

    /**
     * 用户基本信息ID
     */
    @TableField("base_user_id")
    private Long userId;

    /**
     * 学号
     */
    @TableField("student_number")
    private String studentNumber;
    @TableField("student_name")
    private String studentName;
    @TableField("gender")
    private Integer gender;

    /**
     * 班级id
     */
    @TableField("class_id")
    private Long classId;

    /**
     * 专业id
     */
    @TableField("major_id")
    private Long majorId;

    /**
     * 学院id
     */
    @TableField("academy_id")
    private Long academyId;

    /**
     * 入学年份
     */
    @TableField("entrance_year")
    private String entranceYear;

    /**
     * 学籍状态，-1表示毕业，0表示在读
     */
    @TableField("student_status")
    private Integer studentStatus;

    /**
     * 记录状态{0:正常状态；-1:删除状态；}
     */
    @TableField("status")
    private Integer status;

}