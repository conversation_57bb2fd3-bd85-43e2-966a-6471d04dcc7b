package com.hnumi.obe.base.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
* 教师表
* 本表存储教师的详细信息【基本信息表存在的数据在本表不再存储】
*/
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("base_teacher")
public class Teacher extends BaseEntity {

    /**
     * 教师id
     */
    @TableId(value = "teacher_id", type = IdType.AUTO)
    private Long teacherId;

    /**
     * 用户基本信息ID
     */
    @TableField("base_user_id")
    private Long userId;

    /**
     * 教师姓名
     */
    @TableField("teacher_name")
    private String teacherName;

    /**
     * 工号
     */
    @TableField("teacher_number")
    private String teacherNumber;

    /**
     * 密码
     */
    @TableField("password")
    private String password;

    /**
     * 性别，0表示男，1表示女
     */
    @TableField("gender")
    private Integer gender;

    /**
     * 联系方式
     */
    @TableField("phone")
    private String phone;

    /**
     * 职称
     */
    @TableField("teacher_title")
    private String title;

    /**
     * 学院id
     */
    @TableField("academy_id")
    private Long academyId;

    /**
     * 用户图片URL路径
     */
    @TableField("image")
    private String image;

    /**
     * 角色id
     */
    @TableField("role_id")
    private Long roleId;

    /**
     * 记录状态{0:正常状态；-1:删除状态；}
     */
    @TableField("status")
    private Integer status;

}