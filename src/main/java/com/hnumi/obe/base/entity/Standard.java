package com.hnumi.obe.base.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDate;

/**
* 工程教育认证标准库表
*
*/
@Data
//@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("base_standard")
public class Standard extends BaseEntity {

    /**
     * 工程教育认证标准库ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 专业ID
     */
    @TableField("standard_version")
    private Long standardVersion;

    @TableField( "standard_number")
    private Integer standardNumber;

    /**
     * 标准名称
     */
    @TableField("standard_name")
    private String standardName;

    /**
     * 工程认证指标点描述
     */
    @TableField("standard_description")
    private String standardDescription;

    /**
     * 指标点或者工程认证版本，parent_id=0是工程认证版本，非0是对应的认证版本的指标
     */
    @TableField("parent_id")
    private Long parentId;

    /**
     * 科
     */
    private String disciplineType;

    /**
     * 发布日期
     */
    private LocalDate releaseDate;

    /**
     * 记录状态{0:正常状态；-1:删除状态；1:停用状态；}
     */
    @TableField("status")
    private Integer status;

}