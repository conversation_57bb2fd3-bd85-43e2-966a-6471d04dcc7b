package com.hnumi.obe.base.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hnumi.obe.common.annotation.ExcelColumn;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
* 院系表
*
*/
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@TableName("base_academy")
public class Academy extends BaseEntity {

    /**
     * 学院id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 学院名称
     */
    @ExcelColumn("学院名称")
    private String academyName;

    /**
     * 院系负责人id
     */
    private Long academyPresidentId;

    /**
     * 记录状态{0:正常状态；-1:删除状态；1:停用状态；}
     */
    @TableField("status")
    private Integer status;


}