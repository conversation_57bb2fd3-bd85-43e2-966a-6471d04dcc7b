package com.hnumi.obe.base.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 教师角色关联表
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("base_teacher_roles")
public class TeacherRoles extends BaseEntity {

    /**
     * 教师ID
     */
    private String teacherNumber;

    /**
     * 角色ID
     */
    private Long roleId;

}
 