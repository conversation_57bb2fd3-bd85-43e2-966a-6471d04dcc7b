package com.hnumi.obe.base.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hnumi.obe.common.annotation.ExcelColumn;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
* 专业信息表
*
*/
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("base_major")
public class Major extends BaseEntity {

    /**
     * 专业id
     */
    @TableId(value = "major_id", type = IdType.AUTO)
    private Long majorId;

    /**
     * 专业名称
     */
    @ExcelColumn(value = "专业名称")
    @TableField("major_name")
    private String majorName;

    /**
     * 专业概述
     */
    @ExcelColumn(value = "专业概述")
    @TableField("professional_overview")
    private String professionalOverview;

    /**
     * 专业代码
     */
    @ExcelColumn(value = "专业代码")
    @TableField("major_code")
    private String majorCode;

    /**
     * 学院id
     */
    @TableField("academy_id")
    private Long academyId;

    /**
     * 专业负责人，教师id
     */
    @TableField("academy_leader_id")
    private Long academyLeaderId;

    /**
     * 学科类型
     */
    private String discipline;

    /**
     * 记录状态{0:正常状态；-1:删除状态；1:停用状态；}
     */
    @TableField("status")
    private Integer status;

    /**
     * 学院名称（导入时使用）
     */
    @TableField(exist = false)
    private String academyName;

    /**
     * 专业负责人工号（导入时使用）
     */
    @TableField(exist = false)
    private String directorJobNumber;

    /**
     * 专业负责人姓名（导入时使用）
     */
    @TableField(exist = false)
    private String directorName;
}