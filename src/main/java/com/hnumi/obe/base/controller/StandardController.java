package com.hnumi.obe.base.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hnumi.obe.base.dto.StandardCreateDTO;
import com.hnumi.obe.base.dto.StandardDTO;
import com.hnumi.obe.base.dto.StandardQueryDTO;
import com.hnumi.obe.base.entity.Standard;
import com.hnumi.obe.base.mapstruct.StandardConvert;
import com.hnumi.obe.base.service.IStandardService;
import com.hnumi.obe.base.vo.StandardVO;
import com.hnumi.obe.common.entity.R;
import com.hnumi.obe.common.entity.ResultCode;
import com.hnumi.obe.common.valid.ValidGroup;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 工程教育认证标准库表控制器
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@RestController
@RequestMapping("/base/standard")
public class StandardController {
    @Autowired
    IStandardService standardService;

    @PostMapping
    public Object addStandard(@Validated(ValidGroup.Add.class) @RequestBody StandardDTO dto) {
        return standardService.save(StandardConvert.INSTANCE.toEntity(dto));
    }

    /**
     * 新增工程教育认证标准（包含版本和指标点）
     *
     * @param createDTO 创建DTO
     * @return 创建结果
     */
    @PostMapping("/create-with-indicators")
    public R<Long> createStandardWithIndicators(@Validated @RequestBody StandardCreateDTO createDTO) {
        Long standardVersionId = standardService.createStandardWithIndicators(createDTO);
        return R.ok("创建工程教育认证标准成功", standardVersionId);
    }

    /**
     * 更新标准记录
     *
     * @param updateDTO 标准DTO
     * @return 更新结果
     */
    @PutMapping
    public R<Boolean> updateStandard(@Validated(ValidGroup.Add.class) @RequestBody StandardCreateDTO updateDTO) {
        boolean result = standardService.updateStandardRecord(updateDTO);
        return result ? R.ok("更新成功", true) : R.fail(ResultCode.ERROR);
    }

    /**
     * 根据ID删除标准记录
     * parent_id=0的内容（认证版本）：逻辑删除，设置status=-1
     * parent_id!=0的内容（指标点）：物理删除
     *
     * @param id 标准记录ID
     * @return 删除结果
     */
    @DeleteMapping("/{id}")
    public R<Boolean> deleteStandardById(@PathVariable("id") Long id) {
        boolean result = standardService.deleteStandardById(id);
        return result ? R.ok("删除成功", true) : R.fail(ResultCode.ERROR);
    }

    @GetMapping("/{id}")
    public R<StandardVO> getStandardById(@PathVariable("id") Long id) {
        StandardVO result = standardService.getStandardWithIndicators(id);
        return R.ok(result);
    }

    /**
     * 根据父ID查询指标点列表
     *
     * @param parentId 父ID（标准版本ID）
     * @return 指标点列表
     */
    @GetMapping("/indicators/{parentId}")
    public R<List<StandardVO>> getIndicatorsByParentId(@PathVariable("parentId") Long parentId) {
        List<Standard> indicators = standardService.getIndicatorsByParentId(parentId);
        List<StandardVO> indicatorVos = StandardConvert.INSTANCE.toVO(indicators);
        return R.ok("查询成功", indicatorVos);
    }

    /**
     * 分页查询工程教育认证标准库表列表
     *
     * @param query 查询条件
     * @return 分页结果
     */
    @PostMapping("/list")
    public Page<StandardVO> pageStandard(@RequestBody StandardQueryDTO query) {
        // 构建查询条件
        LambdaQueryWrapper<Standard> wrapper = Wrappers.lambdaQuery(Standard.class);

        // 根据标准名称模糊查询
        wrapper.like(StringUtils.hasText(query.getStandardName()), Standard::getStandardName, query.getStandardName());

        // 根据版本精确查询
        wrapper.eq(query.getStandardVersion() != null, Standard::getStandardVersion, query.getStandardVersion());

        // 根据父级ID查询
        wrapper.eq(query.getParentId() != null, Standard::getParentId, query.getParentId());

        // 根据状态查询
        wrapper.eq(query.getStatus() != null, Standard::getStatus, query.getStatus());
        wrapper.eq(StringUtils.hasText(query.getDisciplineType()), Standard::getDisciplineType, query.getDisciplineType());

        wrapper.eq(Standard::getParentId, 0);

        // 排序：parent_id=0的记录在前，然后按创建时间降序
        wrapper.orderByAsc(Standard::getParentId)
               .orderByDesc(Standard::getCreateTime);

        // 设置分页参数
        Page<Standard> page = query.getPage();
        // 执行分页查询
        List<Standard> data = standardService.list(page, wrapper);
        page.setRecords(data);
        return StandardConvert.INSTANCE.toPageVO(page);
    }
}
