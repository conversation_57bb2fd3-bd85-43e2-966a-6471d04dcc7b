package com.hnumi.obe.base.controller;

import com.hnumi.obe.base.service.IClassesService;
import com.hnumi.obe.common.entity.R;
import com.hnumi.obe.common.entity.ResultCode;
import com.hnumi.obe.common.valid.ValidGroup;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import com.hnumi.obe.base.mapstruct.ClassesConvert;
import com.hnumi.obe.base.dto.ClassesDTO;
import com.hnumi.obe.base.dto.ClassesQueryDTO;
import com.hnumi.obe.base.vo.ClassesVO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.web.multipart.MultipartFile;

import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 班级表
 */
@RestController
@RequestMapping("/base/classes")
public class ClassesController {
    @Autowired
    IClassesService classesService;

    @PostMapping
    public R<Boolean> addClasses(@Validated(ValidGroup.Add.class) @RequestBody ClassesDTO dto) {
        dto.setClassId(null);
        dto.setStatus(0);
        boolean result = classesService.save(ClassesConvert.INSTANCE.toEntity(dto));
        return result ? R.ok("新增班级成功", true) : R.fail(ResultCode.ERROR);
    }

    @PutMapping
    public Object updateClasses(@Validated(ValidGroup.Update.class) @RequestBody ClassesDTO dto) {
        boolean result = classesService.updateById(ClassesConvert.INSTANCE.toEntity(dto));
        return result ? R.ok("更新班级成功", true) : R.fail(ResultCode.ERROR);
    }

    @DeleteMapping("/{id}")
    public Object deleteClassesById(@PathVariable Long id) {
        boolean result = (boolean) classesService.deleteById(id);
        return result ? R.ok("删除班级成功", true) : R.fail(ResultCode.ERROR);
    }

    @DeleteMapping("/using/{id}")
    public Object stopClassesUsingById(@PathVariable Long id) {
        boolean result = (boolean) classesService.stopClassesUsingById(id);
        return result ? R.ok("停用班级成功", true) : R.fail(ResultCode.ERROR);
    }

    @GetMapping("/{id}")
    public Object getClassesById(@PathVariable Long id) {
        return classesService.getClassesDetail(id);
    }

    /**
     * 分页查询班级表列表
     *
     * @param query 查询条件
     * @return 分页结果
     */
    @PostMapping("/list")
    public Page<ClassesVO> pageClasses(@RequestBody ClassesQueryDTO query) {
        return classesService.pageClasses(query);
    }

    /**
     * 根据专业ID查询班级列表
     *
     * @param majorId 专业ID
     * @return 班级列表
     */
    @GetMapping("/{majorId}/list")
    public List<ClassesVO> listClassByMajorId(@PathVariable( "majorId") Long majorId) {
        return classesService.listClassByMajorId(majorId);
    }

    /**
     * 导入班级数据
     *
     * @param file Excel文件
     * @param majorId 专业ID
     * @return 导入结果
     */
    @PostMapping("/import")
    public R<Object> importClasses(@RequestParam("file") MultipartFile file, 
                                   @RequestParam("majorId") Long majorId) {
        try {
            // TODO: 实现Excel文件解析和班级数据导入逻辑
            // 这里应该调用service层的导入方法
            return R.ok("导入成功", true);
        } catch (Exception e) {
            return R.fail(ResultCode.ERROR, "导入失败：" + e.getMessage());
        }
    }

    /**
     * 预览同步班级人数
     * @param academyId 学院ID
     * @param majorId 专业ID
     * @param classId 班级ID
     * @return 班级ID、名称、原人数、实际人数
     */
    @GetMapping("/preview-sync-student-count")
    public R<List<Map<String, Object>>> previewSyncStudentCount(
            @RequestParam(value = "academyId", required = false) Long academyId,
            @RequestParam(value = "majorId", required = false) Long majorId,
            @RequestParam(value = "classId", required = false) Long classId) {
        List<Map<String, Object>> list = classesService.previewSyncStudentCount(academyId, majorId, classId);
        return R.ok("预览成功", list);
    }

    /**
     * 同步班级人数（全事务，部分失败则全部回滚）
     * @param academyId 学院ID
     * @param majorId 专业ID
     * @param classId 班级ID
     * @return 详细结果
     */
    @PostMapping("/sync-student-count")
    public R<Object> syncStudentCount(
            @RequestParam(value = "academyId", required = false) Long academyId,
            @RequestParam(value = "majorId", required = false) Long majorId,
            @RequestParam(value = "classId", required = false) Long classId) {
        try {
            Map<String, Object> result = classesService.syncStudentCount(academyId, majorId, classId);
            if ((Boolean) result.get("success")) {
                return R.ok(result.get("message").toString(), result);
            } else {
                // 只用两参，详细结构放message字符串里
                return R.fail(ResultCode.ERROR, result.get("message").toString() + ", 详情: " + result.toString());
            }
        } catch (Exception e) {
            return R.fail(ResultCode.ERROR, "同步班级人数失败：" + e.getMessage());
        }
    }
}
