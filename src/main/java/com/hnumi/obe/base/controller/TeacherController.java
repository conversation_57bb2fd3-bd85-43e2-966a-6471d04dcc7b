package com.hnumi.obe.base.controller;

import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hnumi.obe.base.dto.TeacherDTO;
import com.hnumi.obe.base.dto.TeacherQueryDTO;
import com.hnumi.obe.base.mapstruct.TeacherConvert;
import com.hnumi.obe.base.service.ITeacherService;
import com.hnumi.obe.base.vo.EnumVO;
import com.hnumi.obe.base.vo.TeacherExportVO;
import com.hnumi.obe.base.vo.TeacherTitleOptionsVO;
import com.hnumi.obe.base.vo.TeacherVO;
import com.hnumi.obe.common.entity.R;
import com.hnumi.obe.common.valid.ValidGroup;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.List;

/**
 * 教师管理控制器
 * 
 * 提供教师信息的增删改查、导入导出、状态管理等功能
 * 

 * <AUTHOR>
 * @since 2024-03-21
 */
@Slf4j
@RestController
@RequestMapping("/base/teacher")
public class TeacherController {

    @Autowired
    private ITeacherService teacherService;

    /**
     * 新增教师
     *
     * @param dto 教师信息DTO
     * @return 操作结果
     */
    @PostMapping
    public R<Boolean> addTeacher(@Validated(ValidGroup.Add.class) @RequestBody TeacherDTO dto) {
        log.info("新增教师: {}", dto.getUser().getRealName());
        Boolean result = (Boolean) teacherService.addTeacher(dto);
        return result ? R.ok("教师添加成功", true) : R.fail(500, "教师添加失败");
    }

    /**
     * 修改教师信息
     *
     * @param dto 教师信息DTO
     * @return 操作结果
     */
    @PutMapping
    public R<Boolean> updateTeacher(@Validated(ValidGroup.Update.class) @RequestBody TeacherDTO dto) {
        log.info("修改教师信息: id={}, name={}", dto.getTeacherId(), dto.getUser().getRealName());
        Boolean result = (Boolean) teacherService.updateTeacher(dto);
        return result ? R.ok("教师信息修改成功", true) : R.fail(500, "教师信息修改失败");
    }

    /**
     * 删除教师（逻辑删除）
     *
     * @param id 教师ID
     * @return 操作结果
     */
    @DeleteMapping("/{id}")
    public R<Boolean> deleteTeacherById(@PathVariable("id") Long id) {
        log.info("删除教师: id={}", id);
        Boolean result = (Boolean) teacherService.deleteTeacherById(id);
        return result ? R.ok("教师删除成功", true) : R.fail(500, "教师删除失败");
    }

    /**
     * 停用教师
     *
     * @param id 教师ID
     * @return 操作结果
     */
    @PutMapping("/disable/{id}")
    public R<Boolean> disableTeacher(@PathVariable("id") Long id) {
        log.info("停用教师: id={}", id);
        Boolean result = (Boolean) teacherService.stopTeacherUsingById(id);
        return result ? R.ok("教师停用成功", true) : R.fail(500, "教师停用失败");
    }

    /**
     * 启用教师
     *
     * @param id 教师ID
     * @return 操作结果
     */
    @PutMapping("/enable/{id}")
    public R<Boolean> enableTeacher(@PathVariable("id") Long id) {
        log.info("启用教师: id={}", id);
        Boolean result = teacherService.enableTeacherById(id);
        return result ? R.ok("教师启用成功", true) : R.fail(500, "教师启用失败");
    }

    /**
     * 根据ID查询教师详情
     *
     * @param id 教师ID
     * @return 教师信息
     */
    @GetMapping("/{id}")
    public R<TeacherVO> getTeacherById(@PathVariable Long id) {
        log.info("查询教师详情: id={}", id);
        TeacherVO teacherVO = teacherService.getDetailById(id);
        return R.ok(teacherVO);
    }

    /**
     * 分页查询教师列表
     *
     * @param query 查询条件
     * @return 分页结果
     */
    @GetMapping("/list")
    public R<Page<TeacherVO>> pageTeacher(@Valid TeacherQueryDTO query) {
        log.info("分页查询教师列表: current={}, size={}", query.getCurrent(), query.getSize());
        Page<TeacherVO> result = teacherService.pageTeacher(query);
        return R.ok(result);
    }

    /**
     * 导出教师信息
     *
     * @param query 查询条件
     * @param response HTTP响应
     * @throws IOException IO异常
     */
    @GetMapping("/export")
    public void exportTeacher(TeacherQueryDTO query, HttpServletResponse response) throws IOException {
        log.info("导出教师信息");
        
        // 设置响应头
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("教师信息", "UTF-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
        
        // 获取教师信息
        List<TeacherExportVO> teacherList = teacherService.exportTeacher(query);
        
        // 写入Excel
        EasyExcel.write(response.getOutputStream(), TeacherExportVO.class)
                .sheet("教师信息")
                .doWrite(teacherList);
        
        log.info("教师信息导出完成，共导出{}条记录", teacherList.size());
    }

    /**
     * 导入教师信息
     *
     * @param file Excel文件
     * @return 导入结果
     * @throws Exception 异常
     */
    @PostMapping("/import")
    public R<Object> importTeacher(@RequestParam("file") MultipartFile file) throws Exception {
        log.info("导入教师信息: fileName={}", file.getOriginalFilename());
        
        if (file.isEmpty()) {
            return R.fail(400, "上传文件不能为空");
        }
        
        try {
            InputStream inputStream = file.getInputStream();
            Object result = teacherService.importTeacher(inputStream);
            
            log.info("教师信息导入完成: result={}", result);
            return R.ok("教师信息导入处理完成", result);
            
        } catch (Exception e) {
            log.error("教师信息导入失败: fileName={}", file.getOriginalFilename(), e);
            return R.fail(500, "教师信息导入失败: " + e.getMessage());
        }
    }

    /**
     * 获取教师职称选项
     *
     * @return 职称选项列表
     */
    @GetMapping("/teacher-title-options")
    public R<List<TeacherTitleOptionsVO>> getTeacherTitleOptions() {
        List<TeacherTitleOptionsVO> options = teacherService.getTeacherTitleOptions();
        return R.ok(options);
    }

    /**
     * 获取教师详细信息
     *
     * @param teacherId 教师ID
     * @return 教师详细信息
     */
    @GetMapping("/detail/{teacherId}")
    public R<TeacherVO> getTeacherDetail(@PathVariable("teacherId") Long teacherId) {
        log.info("获取教师详细信息: teacherId={}", teacherId);
        TeacherVO teacherVO = teacherService.getDetailById(teacherId);
        return R.ok(teacherVO);
    }

    /**
     * 根据学院ID获取教师列表
     *
     * @param academyId 学院ID
     * @return 教师选项列表
     */
    @GetMapping("/teacher-by-academy")
    public R<List<EnumVO.Option<String>>> getTeacherByAcademy(@RequestParam("academyId") Long academyId) {
        log.info("根据学院ID获取教师列表: academyId={}", academyId);
        List<EnumVO.Option<String>> teachers = teacherService.getTeacherByAcademyId(academyId);
        return R.ok(teachers);
    }

    /**
     * 根据专业ID获取教师列表
     *
     * @param majorId 专业ID
     * @return 教师选项列表
     */
    @GetMapping("/teacher-by-major")
    public R<List<EnumVO.Option<String>>> getTeacherByMajor(@RequestParam("majorId") Long majorId) {
        log.info("根据学院ID获取教师列表: academyId={}", majorId);
        List<EnumVO.Option<String>> teachers = teacherService.getTeacherByMajorId(majorId);
        return R.ok(teachers);
    }

    /**
     * 批量删除教师
     *
     * @param ids 教师ID列表
     * @return 操作结果
     */
    @DeleteMapping("/batch")
    public R<Boolean> batchDeleteTeachers(@RequestBody List<Long> ids) {
        log.info("批量删除教师: ids={}", ids);
        Boolean result = teacherService.batchDeleteTeachers(ids);
        return result ? R.ok("批量删除成功", true) : R.fail(500, "批量删除失败");
    }

    /**
     * 重置教师密码
     *
     * @param id 教师ID
     * @return 操作结果
     */
    @PutMapping("/reset-password/{id}")
    public R<Boolean> resetTeacherPassword(@PathVariable("id") Long id) {
        log.info("重置教师密码: id={}", id);
        Boolean result = teacherService.resetPassword(id);
        return result ? R.ok("密码重置成功", true) : R.fail(500, "密码重置失败");
    }

    /**
     * 获取教师统计信息
     *
     * @return 统计信息
     */
    @GetMapping("/statistics")
    public R<Object> getTeacherStatistics() {
        log.info("获取教师统计信息");
        Object statistics = teacherService.getTeacherStatistics();
        return R.ok(statistics);
    }

    /**
     * 获取教师组织结构树
     *
     * @return 组织结构树
     */
    @GetMapping("/tree")
    public R<Object> getTeacherTree() {
        log.info("获取教师组织结构树");
        Object tree = teacherService.getTeacherTree();
        return R.ok(tree);
    }

    /**
     * 获取教师下拉列表的数据
     * @return
     */
    @GetMapping("/options")
    public R<Object> getTeacherOptions() {
        Object tree = teacherService.getTeacherOptions();
        return R.ok(tree);
    }
}
