package com.hnumi.obe.base.controller;

import com.alibaba.excel.EasyExcel;
import com.hnumi.obe.base.entity.Student;
import com.hnumi.obe.base.service.IStudentService;
import com.hnumi.obe.base.vo.StudentExportVO;
import com.hnumi.obe.common.valid.ValidGroup;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import com.hnumi.obe.base.mapstruct.StudentConvert;
import com.hnumi.obe.base.dto.StudentDTO;
import com.hnumi.obe.base.dto.StudentQueryDTO;
import com.hnumi.obe.base.vo.StudentVO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;

import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
* 学生表
*
*/
@RestController
@RequestMapping("/base/student")
public class StudentController {
    @Autowired
    IStudentService studentService;

    @PostMapping
    public Object addStudent(@Validated(ValidGroup.Add.class) @RequestBody StudentDTO dto) {
        return studentService.addStudent(dto);
    }

    @PutMapping
    public Object updateStudent(@Validated(ValidGroup.Update.class) @RequestBody StudentDTO dto) {
        return studentService.updateStudent(dto);
    }

    @DeleteMapping("/{id}")
    public Object deleteStudentById(@PathVariable("id") Long id) {
        return studentService.deleteStudentById(id);
    }

    @DeleteMapping("/using/{id}")
    public Object stopStudentUsingById(@PathVariable("id") Long id) {
        return studentService.stopStudentUsingById(id);
    }

    @GetMapping("/{id}")
    public Object getStudentById(@PathVariable("id") Long id) {
        return StudentConvert.INSTANCE.toVO(studentService.getById(id));
    }

    /**
     * 分页查询学生表列表
     *
     * @param query 查询条件
     * @return 分页结果
     */
    @PostMapping("/list")
    public Page<StudentVO> pageStudent(@RequestBody StudentQueryDTO query) {
        return studentService.pageStudent(query);
    }

    @GetMapping("/detail")
    public Object getStudentDetailById(@RequestParam("id") Long id) {
        return studentService.getStudentDetailById(id);
    }

    @PostMapping("/import")
    public Object importStudent(@RequestParam("file") MultipartFile file) throws IOException {
        InputStream inputStream = file.getInputStream();
        return studentService.importStudent(inputStream);
    }

    @GetMapping("/export")
    public void exportStudent(StudentQueryDTO query, HttpServletResponse response) throws IOException {
        // 设置响应头
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("学生信息", "UTF-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
        // 获取所有学院信息
        List<StudentExportVO> studentList = studentService.getStudentList(query);
        // 写入Excel
        EasyExcel.write(response.getOutputStream(), StudentExportVO.class)
                .sheet("学生信息")
                .doWrite(studentList);
    }

    /**
     * 获取学生树结构
     */
    @GetMapping("/tree")
    public List<Object> listTree() {
        return studentService.listTree();
    }
}
