package com.hnumi.obe.base.controller;

import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hnumi.obe.base.dto.AcademyDTO;
import com.hnumi.obe.base.dto.AcademyQueryDTO;
import com.hnumi.obe.base.dto.AcademyDeanDTO;
import com.hnumi.obe.base.entity.Academy;
import com.hnumi.obe.base.mapstruct.AcademyConvert;
import com.hnumi.obe.base.service.IAcademyService;
import com.hnumi.obe.base.vo.AcademyExportVO;
import com.hnumi.obe.base.vo.AcademyVO;
import com.hnumi.obe.base.vo.AcademyImportResultVO;
import com.hnumi.obe.common.util.ExcelUtil;
import com.hnumi.obe.common.util.RequestUtil;
import com.hnumi.obe.common.util.StringUtil;
import com.hnumi.obe.common.valid.ValidGroup;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 院系表
 */
@RestController
@RequestMapping("/base/academy")
public class AcademyController {
    @Autowired
    IAcademyService academyService;

    @PostMapping
    public Object addAcademy(@Validated(ValidGroup.Add.class) @RequestBody AcademyDTO dto) {
        dto.setId(null);
        
        // 校验学院名称是否已存在
        if (academyService.isAcademyNameExists(dto.getAcademyName(), null)) {
            throw new RuntimeException("学院名称 '" + dto.getAcademyName() + "' 已存在，请使用其他名称");
        }
        
        Academy entity = AcademyConvert.INSTANCE.toEntity(dto);
        entity.setCreator(RequestUtil.getUserId());
        entity.setModifier(RequestUtil.getUserId());
        entity.setCreateTime(LocalDateTime.now());
        entity.setModifyTime(LocalDateTime.now());
        return academyService.save(entity); 
    }

    @PutMapping
    public Object updateAcademy(@Validated(ValidGroup.Update.class) @RequestBody AcademyDTO dto) {
        return academyService.updateAcademy(dto);
    }

    @DeleteMapping("/{id}")
    public Object deleteAcademyById(@PathVariable("id") Long id) {
        return academyService.deleteById(id);
    }

    @DeleteMapping("/using/{id}")
    public Object stopAcademyUsingById(@PathVariable("id") Long id) {
        return academyService.stopAcademyUsingById(id);
    }

    @PutMapping("/enable/{id}")
    public Object enableAcademyById(@PathVariable("id") Long id) {
        return academyService.enableAcademyById(id);
    }

    @GetMapping("/detail")
    public Object getAcademyDetailById(@RequestParam("id") Long id) {
        return academyService.getAcademyDetailById(id);
    }

    /**
     * 分页查询院系表列表
     *
     * @param query 查询条件
     * @return 分页结果
     */
    @PostMapping("/list")
    public IPage<AcademyVO> pageAcademy(@RequestBody AcademyQueryDTO query) {
        return academyService.pageList(query);
    }

    @PostMapping("/import")
    public Object importAcademy(@RequestParam("file") MultipartFile file) throws Exception {
        InputStream inputStream = file.getInputStream();
        
        // 读取Excel文件，只获取学院名称
        List<Academy> academyList = ExcelUtil.readAll(inputStream, Academy.class);

        if (academyList.isEmpty()) {
            // 使用新的结果格式返回错误
            return AcademyImportResultVO.failure(0, 0, 0, List.of("导入文件中没有有效的学院名称数据"));
        }
        
        // 调用新的导入服务方法
        return academyService.importAcademies(academyList);
    }

    @GetMapping("/export")
    public void exportAcademy(AcademyQueryDTO query, HttpServletResponse response) throws IOException {
        // 设置响应头
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("学院信息", "UTF-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
        // 获取所有学院信息
        List<AcademyExportVO> academyList = academyService.getAcademyList(query);
        // 写入Excel
        EasyExcel.write(response.getOutputStream(), AcademyExportVO.class)
                .sheet("学院信息")
                .doWrite(academyList);
    }

    @GetMapping("/dean-options")
    public Object getDeanOptions() {
        return academyService.getDeanOptions();
    }

    @GetMapping("/academy-options-for-major")
    public Object getAcademyOptionsForMajor() {
        return academyService.getAcademyOptionsForMajor();
    }

    @GetMapping("/academy-option-by-id")
    public Object getAcademyOptionById(@RequestParam("id") Long id) {
        return academyService.getAcademyOptionById(id);
    }

    @GetMapping("/statistics")
    public Object getAcademyStatistics(@RequestParam(value = "id", required = false) Long id) {
        return academyService.getAcademyStatistics(id);
    }

    /**
     * 设置学院院长
     *
     * @param setDeanDTO 设置院长参数
     * @return 操作结果
     */
    @PutMapping("/set-dean")
    public Object setDean(@Validated @RequestBody AcademyDeanDTO setDeanDTO) {
        return academyService.setDean(setDeanDTO);
    }
}
