package com.hnumi.obe.base.controller;

import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hnumi.obe.base.dto.MajorDTO;
import com.hnumi.obe.base.dto.MajorQueryDTO;
import com.hnumi.obe.base.entity.Major;
import com.hnumi.obe.base.mapstruct.MajorConvert;
import com.hnumi.obe.base.service.IMajorService;
import com.hnumi.obe.base.vo.*;
import com.hnumi.obe.common.entity.R;
import com.hnumi.obe.common.entity.ResultCode;
import com.hnumi.obe.common.util.ExcelUtil;
import com.hnumi.obe.common.util.RequestUtil;
import com.hnumi.obe.common.valid.ValidGroup;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.List;

import static com.hnumi.obe.common.exception.ServiceExceptionUtil.exception;

/**
 * 专业信息表控制器
 */
@Slf4j
@RestController
@RequestMapping("/base/major")
public class MajorController {
    @Autowired
    IMajorService majorService;

    /**
     * 新增专业
     */
    @PostMapping("/add")
    public R<Boolean> addMajor(@Validated(ValidGroup.Add.class) @RequestBody MajorDTO dto) {
        log.info("新增专业请求，专业名称：{}，专业代码：{}", dto.getMajorName(), dto.getMajorCode());
        
        // 检查专业名称是否已存在
        LambdaQueryWrapper<Major> nameWrapper = Wrappers.lambdaQuery(Major.class);
        nameWrapper.eq(Major::getMajorName, dto.getMajorName())
                   .eq(Major::getStatus, 0);
        if (majorService.count(nameWrapper) > 0) {
            throw exception(ResultCode.MAJOR_EXISTS);
        }
        
        // 检查专业代码是否已存在
        LambdaQueryWrapper<Major> codeWrapper = Wrappers.lambdaQuery(Major.class);
        codeWrapper.eq(Major::getMajorCode, dto.getMajorCode())
                   .eq(Major::getStatus, 0);
        if (majorService.count(codeWrapper) > 0) {
            throw exception(ResultCode.MAJOR_CODE_EXISTS);
        }
        
        dto.setId(null);
        dto.setStatus(0); // 默认启用状态
        dto.setCreator(RequestUtil.getUserId());
        dto.setCreateTime(LocalDateTime.now());
        
        boolean result = majorService.save(MajorConvert.INSTANCE.toEntity(dto));
        
        if (result) {
            log.info("新增专业成功，专业名称：{}，专业代码：{}", dto.getMajorName(), dto.getMajorCode());
        } else {
            log.error("新增专业失败，专业名称：{}，专业代码：{}", dto.getMajorName(), dto.getMajorCode());
        }
        
        return result ? R.ok("新增专业成功", true) : R.fail(ResultCode.ERROR);
    }

    /**
     * 更新专业
     */
    @PutMapping("/update")
    public R<Boolean> updateMajor(@Validated(ValidGroup.Update.class) @RequestBody MajorDTO dto) {
        dto.setModifier(RequestUtil.getUserId());
        dto.setModifyTime(LocalDateTime.now());
        boolean result = majorService.updateById(MajorConvert.INSTANCE.toEntity(dto));
        return result ? R.ok("更新专业成功", true) : R.fail(ResultCode.ERROR);
    }

    /**
     * 删除专业（逻辑删除）
     */
    @DeleteMapping("/delete")
    public R<Boolean> deleteMajorById(@RequestParam("id") Long id) {
        log.info("删除专业请求，ID：{}", id);
        boolean result = majorService.deleteById(id);
        return result ? R.ok("删除专业成功", true) : R.fail(ResultCode.ERROR);
    }

    /**
     * 停用专业
     */
    @PutMapping("/disable/{id}")
    public R<Boolean> stopMajorUsingById(@PathVariable("id") Long id) {
        log.info("停用专业请求，ID：{}", id);
        boolean result = majorService.stopMajorUsingById(id);
        return result ? R.ok("停用专业成功", true) : R.fail(ResultCode.ERROR);
    }

    /**
     * 根据ID获取专业信息
     */
    @GetMapping("/{id}")
    public R<MajorVO> getMajorById(@PathVariable("id") Long id) {
        log.info("获取专业信息请求，ID：{}", id);
        MajorVO result = majorService.getDetailById(id);
        return R.ok("获取专业信息成功", result);
    }

    /**
     * 分页查询专业信息表列表
     */
    @GetMapping("/list")
    public R<Page<MajorVO>> pageMajor(MajorQueryDTO query) {
        log.info("分页查询专业列表请求，页码：{}，页大小：{}", query.getCurrent(), query.getPageSize());
        Page<MajorVO> result = majorService.pageList(query);
        log.info("分页查询专业列表成功，总数：{}", result.getTotal());
        return R.ok("查询专业列表成功", result);
    }

    /**
     * 获取专业详情
     */
    @GetMapping("/detail/{id}")
    public R<MajorVO> getMajorDetailById(@PathVariable("id") Long id) {
        log.info("获取专业详情请求，ID：{}", id);
        MajorVO result = majorService.getDetailById(id);
        return R.ok("获取专业详情成功", result);
    }

    /**
     * 导入专业（优化版）
     */
    @PostMapping("/import")
    public R<MajorImportResultVO> importMajor(@RequestParam("file") MultipartFile file) throws Exception {
        log.info("开始导入专业数据，文件名：{}", file.getOriginalFilename());
        
        InputStream inputStream = file.getInputStream();
        List<Major> list = ExcelUtil.readAll(inputStream, Major.class);
        
        if (list.isEmpty()) {
            return R.ok("导入完成", MajorImportResultVO.failure(0, List.of("导入文件中没有有效的专业数据")));
        }
        
        MajorImportResultVO result = majorService.importMajorsOptimized(list);
        
        if (result.isSuccess()) {
            log.info("专业数据导入成功，成功：{}条，失败：{}条", result.getSuccessCount(), result.getFailCount());
            return R.ok("导入完成", result);
        } else {
            log.warn("专业数据导入失败，失败：{}条", result.getFailCount());
            return R.ok("导入完成", result);
        }
    }

    /**
     * 导出专业
     */
    @GetMapping("/export")
    public void exportMajor(MajorQueryDTO query, HttpServletResponse response) throws IOException {
        // 设置响应头
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("专业信息", "UTF-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
        // 获取所有专业信息
        List<MajorExportVO> majorList = majorService.getMajorList(query);
        // 写入Excel
        EasyExcel.write(response.getOutputStream(), MajorExportVO.class)
                .sheet("专业信息")
                .doWrite(majorList);
    }

    /**
     * 获取专业负责人选项
     */
    @GetMapping("/academy-leader-options")
    public R<List<AcademyLeaderOptionsVO>> getAcademyLeaderOptions() {
        log.info("获取专业负责人选项请求");
        List<AcademyLeaderOptionsVO> result = majorService.getAcademyLeaderOptions();
        log.info("获取专业负责人选项成功，数量：{}", result.size());
        return R.ok("获取专业负责人选项成功", result);
    }

    /**
     * 获取我负责的专业 
     */

    @GetMapping("/my-majors")
    public R<List<MajorDetailVO>> getMyMajors() {
        Long userId = RequestUtil.getUserId();
        List<MajorDetailVO> result = majorService.getMyMajors(userId);
        return R.ok("获取我负责的专业成功", result);
    }


    @GetMapping("/teacher/selector")
    public R<List<MajorSelectorVO>> getMajorSelectorByTeacherId() {
        Long teacherId = RequestUtil.getExtendId();
        List<MajorSelectorVO> result = majorService.listMajorSelectorByTeacherId(teacherId);
        return R.ok("获取我负责的专业成功", result);
    }
    @GetMapping("/course-leader/selector")
    public R<List<MajorSelectorVO>> getMajorSelectorByCourseLeader() {
        Long teacherId = RequestUtil.getExtendId();
        List<MajorSelectorVO> result = majorService.listMajorSelectorByCourseLeader(teacherId);
        return R.ok("获取我负责的专业成功", result);
    }

    @GetMapping("/{collegeId}/options")
    public R<List<MajorVO>> getMajorOptionsByCollegeId(@PathVariable("collegeId") Long collegeId) {
        List<MajorVO> result = majorService.getMajorByCollegeId(collegeId);
        return R.ok(result);
    }

    @GetMapping("/course/{courseId}")
    public R<MajorVO> getMajorByCourseId(@PathVariable("courseId") Long courseId) {
        MajorVO result = majorService.getMajorByCourseId(courseId);
        return R.ok(result);
    }
}
