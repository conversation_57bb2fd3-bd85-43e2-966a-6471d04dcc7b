package com.hnumi.obe.base.controller;

import com.hnumi.obe.base.vo.EnumVO;
import com.hnumi.obe.common.entity.R;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/enum")
public class EnumController {

    private static final EnumVO enumVO = new EnumVO();

    static {
        Map<String, Map<String, String>> mapmap = new HashMap<>();

        Map<String, String> planStatusMap = Map.of(
                "1", "已启用",
                "0", "未启用",
                "-1", "删除"
        );
        mapmap.put("planStatus", planStatusMap);

        Map<String, String> standardStatusMap = Map.of(
                "0", "启用",
                "1", "停用",
                "-1", "删除"
        );
        mapmap.put("standardStatus", standardStatusMap);

        Map<String, String> disciplineTypeMap = Map.of(
                "0", "其他",
                "1", "工科",
                "2", "文科",
                "3", "商科",
                "4", "理科",
                "5", "医学",
                "6", "艺术",
                "7", "农学"
        );
        mapmap.put("disciplineType", disciplineTypeMap);

        Map<String, String> majorStatusMap = Map.of(
                "0", "启用",
                "1", "停用",
                "-1", "删除"
        );
        mapmap.put("majorStatus", majorStatusMap);

        Map<String, String> courseCategoryMap = Map.of(
                "1", "公共基础课程",
                "2", "学科基础课程",
                "3", "专业基础课程",
                "4", "专业核心课程",
                "5", "专业选修课程",
                "6", "实践教学环节",
                "7", "毕业设计(论文)",
                "8", "其他（如创新创业）"
        );
        mapmap.put("courseCategory", courseCategoryMap);

        Map<String, String> courseNatureMap = Map.of(
                "1", "必修",
                "2", "选修",
                "3", "限选",
                "4", "任选"
        );
        mapmap.put("courseNature", courseNatureMap);

        Map<String, String> semesterPeriodMap = new HashMap<>();
        for (int i = 1; i <= 8; i++) {
            semesterPeriodMap.put(String.valueOf(i), "第 " + i + " 学期");
        }
        mapmap.put("semesterPeriod", semesterPeriodMap);

        enumVO.setMap(mapmap);

        Map<String, List<EnumVO.Option<String>>> listMap = new HashMap<>();

        mapmap.forEach((key, value) -> {
            List<EnumVO.Option<String>> optionList = value.entrySet().stream()
                    .map(entry -> new EnumVO.Option<>(entry.getValue(), entry.getKey()))
                    .collect(Collectors.toList());
            listMap.put(key, optionList);
        });

        enumVO.setList(listMap);
    }

    @GetMapping
    public R<EnumVO> enums() {
        return R.ok(enumVO);
    }
}
