# TaskWorkMapper 优化总结

## 优化概述

根据要求对 `TaskWorkMapper.java` 进行了全面优化，遵循项目现有的设计模式和性能最佳实践。

## 主要优化点

### 1. 代码风格一致性
- **采用三引号字符串格式**: 使用 `"""` 替代字符串数组格式，与 `CourseMapper` 保持一致
- **移除不必要的注解**: 删除了 `@Results` 和 `@Result` 注解，依赖 MyBatis 自动映射
- **简化导入**: 只保留必要的导入，移除了复杂的注解导入

### 2. 性能优化策略
- **优先使用子查询**: 所有统计计算都通过子查询实现，避免复杂的多表 JOIN
- **最小化数据库调用**: 主查询返回一行一个教学任务，避免数据重复
- **索引友好查询**: 基于主键和外键的查询条件，充分利用数据库索引

### 3. 查询结构优化

#### 主查询 `selectCoursesByTeacherId`
```sql
SELECT DISTINCT
    c.course_id,
    tw.id as task_id,
    c.course_name,
    c.course_code,
    c.plan_id,
    tw.task_term,
    tw.task_year,
    CASE WHEN tw.task_status = 0 THEN 1 ELSE 0 END as is_current_semester,
    (SELECT COUNT(DISTINCT twc.class_id) FROM task_worklist_classes twc WHERE twc.task_id = tw.id) as class_count,
    (SELECT COALESCE(SUM(bc.student_number), 0) FROM task_worklist_classes twc INNER JOIN base_classes bc ON twc.class_id = bc.class_id WHERE twc.task_id = tw.id AND bc.status = 0) as total_students,
    (SELECT COUNT(DISTINCT twt_inner.teacher_id) FROM task_worklist_teachers twt_inner WHERE twt_inner.task_id = tw.id) as teacher_count
FROM task_worklist tw
INNER JOIN tp_course c ON tw.course_id = c.course_id
INNER JOIN task_worklist_teachers twt ON tw.id = twt.task_id
WHERE twt.teacher_id = #{teacherId}
    AND tw.status = 0
    AND c.status = 0
ORDER BY tw.task_year DESC, tw.task_term DESC, c.course_name
```

**优化特点:**
- 使用子查询计算统计数据，避免 GROUP BY 复杂性
- 主查询只进行必要的 JOIN 操作
- 每个教学任务返回一行，数据结构清晰

#### 辅助查询 `getClassesByTaskId`
```sql
SELECT
    bc.class_id, bc.major_id, bc.class_name, bc.entrance_year,
    bc.headteacher_id, bc.student_number, bc.class_status,
    bc.status, bc.creator, bc.create_time, bc.modifier, bc.modify_time
FROM task_worklist_classes twc
INNER JOIN base_classes bc ON twc.class_id = bc.class_id
WHERE twc.task_id = #{taskId} AND bc.status = 0
ORDER BY bc.class_name
```

**优化特点:**
- 独立查询班级详情，避免主查询数据重复
- 可按需调用，提供灵活性
- 简单高效的 JOIN 操作

## 性能对比

| 优化前 | 优化后 |
|--------|--------|
| 使用字符串数组格式 | 使用三引号字符串格式 |
| 包含复杂的 @Results 映射 | 依赖自动映射 |
| 多个重复的查询方法 | 单一优化的查询方法 |
| 延迟加载可能导致 N+1 问题 | 可控的按需查询 |

## 使用建议

### 场景1: 只需要统计数据
```java
List<CourseStaticsByTaskVO> stats = taskWorkMapper.selectCoursesByTeacherId(teacherId);
```

### 场景2: 需要完整的班级详情
```java
List<CourseStaticsByTaskVO> results = taskWorkMapper.selectCoursesByTeacherId(teacherId);
for (CourseStaticsByTaskVO courseStats : results) {
    courseStats.setClasses(taskWorkMapper.getClassesByTaskId(courseStats.getTaskId()));
}
```

## 索引建议

为了最大化查询性能，建议确保以下索引存在：

1. `task_worklist_teachers(teacher_id, task_id)`
2. `task_worklist(id, course_id, status)`
3. `tp_course(course_id, status)`
4. `task_worklist_classes(task_id, class_id)`
5. `base_classes(class_id, status)`

## 总结

优化后的 `TaskWorkMapper` 具有以下优势：

1. **代码风格统一**: 与项目其他 Mapper 保持一致
2. **性能优异**: 通过子查询优化，减少数据库调用
3. **结构清晰**: 主查询专注于核心数据，辅助查询处理详情
4. **维护性好**: 代码简洁，易于理解和维护
5. **扩展性强**: 可以轻松添加新的查询条件或字段

这种优化方案在保持功能完整性的同时，显著提升了查询性能和代码质量。
