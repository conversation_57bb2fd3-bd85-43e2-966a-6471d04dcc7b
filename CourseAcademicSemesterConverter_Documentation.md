# 课程学年学期转换器文档

## 概述

`CourseAcademicSemesterConverter` 是一个专门用于处理入学年份与课程执行学期之间转换关系的工具类。它基于中国高等教育的学期制度设计，支持4年制本科教育的8个学期管理。

## 学期制度说明

### 基本规则
- **学制**: 4年制本科，共8个学期
- **入学时间**: 通常在秋季学期（9月）入学
- **学期安排**: 
  - 奇数学期（1,3,5,7）：秋季学期（9月-1月）
  - 偶数学期（2,4,6,8）：春季学期（2月-7月）

### 学期对应关系
| 课程学期 | 执行时间 | 学年 |
|---------|---------|------|
| 第1学期 | 入学年份9月 | 第一学年秋季 |
| 第2学期 | 入学年份+1年3月 | 第一学年春季 |
| 第3学期 | 入学年份+1年9月 | 第二学年秋季 |
| 第4学期 | 入学年份+2年3月 | 第二学年春季 |
| 第5学期 | 入学年份+2年9月 | 第三学年秋季 |
| 第6学期 | 入学年份+3年3月 | 第三学年春季 |
| 第7学期 | 入学年份+3年9月 | 第四学年秋季 |
| 第8学期 | 入学年份+4年3月 | 第四学年春季 |

## 主要功能

### 1. 正向转换：入学年份 + 课程学期 → 执行学年学期

```java
// 学生2023年9月入学，计算第3学期的执行时间
AcademicSemester semester3 = CourseAcademicSemesterConverter
    .getExecutionSemester(2023, 3);
System.out.println(semester3); // 输出：2024-2025学年秋季学期
```

### 2. 反向转换：执行学年学期 + 课程学期 → 入学年份

```java
// 已知2024年春季学期是某学生的第2学期，推算入学时间
AcademicSemester spring2024 = AcademicSemesterUtil.getAcademicSemester(2024, 3);
AcademicSemester enrollment = CourseAcademicSemesterConverter
    .getEnrollmentSemester(spring2024, 2);
System.out.println(enrollment); // 输出：2023-2024学年秋季学期
```

### 3. 课程学期计算

```java
// 计算2025年秋季学期对应2023年入学学生的第几学期
AcademicSemester autumn2025 = AcademicSemesterUtil.getAcademicSemester(2025, 9);
Integer courseSemester = CourseAcademicSemesterConverter
    .calculateCourseSemester(2023, autumn2025);
System.out.println(courseSemester); // 输出：5
```

## 详细API说明

### 核心转换方法

#### getExecutionSemester(Integer enrollmentYear, Integer courseSemester)
根据入学年份和课程执行学期获取执行年份和学期信息。

**参数:**
- `enrollmentYear`: 入学年份（如2023表示2023年9月入学）
- `courseSemester`: 课程执行学期（1-8）

**返回:** `AcademicSemester` 对象，包含执行学年学期信息

**示例:**
```java
AcademicSemester semester = CourseAcademicSemesterConverter.getExecutionSemester(2023, 1);
// 返回：2023-2024学年秋季学期
```

#### getEnrollmentSemester(AcademicSemester executionSemester, Integer courseSemester)
根据执行学年学期信息获取入学年份学期信息。

**参数:**
- `executionSemester`: 执行学年学期信息
- `courseSemester`: 课程执行学期（1-8）

**返回:** `AcademicSemester` 对象，包含入学学年学期信息

#### calculateCourseSemester(Integer enrollmentYear, AcademicSemester executionSemester)
根据入学年份和执行学年学期信息计算课程执行学期。

**参数:**
- `enrollmentYear`: 入学年份
- `executionSemester`: 执行学年学期信息

**返回:** 课程执行学期（1-8），如果无法计算则返回null

### 辅助功能方法

#### getAllCourseSemesters(Integer enrollmentYear)
获取指定入学年份的所有课程学期信息。

```java
AcademicSemester[] allSemesters = CourseAcademicSemesterConverter
    .getAllCourseSemesters(2023);
// 返回8个学期的完整信息数组
```

#### getCurrentCourseSemester(Integer enrollmentYear)
获取学生当前应该所在的课程学期。

```java
Integer currentSemester = CourseAcademicSemesterConverter
    .getCurrentCourseSemester(2023);
// 根据当前时间计算学生应该在第几学期
```

#### isValidCourseSemester(Integer enrollmentYear, AcademicSemester executionSemester)
判断指定的执行学期是否在有效的课程学期范围内。

```java
boolean isValid = CourseAcademicSemesterConverter
    .isValidCourseSemester(2023, executionSemester);
// 判断执行学期是否在1-8学期范围内
```

### 学生状态判断

#### isGraduated(Integer enrollmentYear)
判断学生是否已毕业。

```java
boolean graduated = CourseAcademicSemesterConverter.isGraduated(2020);
// 判断2020年入学的学生是否已毕业
```

#### isNotEnrolledYet(Integer enrollmentYear)
判断学生是否尚未入学。

```java
boolean notEnrolled = CourseAcademicSemesterConverter.isNotEnrolledYet(2025);
// 判断2025年入学的学生是否尚未入学
```

## 实际应用场景

### 1. 课程安排系统

```java
// 为2023年入学的学生安排第5学期课程
Integer enrollmentYear = 2023;
AcademicSemester semester5 = CourseAcademicSemesterConverter
    .getExecutionSemester(enrollmentYear, 5);

System.out.println("第5学期课程安排时间: " + semester5);
// 输出：第5学期课程安排时间: 2025-2026学年秋季学期
```

### 2. 学生学籍管理

```java
// 检查学生当前状态
Integer enrollmentYear = 2021;
Integer currentSemester = CourseAcademicSemesterConverter
    .getCurrentCourseSemester(enrollmentYear);

if (currentSemester == null) {
    if (CourseAcademicSemesterConverter.isGraduated(enrollmentYear)) {
        System.out.println("学生已毕业");
    } else {
        System.out.println("学生尚未入学");
    }
} else {
    System.out.println("学生当前在第" + currentSemester + "学期");
}
```

### 3. 教务报表生成

```java
// 生成某学期的教学报表
AcademicSemester reportSemester = AcademicSemesterUtil.getAcademicSemester(2024, 9);

// 找出该学期涉及的所有入学年份
for (int enrollmentYear = 2021; enrollmentYear <= 2024; enrollmentYear++) {
    Integer courseSemester = CourseAcademicSemesterConverter
        .calculateCourseSemester(enrollmentYear, reportSemester);
    
    if (courseSemester != null && courseSemester >= 1 && courseSemester <= 8) {
        System.out.printf("%d年入学学生在%s为第%d学期%n", 
                         enrollmentYear, reportSemester, courseSemester);
    }
}
```

### 4. 课程选课系统

```java
// 验证学生是否可以选择特定学期的课程
public boolean canSelectCourse(Integer studentEnrollmentYear, 
                              AcademicSemester courseSemester) {
    return CourseAcademicSemesterConverter
        .isValidCourseSemester(studentEnrollmentYear, courseSemester);
}
```

## 注意事项

1. **参数验证**: 课程学期必须在1-8之间，否则抛出 `IllegalArgumentException`
2. **null处理**: 所有方法对null参数都有适当处理，通常返回null
3. **时间依赖**: 部分方法（如`getCurrentCourseSemester`）依赖当前系统时间
4. **学制假设**: 基于4年制8学期的标准学制设计
5. **入学时间**: 假设学生在秋季学期（9月）入学

## 扩展建议

1. **灵活学制**: 可扩展支持3年制、5年制等不同学制
2. **入学时间**: 可支持春季入学的学生
3. **学期类型**: 可添加夏季学期、短学期等
4. **国际化**: 支持不同国家的学期制度
5. **配置化**: 通过配置文件定义学期规则

## 测试

项目包含完整的单元测试，覆盖所有功能和边界情况：

```bash
mvn test -Dtest=CourseAcademicSemesterConverterTest
```

测试包括：
- 正向和反向转换的准确性
- 边界情况处理
- 异常情况验证
- 双向转换一致性检查
