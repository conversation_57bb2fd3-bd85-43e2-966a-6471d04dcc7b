<?xml version="1.0"?>
<ruleset name="Custom Rules"
         xmlns="http://pmd.sourceforge.net/ruleset/2.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://pmd.sourceforge.net/ruleset/2.0.0 https://pmd.sourceforge.net/ruleset_2_0_0.xsd">

    <description>
        自定义PMD规则集，用于代码质量检查
    </description>
    <!-- 引用 PMD 内置规则集 -->
    <rule ref="category/java/bestpractices.xml" />  <!-- 最佳实践规则 :ml-citation{ref="3,6" data="citationList"} -->
    <rule ref="category/java/codestyle.xml" />      <!-- 代码风格规则 :ml-citation{ref="3" data="citationList"} -->
    <rule ref="category/java/errorprone.xml" />     <!-- 错误预防规则 :ml-citation{ref="6" data="citationList"} -->

    <!-- 自定义扩展规则（可选） -->
    <rule ref="category/java/design.xml/AvoidDeeplyNestedIfStmts" /> <!-- 禁止深层嵌套逻辑 :ml-citation{ref="6" data="citationList"} -->
</ruleset> 