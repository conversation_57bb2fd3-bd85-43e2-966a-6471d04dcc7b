@echo off
REM 设置控制台代码页为UTF-8
chcp 65001 > nul

setlocal enabledelayedexpansion

REM 获取脚本所在目录的绝对路径
set "SCRIPT_DIR=%~dp0"
set "PROJECT_ROOT=%cd%"
set "CHECK_RESULT_FILE=%PROJECT_ROOT%\target\check-result.txt"

REM 设置 Maven 环境变量
set "MAVEN_OPTS=-Dfile.encoding=UTF-8 -Dproject.build.sourceEncoding=UTF-8"

REM 创建输出目录
if not exist "%PROJECT_ROOT%\target" mkdir "%PROJECT_ROOT%\target"

REM 检查JAVA_HOME环境变量
if "%JAVA_HOME%"=="" (
    echo [ERROR] 未设置JAVA_HOME环境变量
    echo CHECK_FAILED > "%PROJECT_ROOT%\target\check-status.txt"
    exit /b 1
)

REM 检查Maven Wrapper是否存在
if not exist "%PROJECT_ROOT%\.mvn\mvnw.cmd" (
    echo [ERROR] Maven Wrapper不存在
    echo CHECK_FAILED > "%PROJECT_ROOT%\target\check-status.txt"
    exit /b 1
)

REM 设置Git配置
git config --local core.quotepath false

REM 获取暂存的Java文件
set "java_files="
for /f "tokens=*" %%f in ('git diff --cached --name-only --diff-filter=ACMR') do (
    set "file_path=%%f"
    powershell -Command "if ('!file_path!' -match '\.java$') { exit 0 } else { exit 1 }"
    if !errorlevel! equ 0 (
        if exist "!file_path!" (
            if "!java_files!"=="" (
                set "java_files=!file_path!"
            ) else (
                set "java_files=!java_files! !file_path!"
            )
        )
    )
)

if "!java_files!"=="" (
    echo [INFO] 没有检测到修改的Java文件，跳过代码检查。
    echo CHECK_PASSED > "%PROJECT_ROOT%\target\check-status.txt"
    exit /b 0
)

echo [INFO] 开始代码检查...

REM 运行Checkstyle检查
set "checkstyle_cmd=%PROJECT_ROOT%\.mvn\mvnw.cmd checkstyle:check"
call !checkstyle_cmd! > nul 2>&1
set checkstyle_result=%errorlevel%
if %checkstyle_result% neq 0 (
    echo [ERROR] Checkstyle检查失败
    if exist "%PROJECT_ROOT%\target\checkstyle-result.xml" (
        echo [ERROR] 错误详情：
        powershell -Command "Get-Content '%PROJECT_ROOT%\target\checkstyle-result.xml' -Encoding UTF8 | Select-String -Pattern '<error' -Context 0,1 | ForEach-Object { $_.Line }"
    )
    echo CHECK_FAILED > "%PROJECT_ROOT%\target\check-status.txt"
    exit /b 1
)

REM 运行PMD检查
@REM set "pmd_cmd=%PROJECT_ROOT%\.mvn\mvnw.cmd pmd:check -Dpmd.skip=false -Dpmd.failOnViolation=true -Dpmd.includes=!java_files!"
@REM call !pmd_cmd! > nul 2>&1
@REM set pmd_result=%errorlevel%
@REM if %pmd_result% neq 0 (
@REM     echo [ERROR] PMD检查失败
@REM     if exist "%PROJECT_ROOT%\target\pmd.xml" (
@REM         echo [ERROR] 错误详情：
@REM         powershell -Command "Get-Content '%PROJECT_ROOT%\target\pmd.xml' -Encoding UTF8 | Select-String -Pattern '<violation' -Context 0,1 | ForEach-Object { $_.Line }"
@REM     )
@REM     echo CHECK_FAILED > "%PROJECT_ROOT%\target\check-status.txt"
@REM     exit /b 1
@REM )

echo [INFO] 代码检查通过
echo CHECK_PASSED > "%PROJECT_ROOT%\target\check-status.txt"
exit /b 0 