#!/bin/sh
PROJECT_ROOT="$(git rev-parse --show-toplevel)"
cd "$PROJECT_ROOT"

# 设置 Git 编码
git config --local core.quotepath false
export LANG=zh_CN.UTF-8
export LC_ALL=zh_CN.UTF-8

if [ -f ".config/scripts/run-checks.bat" ]; then
    echo "开始执行代码检查..."
    echo "当前目录: $(pwd)"
    
    # 执行检查脚本
    .config/scripts/run-checks.bat
    
    # 检查结果文件
    if [ -f "target/check-status.txt" ]; then
        if grep -q "CHECK_PASSED" "target/check-status.txt"; then
            echo "代码检查通过"
            exit 0
        else
            echo "代码检查失败，请查看 target/check-result.txt 获取详细信息"
            exit 1
        fi
    else
        echo "未找到检查结果文件"
        exit 1
    fi
else
    echo "未找到代码检查脚本: .config/scripts/run-checks.bat"
    exit 1
fi

exit 0
