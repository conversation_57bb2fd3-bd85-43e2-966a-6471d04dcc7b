# 学年学期转换工具类文档

## 概述

`AcademicSemesterUtil` 是一个静态工具类，提供学年学期相关的转换和计算功能。配合 `AcademicSemester` 类，可以方便地处理教育系统中的学年学期逻辑。

## 核心类介绍

### AcademicSemester 类

学年学期信息类，包含以下字段：

- `year`: 年份
- `month`: 月份  
- `academicYear`: 执行年份（学年开始年份）
- `semesterType`: 学期类型（春季学期/秋季学期）
- `academicSemesterString`: 学年学期字符串表示

### AcademicSemesterUtil 工具类

提供静态方法进行学年学期相关操作。

## 学年学期规则

- **秋季学期**: 9月-12月，学年开始年份为当前年份
- **春季学期**: 1月-8月，学年开始年份为上一年
- **学年表示**: 如 "2024-2025学年"，表示从2024年9月到2025年8月

## 主要功能

### 1. 获取当前学年学期

```java
// 根据当前时间获取学年学期
AcademicSemester current = AcademicSemesterUtil.getCurrentAcademicSemester();
System.out.println(current); // 输出：2024-2025学年秋季学期
```

### 2. 根据指定时间获取学年学期

```java
// 根据年月获取
AcademicSemester semester1 = AcademicSemesterUtil.getAcademicSemester(2024, 10);
System.out.println(semester1); // 输出：2024-2025学年秋季学期

// 根据日期获取
LocalDate date = LocalDate.of(2025, 3, 15);
AcademicSemester semester2 = AcademicSemesterUtil.getAcademicSemester(date);
System.out.println(semester2); // 输出：2024-2025学年春季学期

// 根据日期时间获取
LocalDateTime dateTime = LocalDateTime.of(2025, 2, 20, 14, 30);
AcademicSemester semester3 = AcademicSemesterUtil.getAcademicSemester(dateTime);
System.out.println(semester3); // 输出：2024-2025学年春季学期
```

### 3. 学期判断

```java
// 判断是否为春季/秋季学期
boolean isSpring = AcademicSemesterUtil.isSpring(2025, 3); // true
boolean isAutumn = AcademicSemesterUtil.isAutumn(2024, 10); // true

// 通过AcademicSemester对象判断
AcademicSemester semester = AcademicSemesterUtil.getAcademicSemester(2024, 9);
boolean isAutumn2 = semester.isAutumn(); // true
boolean isSpring2 = semester.isSpring(); // false
```

### 4. 获取学年所有学期

```java
// 获取2024-2025学年的所有学期
List<AcademicSemester> semesters = AcademicSemesterUtil.getSemestersInAcademicYear(2024);
// 返回：[2024-2025学年秋季学期, 2024-2025学年春季学期]
```

### 5. 学期导航

```java
AcademicSemester current = AcademicSemesterUtil.getAcademicSemester(2024, 10);

// 获取下一个学期
AcademicSemester next = AcademicSemesterUtil.getNextSemester(current);
System.out.println(next); // 输出：2024-2025学年春季学期

// 获取上一个学期
AcademicSemester previous = AcademicSemesterUtil.getPreviousSemester(current);
System.out.println(previous); // 输出：2023-2024学年春季学期
```

### 6. 学期比较

```java
AcademicSemester autumn2024 = AcademicSemesterUtil.getAcademicSemester(2024, 9);
AcademicSemester spring2025 = AcademicSemesterUtil.getAcademicSemester(2025, 3);

int result = AcademicSemesterUtil.compareSemesters(autumn2024, spring2025);
// result < 0，表示autumn2024在spring2025之前
```

### 7. 字符串操作

```java
// 格式化学年学期
String formatted = AcademicSemesterUtil.formatAcademicSemester(2024, "秋季学期");
System.out.println(formatted); // 输出：2024-2025学年秋季学期

// 解析学年学期字符串
AcademicSemester parsed = AcademicSemesterUtil.parseAcademicSemester("2024-2025学年春季学期");
System.out.println(parsed.getAcademicYear()); // 输出：2024
```

### 8. 获取学期信息

```java
AcademicSemester semester = AcademicSemesterUtil.getAcademicSemester(2024, 10);

// 获取各种信息
Integer academicYear = semester.getAcademicYear(); // 2024
String semesterType = semester.getSemesterType(); // "秋季学期"
String academicYearString = semester.getAcademicYearString(); // "2024-2025"
Integer semesterCode = semester.getSemesterCode(); // 2 (1-春季，2-秋季)
String fullString = semester.getAcademicSemesterString(); // "2024-2025学年秋季学期"
```

## 实际应用场景

### 1. 教务系统中的学期管理

```java
// 获取当前学期，用于显示当前学期课程
AcademicSemester currentSemester = AcademicSemesterUtil.getCurrentAcademicSemester();
String currentSemesterDisplay = currentSemester.getAcademicSemesterString();

// 生成学期选择下拉列表
List<AcademicSemester> recentSemesters = new ArrayList<>();
for (int year = 2022; year <= 2024; year++) {
    recentSemesters.addAll(AcademicSemesterUtil.getSemestersInAcademicYear(year));
}
```

### 2. 学生学期计算

```java
// 计算学生当前所在学期
AcademicSemester enrollmentSemester = AcademicSemesterUtil.getAcademicSemester(2023, 9);
AcademicSemester currentSemester = AcademicSemesterUtil.getCurrentAcademicSemester();

// 可以进一步计算学生已完成的学期数等
```

### 3. 报表和统计

```java
// 按学年学期统计数据
AcademicSemester reportSemester = AcademicSemesterUtil.getAcademicSemester(reportDate);
String reportTitle = "《" + reportSemester.getAcademicSemesterString() + "教学统计报告》";
```

## 注意事项

1. **月份范围**: 月份必须在1-12之间，否则会抛出 `IllegalArgumentException`
2. **null处理**: 工具类方法对null参数有适当的处理，通常返回null而不是抛出异常
3. **线程安全**: 所有方法都是静态的且无状态，线程安全
4. **性能**: 所有计算都是基于简单的数学运算，性能良好

## 扩展建议

1. 可以根据学校的具体学期安排调整月份划分规则
2. 可以添加更多的学期类型（如夏季学期）
3. 可以集成到Spring Boot配置中，支持不同学校的不同规则
4. 可以添加国际化支持，支持多语言的学期名称

## 测试

项目包含完整的单元测试 `AcademicSemesterUtilTest`，覆盖了所有主要功能和边界情况。运行测试确保功能正确性：

```bash
mvn test -Dtest=AcademicSemesterUtilTest
```
