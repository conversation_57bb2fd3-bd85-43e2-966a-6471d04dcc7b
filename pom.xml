<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.hnumi</groupId>
    <artifactId>obe-server</artifactId>
    <version>1.0.0</version>
    <name>obe-server</name>
    <description>工程教育专业认证智能管理信息系统-后端</description>
    <properties>
        <ip2region.version>2.7.0</ip2region.version>
        <easyexcel.version>4.0.3</easyexcel.version>
        <pinyin4j.version>2.5.1</pinyin4j.version>
        <commons-pool2.version>2.12.1</commons-pool2.version>
        <mapstruct.version>1.5.5.Final</mapstruct.version>
        <fastjson2.version>2.0.57</fastjson2.version>
        <sa-token.version>1.42.0</sa-token.version>
        <hutool.version>5.8.37</hutool.version>
        <mybatis-plus.version>3.5.12</mybatis-plus.version>
        <druid.version>1.2.24</druid.version>
        <aliyun-java-sdk-core.version>4.7.5</aliyun-java-sdk-core.version>
        <aliyun-java-sdk-ecs.version>5.11.19</aliyun-java-sdk-ecs.version>
        <lombok-mapstruct-binding.version>0.2.0</lombok-mapstruct-binding.version>
        <java.version>21</java.version>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <maven.compiler.release>21</maven.compiler.release>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <spring-boot.version>3.4.5</spring-boot.version>
        <checkstyle.version>3.6.0</checkstyle.version>
        <pmd.version>3.26.0</pmd.version>
        <checkFile>com.hnumi.obe.common.log</checkFile>
        <password4j.version>1.8.3</password4j.version>
        <redisson.version>3.47.0</redisson.version>
        <projectlombok.version>1.18.38</projectlombok.version>
    </properties>
    <dependencies>
        <dependency>
            <groupId>org.lionsoul</groupId>
            <artifactId>ip2region</artifactId>
            <version>${ip2region.version}</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>${easyexcel.version}</version>
        </dependency>
        <dependency>
            <groupId>com.belerweb</groupId>
            <artifactId>pinyin4j</artifactId>
            <version>${pinyin4j.version}</version>
        </dependency>
        <!--引入阿里短信服务依赖-->
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-core</artifactId>
            <version>${aliyun-java-sdk-core.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>commons-logging</artifactId>
                    <groupId>commons-logging</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-ecs</artifactId>
            <version>${aliyun-java-sdk-ecs.version}</version>
        </dependency>
        <!-- Sa-Token 整合 Redis （使用 jdk 默认序列化方式） -->
        <dependency>
            <groupId>cn.dev33</groupId>
            <artifactId>sa-token-redis-template</artifactId>
            <version>${sa-token.version}</version>
        </dependency>
        <!-- Sa-Token 整合 Fastjson2 -->
        <dependency>
            <groupId>cn.dev33</groupId>
            <artifactId>sa-token-fastjson2</artifactId>
            <version>${sa-token.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>fastjson2</artifactId>
                    <groupId>com.alibaba.fastjson2</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-pool2</artifactId>
            <version>${commons-pool2.version}</version>
        </dependency>
        <!--增加Redis基本操作-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok-mapstruct-binding</artifactId>
            <version>${lombok-mapstruct-binding.version}</version>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
            <version>${mapstruct.version}</version>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <version>${mapstruct.version}</version>
        </dependency>
        <!--JSON处理-->
        <dependency>
            <groupId>com.alibaba.fastjson2</groupId>
            <artifactId>fastjson2</artifactId>
            <version>${fastjson2.version}</version>
        </dependency>
        <!-- Sa-Token 权限认证，在线文档：https://sa-token.cc -->
        <dependency>
            <groupId>cn.dev33</groupId>
            <artifactId>sa-token-spring-boot3-starter</artifactId>
            <version>${sa-token.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>${hutool.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>
        <!--MyBatis-Plus代码生成器-->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-generator</artifactId>
            <version>${mybatis-plus.version}</version>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-jsqlparser</artifactId>
            <version>${mybatis-plus.version}</version>
        </dependency>
        <!--MyBatis-Plus依赖-->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
            <version>${mybatis-plus.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
            <version>${projectlombok.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.password4j</groupId>
            <artifactId>password4j</artifactId>
            <version>${password4j.version}</version>
        </dependency>
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson-spring-boot-starter</artifactId>
            <version>${redisson.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-devtools</artifactId>
            <scope>runtime</scope>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.jetbrains</groupId>
            <artifactId>annotations</artifactId>
            <version>13.0</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.11.0</version>
                <configuration>
                    <source>${maven.compiler.source}</source>
                    <target>${maven.compiler.target}</target>
                    <release>${maven.compiler.release}</release>
                    <encoding>UTF-8</encoding>
                    <compilerArgs>
                        <arg>-Xlint:none</arg>
                        <arg>-parameters</arg>
                    </compilerArgs>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${mapstruct.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${projectlombok.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok-mapstruct-binding</artifactId>
                            <version>${lombok-mapstruct-binding.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <configuration>
                    <mainClass>com.hnumi.obe.AppRun</mainClass>
                    <addResources>true</addResources>
                </configuration>
                <executions>
                    <execution>
                        <id>repackage</id>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <!-- Maven Checkstyle Plugin -->
<!--            <plugin>-->
<!--                <groupId>org.apache.maven.plugins</groupId>-->
<!--                <artifactId>maven-checkstyle-plugin</artifactId>-->
<!--                <version>${checkstyle.version}</version>-->
<!--                <dependencies>-->
<!--                    <dependency>-->
<!--                        <groupId>com.puppycrawl.tools</groupId>-->
<!--                        <artifactId>checkstyle</artifactId>-->
<!--                        <version>10.12.7</version>-->
<!--                    </dependency>-->
<!--                </dependencies>-->
<!--                <executions>-->
<!--                    <execution>-->
<!--                        <id>validate</id>-->
<!--                        <phase>validate</phase>-->
<!--                        <goals>-->
<!--                            <goal>check</goal>-->
<!--                        </goals>-->
<!--                    </execution>-->
<!--                </executions>-->
<!--                <configuration>-->
<!--                    <configLocation>${project.basedir}/.config/google_checks.xml</configLocation>-->
<!--                    <inputEncoding>UTF-8</inputEncoding>-->
<!--                    <outputEncoding>UTF-8</outputEncoding>-->
<!--                    <consoleOutput>true</consoleOutput>-->
<!--                    <failsOnError>true</failsOnError>-->
<!--                    <linkXRef>false</linkXRef>-->
<!--                    <includes>${checkFile}</includes>-->
<!--                </configuration>-->
<!--            </plugin>-->

            <!-- Maven PMD Plugin -->
            <!--<plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-pmd-plugin</artifactId>
                <version>${pmd.version}</version>
                <executions>
                    <execution>
                        <id>validate</id>
                        <phase>validate</phase>
                        <goals>
                            <goal>check</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <linkXRef>false</linkXRef>
                    <minimumTokens>100</minimumTokens>
                    <targetJdk>21</targetJdk>
                    <rulesets>
                        <ruleset>${project.basedir}/.config/ruleset.xml</ruleset>
                    </rulesets>
                    <printFailingErrors>true</printFailingErrors>
                    <skipEmptyReport>false</skipEmptyReport>
                    <analysisCache>true</analysisCache>
                    <analysisCacheLocation>${project.build.directory}/pmd/pmd.cache</analysisCacheLocation>
                    <failOnViolation>true</failOnViolation>
                    <verbose>true</verbose>
                    &lt;!&ndash;<includes>${checkFile}/*</includes>&ndash;&gt;
                </configuration>
            </plugin>-->
        </plugins>
    </build>
</project>

