# PoMatrix数据清理功能实现文档

## 功能概述

在PoMatrixServiceImpl中实现了数据清理优化功能，用于清理引用已删除或不存在PO记录的PoMatrix记录，确保课程支撑矩阵数据的完整性和一致性。

## 实现的功能

### 1. 核心清理方法

#### 1.1 `cleanupInvalidPoMatrixRecords(Long planId)`
- **功能**：清理指定计划（或所有计划）的无效PoMatrix记录
- **参数**：planId - 计划ID，如果为null则清理所有计划的数据
- **返回值**：清理的记录数量
- **清理逻辑**：
  - 查询指定计划的所有有效PoMatrix记录
  - 批量查询这些记录引用的PO是否存在且有效（status=0）
  - 物理删除引用无效PO的PoMatrix记录

#### 1.2 `cleanupInvalidPoMatrixRecordsByCourseId(Long courseId)`
- **功能**：清理指定课程的无效PoMatrix记录
- **参数**：courseId - 课程ID
- **返回值**：清理的记录数量
- **清理逻辑**：与上述方法类似，但限定在特定课程范围内

### 2. 自动清理集成

#### 2.1 在查询方法中集成清理
修改了以下方法，在查询前自动执行清理：
- `getPoListByCourseId(Long courseId)`
- `getCourseIndicatorsByCourseId(Long courseId)`

这确保了在获取课程支撑矩阵数据时，自动清理无效引用，保证返回数据的准确性。

### 3. 性能优化特性

#### 3.1 批量查询优化
```java
// 批量查询有效的PO记录，避免逐条查询
LambdaQueryWrapper<Po> poWrapper = new LambdaQueryWrapper<>();
poWrapper.in(Po::getId, poIds)
        .eq(Po::getStatus, 0);
List<Po> validPoList = poMapper.selectList(poWrapper);
```

#### 3.2 集合操作优化
```java
// 使用Set进行高效的包含性检查
Set<Long> validPoIds = validPoList.stream()
        .map(Po::getId)
        .collect(Collectors.toSet());

// 过滤出无效的PoMatrix记录
List<Long> invalidMatrixIds = poMatrixList.stream()
        .filter(matrix -> matrix.getPoId() == null || !validPoIds.contains(matrix.getPoId()))
        .map(PoMatrix::getId)
        .collect(Collectors.toList());
```

#### 3.3 批量删除
```java
// 批量物理删除无效记录
int deletedCount = poMatrixMapper.deleteBatchIds(invalidMatrixIds);
```

## 支持功能

### 1. 定时清理任务 (`PoMatrixCleanupTask`)

#### 1.1 自动定时清理
```java
@Scheduled(cron = "0 0 2 * * ?") // 每天凌晨2点执行
public void cleanupInvalidPoMatrixRecords()
```

#### 1.2 手动清理接口
- `manualCleanup()` - 手动触发全量清理
- `cleanupByPlanId(Long planId)` - 清理指定计划

### 2. 管理API接口 (`PoMatrixManagementController`)

#### 2.1 清理接口
- `POST /api/po-matrix/management/cleanup/all` - 清理所有无效记录
- `POST /api/po-matrix/management/cleanup/plan/{planId}` - 清理指定计划
- `POST /api/po-matrix/management/cleanup/course/{courseId}` - 清理指定课程

#### 2.2 监控接口
- `GET /api/po-matrix/management/check/integrity` - 数据完整性检查
- `GET /api/po-matrix/management/stats/cleanup` - 清理统计信息

### 3. 测试支持 (`PoMatrixCleanupTest`)

提供了完整的单元测试，覆盖以下场景：
- 清理引用已删除PO的记录
- 清理引用不存在PO的记录
- 按课程ID清理
- 无无效记录时的处理
- 空参数处理

## 清理逻辑详解

### 1. 无效记录识别

无效的PoMatrix记录包括：
1. **poId为null**：PoMatrix记录中的poId字段为空
2. **引用已删除的PO**：引用的PO记录status=-1
3. **引用不存在的PO**：引用的PO记录在数据库中不存在

### 2. 清理策略

#### 2.1 物理删除
使用物理删除而非软删除，确保：
- 彻底清理无效数据
- 减少数据库存储空间
- 提高查询性能

#### 2.2 事务安全
所有清理操作都在事务中执行：
```java
@Override
@Transactional
public int cleanupInvalidPoMatrixRecords(Long planId)
```

#### 2.3 批量操作
- 批量查询：一次性查询所有需要验证的PO记录
- 批量删除：一次性删除所有无效的PoMatrix记录

### 3. 执行时机

#### 3.1 自动清理时机
1. **查询时清理**：在`getPoListByCourseId`和`getCourseIndicatorsByCourseId`方法中
2. **定时清理**：每天凌晨2点自动执行
3. **PO更新后**：可以在PO数据更新后触发清理

#### 3.2 手动清理时机
1. **数据维护**：通过管理API手动触发
2. **问题排查**：发现数据异常时手动清理
3. **系统升级**：系统升级后的数据清理

## 日志记录

### 1. 详细的操作日志
```java
log.info("开始清理无效的PoMatrix记录，planId: {}", planId);
log.info("成功清理了 {} 条无效的PoMatrix记录，删除的记录ID: {}", deletedCount, invalidMatrixIds);
```

### 2. 异常处理日志
```java
log.error("PoMatrix数据清理任务执行失败", e);
```

## 性能影响

### 1. 查询性能提升
- 清理无效数据后，查询结果更准确
- 减少了无效数据的处理开销

### 2. 存储空间优化
- 物理删除无效记录，释放存储空间
- 减少索引维护开销

### 3. 数据一致性保证
- 确保PoMatrix与PO数据的引用完整性
- 避免前端显示异常数据

## 注意事项

### 1. 数据备份
在执行大规模清理前，建议备份相关数据表。

### 2. 业务影响
清理操作可能会影响正在进行的业务操作，建议在业务低峰期执行。

### 3. 监控告警
建议对清理操作进行监控，当清理的记录数量异常时及时告警。

### 4. 权限控制
管理API接口应该有适当的权限控制，避免误操作。

这个实现确保了PoMatrix数据的完整性和一致性，同时提供了灵活的清理策略和完善的监控机制。
