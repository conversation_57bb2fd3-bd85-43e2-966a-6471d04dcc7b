# MyBatis-Plus TooManyResultsException 问题修复

## 问题描述

在使用MyBatis-Plus时遇到以下异常：

```
org.apache.ibatis.exceptions.TooManyResultsException: Expected one result (or null) to be returned by selectOne(), but found: 2
```

**错误位置**：
- 类：`MajorServiceImpl`
- 方法：`getMajorByAcademyLeaderId(Long userId)`
- 行号：218

## 问题原因分析

### 1. 根本原因
- `getMajorByAcademyLeaderId`方法使用了MyBatis-Plus的`getOne(wrapper)`方法
- `getOne()`方法内部调用`selectOne()`，期望返回0或1个结果
- 但数据库中存在多个专业记录具有相同的`academy_leader_id`值
- 这违反了方法的预期，导致异常

### 2. 数据库设计问题
从数据库表结构可以看到：
```sql
CREATE TABLE `base_major` (
  `major_id` bigint NOT NULL AUTO_INCREMENT COMMENT '专业id',
  `academy_leader_id` bigint NULL DEFAULT NULL COMMENT '专业负责人，用户ID',
  ...
  UNIQUE INDEX `base_major_leader_unique`(`academy_leader_id` ASC) USING BTREE
)
```

虽然有唯一索引`base_major_leader_unique`，但可能由于以下原因导致重复数据：
- 历史数据问题
- 索引被删除或修改
- 数据导入时绕过了约束检查
- NULL值不受唯一约束限制

### 3. 业务逻辑问题
从业务角度看，一个用户可能负责多个专业，这在实际场景中是合理的，但当前的方法设计假设一对一关系。

## 解决方案

### 方案1：修改现有方法，处理多结果情况（已实施）

**修改前**：
```java
@Override
public Major getMajorByAcademyLeaderId(Long userId) {
    LambdaQueryWrapper<Major> wrapper = Wrappers.lambdaQuery(Major.class);
    wrapper.eq(Major::getAcademyLeaderId, userId).eq(Major::getStatus, 0);
    return getOne(wrapper); // 这里会抛出异常
}
```

**修改后**：
```java
@Override
public Major getMajorByAcademyLeaderId(Long userId) {
    LambdaQueryWrapper<Major> wrapper = Wrappers.lambdaQuery(Major.class);
    wrapper.eq(Major::getAcademyLeaderId, userId).eq(Major::getStatus, 0);
    
    // 使用list()方法获取所有匹配的记录，然后返回第一个
    List<Major> majors = list(wrapper);
    if (majors.isEmpty()) {
        return null;
    }
    
    // 如果有多个结果，记录警告日志并返回第一个
    if (majors.size() > 1) {
        log.warn("发现多个专业具有相同的负责人ID: {}, 返回第一个专业: {}", userId, majors.get(0).getMajorId());
    }
    
    return majors.get(0);
}
```

**优点**：
- 向后兼容，不影响现有调用代码
- 提供了警告日志，便于发现数据问题
- 返回第一个匹配的结果，保证方法不会抛出异常

**缺点**：
- 可能隐藏了数据一致性问题
- 返回结果的选择逻辑不够明确

### 方案2：新增方法处理多结果情况（已实施）

新增了一个方法来明确处理多个结果的情况：

```java
/**
 * 根据专业负责人ID获取所有专业列表
 * @param userId 专业负责人ID
 * @return 专业列表
 */
@Override
public List<Major> getMajorsByAcademyLeaderId(Long userId) {
    LambdaQueryWrapper<Major> wrapper = Wrappers.lambdaQuery(Major.class);
    wrapper.eq(Major::getAcademyLeaderId, userId).eq(Major::getStatus, 0);
    return list(wrapper);
}
```

**优点**：
- 明确表达了可能返回多个结果的意图
- 调用方可以根据业务需求处理多个结果
- 不会隐藏数据问题

### 方案3：数据库层面的解决方案（建议）

#### 3.1 检查数据一致性
```sql
-- 查找重复的academy_leader_id
SELECT academy_leader_id, COUNT(*) as count
FROM base_major 
WHERE status = 0 AND academy_leader_id IS NOT NULL
GROUP BY academy_leader_id 
HAVING COUNT(*) > 1;
```

#### 3.2 清理重复数据
```sql
-- 保留最新的记录，删除旧的重复记录
DELETE m1 FROM base_major m1
INNER JOIN base_major m2 
WHERE m1.academy_leader_id = m2.academy_leader_id 
  AND m1.major_id < m2.major_id 
  AND m1.status = 0 
  AND m2.status = 0;
```

#### 3.3 确保唯一约束
```sql
-- 检查唯一索引是否存在
SHOW INDEX FROM base_major WHERE Key_name = 'base_major_leader_unique';

-- 如果不存在，重新创建
ALTER TABLE base_major 
ADD UNIQUE INDEX base_major_leader_unique(academy_leader_id);
```

## 最佳实践建议

### 1. 代码层面
- 使用`list()`方法代替`getOne()`当可能存在多个结果时
- 添加适当的日志记录来监控数据异常
- 为可能返回多个结果的场景提供专门的方法

### 2. 数据库层面
- 定期检查数据一致性
- 确保唯一约束正确设置
- 在数据导入时进行重复检查

### 3. 业务层面
- 明确业务规则：一个用户是否可以负责多个专业
- 如果允许一对多关系，调整数据库设计和业务逻辑
- 如果不允许，加强数据验证和约束

## 测试建议

### 1. 单元测试
```java
@Test
public void testGetMajorByAcademyLeaderIdWithMultipleResults() {
    // 测试多个结果的情况
    Long userId = 6192L;
    Major result = majorService.getMajorByAcademyLeaderId(userId);
    assertNotNull(result);
    
    // 验证返回的是第一个结果
    List<Major> allResults = majorService.getMajorsByAcademyLeaderId(userId);
    assertEquals(result.getMajorId(), allResults.get(0).getMajorId());
}
```

### 2. 集成测试
- 测试在有重复数据的情况下方法的行为
- 验证日志记录是否正确
- 确保不会抛出异常

## 监控和维护

### 1. 日志监控
- 监控警告日志中的重复数据报告
- 定期检查数据一致性

### 2. 数据维护
- 建立数据清理流程
- 在数据导入时加强验证

这个修复确保了系统的稳定性，同时保持了向后兼容性，并为将来的数据清理和业务规则调整提供了基础。
